<script>
    $(function () {
        let p = <%= profondeur %>
            $('.sortablefolder').sortable({
                start: function (event, ui) {
                    let start_pos = ui.item.index();
                    ui.item.data('start_pos', start_pos);
                },
                update: function (event, ui) {
                    let data = $(this).sortable('serialize');
                    console.log(data)
                    $.ajax({
                        data: data,
                        type: 'POST',
                        url: '/admin/reorder?rootFolder=<% if (typeof rootproj != 'undefined') {%><%= rootproj%><% } else {%>0<% } %>'
                })
                        .done(function (data) {
                            location.reload()
                            //alert('OK');
                        })
                        .fail(function (data) {
                            alert('ERROR :' + data)
                        })
                },
                axis: 'y'
            });
    });
</script>

<div id="main_orderfolder">
    <h3><%=__('admin.folder.order')%></h3>
    <% if (rootproj) { %>
    <p>
        <span><%= __('admin.content.BranchSelection') %></span>
        <span class="fw-bold"><%= rootproj %></span>
    </p>
    <% } %>

    <% if (folderName) { %>
    <h4><%=__('project') + ' ' + folderName %></h4>
    <% } %>

    <div class="container" id="synch">
        <% if (!rootproj) { %>
        <% if (userStatus === 'admin') { %>
        <%- include('../utils/admin/branch-form', { id: "synchro", label: "rootproj", route_url: "/admin/folderOrder" }) %>
        <% } %>
        <% } else { %>

        <% if (( listfolder === '') && ( rootproj !== '' ) && (folderName ==='') ) { %>

        <% if (listproject.length) { %>
        <%# liste des projets root %>
        <form id="synchro" action="/admin/folderOrder" method="post">
            <input type="hidden" value="<%= rootproj %>" name="rootproj" id="rootproj" />
            <div class="col-6 offset-3 d-flex flex-column">
                <label for="project" class="form-label"><%= __('admin.content.folder.order.ProjectSelection') %></label>
                <select class="form-select" name="idFolder" id="idFolder">
                    <% for (let i=0; i < listproject.length; i++) { %>
                    <option value="<%= listproject[i]['id'] %>"> <%= listproject[i]['folder_name']%> </option>
                    <% } %>
                </select>
            </div>

            <div class="d-flex justify-content-center my-3 gap-2">
                <input class="btn btn-secondary" type="submit" value="Choisir ce projet" />
                <a type="button" href="/admin/folderOrder" class="btn btn-outline-secondary"><%=__('cancel') %></a>
            </div>
        </form>
        <% } else { %>
            <%= __('admin.content.folder.permissions.noneAssigned') %>
        <% } %>
        <% } else { %>
        <% if (( listfolder != '') && ( rootproj != '' )) { %>

        <div class="container" id="orderfolderid">
            <%= __('admin.content.folder.order.submit') %>

            <%# Dans l'id du li on récupère le rank en propriété en mettant r_... et avec i_... on récupère automatiquement la valeur de l'id dans la valeur de l'ui 'sortable serialize ) %>
            <% for (let p = 2 ; p <= profondeur; p++) { %>
            <h3><%= __('admin.content.folder.order.depth') %> <%= p-1 %> :</h3>
            <ul id="sortable" class="list-group list-unstyled ui-sortable sortablefolder">
                <% for (let l = 0; l< listfolder.length; l++) {  %><%# On boucle sur les items %><% if (listfolder[l].level === p) { %>
                <li class="list-group-item ui-state-default"
                    id="r_<%= listfolder[l]['rank'] %>_i_<%= listfolder[l]['id'] %>"><%= listfolder[l]['path'] %></li>
                <% if (l < listfolder.length-1 ) { let affichage = 0; for (let m = l+1 ; m < listfolder.length-1; m++) { if (listfolder[m].level === p) {  if (affichage === 0) {%>
                <% if (listfolder[l]['path'].split('/')[listfolder[l]['path'].split('/').length-2] !== listfolder[m]['path'].split('/')[listfolder[m]['path'].split('/').length-2]) { %>
            </ul>
            <ul id="sortable" class="list-group list-unstyled ui-sortable sortablefolder">
                <%= listfolder[m]['path'].split('/')[listfolder[m]['path'].split('/').length-2]%>
                <% } affichage = 1;  }  }  }  }  }  } %>
            </ul>
            <% }%>
        </div>
        <% } %>
        <% } %>
        <% } %>
    </div>
</div>