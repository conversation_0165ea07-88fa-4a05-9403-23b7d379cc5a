<%- include('utils/title-content', { home: "home" }) %>

<!-- Hidden fields for JavaScript -->
<input type="hidden" id="user-id" value="<%= user ? user.id : '' %>">
<input type="hidden" id="project-id" value="<%= typeof projectId !== 'undefined' ? projectId : '' %>">
<input type="hidden" id="language" value="<%= lng || 'en' %>">

<script src="/js/searchable-dropdown.js"></script>
<script src="/js/archeogrid.js"></script>
<script src="/js/lib/imagesloaded.pkgd.min.js"></script>
<script src="/js/lib/masonry.pkgd.min.js"></script>
<script src="/js/mobile-vitrine.js"></script>
<script src="/js/selection-dropdown.js"></script>
<script src="/js/lazy-loading.js"></script>
<script src="/js/size-sliders.js"></script>

<link rel="stylesheet" href="/css/mobile-vitrine.css">
<link rel="stylesheet" href="/css/selection-dropdown.css">
<link rel="stylesheet" href="/css/size-sliders.css">
<link rel="stylesheet" href="/css/folders-tree-vitrine.css">

<div class="d-flex justify-content-between" style="height: 79vh;" id="GROS">

    <div id="menuGauche" class="hide-on-mobile d-flex flex-column justify-content-between col-3"
         style="max-height: none; overflow: visible; width: calc(25% + 20px);">
        
        <div id="left-top" class="d-flex flex-column gap-2">
            <!-- Navigation Links -->
            <div class="container">
                <a href="/thesaurusV/<%= root %>,<%= projectId %>" class="btn btn-outline-primary btn-sm mb-2">
                    <%=__('allThesauri')%>
                </a>
            </div>

            <!-- PACTOLS All List -->
            <div class="container">
                <a href="https://pactols.frantiq.fr/opentheso/" title="<%=__('see')%> <%=__('resource')%>">
                    <img src="../assets/images/pactolsbl.png" class="img-fluid mb-2" />
                </a>
                <h6><%= __('browseThesaurus')%> <%=__('by')%><%=__('branch')%></h6>
                
                <% for (let i=0; i < thesaurus.length; i++ ) { %>
                    <% if (thesaurus[i]['name'].indexOf('[') === -1) { %>
                        <div class="mb-2">
                            <a type="button" href="/thesaurusPactolsOneV/<%=root%>,<%= projectId %>,<%= thesaurus[i].id %>"
                               class="btn btn-vitrine-secondary underline"
                               title="<%= thesaurus[i]['name']%>">
                               <%= thesaurus[i]['name']%>
                               <% if (thesaurus[i].nb_tot_item) { %>
                                   <span class="badge badge-pill text-dark"> <%='('+thesaurus[i].nb_tot_item+')'%></span>
                               <% } %>
                            </a>
                        </div>
                    <% } %>
                <% } %>
            </div>
        </div>

        <div id="left-bot" style="display: none;">
            <!-- This section is not used in PACTOLS All view -->
        </div>
    </div>

    <div class="col-12 col-md-9" id="menuCentre" style="display: none;">
        <% if (locals.flash && locals.flash.ok) { %>
        <div class="m-2 alert alert-success text-capitalize">
            <%= flash.ok %>
        </div>
        <% } %>

        <%- include('explore/exploreVitrine') %>
    </div>

</div>

<!-- Scripts -->
<script src="/js/explore.js"></script>
<script src="/js/selectionVitrine.js"></script>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="thesaurus-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toastElement = document.getElementById("thesaurus-toast");
        if (toastElement && typeof bootstrap !== 'undefined') {
            bootstrap.Toast.getOrCreateInstance(toastElement);
        }

        // Initialize toast variables for selection functionality
        setTimeout(function() {
            const toastElement = document.getElementById("thesaurus-toast");
            if (toastElement) {
                window.toastContent = toastElement.querySelector(".toast-body");
                window.projectToast = bootstrap.Toast.getOrCreateInstance(toastElement);
            }
        }, 100);
    });
</script>

<!-- Server-side variables -->
<script>
    window.SERVER_DATA = {
        USER_READ: <%- JSON.stringify((user && user.read) ? user.read : []) %>,
        USER_STATUS: '<%= (user && user.user_status) ? user.user_status : "guest" %>',
        LNG: '<%= lng || "en" %>',
        USER_WRITE: <%- JSON.stringify((user && user.write) ? user.write : []) %>,
        PROJECT_ID: '<%= typeof projectId !== "undefined" ? projectId : "" %>'
    };
</script>