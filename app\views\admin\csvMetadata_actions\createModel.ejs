<style>
    #auto-complete-projects {
        margin-bottom: 0.6rem;
    }

    #branchSelector {
        margin-bottom: 0.6rem;
    }

    #csvFileSelector {
        display: flex;
        flex-wrap: wrap;
    }

    #csvFileSelector div {
        padding: 0.5rem;
        border: 1px solid #f0f0f0;
        margin: 0.2rem;
        border-radius: 5px;
    }

    #csvFileSelector div.selected {
        background-color: #f0f0f0;
    }

    #csvFileSelector div:hover {
        background-color: #f0f0f0;
    }

    #csvFileSelector div input {
        margin-left: 0.5rem;
    }

    .step2 input[type="submit"] {
        margin-top: 1rem;
    }

    .metadata-container {
        display: flex;
        padding: 0.6rem;
        border: solid 1px #f0f0f0;
        border-radius: 5px;
        margin-bottom: 1rem;
        justify-content: space-evenly;
        align-items: center;
    }

    .metadata-container h5 {
        width: 15%;
    }

    table {
        margin: 1rem 0 ;
        width: 100%;
    }

    table th{
        padding: 0.5rem;
        background-color: #f0f0f0;
    }

    table th i {
        cursor: pointer;
    }

    table td {
        padding: 0.5rem;
        border-top: #f0f0f0 solid 1px;
    }

    table tr {
        gap: 1rem;
    }

    table td:has(input[type="checkbox"]) {
        text-align: center;
    }

    #btn-actions-container {
        display: flex;
        gap: 1rem;
    }

    .col-md-7 {
        margin-bottom: 1rem;
    }

    .animated-row-content {
        overflow: hidden;
        max-height: 0;
        opacity: 0;
        max-width: 0;
        padding-top: 0.5rem;
        transition: max-height 0.5s ease, opacity 0.2s ease 0.5s, max-width 0.5s cubic-bezier(0.4, 0, 1, 1);
    }

    .statusSelector {
        transform: translateY(0.25rem);
        transition: transform 0.2s ease;
    }

    .statusSelector.expand {
        transform: translateY(0);
    }

    .statusSelector.expand + .animated-row-content {
        max-height: 50px;
        opacity: 1;
        max-width: 400px;
    }

    .statusSelector.expand + .animated-row-content select {
        width: 100%;
    }
</style>
<% if(step < 4) { %>
    <div class="container">
        <h4><%=__('admin.content.csvMetadata.model.CreateModelTitle')%></h4>
        <h6><%=__('admin.content.csvMetadata.model.CreateModelDescription')%></h6>
    </div>
<% }else if(status){ %>
    <div class="container">
        <h4><%=__('admin.content.csvMetadata.model.CreateModelEndTitle')%></h4>
        <h6><%=__('admin.content.csvMetadata.model.CreateModelEndDescription')%></h6>
    </div>
<% } %>

<div id="createModel-form" class="container">
    <% switch(step) {
        case 1: %>
            <form method="post" class="step1 row g-3">
                <input type="hidden" name="step" value="1">
                <input type="hidden" name="action" value="createModel">
                <div class="col-md-7">
                    <label for="branchSelector" class="form-label"><%= __('admin.content.BranchSelection') %></label>
                    <select name="requestedBranch" id="branchSelector" size="3" class="form-select" required>
                        <% for(const branch of branches) { %>
                            <option value="<%= branch.branchename %>" <% if( branch.branchename === 'pft3d' ) { %> selected <% } %> ><%= branch.description %></option>
                        <% } %>    
                    </select>

                    
                    <label for="projectSelector" class="form-label"><%= __('admin.content.ProjectSelection') %></label>
                    <input type="text" class="form-control" id="auto-complete-projects" placeholder="<%= __('admin.content.SearchProject') %>" >
                    <select name="project" size="15" class="form-select" id="projectSelector" required>
                        <% for(const project of listprojectByBranch[0].listproject) { %>
                            <option value="<%= project %>"><%= project %></option>
                        <% } %>  
                    </select>
                </div>
                <div class="col-12">
                    <input class="btn btn-primary" type="submit" value="<%=__('Next')%>">
                </div>
            </form>
            <% break;
        case 2: %>
            <% if(csvFiles.length > 0) { %>
                <form method="post" class="step2">
                    <input type="hidden" name="step" value="2">
                    <input type="hidden" name="action" value="createModel">
                    <input type="hidden" name="requestedBranch" value="<%= requestedBranch %>">
                    <input type="hidden" name="projectFolder" value="<%= projectFolder %>">

                    <div id="csvFileSelectorContainer" class="col-md-7">
                        <h5><%= __('admin.content.csvMetadata.ListOfCSV') %> <%= projectFolder %></h5> 
                        <fieldset id="csvFileSelector" class="form-group">
                            <% for(const file of csvFiles) { %>
                                <div>
                                    <label for="<%= file.name %>"><%= file.name %></label>
                                    <input type="radio" name="csvFile" value="<%= file.name %>" id="<%= file.name %>" required <% if( file.name === default_values.csv_file_name ) { %> checked <% } %> >
                                </div>
                            <% } %>
                        </fieldset>   
                    </div>

                    <div class="col-md-7">
                        <label for="delimiter" class="form-label"><%=__('Delimiter')%></label>
                        <br>
                        <i style="color: grey;"><%= __('admin.content.csvMetadata.UseTab') %></i>
                        <input type="checkbox" class="form-check-input" id="delimiterIsTab" name="delimiterIsTab" <% if( default_values.delimiterIsTab ) { %> checked <% } %>>
                        <input type="text" class="form-control" id="delimiter" name="delimiter" value="<%= default_values.csv_delimiter?.trim() ?? '#' %>" required <% if( default_values.delimiterIsTab ) { %> disabled <% } %>>
                    </div>
                    
                    <div class="col-md-7">
                        <label for="modelName" class="form-label"><%= __('admin.content.csvMetadata.ModelName') %></label>
                        <input type="text" class="form-control" id="modelName" name="modelName" value="<%= default_values.model_name ?? '' %>" required>
                    </div>

                    <div class="input-container col-md-7" style="margin-bottom: 0.6rem;">
                        <label for="modelType" class="form-label"><%= __('admin.content.csvMetadata.ModelItemType') %></label>
                        <select class="form-control" name="modelItemType" id="modelType">
                            <option value="file" <% if( default_values.modelItemType === 'file' ) { %> selected <% } %> ><%=__('File')%></option>
                            <option value="folder" <% if( default_values.modelItemType === 'folder' ) { %> selected <% } %> ><%=__('Folder')%></option>
                            <option value="object" <% if( default_values.modelItemType === 'object' ) { %> selected <% } %> ><%=__('Object')%></option>
                        </select>
                    </div>

                    <input class="btn btn-primary" type="submit" value="<%=__('Next')%>">
                </form>
            <% } else { %>
                <div class="alert alert-warning"><%= __('admin.content.csvMetadata.NoCSV') %> <%= __('for') %>:  <%= projectFolder %></div>
            <% } %>
            <% break;
        case 3: %>
            <form method="post" class="step3">
                <input type="hidden" name="step" value="3">
                <input type="hidden" name="requestedBranch" value="<%= requestedBranch %>">
                <input type="hidden" name="action" value="createModel">
                <input type="hidden" name="projectFolder" value="<%= projectFolder %>">
                <input type="hidden" name="csvFile" value="<%= csvFile %>">
                <input type="hidden" name="delimiter" value="<%= csvDelimiter %>">
                <% if( exists_model.exists ) { %>
                    <input type="hidden" id="metadataModelId" name="metadataModelId" value="<%= exists_model.metadata_model_id %>">
                <% } %>
                <% if( exists_entry.exists ) { %>
                    <input type="hidden" id="csvMetadataId"   name="csvMetadataId"   value="<%= exists_entry.csv_metadata_id %>">
                <% } %>

                <div class="input-container col-md-7" style="margin-bottom: 0.6rem;">
                    <label for="modelName"><%= __('admin.content.csvMetadata.ModelName') %></label> 
                    <input type="text" class="form-control" name="modelName" id="modelName" value="<%= modelName %>">
                </div>

                <input type="hidden" name="modelItemType" value="<%= modelType %>">

                <div class="input-container col-md-7" style="margin-bottom: 0.6rem;">
                    <label for="modelDesc"><%= __('ModelDescription') %></label> 
                    <textarea class="form-control" id="modelDesc" name="modelDesc"></textarea>
                </div>

                <table id="modelTable">
                    <thead>
                        <tr>
                            <th>Code <i class="fas fa-info-circle" title="<%=__('admin.content.csvMetadata.model.Code')%>"></i></th>
                            <th>Label <i class="fas fa-info-circle" title="<%=__('admin.content.csvMetadata.model.Label')%>"></i></th>
                            <th>Description <i class="fas fa-info-circle" title="<%=__('admin.content.csvMetadata.model.Description')%>"></i></th>
                            <th>Type <i class="fas fa-info-circle" title="<%=__('admin.content.csvMetadata.model.Type')%>"></i></th>
                            <th>Is Unique <i class="fas fa-info-circle" title="<%=__('admin.content.csvMetadata.model.IsUnique')%>"></i></th>
                            <th>Is Required <i class="fas fa-info-circle" title="<%=__('admin.content.csvMetadata.model.IsRequired')%>"></i></th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for(const {code, label} of columnInfo) { %>
                            <tr>
                                <td>
                                    <%= code %>
                                    <input type="hidden" name="<%= code %>-code" value="<%= code %>">
                                </td>
                                <td>
                                    <input type="text" class="form-control" name="<%= code %>-label" value="<%= label %>">
                                </td>
                                <td>
                                    <input type="text" class="form-control" name="<%= code %>-description">
                                </td>
                                <td>
                                    <select name="<%= code %>-type" class="form-select statusSelector" id="<%= code %>-type">
                                        <% for(const status of metadataStatusList) { %>
                                            <option value="<%= status %>" <% if(status === 'char') { %> selected <% } %> ><%= status %></option>    
                                        <% } %>
                                    </select>

                                    <div class="animated-row-content">
                                        <select name="<%= code %>-list" class="form-select listSelector" id="<%= code %>-list"></select>
                                    </div>
                                </td>
                                <td>
                                    <input type="checkbox" name="<%= code %>-isunique" checked>
                                </td>
                                <td>
                                    <input type="checkbox" name="<%= code %>-isrequired">
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
                <input class="btn btn-primary" type="submit" value="<%=__('Create')%>">
            </form>
            <% break;
        case 4: %>
        <% if(status){ %> 
                <div id="btn-actions-container">
                    <form action="/admin/csvMetadata" method="post">
                        <input type="hidden" name="step" value="1">
                        <input type="hidden" name="action" value="createTable">
                        <input type="hidden" name="csv_metadata_id" value="<%= csv_metadata_id %>">
                        <input id="btn-createTable" class="btn btn-primary" type="submit" value="<%= __('admin.content.csvMetadata.CreateTable') %>"> 
                    </form>
                    <form action="/admin/csvMetadata" method="post">
                        <input type="hidden" name="step" value="0">
                        <input type="hidden" name="action" value="ingestCSV">
                        <input type="hidden" name="csv_metadata_id" value="<%= csv_metadata_id %>">    
                        <input id="btn-ingestCSV" class="btn btn-primary" type="submit" value="<%= __('admin.content.csvMetadata.IngestCSV') %>">
                    </form>
                    <form action="/admin/csvMetadata" method="get">    
                        <input id="btn-menuCSV" class="btn btn-secondary" type="submit" value="<%= __('admin.content.csvMetadata.Menu') %>">
                    </form>
                </div>
            <% } else { %>
                <div class="alert alert-danger"><%= bad %></div>
            <% } %>
            <% break;
    } %>
</div>

<% switch (step) {
    case 1: %>
        <script>
            const listprojectByBranch = JSON.parse('<%- JSON.stringify(listprojectByBranch) %>');

            document.addEventListener("DOMContentLoaded", () => {
                const branchSelector = document.getElementById("branchSelector");
                const projectSelector = document.getElementById("projectSelector");

                branchSelector.addEventListener("change", (e) => {
                    const projects = listprojectByBranch.find(p => p.branch === branchSelector.value)?.listproject;

                    projectSelector.innerHTML = '';
                    for(const project of projects){
                        const option = document.createElement("option");
                        option.value = project;
                        option.text = project;
                        projectSelector.appendChild(option);
                    }
                })

                const autoCompleteProjects = document.getElementById("auto-complete-projects");
                
                autoCompleteProjects.addEventListener('input', () => {
                    const value = autoCompleteProjects.value;

                    console.log(value);

                    for(const option of projectSelector.options){
                        if(option.text.toLowerCase().includes(value.toLowerCase())){
                            option.hidden = false;
                        }else{
                            option.hidden = true;
                        }
                    }
                });
            })
        </script>        
        <% break;
    case 2: %>
        <script>
            document.addEventListener("DOMContentLoaded", () => {
                const csvFileSelector = document.getElementById("csvFileSelector");
                csvFileSelector.addEventListener("change", (e) => {
                    const selectedElement = csvFileSelector.querySelectorAll("input[type=radio]:checked");
                    const selected = [...selectedElement].map(el => el.value);
                    console.log(selected);
                });

                const modelName = document.getElementById("modelName");

                const inputRadio = document.querySelectorAll("input[type=radio]");
                for(const input of inputRadio){
                    input.addEventListener("change", (e) => {
                        if(input.checked){
                            input.parentElement.classList.add("selected");

                            const modelNameValue = input.value.split(".")[0];
                            modelName.value = `pft3d_${modelNameValue}`;
                        }

                        inputRadio.forEach(el => {
                            if(el !== input){
                                el.parentElement.classList.remove("selected");
                            }
                        })
                    })
                }

                const checkboxDelimiterTab = document.getElementById("delimiterIsTab");
                var prevDel = '#';
                checkboxDelimiterTab.addEventListener("change", (e) => {
                    if(checkboxDelimiterTab.checked){
                        document.getElementById("delimiter").disabled = true;
                        prevDel = document.getElementById("delimiter").value;
                        document.getElementById("delimiter").value = '';
                    }else{
                        document.getElementById("delimiter").disabled = false;
                        document.getElementById("delimiter").value = prevDel;
                    }
                });
            })    
        </script>
        <% break;
    case 3: %>
        <% if(exists_model.exists){ %>
            <script>
                document.addEventListener("DOMContentLoaded", () => {
                    setTimeout(() => {
                        if(!confirm(`Le modèle de métadonnées <%= modelName %> existe deja, si vous continuez le modèle sera remplacé et les anciennes données seront perdus.`)){
                            window.history.go(-1); 
                            return;
                        }
                    }, 200);
                })    
            </script>
        <% } %>
        <script>

            const thesaurusByType = <%- JSON.stringify(thesaurusByType) %>

            document.addEventListener("DOMContentLoaded", () => {
                const selectType = Array.from(document.getElementsByClassName("statusSelector"));
                const selectList = Array.from(document.getElementsByClassName("listSelector"));

                console.log(Object.keys(thesaurusByType))

                for(let i = 0; i < selectType.length; i++){
                    selectType[i].addEventListener("change", () => {
                        const type = selectType[i].value;
                        const wrapper = selectList[i].parentElement;
                        if(Object.keys(thesaurusByType).includes(type)){
                            selectList[i].innerHTML = '';
                            for(const item of thesaurusByType[type]){
                                const option = document.createElement("option");
                                option.value = item;
                                option.text = item;
                                selectList[i].appendChild(option);
                            }
                            selectType[i].classList.add("expand");
                        }else{
                            selectType[i].classList.remove("expand");
                        }
                    })
                }     
            })
        </script>
        <% break;
} %>