class MasonryLayout extends HTMLElement {
  static get observedAttributes() {
    return ['axis'];
  }

  connectedCallback() {
    this.axis = this.getAttribute('axis') || 'horizontal';
    this.gap = parseFloat(this.getAttribute('gap')) || 4;

    this.resizeObserver = new ResizeObserver(() => this.layout());
    this.resizeObserver.observe(this);

    this.style.display = 'flex';
    this.style.flexDirection = this.axis;
    this.style.flexWrap = 'wrap';
    this.style.gap = `${this.gap}px`;

    window.requestAnimationFrame(() => this.layout());
  }

  attributeChangedCallback(name, oldValue, newValue) {
    if (name === 'axis' && oldValue !== newValue) {
      this.axis = newValue;
      this.layout();
    }
  }

  layout() {
    const children = Array.from(this.children);
    const axis = this.axis === 'vertical' ? 'height' : 'width';
    const containerSize = this[`client${axis[0].toUpperCase()}${axis.slice(1)}`];

    // Mesure et tri
    const sizes = children.map(el => el.getBoundingClientRect()[axis]);
    const lines = [];
    const spaceleft = [];

    children.forEach((el, i) => {
      const size = sizes[i];
      let placed = false;

      let indexLine = 0;
      for (const line of lines) {
        const totalSize = line.reduce((sum, item) => {
          const s = item.getBoundingClientRect()[axis];
          return sum + s + this.gap;
        }, 0);

        if (totalSize + size <= containerSize) {
          line.push(el);
          spaceleft[indexLine] = containerSize - totalSize - size;
          placed = true;
          break;
        }
        indexLine++;
      }

      if (!placed) {
        lines.push([el]);
      }
    });

    // Ajustement des lignes
    lines.forEach((line, i) => {
      if(line.length < 5) return;
      let delta = (spaceleft[i] / line.length);
      line.forEach((el, j) => {
        if(j === line.length - 1) {
          delta = spaceleft[i] - (delta * (line.length - 1));
        }
        el.style[axis] = `${Math.floor(el.getBoundingClientRect()[axis]) + delta}px`;
      });
    });

    lines.sort((a, b) => {
      return b.length - a.length;
    });

    // Réinjection DOM
    this.innerHTML = '';
    lines.forEach(line => line.forEach(el => this.appendChild(el)));
  }

  disconnectedCallback() {
    this.resizeObserver.disconnect();
  }
}

customElements.define('masonry-layout', MasonryLayout);
