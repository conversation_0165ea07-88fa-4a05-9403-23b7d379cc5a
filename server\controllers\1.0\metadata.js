const db = require("../../helpers/db").default;
const responseHelper = require("../../helpers/response");
const {
  deleteThesMultiItem,
  insertOrUpdateThesItem,
  insertOrUpdateThesPeriodoItem,
  insertPassportNonUnique,
  insertPassportNonUniqueReplaceValue,
  insertThesItem,
  insertThesMultiItem,
  insertThesMultiItemWithQual,
  insertThesPactolsItem,
  insertThesPeriodoItem,
  udpateRAZPassportNonUnique,
  updateThes,
  updpateElemPassportNonUnique,
} = require("../../helpers/db_tools");
const { getuniqueArray, noproblemoArray, nodoublequote, noquote } = require("../../helpers/tools");

// Create url endpoint for /metadata/:item_id,root,model
// to get value of metadata for the id (file or folder or object) for the concerned metadata model
exports.getMetadataValue = async function (req, res) {
  let itemId = parseInt(req.params.item_id);
  let { root } = req.params;
  let { model } = req.params;
  let itemType = req.params.item_type;
  let lang = req.params.lang === 'fr' ? `'fr'` :  `'en'`;

  try {
    const metadata_value = await db.any(
      // pour récupérer aussi nb_images et la loc
      `SELECT get_metadata_json(${lang}, $1, $2, $3, $4) as ${model} `,
      [itemId, model, root, itemType]
    );
    // success

    if (metadata_value[0][model]) {
      res.status(200).sendData(metadata_value[0][model]);
    } else {
      res.status(200).sendData("0");
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error", e, req, res);
  }
};

// Create url endpoint for /metadatavalue/:item_id,:item_type,:model,:root,:lang PUT
// to post value of metadata for the id (file or folder or object) for the concerned metadata model
exports.createMetadataValue = async function (req, res) {
  let itemId = parseInt(req.params.item_id);
  let { root } = req.params;
  let itemType = req.params.item_type;
  let root_dir = "";
  let isunique = 1; // pour différencier les cas unique / non unique

  let resinfo = [];
  let error = 0;
  let userId = 0;
  let folder = [];
  let qualifier = ""; // pour qualifier une liaison dans le thesaurus_multi

  if (itemType === "folder") {
    try {
      const folder_name = await db.any(`SELECT folder_name FROM ${req.params.root}_folder ` + `WHERE id = $1 `, itemId);
      folder = folder_name;
    } catch (e) {
      responseHelper.sendError(500, "server_error", e, req, res);
    }
  } else if (itemType === "object") {
    for (let prop in req.body) {
      if (!prop.indexOf("idObj_")) {
        delete req.body[prop];
      } else if (!prop.indexOf("object_")) {
        delete req.body[prop];
      }
    }
    if (req.body.nakalaLink) {
      delete req.body.nakalaLink;
    }
    if (req.body.split) {
      delete req.body.split;
    }
  }

  //    console.log(req.body)
  // userId useless
  // NON !! On le garde pour renseigner la tables passport !
  if (req.body.userId) {
    userId = req.body.userId;
    delete req.body.userId;
  }
  if (req.body.idUserDeposant) {
    delete req.body.idUserDeposant;
  }
  if (req.body.nbObject) {
    delete req.body.nbObject;
  }
  if (req.body.rootUpload) {
    delete req.body.rootUpload;
  }
  if (req.body.geonames) {
    delete req.body.geonames;
  }
  if (req.body.assignRights) {
    delete req.body.assignRights;
  }
  if (req.body.typeDepot) {
    // demand ou aLTAG3D
    delete req.body.typeDepot;
  }
  if (req.body.id_parent) {
    // pour créer le folder du dépôt avant, inutile ici
    delete req.body.id_parent;
  }
  if (req.body.deposit) {
    // pour créer le nom du dépôt comme étant le nom du folder avant, inutile maintenant
    delete req.body.deposit;
  }
  if (req.body.subject) {
    // pour récupérer les mots-clés sujet de aLTAG3D, traité en amont dans app_cnd3d
    delete req.body.subject;
  }
  if (req.body.tailleProjet) {
    // pour récupérer la taille du projet à ingérer dans la table conservatoire3d_info traité en amont dans app_cnd3d
    delete req.body.tailleProjet;
  }
  if (req.body.qualifier) {
    qualifier = req.body.qualifier;
    delete req.body.qualifier;
  }

  if (req.body.rootDir) {
    if (req.body.rootDir === "/conservatoire3d") root_dir = folder[0].folder_name;
    else {
      root_dir = req.body.rootDir.replace(/ /g, "");
    }
    delete req.body.rootDir;
  } else {
    root_dir = folder[0].folder_name;
  }

  for (let champ in req.body) {
    let champOrig = champ;
    if (champ.includes("#")) {
      if (parseInt(champ.split("#")[0]) === 1) {
        isunique = 1; // Le champ n'est pas unique on ne fait pas une mise à jour mais un autre insert
        champ = champ.split("#")[1];
      } else {
        isunique = 0;
        champ = champ.split("#")[1];
      }
    }
    if (champ.includes("opentheso")) {
      continue; // il est indexé par ailleurs ...
    }
    let value = req.body[champOrig];

    if (isunique) {
      if (!value.replace(/\s/g, "").length) {
        // le champ ne contient qu'une suite d'espaces, on les supprime
        value = "";
      }
    } else {
      let vide_cmpt = 0
      for (let v = 0; v < value.length; v++) {
        if (!value[v].replace(/\s/g, "").length) {
          // le champ ne contient qu'une suite d'espaces, on les supprime
          //value[v] = "";
          vide_cmpt++
        }
        if (value.length === vide_cmpt) {
          value = ""
        }
      }
    }
    if (champ === "project") {
      // récupérer l'id  metadata correspondant au rank 1 du model "project"
      db.one(
        `SELECT id FROM ${root}_metadata WHERE id_metadata_model = (SELECT id from ${root}_metadata_model ` +
          `WHERE name = $1 ) AND rank = 1 `,
        champ
      )
        .then((data) => {
          db.one(
            `INSERT INTO ${req.params.root}_passport (id_item, item_type, id_metadata, ` +
              `root_dir, value,  date_created, id_user ) VALUES ($1, $2, $3, $4, $5 , now(), $6 ) RETURNING id `,
            [itemId, itemType, data.id, root_dir, root_dir, userId]
          )
            .then((datap) => {
              //res.status(201).sendData([])
            })
            .catch((e) => {
              error = 1;
              console.log("unable to insert project first metadata ");
              //responseHelper.sendError(500, 'server_error', e, req, res)
              //console.log('ERROR update folder :', error)
            });
        })
        .catch((e) => {
          error = 1;
          console.log("no id_metadata match for project ... ");
          //responseHelper.sendError(500, 'server_error', e, req, res)
        });
    } else {
      // deal with metadata object ?
      //let value = req.body[champ]
      // 2 CAS : CAS 1/ si c'est une metadata => ok
      // TODO CAS 2/ si c'est une données thesaurus Pactols ou autre = theso => A tester
      // TODO cas 3/ si c'est une données pour le thesaurus maison = nomen(clature) = > TODO
      if (champ.startsWith("theso")) {
        // Insertion dans la table thesaurus  ou periodo
        // value = <id_thesaurus>_<id_thes_thesaurus>
        // champ = theso_<thesaurus>_id et theso_<thesaurus>_value (pour l'info mais on ne s'en sert pas pour indexer)

        // TODO PERIODO :
        // theso_periodo_id: '122_p0qhb66fgnh_-1600_-1401_fr',
        // champ : theso_periodo_id
        // value : 122_p0qhb66fgnh_-1600_-1401_fr  :

        if (champ.split("_")[2] === "id") {
          let thesaurus = champ.split("_")[1];
          if (thesaurus === "periodo") {
            let id_periodo = value.split("_")[0];
            let id_thes_periodo = value.split("_")[1];
            let lang = value.split("_")[4];
            // on insert dans le thesaurus Periodo item
            if (insertThesPeriodoItem(req.params.root, id_periodo, id_thes_periodo, itemId, itemType, lang, userId)) {
              // ?
            } else {
              console.log("server_error in metadata thesaurus periodo insert ");
              error = 1;
            }
          } else {
            let id_thesaurus = value.split("_")[0];
            let id_thes_thesaurus = value.split("_")[1];
            // ATTENTION : pour le conservatoire thesaurus = deposant NE DOIT PAS ALLER dans le thesaurus pactols
            // au 15/11/2020, l'indexation avec le thes pactols n'est plus traité ici
            // on insert dans le thesaurus thesaurus tout court !
            if (id_thesaurus) {
              insertOrUpdateThesItem(
                req.params.root,
                id_thesaurus,
                id_thes_thesaurus,
                thesaurus,
                itemId,
                itemType,
                userId
              );
            }
          }
        }
      } else if (champ.startsWith("multi")) {
        if (value !== "") {
          let tabValue = [];
          // Maintenant, on peut récupérer un tableau de valeur séparé par des virgules ??
          if (value.indexOf(",") !== -1) {
            tabValue = value.split(",");
          } else {
            tabValue[0] = value;
          }

          for (let i = 0; i < tabValue.length; i++) {
            // Préparer insertion dans la table thesaurus_multi_item
            // champ = multi_<thesaurus>_id
            // 'multi_geo_id': '418_12048459'
            // value = <id>_<id_thes>
            let id_thesaurus = tabValue[i].split("_")[0];
            let id_thes = tabValue[i].split("_")[1];
            let thesaurus = champ.split("_")[1].replace("_id", "");
            // regle : thesaurus multi : on ajoute sans regarder si ça existe deja
            if (qualifier === "") {
              if (insertThesMultiItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, userId)) {
                console.log("insert thes multi item ok");
              } else {
                // error
                console.log("error in insert thes multi item");
              }
            } else {
              if (
                insertThesMultiItemWithQual(
                  req.params.root,
                  id_thesaurus,
                  id_thes,
                  thesaurus,
                  itemId,
                  itemType,
                  userId,
                  qualifier
                )
              ) {
                console.log(`insert thes multi with qualifier item ok ${qualifier}`);
              } else {
                // error
                console.log("error in insert thes multi item");
              }
            }
          }
        }
      } else if (champ.startsWith("nomen")) {
        //  'nomen--plan_classement': '2_2',
        let id_thesaurus = value.split("_")[0];
        let id_thes_thesaurus = value.split("_")[1];
        let thesaurus = champ.split("--")[1];
        // Attention, on ne teste pas la valeur de retour de l'insert car même si ça se passe bien, il code retour
        // n'est pas renvoyé assez vite et ça fait planter la proc alors que tout s'est bien passé ...
        insertOrUpdateThesItem(req.params.root, id_thesaurus, id_thes_thesaurus, thesaurus, itemId, itemType, userId);
      } else if (champ.startsWith("latlng")) {
        // champ = latlng--<id_metadata>
        champ = champ.split("--")[1];
        if (value !== "") {
          value = value.split();
          let stri = `{"${value.join()}"}`;
          insertPassportNonUnique(req.params.root, itemId, itemType, champ, root_dir, stri, userId);
        }
      } else {
        // TODO : etudier le cas où dans les caractères de value il y a des " par exemple ...
        if (champ !== "idFolder") {
          //champ = champ.replace('metadata_', '')
          // Différencier le cas unique du cas non unique
          if (isunique) {
            let string_unique = "";
            if (value !== "") {
              if (typeof value == "string") {
                if (value.includes(",")) {
                  // DANS le cas où il y aurait une virgule dans le text, il faut mettre des "" pour que la valeur
                  // ne soit pas considérée comme un tableau avec plusieurs item !
                  string_unique = `{"${value.replace(/"/g, '\\"')}"}`;
                } else {
                  string_unique = `{${value.replace(/"/g, '\\"')}}`;
                }

                //value = value.split() // c'est la que value devient un tableau
              }
              insertPassportNonUnique(req.params.root, itemId, itemType, champ, root_dir, string_unique, userId);
            }
          } else {
            console.log(`non unique : ${champ}`);
            if (typeof value == "string") {
              if (value !== "") {
                value = value.split();
                let valo = getuniqueArray(value);

                // suppression des virgules à l'intérieur du tableau si besoin, on remplace par une chaine
                let goodvalo = noproblemoArray(valo, "ZOOB");

                // la chaine obtenu stri contient (peut-être ) des ZOOB à supprimer ensuite
                let stri = `{${goodvalo.join()}}`;
                if (stri !== "{}") {
                  insertPassportNonUniqueReplaceValue(
                    req.params.root,
                    itemId,
                    itemType,
                    champ,
                    root_dir,
                    stri,
                    userId,
                    "ZOOB"
                  );
                }
              }
            } else {
              let valo = getuniqueArray(value);

              if (valo.length > 0) {
                // suppression des virgules à l'intérieur du tableau si besoin, on remplace par une chaine
                let goodvalo = noproblemoArray(valo, "ZOOB");

                // la chaine obtenu stri contient (peut-être ) des ZOOB à supprimer ensuite
                let stri = `{${goodvalo.join()}}`;
                insertPassportNonUniqueReplaceValue(
                  req.params.root,
                  itemId,
                  itemType,
                  champ,
                  root_dir,
                  stri,
                  userId,
                  "ZOOB"
                );
              }
            }
          }
        }
      }
    }
  }

  if (error) {
    responseHelper.sendError(500, "server_error on insert metadata", error, req, res);
  } else {
    res.status(201).sendData(resinfo);
  }
};

// Create url endpoint for  /metadatavalue/:item_id,:item_type,:model,:root,:lang
// to put/update value of metadata for the id (file or folder or object) for the concerned metadata model
exports.patchMetadataValue = async function (req, res) {
  let itemId = parseInt(req.params.item_id);
  let itemType = req.params.item_type;
  let root_dir = "";
  let folder = "";
  let geo = 0; // pour savoir s'il faut aller mettre à jour le code geonames du dépôt
  let nomDepot = 0; // pour savoir s'il faut mettre à jour le nom du dépôt (fr ou en)
  let nakalaImage = 0; // pour savoir s'il faut mettre à jour l'image dans l'objet (CND3D)
  let nakalaImageValue = "";
  let lang = req.params.lang === 'fr' ? 'fr' : 'en' ;
  let { root } = req.params;
  let isunique = 1; // pour différencier les cas unique / non unique

  // récupérer root_dir (= folder_name de la table _folder pour les type folder)
  // todo : root_dir pour les type file ???
  if (itemType === "folder") {
    try {
      const folder_name = await db.any(`SELECT passport FROM ${req.params.root}_folder ` + `WHERE id = $1 `, itemId);
      folder = folder_name;
    } catch (e) {
      responseHelper.sendError(500, "server_error", e, req, res);
    }
  } else if (itemType === "file") {
    try {
      const root_dir = await db.any(
        `SELECT passport FROM ${root}_folder WHERE  id = (SELECT CAST( ltree2text(subpath(folder_path,0,1)) AS INT) ` +
          `FROM ${root}_folder fo INNER JOIN ${root}_file_folder fio ON fio.id_folder = fo.id ` +
          // ATTENTION au cas ou le file serait également dans un dossier virtuel (= folder_name NULL) : ne pas le récupérer
          `WHERE id_file = $1  AND folder_name IS NOT NULL )`,
        itemId
      );
      folder = root_dir;
    } catch (e) {
      responseHelper.sendError(500, "server_error", e, req, res);
    }
  } else if (itemType === "object") {
    // ERREUR CND3D : on prend le parent du folder attaché à l'objet pour récupérer la route ...
    // Or, dans la table des objets , on a une route !(root_dir) il suffit de la récupérer en tant que passport
    // pour coller à la requête précédente des cas folders?
    try {
      const root_dir = await db.any(
        `SELECT root_dir as passport  FROM ${root}_object WHERE id = $1`,
        itemId
        //'SELECT passport FROM '+root+'_folder WHERE  id = (SELECT CAST( ltree2text(subpath(folder_path,0,1)) AS INT) ' +
        //'FROM '+root+'_folder fo INNER JOIN '+root+'_folder_object ffo ON ffo.id_folder = fo.id ' +
        //'WHERE id_object = $1 )', itemId
      );
      folder = root_dir;
    } catch (e) {
      responseHelper.sendError(500, "server_error", e, req, res);
    }
  }

  // ATTENTION: root_dir doit être le nom du répertoire physique
  root_dir = folder[0].passport;
  let update_multi = [];
  let lot = 0; // 0 : on écrase les valeurs existantes , 1: on ajoute aux valeurs existantes qu'on ne connait pas à l'avance (traitement par lot)
  let erreur = 0;
  if (req.body.hasOwnProperty("update_multi")) {
    if (typeof req.body.update_multi == "string") update_multi[0] = req.body.update_multi;
    else update_multi = req.body.update_multi;
    delete req.body.update_multi;
  }
  if (req.body.hasOwnProperty("lot")) {
    // on récupère l'info traitement par lot ou non
    lot = parseInt(req.body.lot);
    delete req.body.lot;
    // on récupère le user :
    if (req.body.hasOwnProperty("userId")) {
      let user = req.body.userId;
      delete req.body.userId;

      // on supprime les champs qui mettent opentheso à jour ?
      // champ : 0#132 ou 1#132 selon unicité ou pas (pour tous les champs simple non thesaurus)
      for (let champ in req.body) {
        let champOrig = champ;
        if (parseInt(champ.split("#")[0]) === 1) { // Le champ est unique
          isunique = 1; 
          champ = champ.split("#")[1];
        } else { // Le champ n'est pas unique
          isunique = 0;
          champ = champ.split("#")[1];
        }
        if (champ.includes("opentheso")) {
          continue; // il est indexé par ailleurs ...
        }
        let value = req.body[champOrig];

        if (champ.startsWith("theso")) {
          // plusieurs cas : theso pactols ou theso periodo ou theso maison
          if (champ.includes("periodo")) {
            // champ = theso_periodo_id
            // value : <id_periodo>_<id_thes_periodo>_<language>
            // example  : 403_p0qhb66qj4c_fr'
            // TODO ATTENTION  : valeur du periodo
            // value: theso_periodo_id: '365_p0qhb66bf45_-0050_0499_fra-latn',
            // value : <id_periodo>_<id_thes_periodo>_<min>_<max>_<language>
            let id_periodo = value.split("_")[0];
            let id_thes_periodo = value.split("_")[1];
            let lang = value.split("_")[4];
            if (value !== "") {
              // on accepte qu'une seule valeur de periodo
              insertOrUpdateThesPeriodoItem(req.params.root, id_periodo, id_thes_periodo, itemId, itemType, lang, user);
            }
          } else if (champ.includes("pactols")) {
            if (value !== "") {
              // champ = thesopactols_<thesaurus>
              // value = thesopactols_<id>_<id_thes>
              let id_thesaurus = value.split("_")[0];
              let id_thes = value.split("_")[1];
              let thesaurus = champ.split("_")[1];

              let query =
                `SELECT count(*) FROM ${req.params.root}_thesaurus_pactols_item ` +
                `WHERE id_thesaurus = $1 AND thesaurus = $2 AND id_thes_thesaurus = $3 ` +
                ` AND id_item = $4 AND item_type = $5`;
              db.any(query, [id_thesaurus, thesaurus, id_thes, itemId, itemType])
                .then((data) => {
                  //console.log(typeof data[0]['count'])
                  if (data[0].count === "1") {
                    console.log("on a déjà cette indexation, on ne fait rien");
                  } else {
                    //if (insertOrUpdateThesPactolsItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user)) {
                    if (
                      insertThesPactolsItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user)
                    ) {
                      console.log("c'est fait ... un insert pour les pactols ");
                    } else {
                      console.log("server_error in insert thesaurus_pactols_item ");
                      erreur = 1;
                    }
                  }
                })
                .catch((e) => {
                  console.log("ERROR select thesaurus pactols item metadata value :", e);
                  erreur = 1;
                  //responseHelper.sendError(500, 'server_error', e, req, res)
                });
            }
          } else {
            // C'est le thesaurus maison
            if (value !== "") {
              // champ = theso_<thesaurus>
              // value = theso_<id>_<id_thes>
              // ERROR TODO : vérifier pour l'entrée :  1#theso_MainCategory': 'theso_312_8',
              let id_thesaurus = value.split("_")[0];
              let id_thes = value.split("_")[1];
              let thesaurus = champ.split("--")[1];

              db.any(
                `SELECT count(*)  FROM ${req.params.root}_thesaurus_item ` +
                  `WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 `,
                [thesaurus, itemId, itemType]
              )
                .then((count) => {
                  if (count[0].count === "0") {
                    if (insertThesItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user)) {
                      console.log(`insert thes item ok for patch metadata ${thesaurus}`);
                    } else {
                      // error
                      console.log("error in insert thes item");
                      erreur = 1;
                    }
                  } else if (count[0].count === "1") {
                    // une indexation existe, on fait un update sur l'id_thes qui change
                    if(lot === "0") {
                      if (updateThes(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user)) {
                        console.log("update thes item ok");
                      } else {
                        console.log("update thes item ko");
                        erreur = 1;
                      }
                    }else{
                      console.log("update thes item skipped");
                    }
                  }
                })
                .catch((e) => {
                  console.log("ERROR select thesaurus item metadata value :", e);
                  erreur = 1;
                });
            }
          } // fin des champs thesaurus
        } else if (champ.startsWith("nomen")) {
          //nomenclature
          if (value !== "") {
            // TODO : préparer insertion dans la table thesaurus_item
            // champ = nomen--<thesaurus>
            // value = <id>_<id_thes>
            let id_thesaurus = value.split("_")[0];
            let id_thes = value.split("_")[1];
            let thesaurus = champ.split("--")[1];

            // regle : si on a déjà une valeur pour ce thesaurus pour cet item , on fait un update
            // pour prendre en compte la nouvelle valeur du thesaurus
            db.any(
              `SELECT count(*)  FROM ${req.params.root}_thesaurus_item ` +
                //'WHERE id_thesaurus = $1 AND thesaurus = $2 AND id_thes_thesaurus = $3 ' +
                `WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 `,
              [thesaurus, itemId, itemType]
            )
              .then((count) => {
                // nomenclature : 1 seule info possible : si info  déjà présente, on remplace par la nouvelle
                //if ((count > 0 ) && (value !== '')) {
                if (count[0].count === "0") {
                  console.log("insert ?"); // rien => on fait un insert
                  if (insertThesItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user)) {
                    console.log("insert thes item nomen ok");
                  } else {
                    // error
                    console.log("error in insert thes nomen item");
                    erreur = 1;
                  }
                } else if (count[0].count === "1") {
                  console.log(" update ?");
                  // une indexation existe, on fait un update sur l'id_thes qui change
                  if (updateThes(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user)) {
                    console.log("update thes item ok");
                  } else {
                    console.log("update thes item ko");
                    erreur = 1;
                  }
                }
              })
              .catch((e) => {
                console.log("ERROR select thesaurus item metadata value :", e);
                erreur = 1;
                //responseHelper.sendError(500, 'server_error', e, req, res)
              });
          }
        } else if (champ.startsWith("multi")) {
          if (req.params.root === "conservatoire3d") {
            // TODO : à propager pour les "petits" thesaurus pour Archeogrid Projects
            if (update_multi.length) {
              // il y a des changements à faire
              //update_multi: [ '-deposant_422_2', '-deposant_440_13', '-deposant_430_14', '+deposant_424_6' ]
              for (let u = 0; u < update_multi.length; u++) {
                const thesaurus = update_multi[u].slice(1).split("_")[0];
                const id_thesaurus = update_multi[u].slice(1).split("_")[1];
                const id_thes = update_multi[u].slice(1).split("_")[2];
                if (update_multi[u].substring(0, 1) === "-") {
                  // on supprime -deposant_422_2 (thesaurus, id_thesaurus, id_thes_thesaurus)
                  deleteThesMultiItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType);
                } else if (update_multi[u].substring(0, 1) === "+") {
                  // on ajoute
                  insertThesMultiItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user);
                }
              }
            } else {
              console.log("pas de mise à jour sur le thesaurus multi");
            }
          } else {
            if (value !== "") {
              // Polyhiérarchie : la valeur récupérée contient le path (en 3eme position) en plus de l'id (qui peut changer en cas de recharge du thesaurus
              // VALUE : [ '422_2', '419_22' ]
              // VALUE : [ '36806_2627_28123.1000.1461.1586.1600.1601.2627', ...]
              let tabValue = [];
              // Maintenant, on peut récupérer un tableau de valeur séparé par des virgules
              //if (value.indexOf(',') !== -1) {
              if (typeof value == "string") {
                tabValue[0] = value;
              } else {
                tabValue = value;
              }
              for (let i = 0; i < tabValue.length; i++) {
                // TODO : récupérer toutes les valeurs existantes et les supprimer ?
                // TODO comme ça on remet les valeurs demandées et seulement celles-ci ...
                // TODO attention pour archeogrid projet ...
                // ATTENTION ! cela implique que partout dans les formulaires, on a mis en place
                // le select multiple qui permet de récupérer toutes les valeurs
                // Préparer insertion dans la table thesaurus_multi_item
                // champ = multi--<thesaurus>_t_id
                // AVANT : value = <id>_<id_thes>
                // Maintenant: value = <id>_<id_thes>_<path>
                // ou alors si plusieurs item dans le select : on a un tableau de valeurs !
                let id_thesaurus = tabValue[i].split("_")[0];
                let id_thes = tabValue[i].split("_")[1];
                let thes_path = tabValue[i].split("_")[2];
                let thesaurus = champ.split("--")[1].replace("_t_id", "");
                // regle : thesaurus multi : on ajoute sans regarder si ça existe deja
                db.any(
                  `SELECT count(*)  FROM ${req.params.root}_thesaurus_multi_item ` +
                    //'WHERE id_thesaurus = $1 AND thesaurus = $2 AND id_thes_thesaurus = $3 ' +
                    `WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 ` +
                    //'AND id_thesaurus = $4 ',
                    // polyhierarchie : on vérifie l'égalité des path (nouvelle colonne dans la table thesaurus_multi_item)
                    `AND thes_path = $4 `,
                  [thesaurus, itemId, itemType, thes_path]
                )
                  .then((count) => {
                    if (count[0].count === "0") {
                      //console.log('insert ?') // rien => on fait un insert
                      if (
                        insertThesMultiItem(req.params.root, id_thesaurus, id_thes, thesaurus, itemId, itemType, user)
                      ) {
                        console.log("insert thes multi item ok");
                      } else {
                        // error
                        console.log("error in insert thes multi item");
                        erreur = 1;
                      }
                    } else {
                      console.log(" already in thes multi, nothing to do");
                    }
                  })
                  .catch((e) => {
                    console.log("ERROR select thesaurus multi item metadata value :", e);
                    erreur = 1;
                    //responseHelper.sendError(500, 'server_error', e, req, res)
                  });
              }
            }
          }
        } else if (champ.startsWith("latlng")) {
          // champ = latlng--<id_metadata>
          champ = champ.split("--")[1];
          // value = lat,lng exmple : '-21.017854937856104,-39.11132812500001'
          // on récupère value[1] au lieu de value anciennement puisque le champ value est un tableau désormais
          db.oneOrNone(
            `SELECT value[1] as value FROM ${req.params.root}_passport WHERE id_item = $1 ` +
              `AND item_type = $2 AND id_metadata = $3 AND root_dir = $4 `,
            [itemId, itemType, champ, root_dir]
          )
            .then((idpassport) => {
              if (!idpassport && value !== "") {
                // Aucune donnée présente , on insert les nouvelles données :
                // il faut transformer la chaîne en tableau qui puisse être inséré dans un tableau d'une table postgres :
                let stri = `{"${value}"}`;
                db.one(
                  `INSERT INTO ${req.params.root}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, id_user ) ` +
                    ` VALUES ($1, $2, $3, $4, $5 , now(), $6) RETURNING id `,
                  [itemId, itemType, champ, root_dir, stri, user]
                )
                  .then((datai) => {
                    //console.log('on a ajouté une valeur unique')
                  })
                  .catch((e) => {
                    console.log("ERROR insert passport pour latlng :", e);
                    erreur = 1;
                    //responseHelper.sendError(500, 'server_error', e, req, res)
                  });
              } else if (idpassport && value !== "") {
                //let stri = '"'+value+'"'
                let stri = value.split();
                stri = stri.join();
                // nouvelle valeur a remplacer
                if (idpassport.value !== value) {
                  // il existe une valeur en base différente de la valeur du formulaire
                  // modification de l'update depuis la modification du champ value de text en text[] => value => value[1]
                  db.none(
                    `UPDATE ${req.params.root}_passport SET value[1] = $1 ` +
                      `, date_modified = now() , id_user = $2 ` +
                      `WHERE id_item = $3 AND item_type = $4 AND id_metadata = $5 ` +
                      `AND root_dir = $6 `,
                    [stri, user, itemId, itemType, champ, root_dir]
                  )
                    .then((dataupdate) => {
                      console.log("on a modifie une valeur unique pour latlng");
                    })
                    .catch((e) => {
                      console.log("ERROR update passport metadata value  pour latlng:", e);
                      erreur = 1;
                      //responseHelper.sendError(500, 'server_error', e, req, res)
                    });
                }
              } else {
                // value === ''
                //console.log('Nothing to update')
              }
            })
            .catch((e) => {
              console.log("ERROR select passport metadata value :", e);
              erreur = 1;
              //responseHelper.sendError(500, 'server_error', e, req, res)
            });
        } else {
          if (champ.startsWith("geonames")) {
            geo = 1;
            // champ = geonames--<id_metadata>
            champ = champ.split("--")[1];
          } else geo = 0;
          // Pour les métadonnées de type choix ouvert (choico) pour entrer la valeur dans le questionnaire,
          // il faut lui associer une lettre qu'on supprime maintenant
          if (champ.includes("_t")) {
            champ = champ.replace("_t", "");
          }
          // Si dans les champs on a des non unique : 132_1, 132_2, 132_3 ... C'est qu'on a plusieurs valeurs à rentrer
          //else if (champ.indexOf('_') !== -1) {
          //    champ = champ.split('_')[0] // on a 132, 132, 132
          //}
          if (isunique) {
            if (champ.startsWith("nomDepot")) {
              //if req.params.root === 'conservatoire3d'
              nomDepot = 1;
              // champ = nomDepot--<id_metadata>
              champ = champ.split("--")[1];
            } else nomDepot = 0;
            // traitement pour l'image Nakala (à mettre aussi dans l'objet en plus du passport de métadonées
            if (champ.startsWith("NakalaImage")) {
              //if req.params.root === 'conservatoire3d'
              nakalaImage = 1;
              champ = champ.split("--")[1];
              console.log(`champ nakala : ${value}`);
              nakalaImageValue = value;
            }
            // on récupère value[1] au lieu de value anciennement puisque le champ value est un tableau désormais
            db.oneOrNone(
              `SELECT value[1] as value FROM ${req.params.root}_passport WHERE id_item = $1 ` +
                `AND item_type = $2 AND id_metadata = $3 AND root_dir = $4 `,
              [itemId, itemType, champ, root_dir]
            )
              .then((idpassport) => {
                if (!idpassport && value !== "") {
                  let string_unique = "";
                  if (typeof value == "string") {
                    if (value.includes(",")) {
                      // DANS le cas où il y aurait une virgule dans le text, il faut mettre des "" pour que la valeur
                      // ne soit pas considérée comme un tableau avec plusieurs item !
                      string_unique = `{"${value.replace(/"/g, '\\"')}"}`;
                    } else {
                      string_unique = `{${value.replace(/"/g, '\\"')}}`;
                    }
                    //value = value.split() // c'est la que value devient un tableau
                  }
                  //    insertPassportNonUnique(req.params.root, itemId, itemType, champ, root_dir, string_unique, userId)

                  // Aucune donnée présente , on insert les nouvelles données :
                  // il faut transformer la chaîne en tableau qui puisse être inséré dans un tableau d'une table postgres :
                  //let stri = '{'+value+'}'
                  db.one(
                    `INSERT INTO ${req.params.root}_passport (id_item, item_type, id_metadata, root_dir, value, date_created, id_user ) ` +
                      ` VALUES ($1, $2, $3, $4, $5 , now(), $6) RETURNING id `,
                    [itemId, itemType, champ, root_dir, string_unique, user]
                  )
                    .then((datai) => {
                      //console.log('on a ajouté une valeur unique')
                    })
                    .catch((e) => {
                      console.log("ERROR insert passport  :", e);
                      erreur = 1;
                      //responseHelper.sendError(500, 'server_error', e, req, res)
                    });
                } else if (idpassport && value !== "") {
                  // nouvelle valeur a remplacer
                  if (idpassport.value !== value) {
                    // il existe une valeur en base différente de la valeur du formulaire
                    // modification de l'update depuis la modification du champ value de text en text[] => value => value[1]
                    // Pour ne pas avoir plusieurs element en cas de virgule dans la valeur du champ :
                    let string_unique = "";
                    if (typeof value == "string") {
                      if (value.includes(",")) {
                        // DANS le cas où il y aurait une virgule dans le text, il faut mettre des "" pour que la valeur
                        // ne soit pas considérée comme un tableau avec plusieurs item !
                        //string_unique = value.replace(/"/g, '\\"');
                        string_unique = `${value}`
                      } else {
                        string_unique = value
                      }
                      //value = value.split() // c'est la que value devient un tableau
                    }
                    db.none(
                      `UPDATE ${req.params.root}_passport SET value[1] = $1 ` +
                        `, date_modified = now() , id_user = $2 ` +
                        `WHERE id_item = $3 AND item_type = $4 AND id_metadata = $5 ` +
                        `AND root_dir = $6 `,
                      [string_unique, user, itemId, itemType, champ, root_dir]
                    )
                      .then((dataupdate) => {
                        // si c'est geo il faut faire la mise à jour du dépot
                        if (geo) {
                          value = parseInt(value);
                          db.one(`select update_depot_geonames($1, $2, ${itemId}, ${value})`, [root, itemType])
                            .then((geoup) => {
                              console.log("update geoname depot OK");
                            })
                            .catch((e) => {
                              console.log("ERROR update code geonames in depot : ", e);
                              erreur = 1;
                            });
                        }
                        if (nomDepot) {
                          //value = parseInt(value)
                          db.one("select update_depot_name($1, $2, $3, $4, $5)", [root, itemType, lang, itemId, value])
                            .then((nameUP) => {
                              console.log("update nomdepot depot OK");
                            })
                            .catch((e) => {
                              console.log("ERROR update code nomDepot in depot in patch metadata : ", e);
                              erreur = 1;
                            });
                        }
                        if (nakalaImage) {
                          db.none(`UPDATE ${root}_object SET id_nakala = $1 WHERE id = $2`, [nakalaImageValue, itemId])
                            .then((updateNaka) => {
                              console.log("ajout id_nakala objet OK");
                            })
                            .catch((e) => {
                              console.log("ERROR update id_nakala in object patch metadata ", e);
                              erreur = 1;
                            });
                        }
                        console.log("on a modifie une valeur unique");
                      })
                      .catch((e) => {
                        console.log("ERROR update passport metadata value :", e);
                        erreur = 1;
                        //responseHelper.sendError(500, 'server_error', e, req, res)
                      });
                  }
                } else {
                  // value === ''
                  if (idpassport && value === "") {
                    db.none(
                      `DELETE FROM ${req.params.root}_passport ` +
                        ` WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4  ` +
                        `  `,
                      [itemId, itemType, champ, root_dir]
                    )
                      .then((dataii) => {
                        console.log("on a supprimé une valeur devenue nulle");
                      })
                      .catch((e) => {
                        console.log("ERROR delete passport  :", e);
                        erreur = 1;
                      });
                  }
                  //console.log('value : '+value +',champ : '+ champ+', Nothing to update')
                }
              })
              .catch((e) => {
                console.log("ERROR select passport metadata value :", e);
                erreur = 1;
                //responseHelper.sendError(500, 'server_error', e, req, res)
              });
          } else {
            // NON UNIQUE
            // Ce champ n'est pas unique, A traiter différemment:
            // On ajoute les éléments au tableau des valeurs s'il en existe déjà un
            // On fait un insert pour ajouter les elements sinon.
            // A chaque insert on fait attention à ne pas insérer une valeur déjà présente (val <>ALL(value)
            if (typeof value == "string") {
              value = value.split();
            }
            db.oneOrNone(
              `SELECT id , value FROM ${req.params.root}_passport WHERE id_item = $1 ` +
                `AND item_type = $2 AND id_metadata = $3 AND root_dir = $4 `,
              [itemId, itemType, champ, root_dir]
            )
              .then((passport) => {     
                let new_val, del_val;
                if(passport){
                  del_val = passport.value.filter(v => !value.some(p_v => p_v === v)); // Supprimer les valeurs qui ne sont plus présentes
                  new_val = value.filter(v => passport.value.every(p_v => p_v !== v)); // Ne garder que les valeurs qui ne sont pas déjà présentent 
                  new_val = new_val.reduce((acc, v) => {
                    if (!acc.includes(v)) {
                      acc.push(v);
                    }
                    return acc;
                  }, []); // Pour supprimer les doublons dans le tableau de nouvelles valeurs
                  if(new_val.length > 0){

                    const nb_passport_values = passport.value.length;
                    
                    for(let index = 0, counter = nb_passport_values; index < new_val.length; index++, counter++){
                      db.none(`
                        UPDATE ${req.params.root}_passport
                        SET value[${counter}] = '${new_val[index]}'
                        WHERE id_item = ${itemId} AND item_type = '${itemType}' AND id_metadata = ${champ} AND root_dir = '${root_dir}'`
                      );
                    }
                  }
                  if(del_val.length > 0){
                    for(let index = 0; index < del_val.length; index++){
                      db.none(`
                        UPDATE ${req.params.root}_passport
                        SET value = array_remove(value, '${del_val[index]}')
                        WHERE id_item = ${itemId} AND item_type = '${itemType}' AND id_metadata = ${champ} AND root_dir = '${root_dir}'`
                      );
                    }
                  }
                }else{
                  const first_value = value.pop()
                  db.none(`
                    INSERT INTO ${req.params.root}_passport (id_item, item_type, id_metadata, root_dir, value[0], date_created, id_user)
                    VALUES ($1, $2, $3, $4, $5, now(), $6)`,
                    [itemId, itemType, champ, root_dir, first_value, user]
                  );

                  for(let index = 0, counter = 1; index < value.length; index++, counter++){
                    db.none(`
                      UPDATE ${req.params.root}_passport
                      SET value[$1] = $2
                      WHERE id_item = $3 AND item_type = $4 AND id_metadata = $5 AND root_dir = $6`,
                      [counter, value[index], itemId, itemType, champ, root_dir]
                    );
                  }
                }
              })
              .catch((e) => {
                console.log("ERROR select count passport metadata value :", e);
                erreur = 1;
                //responseHelper.sendError(500, 'server_error', e, req, res)
              });
          }
        }
      }
    } else {
      erreur = 1;
    }
  } else {
    // pas d'info sur le traitement par lot
    console.log("pas infos traitement par lot");
  }

  if (erreur) responseHelper.sendError(500, "server_error", "unable to update metadata ", req, res);
  else res.status(200).sendData([]);
};

// Create url endpoint /metadatamodel/:root,:lang to get all metadata model for one root
exports.getMetadataModel = async function (req, res) {
  let { root } = req.params;
  let lang = req.params.lang === 'fr' ?  `'${req.params.lang}'` : `'en'` ;

  try {
    const metadata_model = await db.any(
      `SELECT id, metadata_type, m.name, description FROM ${root}_metadata_model m INNER JOIN ${root}_metadata_model_label l ` +
        `ON l.id_metadata_model = m.id WHERE language = ${lang} AND visible = 1 `
    );
    res.status(200).sendData(metadata_model);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getMetadataModel", e, req, res);
  }
};

// Create url endpoint /metadatamodelProject/:root,:lang to get all metadata model for one root AND one project
// Si ce sont des projets Archeovision (id_site = 1) : on  récupère tous les modèles pour le moment
// TODO: trier les modèles pour ne récupérer que les bons...
// Si c'est un projet autre (id_site = 2 ), on va chercher dans la table
// pour Notre-Dame: tous les modèles de métadonnées ne sont pas affichés mais seulement ceux utilisés par le projet Notre-Dame
// info project-models dans une nouvelle table
exports.getMetadataModelProject = async function (req, res) {
  let { root } = req.params;
  let lang = req.params === 'fr' ? 'fr' : req.params.lang;
  let { idFolder } = req.params;

  try {
    const metadata_modelpro = await db.oneOrNone(
      ` SELECT ${root}_get_metadata_modelv4($1, $2) as  get_metadata_modelv4 `,
      [idFolder, lang]
    );
    res.status(200).sendData(metadata_modelpro.get_metadata_modelv4.models);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getMetadataModelProject", e, req, res);
  }
};

exports.getMetadataModelCorpus = async function (req, res) {
  let { root } = req.params;
  let lang = req.params === 'fr' ? 'fr' : 'en';
  let { idFolder } = req.params;

  try {
    const metadata_modelpro = await db.one(` SELECT ${root}_get_metadata_modelv4($1, $2) as  get_metadata_modelv4 `, [
      idFolder,
      lang,
    ]);
    res.status(200).sendData(metadata_modelpro.get_metadata_modelv2.models);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getMetadataModelCorpus", e, req, res);
  }
};

// language, id, root, type
// Createurl endpoint /allmetadata/:id,:item_type,:root,:lang to get all metadata for one item (file/folder/object)
exports.getAllMetadata = async function (req, res) {
  let iId = parseInt(req.params.id);
  // bad !  how come to have string parameter ?
  let root = `'${req.params.root}'`;
  let type = `'${req.params.item_type}'`;
  let lang =  `'${req.params.lang}'` ;

  let result = [];
  //let querythespactols = 'SELECT get_info_thespactols_gen('+lang+', $1 , $2, $3 ) as thespactolsinfo'
  // nouvelle fonction pour récupérer les tag de thesaurus pactols en s'affranchissant des modeles de métadonnées  :
  // les thesaurus ne sont pas dans le modèle de métadonnées mais indépendants => V3 de la fonction
  let querythespactols = "SELECT get_info_thespactols_gen3( $1 , $2, $3 ) as thespactolsinfo";
  let querythesmaison = `SELECT get_info_thes_gen(${lang}, $1 , $2, $3 ) as thesmaisoninfo`;
  let querynomenclature = `SELECT get_info_nomen_gen(${lang}, $1 , $2, $3 ) as nomeninfo`;
  // on récupère le thes_path en plus pour gerer la polyhierarchie
  // utilisé par le cnd3d (edit, object...) et dans checkAll et unchekAll => explore => allmetadata ! on garde la v3 pour le moment
  let querymulti = `SELECT get_info_multi_gen3(${lang}, $1 , $2, $3 ) as multiinfo`;
  let queryPeriodo = `SELECT get_info_periodo_gen(${lang}, $1 , $2, $3 ) as periodoinfo`;

  try {
    const query = await db.any(`SELECT get_all_metadata_json(${lang}, $1, $2:raw, $3:raw) as myquery`, [
      iId,
      root,
      type,
    ]);

    const ress = await db.any("SELECT $1:raw", query[0].myquery);

    try {
      //console.log(querythes)
      const thespactols = await db.any(querythespactols, [iId, req.params.root, req.params.item_type]);
      result = ress;
      if (typeof result[0] !== "undefined") {
        result[0].pactols = thespactols;
        try {
          const nomen = await db.any(querynomenclature, [iId, req.params.root, req.params.item_type]);

          result[0].nomenclature = nomen;

          try {
            const periodo = await db.any(queryPeriodo, [iId, req.params.root, req.params.item_type]);

            result[0].periodo = periodo;

            try {
              const multi = await db.any(querymulti, [iId, req.params.root, req.params.item_type]);

              result[0].multi = multi;

              try {
                const thesmaison = await db.any(querythesmaison, [iId, req.params.root, req.params.item_type]);

                result[0].thesaurus = thesmaison;
                res.status(200).sendData(result);
              } catch (er) {
                responseHelper.sendError(
                  500,
                  "server_error in get maison thesaurus info in getAllMetadata",
                  er,
                  req,
                  res
                );
              }
            } catch (er) {
              responseHelper.sendError(500, "server_error in get multi thesaurus info in getAllMetadata", er, req, res);
            }
          } catch (err) {
            responseHelper.sendError(500, "server_error in get periodo info in getAllMetadata", err, req, res);
          }
        } catch (e) {
          responseHelper.sendError(500, "server_error in get nomenclature info in getAllMetadata", e, req, res);
        }
      } else {
        console.log("result[0] is undefined ... ?");
        responseHelper.sendError(500, "server_error in getAllMetadata", "result[0] is undefined ...", req, res);
      }
    } catch (e) {
      responseHelper.sendError(500, "server_error in get thes info in getAllMetadata", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getAllMetadata", e, req, res);
  }
};

//create endpoint for /metadata/:root,:model,:lang
exports.getMetadata = async function (req, res) {

  const lang = req.params.lang === 'fr' ? 'fr' : 'en';
  try {
    const metadata = await db.any(
      `SELECT m.*, l.* FROM ${req.params.root}_metadata m INNER JOIN ${req.params.root}_metadata_label l ` +
        `ON l.id_metadata = m.id  INNER JOIN ${req.params.root}_metadata_model mo ON mo.id = m.id_metadata_model ` +
        `WHERE mo.name = $1 and language = $2 ORDER BY rank`,
      [req.params.model, lang]
    );

    res.status(200).sendData(metadata);
  } catch (e) {
    responseHelper.sendError(500, "server_error", e, req, res);
  }
};

//create endpoint for /metadataModelType/:root,:model,:itemType,:lang
// La route précédente n'est plus valable maintenant qu'il y a un model DublinCore pour les file et pour les objects
// à partir de 24/11/2021 on précise le type de modèle concerné
exports.getMetadataWithType = async function (req, res) {
  //const lang = req.params.lang === 'fr' ? 'fr' : 'en'
  const lang = req.params.lang // multi langue => ATTENTION Ajouter avant dans la table _metadata_label , les labels dans la langue voulue
  let query =
    `SELECT m.*, l.* FROM ${req.params.root}_metadata m ` +
    `INNER JOIN ${req.params.root}_metadata_label l ON l.id_metadata = m.id  ` +
    `INNER JOIN ${req.params.root}_metadata_model mo ON mo.id = m.id_metadata_model ` +
    `WHERE mo.name = $1 AND mo.metadata_type = $2 AND language = $3 ` +
    `ORDER BY rank`;

  try {
    const metadata = await db.any(query, [req.params.model, req.params.itemType, lang]);

    res.status(200).sendData(metadata);
  } catch (e) {
    responseHelper.sendError(500, "server_error", e, req, res);
  }
};
//create endpoint for /metadataList/:root
exports.getMetadataList = async function (req, res) {
  try {
    const metadatalist = await db.any(
      `SELECT id , id_metadata_model, id_list, name, nb FROM ${req.params.root}_metadata_list ` + ` ORDER BY name `
    );

    res.status(200).sendData(metadatalist);
  } catch (e) {
    responseHelper.sendError(500, "server_error in metadata list", e, req, res);
  }
};

//create endpoint for /metadataListId/:root,listId
exports.getMetadataListId = async function (req, res) {
  try {
    const metadatalistId = await db.any(
      `SELECT * FROM ${req.params.root}_metadata_list ` + `WHERE id_list = $1 ORDER BY name `,
      [req.params.listId]
    );

    res.status(200).sendData(metadatalistId);
  } catch (e) {
    responseHelper.sendError(500, "server_error in metadata list id", e, req, res);
  }
};

exports.getMetadataMappingaLTAG3D = async function (req, res) {
  // From json export aLTAG3D to model conservatoire3d deposit
  //console.log(req.body)
  res.status(200).sendData(req.body);
};

//create endpoint for /metadataIdFromCode/:root,:code
exports.getMetadataIdFromCode = async function (req, res) {
  let code = `'${req.params.code}'`;

  //console.log(code)
  // code has to be unique in table <root>_metadata
  try {
    const metadataId = await db.any(`SELECT id  FROM ${req.params.root}_metadata ` + `WHERE code  = $1 `, [
      req.params.code,
    ]);

    res.status(200).sendData(metadataId[0]);
  } catch (e) {
    responseHelper.sendError(500, "server_error in metadata id from Code", e, req, res);
  }
};

// create endpoint for /metadataValueFromCode/:branche,:idItem,:code GET
// pour récupérer la valeur d'une métadonnée par rapport à son code
// Fait pour récupérer un copyrights pour le CND3D pour un footer personalisé pour le viewer
exports.getMetadataValueFromCode = async function (req, res) {
  let { branche } = req.params;
  let { code } = req.params;
  let id = req.params.idItem;

  try {
    const codeInfo = await db.any(
      `SELECT value ` +
        `FROM ${branche}_passport WHERE id_item = $1 ` +
        `AND id_metadata in ( ` +
        ` SELECT  id FROM ${branche}_metadata WHERE code IS NOT NULL AND lower(code) like '%${code}%'` +
        ` ) `,
      [id]
    );
    res.status(200).sendData(codeInfo);
  } catch (e) {
    responseHelper.sendError(500, "server_error in metadata Value Site", e, req, res);
  }
};

// create endpoint for /metadataLabels/:root,:idFolder,:lang
// pour retourner la liste de métadonnées du passport d'un projet
// TODO : Faire un JSON comme getAllMetadata
exports.getMetadataLabels = async function (req, res) {
  let branch = req.params.root;
  let projectId = req.params.idFolder;
  let lang = req.params.lang === 'fr' ? 'fr' : 'en';

  let models_select = `SELECT DISTINCT(mm.id) as id_model, mm.name, mml.label, mml.description `
  let labels_select = `SELECT DISTINCT(m.id) as id_metadata, m.status as type, ml.label, mm.id as id_model, mm.name as model, ml.description `

  let models_from = `
    FROM ${branch}_metadata_model mm 
    INNER JOIN ${branch}_metadata m ON m.id_metadata_model = mm.id 
    LEFT JOIN ${branch}_passport       p ON p.id_metadata = m.id
    LEFT JOIN ${branch}_actor_item     a ON a.id_metadata = m.id
    LEFT JOIN ${branch}_datation_item  d ON d.id_metadata = m.id
    LEFT JOIN ${branch}_location_item  l ON l.id_metadata = m.id
    LEFT JOIN ${branch}_inventory_item i ON i.id_metadata = m.id `

  let labels_from = `
    FROM ${branch}_metadata_model mm 
    INNER JOIN ${branch}_metadata m ON m.id_metadata_model = mm.id 
    LEFT JOIN ${branch}_passport       p ON p.id_metadata = m.id 
    LEFT JOIN ${branch}_actor_item     a ON a.id_metadata = m.id 
    LEFT JOIN ${branch}_datation_item  d ON d.id_metadata = m.id 
    LEFT JOIN ${branch}_location_item  l ON l.id_metadata = m.id 
    LEFT JOIN ${branch}_inventory_item i ON i.id_metadata = m.id `


  if ((branch === "pft3d") || (branch === "corpus")) {
    const joinFolder = ` LEFT JOIN ${branch}_folder f ON f.folder_name = p.root_dir `;
    models_from += joinFolder;
    labels_from += joinFolder;
  }

  models_from += `INNER JOIN ${branch}_metadata_model_label mml ON mml.id_metadata_model = mm.id `;
  labels_from += `INNER JOIN ${branch}_metadata_label ml ON ml.id_metadata = m.id `;

  let both_where = `
    WHERE (f.folder_path <@ '${projectId}'    
    OR
    CASE WHEN a.item_type = 'folder' THEN a.id_item IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}')  
         WHEN a.item_type = 'object' THEN a.id_item IN (SELECT id FROM ${branch}_object WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))   
         WHEN a.item_type = 'file'   THEN a.id_item IN (SELECT id FROM ${branch}_file WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
         WHEN a.item_type = 'unico'  THEN a.id_item IN (SELECT u.id FROM ${branch}_unico u INNER JOIN ${branch}_file f ON f.id = u.id_file WHERE f.id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
    END
    OR
    CASE WHEN d.item_type = 'folder' THEN d.id_item IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}')  
         WHEN d.item_type = 'object' THEN d.id_item IN (SELECT id FROM ${branch}_object WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))   
         WHEN d.item_type = 'file'   THEN d.id_item IN (SELECT id FROM ${branch}_file WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
         WHEN d.item_type = 'unico'  THEN d.id_item IN (SELECT u.id FROM ${branch}_unico u INNER JOIN ${branch}_file f ON f.id = u.id_file WHERE f.id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
    END
    OR
    CASE WHEN l.item_type = 'folder' THEN l.id_item IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}')  
         WHEN l.item_type = 'object' THEN l.id_item IN (SELECT id FROM ${branch}_object WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))   
         WHEN l.item_type = 'file'   THEN l.id_item IN (SELECT id FROM ${branch}_file WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
         WHEN l.item_type = 'unico'  THEN l.id_item IN (SELECT u.id FROM ${branch}_unico u INNER JOIN ${branch}_file f ON f.id = u.id_file WHERE f.id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
    END
    OR
    CASE WHEN i.item_type = 'folder' THEN i.id_item IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}')  
         WHEN i.item_type = 'object' THEN i.id_item IN (SELECT id FROM ${branch}_object WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))   
         WHEN i.item_type = 'file'   THEN i.id_item IN (SELECT id FROM ${branch}_file WHERE id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
         WHEN i.item_type = 'unico'  THEN i.id_item IN (SELECT u.id FROM ${branch}_unico u INNER JOIN ${branch}_file f ON f.id = u.id_file WHERE f.id_folder IN (SELECT id FROM ${branch}_folder WHERE folder_path <@ '${projectId}'))
    END)`;

    const modelsQuery = models_select + models_from + both_where + ` AND mml.language = '${lang}' ORDER BY mm.id`;
    const labelsQuery = labels_select + labels_from + both_where + ` AND ml.language  = '${lang}' ORDER BY m.id`;

  try {
    const models = await db.any(modelsQuery);
    const labels = await db.any(labelsQuery);

    let results = [];

    for (let i = 0; i < models.length; ++i) {
      let model = {
        id_model: models[i].id_model,
        name: models[i].name,
        label: models[i].label,
        description: models[i].description,
        metadata: [],
      };
      results[models[i].id_model] = model;
    }
    for (let i = 0; i < labels.length; ++i) {
      let label = {
        id_metadata: labels[i].id_metadata,
        type: labels[i].type,
        label: labels[i].label,
        description: labels[i].description,
      };
      results[labels[i].id_model].metadata.push(label);
    }

    res.status(200).sendData(results);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getMetadataLabels", e, req, res);
  }
};

// Create Url Endpoint for '/metadatavalue/:item_id,:item_type,:model,:root,:lang' DELETE
// supprimer une valeur de métadonnées dans un passport (un champ rempli ne l'est plus)
// on passe en paramètre supplémentaire l'id_metadata du passport
exports.deleteMetadataValue = async function (req, res) {
  const branch = req.params.root;
  const id = req.params.item_id;

  let { id_metadata } = req.body,
    { root_dir } = req.body;

  let query = `DELETE FROM ${branch}_passport WHERE id_item = $1 AND item_type = $2 AND id_metadata = $3 AND root_dir = $4`;
  await db
    .none(query, [id, req.params.item_type, id_metadata, root_dir])
    .then(() => {
      res.status(200).sendData({});
    })
    .catch((e) => {
      responseHelper.sendError(500, "server_error in delete Passport value", e, req, res);
    });
};

// create endpoint for /metadataLabelsFromCode/:root,:code,:lng
// pour retourner la liste de métadonnées du passport d'un projet
// TODO : Faire un JSON comme getAllMetadata
exports.getMetadataLabelsFromCode = async function (req, res) {
  let branche = req.params.root;
  let { code } = req.params,
    lng = req.params === 'fr' ? 'fr' : 'en';

  try {
    const listLabels = await db.oneOrNone(
      `SELECT *
                        FROM ${branche}_metadata m
                        INNER JOIN ${branche}_metadata_label ml ON ml.id_metadata = m.id
                        WHERE m.code = $1 and language = $2 `,
      [code, lng]
    );
    res.status(200).sendData(listLabels);
  } catch (e) {
    responseHelper.sendError(500, "server_error in  getMetadataLabelsFromCode GET", e, req, res);
  }
};

// create endpoint for /itemsFromCodeValue/:branche,:code,:value' GET
// pour retourner la liste de objets dont le dépôt a telle valeure pour telle métadonnées (accessible par son code)
// qui correspondent à des sites (à la valeur donnée pour le code voulu
// au départ pour le code siteNom puis étendu à lieuDecouverte, mais pour n'importe quel code
exports.getItemsFromCodeValue = async function (req, res) {
  let { value } = req.params,
    { code } = req.params,
    { branche } = req.params;

  try {
    const listObjects = await db.any(
      `SELECT o.id, o.name as value, o.id_file_representative as id_file, id_nakala as nakala, o.doi ` +
        ` FROM ${branche}_object o WHERE o.id in (SELECT id_object FROM ${branche}_folder_object where id_folder in ( ` +
        `SELECT d.id_item  ` +
        `FROM ${branche}_passport s ` +
        `INNER JOIN ${branche}_passport d ON d.id_item = s.id_item AND d.item_type = s.item_type  AND d.id_metadata = ` +
        ` (SELECT id FROM ${branche}_metadata WHERE code = 'nomDepot' limit 1 ) ` +
        `INNER JOIN ${branche}_folder f ON f.id = d.id_item AND d.item_type = 'folder' ` +
        `WHERE s.id_metadata= (SELECT id FROM ${branche}_metadata WHERE code = $1 limit 1 ) AND s.value[1]= $2 ` +
        `AND s.item_type = 'folder' ))`,
      [code, decodeURI(value)]
    );
    res.status(200).sendData(listObjects);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getItemsFromCodeValue GET", e, req, res);
  }
};

// create endpoint for /itemsFromModelCodeValue/:branche,:code,:value' GET
// pour retourner la liste des items dont la métadonnées dont le code / model est donné
// a telle valeur pour telle métadonnées (accessible par son code)
// qui correspondent à la première partie d'une nouvelle API pour récupérer toutes les informations
// pour Notre-Dame pour commencer
exports.getItemsFromModelCodeValue = async function (req, res) {
  let { value } = req.params,
    { model } = req.params,
    { code } = req.params,
    { branche } = req.params;

  let query = `SELECT id_item, item_type, concat('https://www.archeogrid.fr/allmetadataItem/',id_item,',',item_type) as suggested_url FROM ${branche}_passport
               WHERE id_metadata =
                    (SELECT m.id FROM ${branche}_metadata m INNER JOIN ${branche}_metadata_model mo ON mo.id = m.id_metadata_model
                    WHERE mo.name = $1  AND m.code = $2 )
               AND value[1] = $3`;

  try {
    const listItems = await db.any(query, [model, code, decodeURI(value)]);
    res.status(200).sendData(listItems);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getItemsFromModelCodeValue GET", e, req, res);
  }
};
