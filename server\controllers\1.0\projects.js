const db = require("../../helpers/db").default;
const responseHelper = require('../../helpers/response')
const { insertOverallProjects } = require('../../helpers/db_tools')

// Create url endpoint for /projects
exports.getProjects = async function(req, res) {


    //let root = 'pft3d'
    let root = req.query.root

    try {
        const projects = await db.any(
            // pour récupérer aussi nb_images et la loc
            'SELECT id, name, id_parent, folder_path, passport, date_creation, rank, global_rank, ' +
            'status, id_site, visible, uploadable, commentable, id_representative_picture, ' +
            'folder_passport, file_passport, ' +
            'nb_tot_images_gen(id, $1) as nb_images, ' +
            'get_loc_from_id_gen(id, $1)  AS location, ' +
            'date_update FROM '+root+'_folder ' +
            'WHERE id_parent IS NULL ' +
            'AND folder_passport in (\'projets\', \'corpus\', \'decors_site\', \'project\' ) ' +
            'AND visible = \'true\' ' +
            //'AND get_loc_from_id_gen(id, $1) IS NOT NULL ' +
            'ORDER BY name ', root)
        // success
        res.status(200).sendData(projects)
    }
    catch (e) {
        // error
        responseHelper.sendError(500, 'server_error', e, req, res)
    }
}

// Create url endpoint for /projectsOverall GET
exports.getOverallProjects = async function(req, res) {

    let root = req.query.root

    try {
        const projects = await db.any(
            // pour récupérer aussi nb_images et la loc
            'SELECT id, name, id_parent, folder_path, passport, date_creation, rank, global_rank, ' +
            'status, id_site, visible, uploadable, commentable, id_representative_picture, ' +
            'folder_passport, file_passport, ' +
            'nb_tot_images_gen(id, $1) as nb_images, ' +
            'get_loc_from_id_gen(id, $1)  AS location, ' +
            'folder_name, ' +
            'date_update FROM '+root+'_folder ' +
            'WHERE folder_passport = \'overall\' AND id_parent IS NULL ' +
            'AND visible = \'true\' ' +
            //'AND get_loc_from_id_gen(id, $1) IS NOT NULL ' +
            'ORDER BY name ', root)
        // success
        res.status(200).sendData(projects)
    }
    catch (e) {
        // error
        responseHelper.sendError(500, 'server_error', e, req, res)
    }
}

exports.createProject = async function(req, res) {

    let name = req.body.projectName
    let root = req.body.root
    let user = req.body.user
    let site = req.body.site


    if (root == 'pft3d' || root == 'corpus') {
        // Aa : Archeovision project, id_site = 1 dans la table pft3d_site
        // Ee : Ext project ie pas archeovision, ils sont stockés ailleurs cf table pft3d_site id_site = 2
        if (site == 'A' || site == 'a') site = 1
        else if (site == 'E' || site == 'e') site = 2
    }

    try {
        const insertProj = await db.oneOrNone(
            'INSERT INTO ' + root + '_folder ' +
                        '(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, ' +
                        'nb_images, id_parent, rank, status, folder_path, passport, folder_passport, id_site) ' +
                        'SELECT nextval(\'' + root + '_folder_id_seq\'), $1, $1, now(), $2, $3, $2, ' +
                        ' 0, 0, NULL, COALESCE(max(rank)+1,1), \'private\' , \'\' , $1,  \'project\' , $4 ' +
                        'FROM '+root+'_folder WHERE id_parent IS NULL  ' +
                        'RETURNING id',
                        [name, 'true', 'false', site]
        )
        console.log('projet cree')
        // Donner le droit au user sur ce nouveau folder s'il est scribe (s'il est admin, il a tous les droits)
        if (req.body.user_status === 'scribe') {
            console.log(insertProj)
            try {
                const giveRight = await db.oneOrNone(
                    'INSERT INTO ' + root + '_user_access (id_user, id_folder) VALUES($1, $2)', [user,insertProj.id ]
                )
                // TODO : cree a la chaine les 13 répertoires de bases du projet
                try {
                    const create_primaire = await db.oneOrNone(
                        'SELECT insert_folder_primairev2_'+root+'($1, $2)', [insertProj.id , site]
                    )
                    res.status(201).sendData([insertProj])
                }
                catch (e) {
                    console.log(e)
                    responseHelper.sendError(500, 'server_error IN CREATE PRIMAIRE PROJECTS FOLDER', e, req, res)
                }
            }
            catch (e) {
                console.log(e)
                responseHelper.sendError(500, 'server_error IN INSERT ACCESS PROJECT FOLDER', e, req, res)
            }
        } else {
            try {
                const create_primaire = await db.oneOrNone(
                    'SELECT insert_folder_primairev2_'+root+'($1, $2)', [insertProj.id, site ]
                )
                res.status(201).sendData([insertProj])
            }
            catch (e) {
                console.log(e)
                responseHelper.sendError(500, 'server_error IN CREATE PRIMAIRE PROJECTS FOLDER', e, req, res)
            }
        }
    }
    catch(e) {
        responseHelper.sendError(500, 'server_error IN INSERT PROJECT FOLDER', e, req, res)
    }


}

exports.createOverallProject = async function(req, res) {

    let name = req.body.projectName
    let root = req.body.root
    let user = req.body.user
    let site = req.body.site


    if (root === 'pft3d') {
        // Aa : Archeovision project, id_site = 1 dans la table pft3d_site
        // Ee : Ext project ie pas archeovision, ils sont stockés ailleurs cf table pft3d_site id_site = 2
        if (site == 'A' || site == 'a') site = 1
        else if (site == 'E' || site == 'e') site = 2
    }

    try {
        const insertProj = await db.oneOrNone(
            'INSERT INTO ' + root + '_folder ' +
            '(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, ' +
            'nb_images, id_parent, rank, status, folder_path, passport, folder_passport, id_site) ' +
            'SELECT nextval(\'' + root + '_folder_id_seq\'), $1, $1, now(), $2, $3, $2, ' +
            ' 0, 0, NULL, COALESCE(max(rank)+1,1), \'private\' , \'\' , $1,  \'overall\' , $4 ' +
            'FROM '+root+'_folder WHERE id_parent IS NULL  ' +
            'RETURNING id',
            [name, 'true', 'false', site]
        )
        console.log('projet général créé')
        // Donner le droit au user sur ce nouveau folder s'il est scribe (s'il est admin, il a tous les droits)
        if (req.body.user_status === 'scribe') {
            try {
                const giveRight = await db.oneOrNone(
                    'INSERT INTO ' + root + '_user_access (id_user, id_folder) VALUES($1, $2)', [user,insertProj.id ]
                )
                res.status(201).sendData([insertProj])
            }
            catch (e) {
                console.log(e)
                responseHelper.sendError(500, 'server_error IN INSERT ACCESS OVERALL PROJECT FOLDER', e, req, res)
            }
        } else {
            res.status(201).sendData([insertProj])
        }
    }
    catch(e) {
        responseHelper.sendError(500, 'server_error IN INSERT OVERALL PROJECT FOLDER', e, req, res)
    }


}

// Create url endpoint for /projectsCard
exports.getProjectsCard = async function(req, res) {

    let root='pft3d'

    let query = 'SELECT f.id, f.name,f.folder_name, f.date_creation, f.id_representative_picture, f.rank, '+
        'nb_tot_images_gen(f.id, \'pft3d\') AS nbimages, p.value[1] as loc, s.pft3d_url as url, ' +
        ' get_first_metadata( f.id, \'pft3d\', \'folder\', \'project\') AS project_name ' +
        ', concat(\'https://www.archeogrid.fr/projectv/\', f.id) as urlProject ' +
        ', folder3d.nb_tot_images as tot_images_3d, folder3d.nb_images as images_3d, folder3d.id as id_3d, folder3d.status as status_3d '  +
        'FROM pft3d_folder f INNER JOIN pft3d_passport p ON p.id_item = f.id ' +
        'INNER JOIN pft3d_metadata m ON m.id = p.id_metadata ' +
        'INNER JOIN pft3d_metadata_label l ON l.id_metadata = m.id ' +
        'INNER JOIN pft3d_site s ON s.id = f.id_site '+
        'LEFT OUTER JOIN pft3d_folder folder3d ON folder3d.folder_path <@ f.folder_path  AND folder3d.folder_name = \'3DOnline\' ' +
        'AND folder3d.status = \'public\' AND folder3d.nb_images != 0 '+
        'WHERE l.language = \'fr\' AND l.label = \'Localisation\' AND item_type = \'folder\' ' +
        'AND f.id_parent IS NULL AND f.visible = \'true\' ORDER BY rank'

    try {
        const projectsCard = await db.any(query )
        res.status(200).sendData(projectsCard)
        //console.log('les data : '+projectsLocation)
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error', e, req, res)
    }
}

// Create url endpoint for /projectsLocation
exports.getProjectsMap = async function(req, res) {

    let root='pft3d'

    try {
        const projectsMap = await db.any(
            'SELECT p.id, name,folder_name, date_creation, id_representative_picture, rank, ' +
            'nb_tot_images_gen(p.id, \'pft3d\') AS nbimages, pft3d_url as url,' +
            'get_loc_from_id_pass_gen(p.id, \'pft3d\') AS loc, ' +
            ' get_first_metadata( p.id, \'pft3d\', \'folder\', \'project\') AS project_name ' +
            'FROM pft3d_folder p ' +
            'INNER JOIN pft3d_site s ON s.id = p.id_site  WHERE id_parent IS NULL AND visible = \'true\' ORDER BY rank '
            )
        res.status(200).sendData(projectsMap)
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error', e, req, res)
    }
}


// Create url endpoint for /projectsFull
exports.getProjectsFull = async function(req, res) {

    try {
        const projectsFull = await db.any(
            'SELECT get_projectsfull(\'fr\') AS projects ' )
        res.status(200).sendData(projectsFull)
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error', e, req, res)
    }
}

// Create url endpoint for projectFull/:root,:idPproject,:lang
// obsolete
exports.getProjectFull = async function(req, res) {

    let root = req.params.root
    let lang = req.params.lang = 'fr' ? "'"+req.params.lang+"'" : 'en'
    let pId = parseInt(req.params.idProject)
    let postgres_function = ''

    postgres_function = 'get_projectfull'

    // sur les thesaurus maison  = nomenclature
    let querynomen = 'SELECT get_projectfull_nomen2('+lang+', id) AS nomeninfo ' +
        'FROM pft3d_folder WHERE folder_passport = \'project\' ' +
        ' AND visible = \'true\' ' +
        ' AND id = $1 '
    let querymulti = 'SELECT get_info_multi_gen2('+lang+', id ,  \'pft3d\', \'folder\' ) as thesmultiinfo ' +
        'FROM pft3d_folder WHERE (folder_passport = \'project\' OR folder_passport = \'overall\' ) ' +
        ' AND visible = \'true\' ' +
        ' AND id = $1 '

    let query ='SELECT '+postgres_function+'('+lang+', id) AS project ' +
            'FROM '+root+'_folder WHERE (folder_passport = \'project\' OR folder_passport = \'overall\')  AND id = $1 limit 1'

    let project = []

    db.oneOrNone(query, pId )
        .then(data => {
            //console.log(data)
            project  = data
            if (data) {
                db.any(querynomen, pId)
                    .then(datanomen => {
                        db.any(querymulti, pId)
                            .then(datamulti => {
                                project['nomenclature'] = Object.assign(datanomen)
                                project['multi'] = Object.assign(datamulti)
                                res.status(200).sendData(project)
                            })
                            .catch(err => {
                                responseHelper.sendError(500, 'server_error in get depot info multi ', err, req, res)
                            })
                    })
                   .catch(err => {
                        responseHelper.sendError(500, 'server_error in get project info nomenclature', err, req, res)
                    })
            } else {
                res.status(200).sendData([])
            }
        })
        .catch(err => {
            responseHelper.sendError(500, 'server_error in get full project info ', err, req, res)
        })


/*
    try {
        const jsonProj  = await db.any('SELECT '+postgres_function+'('+lang+', id) AS project ' +
            'FROM '+root+'_folder WHERE folder_passport = \'project\'  ' +
            'AND id = $1 limit 1', pId )
        res.status(200).sendData(jsonProj)
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error', e, req, res)
    }

 */

}

// Create url endpoint for projectsRoot/:root
// POUR admin/Synch
// Liste  de tous les projets racines (sans les dossiers virtuels)
// ??? Comment récupérer les dépôts ?
exports.getProjectsRootList = async function(req, res) {

    if (req.params.root === 'pft3d') {
        try {
            const lesProjets = await db.any(
                'SELECT id, name, folder_name , ' + req.params.root + '_get_real_path(id) as path ' +
                'FROM ' + req.params.root + '_folder ' +
                'WHERE folder_name IS NOT NULL AND id_parent IS NULL ' +
                'ORDER BY folder_name')
            res.status(200).sendData(lesProjets)
        } catch (e) {
            responseHelper.sendError(500, 'server_error', e, req, res)
        }
    } else if (req.params.root === 'conservatoire3d') {
        try {
            const lesProjets = await db.any(
                'SELECT id, name, folder_name , ' + req.params.root + '_get_real_path(id) as path ' +
                'FROM ' + req.params.root + '_folder ' +
                'WHERE folder_name IS NOT NULL AND folder_passport = \'deposit\' ' +
                'ORDER BY folder_name')
            res.status(200).sendData(lesProjets)
        } catch (e) {
            responseHelper.sendError(500, 'server_error', e, req, res)
        }
    } else {
        try {
            const lesProjets = await db.any(
                'SELECT id, name, folder_name , ' + req.params.root + '_get_real_path(id) as path ' +
                'FROM ' + req.params.root + '_folder ' +
                'WHERE folder_name IS NOT NULL ' +
                'AND id_parent IS NULL ' +
                //'AND folder_passport = \'corpus\' ' +
                'ORDER BY folder_name')
            res.status(200).sendData(lesProjets)
        } catch (e) {
            responseHelper.sendError(500, 'server_error', e, req, res)
        }
    }
}

// Create url endpoint for projectName/:branch,:idProject
exports.getProjectName = async function(req, res) {
    let id = parseInt(req.params.idProject)
    let branch = req.params.branch
    let query = ''
    if (branch === 'pft3d') {
        query = `SELECT value FROM pft3d_passport WHERE item_type = 'folder' AND id_metadata =
            (SELECT id FROM pft3d_metadata WHERE id_metadata_model =
                (SELECT id from pft3d_metadata_model WHERE metadata_type = 'folder' AND name like 'proje%' )
            AND rank = 1 )
            AND id_item = ${id};`
    } else if (branch === 'corpus') {
        query = `SELECT value FROM corpus_passport WHERE item_type = 'folder' AND id_metadata =
            (SELECT m.id FROM corpus_metadata m
                INNER JOIN corpus_metadata_model mo ON mo.id = m.id_metadata_model AND mo.metadata_type = 'folder'
                WHERE mo.name = 'projets'
                AND m.rank = 1 ) AND id_item = ${id};`
    }

    try {
        const projectName = await db.any(query)
        res.status(200).sendData(projectName[0]['value'])
    } catch (e) {
        responseHelper.sendError(500, 'error in getProjectName', e, req, res)
    }
}

// Create url endpoint for projectName/:branch,:idFile
exports.getProjectId = async function (req, res) {
    let idFile = parseInt(req.params.idFile)
    let branch = req.params.branch

    let query = `SELECT subltree(fo.folder_path, 0, 1)
        FROM ${branch}_folder fo
        INNER JOIN ${branch}_file fi ON fi.id_folder = fo.id
        WHERE fi.id = ${idFile};`

    try {
        const projectId = await db.any(query)
        res.status(200).sendData(projectId[0]['subltree'])
    } catch (e) {
        responseHelper.sendError(500, 'error in getProjectId', e, req, res)
    }
}

// Create url endpoint for '/overallProject/:branche,:idOverallProject getProjectsIdFromOverall GET
// Récupérer tous les projets de sites (project) liés à un projet scientifique (overall)
exports.getProjectsIdFromOverall = async function(req, res) {
    let branche = req.params.branche
    let idop = req.params.idOverallProject

    let query = `SELECT *
        FROM ${branche}_folder f
        INNER JOIN ${branche}_folder_overall fo ON fo.id_folder = f.id
        WHERE fo.id_overall = ${idop} AND f.folder_passport = 'project' ORDER BY name ;`

    try {
        const projectsId = await db.any(query)
        if (projectsId)
            res.status(200).sendData(projectsId)
        else
            res.status(200).sendData([])
    } catch (e) {
        responseHelper.sendError(500, 'error in GET project id  and details from overallProject', e, req, res)
    }

}

//
exports.linkProjectsIdFromOverall = async function(req, res) {
    let branche = req.params.branche
    try {
        const folder = await db.none('INSERT INTO '+branche+'_folder_overall(id_folder, id_overall) ' +
            'VALUES($1, $2) ', [ req.body.idFolder,  req.params.idOverallProject])
        res.status(201).sendData([])
    }
    catch(e){
        responseHelper.sendError(500, "impossible insert into _folder_overall", e, req, res)
    }
}

// create url endpoint for  '/overallProject/:branche,:idOverallProject' DELETE
exports.deleteProjectIdSiteFromOverall = async function(req, res) {
    let branche = req.params.branche
    let idSite = req.body.idSite
    let idOP = req.params.idOverallProject

    try {
        const projectsId = await db.any('DELETE FROM '+branche+'_folder_overall ' +
            'WHERE id_folder = $1 AND id_overall = $2', [idSite, idOP])
        res.status(200).sendData(projectsId)
    } catch (e) {
        responseHelper.sendError(500, 'error in delete link overall from Project', e, req, res)
    }
}


// '/projectWithOverall/:branche,:idProject' GET
// Récupérer tous les projets scientifiques (overall) d'un projet (projet de site pour pft3d) sans les détails
exports.getOverallFromProject = async function(req, res) {
    let branche = req.params.branche
    let idP = req.params.idProject

    let query = `SELECT array_agg(id_overall) as list
        FROM ${branche}_folder f
        INNER JOIN ${branche}_folder_overall fo ON fo.id_folder = f.id
        WHERE f.id = ${idP} AND f.folder_passport = 'project';`

    try {
        const projectsId = await db.oneOrNone(query)
        if (projectsId['list'])
            res.status(200).sendData(projectsId['list'])
        else
            res.status(200).sendData([])
    } catch (e) {
        responseHelper.sendError(500, 'error in get overall from Project', e, req, res)
    }

}

///projectWithOverall/:branche,:idProject 'PATCH'
exports.linkOverallToProject = async function(req, res) {
    let branche = req.params.branche
    let overall = req.body.overall
    let idP = req.params.idProject
    let nbOverall = 0
    let resu = []

    if (overall) {
        if (typeof overall === 'string') {

            try {
                const folder = await db.none('INSERT INTO ' + branche + '_folder_overall(id_folder, id_overall) ' +
                    'VALUES($1, $2) ', [idP, overall])
                resu['nbOverall'] = 1
                res.status(201).sendData(resu)
            } catch (e) {
                responseHelper.sendError(500, "impossible insert into _folder_overall", e, req, res)
            }
        } else { // plusieurs à relier
            for (let o = 0; o < overall.length; o++) {
                nbOverall++
                insertOverallProjects(branche, idP, overall[o])
            }
            resu['nbOverall'] = nbOverall
            res.status(201).sendData(resu)
        }
    } else {
        res.status(201).sendData(resu)
    }
}

// /projectWithOverall/:branche,:idProject 'DELETE'
// Supprimer tous les liens entre un projet de site et un projet scientifique (avant de lui remettre des liens)
exports.deleteOverallProjectLink = async function(req, res) {
    let branche = req.params.branche
    let idP = parseInt(req.params.idProject)
    try {
        const projectsId = await db.any('DELETE FROM '+branche+'_folder_overall WHERE id_folder = $1', idP)
        res.status(200).sendData(projectsId)
    } catch (e) {
        responseHelper.sendError(500, 'error in delete link overall from Project', e, req, res)
    }
}

// '/projectWithOverallDetail/:branche,:idProject' GET
// Récupérer tous les détails de tous les projets overall liés à un projet de site
exports.getOverallDetailsFromProject = async function(req, res) {
    let branche = req.params.branche
    let idP = req.params.idProject

    let query = `SELECT *
        FROM ${branche}_folder_overall fo
        INNER JOIN ${branche}_folder f ON f.id = fo.id_overall
        WHERE fo.id_folder = ${idP};`

    try {
        const projectsOverall = await db.any(query)
        res.status(200).sendData(projectsOverall)
    } catch (e) {
        responseHelper.sendError(500, 'error in get overall projects details from Project', e, req, res)
    }

}

// Create url endpoint for '/overallProjectFull/:branche,:idOverallProject,:lang' GET
// récupérer toutes les métadonnées d'un project scientifique
exports.getOverallProjectFull = async function(req, res) {
    let branche = req.params.branche
    let idP = req.params.idOverallProject
    let lang = req.params.lang

    let query = 'SELECT get_overallprojectfull(\'fr\', $1) AS project '
    try {
        const overallProjectFull = await db.oneOrNone(query, idP)
        res.status(200).sendData(overallProjectFull)
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error in getProjectFull', e, req, res)
    }
}

// Create url endpoint for /projectsCard/:classement
exports.getProjectsCardClass = async function(req, res) {

    let root='pft3d'

    let query = 'SELECT f.id, f.name,f.folder_name, f.date_creation, f.id_representative_picture, f.rank, '+
        'nb_tot_images_gen(f.id, \'pft3d\') AS nbimages, p.value[1] as loc, s.pft3d_url as url, ' +
        ' get_first_metadata( f.id, \'pft3d\', \'folder\', \'project\') AS project_name ' +
        ', concat(\'https://www.archeogrid.fr/project/\', f.id) as urlProject ' +
        ', folder3d.nb_tot_images as tot_images_3d, folder3d.nb_images as images_3d, folder3d.id as id_3d, folder3d.status as status_3d '  +
        'FROM pft3d_folder f INNER JOIN pft3d_passport p ON p.id_item = f.id ' +
        'INNER JOIN pft3d_metadata m ON m.id = p.id_metadata ' +
        'INNER JOIN pft3d_metadata_label l ON l.id_metadata = m.id ' +
        'INNER JOIN pft3d_site s ON s.id = f.id_site ' +
        'INNER JOIN pft3d_thesaurus_item it ON it.id_item =  f.id AND it.thesaurus =  \'plan_classement\' AND it.item_type = \'folder\' ' +
        'INNER JOIN pft3d_thesaurus t ON t.thesaurus = it.thesaurus AND t.id_thes = it.id_thes_thesaurus '+
        'LEFT OUTER JOIN pft3d_folder folder3d ON folder3d.folder_path <@ f.folder_path  AND folder3d.folder_name = \'3DOnline\' ' +
        'AND folder3d.status = \'public\' AND folder3d.nb_images != 0 '+
        'WHERE l.language = \'fr\' AND l.label = \'Localisation\' AND p.item_type = \'folder\' ' +
        'AND f.id_parent IS NULL AND f.visible = \'true\' ' +
        'AND s.id = 1 ' + // on ne prend que les projets Archeovision => id = 1 (id = 2 pour tous les projets extérieurs à Archeovision)
        'AND t.short_name = $1 ORDER BY id DESC '

    try {
        const projectsCard = await db.any(query, req.params.classement )
        res.status(200).sendData(projectsCard)
        //console.log('les data : '+projectsLocation)
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error in get projects Card Classement', e, req, res)
    }
}

// Create url endpoint for limitiedProjectsRoot/:root
// POUR admin/Synch, admin/FolderOrder
// Liste  de tous les projets racines (sans les dossiers virtuels)
// pour les scribes pour certains projets seulement, déclarés dans la table _user_folder
exports.getLimitedProjectsRootList = async function(req, res) {

    let userId = req.query.userId
    if (req.params.root === 'pft3d') { // TODO : idem pour corpus on a des projets tout pareils ?
        try {
            const lesProjets = await db.any(
                'SELECT id, name, folder_name , ' + req.params.root + '_get_real_path(id) as path ' +
                'FROM ' + req.params.root + '_folder fo ' +
                'INNER JOIN ' + req.params.root + '_user_folder uf ON uf.id_folder = fo.id AND uf.id_user = $1 ' +
                'WHERE folder_name IS NOT NULL AND id_parent IS NULL AND folder_passport = \'project\' ' +
                'ORDER BY folder_name', userId)
            res.status(200).sendData(lesProjets)
        } catch (e) {
            responseHelper.sendError(500, 'server_error in limitedProjectsRoot pft3d', e, req, res)
        }
    } else if (req.params.root === 'conservatoire3d') {
        try {
            const lesProjets = await db.any(
                'SELECT id, name, folder_name , ' + req.params.root + '_get_real_path(id) as path ' +
                'FROM ' + req.params.root + '_folder ' +
                'WHERE folder_name IS NOT NULL AND folder_passport = \'deposit\' ' +
                'ORDER BY folder_name')
            res.status(200).sendData(lesProjets)
        } catch (e) {
            responseHelper.sendError(500, 'server_error limitedProjectsRoot conservatoire3d', e, req, res)
        }
    } else {
        try {
            const lesProjets = await db.any(
                'SELECT id, name, folder_name , ' + req.params.root + '_get_real_path(id) as path ' +
                'FROM ' + req.params.root + '_folder ' +
                'WHERE folder_name IS NOT NULL ' +
                'AND id_parent IS NULL ' +
                //'AND folder_passport = \'corpus\' ' +
                'ORDER BY folder_name')
            res.status(200).sendData(lesProjets)
        } catch (e) {
            responseHelper.sendError(500, 'server_error in limitedProjectsRoot ', e, req, res)
        }
    }
}

// create url endpoint /projectsRoot/:branch/:name GET
// autocompletion sur les nom des folders de type project ou deposit ou corpus
exports.getProjectsRootName = async function(req, res) {

    let branch = req.params.branch
    let name = decodeURIComponent(req.params.name)

    try {
        const projectsgeneral = await db.any(
            `SELECT
                DISTINCT f.name AS name,
                f.name AS value,
                id,
                f.name AS label
            FROM ${branch}_folder f
            WHERE f.folder_passport in ('deposit', 'project', 'corpus') AND f.name ILIKE '%${name}%'
            ORDER BY label`
        )

        res.status(200).sendData(projectsgeneral)
    }
    catch(e) {
        responseHelper.sendError(500, 'server_error in projectsRoot name general ', e, req, res)
    }
}

// create url endpoint /assignedProjects/:branche,:idUserScribe GET
// liste des projets assignés à un user de type scribe
exports.getListProjectFromUser = async function(req, res) {

    let branche = req.params.branche
    let idUser = req.params.idUserScribe

    db.any(`SELECT id_folder, folder_name FROM ${branche}_user_folder uf
                            INNER JOIN ${branche}_folder f ON f.id = uf.id_folder WHERE id_user = $1 `, idUser)
                .then((dataR) => {
                    res.status(200).sendData(dataR)
                })
                .catch(e => {
                    console.log(e)
                    responseHelper.sendError(500, 'server_error IN  assignedProjects GET', e, req, res)
                })

}
