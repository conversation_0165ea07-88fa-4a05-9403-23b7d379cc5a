/* Folders Tree Vitrine Styles */
#project-folders-container {
  margin-top: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 5px;
  background-color: #f8f9fa;
  overflow-y: auto;
  overflow-x: auto;
  height: auto;
  min-height: 0;
  flex: 1 1 auto;
}

#folders-container {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: visible;
  height: auto;
  max-height: none;
  justify-content: flex-start;
  flex: 1 1 auto;
  width: max-content;
  min-width: 100%;
}

.folders-tree {
  padding-top: 5px;
  list-style-type: none;
  padding-left: 0;
  min-height: 0;
  overflow-y: auto;
  overflow-x: visible;
  margin-bottom: 0;
  max-height: none;
  width: max-content;
  min-width: 100%;
}

/* Custom scrollbar styling for the folder trees and containers */
.folders-tree,
#folders-container,
#project-folders-container {
  scrollbar-width: thin;
  scrollbar-color: #ccc #f1f1f1;
}

/* Folder section containers */
.folders-section, .collections-section {
  display: flex;
  flex-direction: column;
  min-height: 0;
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: visible;
  width: max-content;
  min-width: 100%;
}

/* Distribute space between the two sections */
.folders-section {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: visible;
}

.collections-section {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: visible;
}

/* Container for the scrollable tree */
.folders-tree-container {
  min-height: 0;
  overflow-y: auto;
  overflow-x: visible;
  flex-direction: column;
  height: auto;
  max-height: none;
  display: flex;
  flex: 1 1 auto;
  width: max-content;
  min-width: 100%;
}

/* Separator between normal and virtual folders */
.tree-separator {
  height: 1px;
  background-color: #dee2e6;
  margin: 15px 0 10px 0;
  width: 100%;
  min-width: 100%;
  align-self: stretch;
}

/* Project header - main header for the tree */
.project-header {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 15px 0;
  padding: 10px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #dee2e6;
}

/* Folders header */
.folders-header {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0 10px;
  color: #495057;
  border-radius: 4px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
}

/* Header link styles */
.header-link {
  color: inherit;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.header-link:hover {
  text-decoration: underline;
  color: inherit;
}

/* Override for project-name class to ensure it gets the branch color */
.header-link.project-name {
  color: inherit;
}

/* Prevent underline on hover for the container */
.header-link.project-name:hover {
  text-decoration: none;
}

/* Project name styling - applied to the entire header link */
.project-name {
  /* The color will be set by corpus.css and pft3d.css */
  font-weight: bold;
}

/* Make only the link inside project-name underline on hover */
.project-name a {
  text-decoration: none;
}

.project-name a:hover {
  text-decoration: underline;
}

/* Collections header */
.collections-header {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0 10px;
  color: #495057;
  border-radius: 4px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Add collection button */
#add-collection-btn {
  margin-left: auto;
  padding: 0.5rem;
  font-size: 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;
}

#add-collection-btn:hover {
  color: #0066cc;
  background-color: rgba(0, 102, 204, 0.1);
}

.folders-tree li {
  padding: 3px 5px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
  list-style-type: none;
  margin: 2px 0;
}

.folders-tree li .d-flex {
  align-items: center;
  display: flex !important;
  flex-wrap: nowrap;
  width: max-content;
  min-width: 100%;
}

/* Folder icons */
.folder-parent, .folder-spacer {
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-right: 8px;
  flex-shrink: 0;
}

.folder-parent {
  cursor: pointer;
  position: relative;
}

.folder-parent::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666;
  transition: transform 0.2s ease;
}

/* When folder is open, rotate the arrow */
.folder-parent-down::before {
  transform: translate(-50%, -50%) rotate(-90deg);
}

/* Container for folder name and counter */
.folder-name-container {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  flex-grow: 1;
  width: max-content;
  cursor: pointer;
}

.folder-name {
  white-space: nowrap;
  position: relative;
}

.folder-number {
  color: #666;
  font-size: 0.85em;
  white-space: nowrap;
  margin-left: 4px;
  flex-shrink: 0;
}

/* Child folders */
.folder-children {
  display: none;
  padding-left: 20px;
  margin-top: 5px;
  overflow: visible;
  max-height: 0;
  transition: max-height 0.3s ease-out;
}

.folder-children.active {
  display: block;
  max-height: none;
  transition: max-height 0.5s ease-in;
}

.folder-selected {
  font-weight: bold;
}

.folder-selected > .d-flex {
  border-radius: 3px;
}

.folder-hover > .d-flex {
  background-color: #f1f3f5;
  border-radius: 3px;
}

/* Normal folder */
.folders-tree li:not(.virtual) .folder-name i {
  color: darkred;
}


.folders-tree li:not(.virtual).folder-hover > .d-flex {
  background-color: rgba(139, 0, 0, 0.05);
  border-radius: 3px;
}

.folders-tree li:not(.virtual).folder-selected > .d-flex {
  background-color: rgba(139, 0, 0, 0.05);
  border-left: 2px solid rgba(139, 0, 0, 0.7);
  border-radius: 3px;
  margin-left: -2px;
}

/* Virtual folder */
.virtual .folder-name i {
  color: #0066cc;
}

.virtual.folder-hover > .d-flex {
  background-color: rgba(0, 102, 204, 0.1);
  border-radius: 3px;
}

.virtual.folder-selected > .d-flex {
  background-color: rgba(0, 102, 204, 0.1);
  border-left: 2px solid rgba(0, 102, 204, 0.7);
  border-radius: 3px;
  margin-left: -2px;
}

/* Responsive styles for smaller screens */
@media (max-width: 768px) {
  #folders-container {
    max-height: none;
  }

  .folders-section, .collections-section {
    flex: 1 1 auto;
  }

  .folders-tree {
    max-height: none;
  }
}
