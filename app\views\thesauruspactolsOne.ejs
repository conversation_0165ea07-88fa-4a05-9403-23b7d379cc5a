<%- include('utils/title-content') %>

<div class="row">
    <div class="offset-1 col-2 col-lg-2">
        <div class="container">
            <img src="../assets/images/pactolsbl.png" />
            <br>
            <br>
        </div>
    </div>
    <div class="col-6 text-center">
        <h4><%= thesaurus['name']%></h4>

    </div>


</div>


<div id="menuGauche" class="offset-1">
    <ul id="main-ul"><% if (tree.length >= 1) { %><% let prevDepth = 0 %><% let depth = 0 %><% let nextDepth = 0 %>
        <li>
            <input type="checkbox" id="<%= tree[0].id%>" />
            <i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>
            <label id="toptop" class="fakelink" for="<%= tree[0].id%>" style="margin-bottom: 0px">
                <%= tree[0].name%>
                <% if (tree[0].nb_tot_item !== 0) {%><em>(<%=__('indexItems') %><%= tree[0].nb_tot_item%>)</em><% } %>
            </label>
            <ul><% if (tree.length >= 3) { %><% prevDepth = tree[0].depth %><% depth = tree[1].depth %><% nextDepth = tree[2].depth %>
                <% for (let i = 1; i < tree.length - 1; i++) { %><% prevDepth = depth %><% depth = tree[i].depth %><% nextDepth = tree[i + 1].depth %>
                <% for (let j = 0; j < (prevDepth - depth); ++j) { %>
            </ul>
        </li><% } %>
        <li>
            <input type="checkbox" id="<%=tree[i].id%>">
            <% if (tree[i].get_children !== null) { %>
            <i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;
            <% } else { %>
            <i class="far fa-folder"></i><i class="far fa-folder-open"></i>&nbsp;
            <% } %>
            <a href="javascript:"
               <% if (tree[i].nb_item > 0) { %>
               onclick="exploreFolder(<%= tree[i].id %>);"
               <% } %>
               <% if (tree[i].get_children !== null || tree[i].nb_item > 0 || tree[i].nb_tot_item > 0) { %>
               style="text-decoration: none;font-weight: bolder;"
               <% } %>>
                <label class="fakelink"
                       <% if (tree[i].get_children !== null || tree[i].nb_tot_item > 0) { %>for="<%=tree[i].id%>" <%}%> style="margin-bottom: 0px;">
                                    <% let foldername = tree[i].name %>
                       <% if (tree[i].nb_item > 0) { foldername += ' ['+tree[i].nb_item+']' } %>
                       <% if (tree[i].nb_tot_item > 0 && tree[i].get_children !== null) { foldername += ' ('+tree[i].nb_tot_item+')' } %><%= foldername%>
                       </label>
            </a>
            <% if (nextDepth > depth) { %>
            <ul><% } %>
                <% if (nextDepth == depth) { %>
        </li>
        <% } %><% } %><% } %>
        <% prevDepth = depth %><% depth = tree[tree.length - 1].depth %>
        <% for (let j = 0; j < (prevDepth - depth); ++j) { %>
    </ul>
    </li>
    <% } %>
    <% if (tree.length >= 2) { %>
    <li>
        <input type="checkbox" id="<%=tree[tree.length - 1].id%>">
        <% if (tree[tree.length - 1].get_children !== null) { %>
        <i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;
        <% } else { %>
        <i class="far fa-folder"></i><i class="far fa-folder-open"></i>&nbsp;
        <% } %>

        <a href="javascript:"
           <% if (tree[tree.length - 1].nb_item > 0) { %>
           onclick="exploreFolder(<%= tree[tree.length - 1].id %>);"
           <% } %>
           <% if (tree[tree.length - 1].get_children !== null || tree[tree.length - 1].nb_item > 0 || tree[tree.length - 1].nb_tot_item > 0) { %>
           style="text-decoration: none;font-weight: bolder;"
           <% } %>>
            <label class="fakelink"
                   <% if (tree[tree.length - 1].nb_tot_item > 0 || tree[tree.length - 1].nb_item > 0) { %>for="<%=tree[tree.length - 1].id%>" <%}%> style="margin-bottom: 0px;">
                                <% let foldername = tree[tree.length - 1].name %>
                   <% if (tree[tree.length - 1].nb_item > 0) { foldername += ' ['+tree[tree.length - 1].nb_item+']' } %>
                   <% if (tree[tree.length - 1].nb_tot_item > 0 && tree[tree.length - 1].get_children !== null) { foldername += ' ('+tree[tree.length - 1].nb_tot_item+')' } %>
                   <%= foldername %>

                   </label>
        </a>
    </li>
    <% } %>
    </ul> <%# close first folder ul %>
    </li> <%# close first folder li %>
    <% } %>
    </ul>
</div><%# menuGauche%>