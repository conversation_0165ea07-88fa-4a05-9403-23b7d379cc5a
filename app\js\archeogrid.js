(function() {
    const navigationEntries = window.performance.getEntriesByType("navigation");
    if (navigationEntries.length > 0 && navigationEntries[0].type === 'reload') {
        localStorage.setItem('selection', '[]');
    }
})();

function initMapGeoloc(name, lat, lng) {
    try {
        // Create map with improved options
        let mymap = L.map(name, {
            scrollWheelZoom: false,
            fadeAnimation: true,
            zoomAnimation: true,
            markerZoomAnimation: true
        }).setView([lat, lng], 4);

        // Add tile layer with improved options for better loading
        L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>' +
            ' contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
            maxZoom: 18,
            updateWhenIdle: false,
            updateWhenZooming: true,
            keepBuffer: 4,
            tileSize: 256
        }).addTo(mymap);

        window.addEventListener('resize', function() {
            mymap.invalidateSize();
        });

        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                setTimeout(function() {
                    mymap.invalidateSize();
                }, 200);
            }
        });

        setTimeout(function() {
            mymap.invalidateSize();
        }, 300);

        if (window.IntersectionObserver) {
            const observer = new IntersectionObserver(
                function(entries) {
                    if (entries[0].isIntersecting) {
                        setTimeout(function() {
                            mymap.invalidateSize();
                        }, 200);
                    }
                },
                { threshold: 0.1 }
            );

            const mapElement = document.getElementById(name);
            if (mapElement) {
                observer.observe(mapElement);
            }
        }

        return mymap;
    } catch (error) {
        console.error("Error initializing map:", error);
        try {
            let fallbackMap = L.map(name).setView([lat, lng], 4);
            L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png').addTo(fallbackMap);
            L.marker([lat, lng]).addTo(fallbackMap);
        } catch (fallbackError) {
            console.error("Failed to create fallback map:", fallbackError);
        }
    }
}


function getmodeldescription(model,root, idItem,type, idFolder) {

    window.location.href = '/edit,'+root+','+model+','+type+','+idItem+','+idFolder

}

function putChoiceInForm(value, selector, option) {
    let dataText = '', dataId = '';
    if (option) {
        // C'est un choix multiple, on concatene les valeurs
        // Récupérer les valeurs:
        let oldVal =  $('#'+selector).val()
        // pour afficher le nom dans le textarea
        //let varToDisplay = $("#"+option+" option:selected").text()
        let varToDisplay  = $("#"+option+" option:selected").attr('data_short_name')
        if (oldVal === '') dataText = varToDisplay
        else dataText = oldVal + '\r\n' + varToDisplay
        $('#'+selector).text(dataText)
        // Il faut aussi récupérer chaque id correspondant pour les mises à jour
        let oldId = $('#'+selector+'_id').val()

        if (oldId == '') dataId = value
        else dataId = oldId+ ','+ value
        $('#'+selector+'_id').text(dataId)
    } else {
        // C'est un choix simple, on remplace la valeur directement
        $('#'+selector).val(value)
    }

}


function getMoreInfoImage(id, root) {

    let htmltext = ''
    let iptc_exif = []
    if ( $('#divinfoimage_'+id).text().length !== 0) {
        if ($('#exifiptcB').text() == 'Hide') {
            $('#exifiptcB').html('Original IPTC EXIF data ?')
            $('#divinfoimage_'+id).hide()
        } else {
            $('#exifiptcB').html('Hide')
            $('#divinfoimage_'+id).show()
        }

    } else {
        let div = '#divinfoimage_' + id
        $('#divinfoimage_' + id).empty()
        $.ajax({
            url: '/exifIptc/' + root
        })
            .done(function (data) {

                iptc_exif = data
            })

        console.log(iptc_exif)

        $.ajax({
            url: '/moreInfoImage/' + id + ',' + root
        })
            .done(function (image) {

                if ((image['iptc_gm']) && (Object.keys(image['iptc_gm']).length != 0)) {
                    let i = 0
                    htmltext += '<br><h4>IPTC original: </h4><table class="table table-sm"><tbody>'
                    for (let key in image['iptc_gm']) {
                        if (Object.prototype.hasOwnProperty.call(image['iptc_gm'], key)) {
                            for (iptc_key in iptc_exif) {
                                if ((image['iptc_gm'][key] != null)
                                    && (key.slice(key.indexOf('[')) == iptc_exif[iptc_key]['code'])) {

                                    htmltext += '<tr><td style="text-align: right;width: 30%">' +
                                        '<strong>' + iptc_exif[iptc_key]['label'] + '</strong></td>' +
                                        '<td style="width: 70%">' + image['iptc_gm'][key] + '</td></tr>'
                                }
                            }
                        }
                        i++
                    }
                    htmltext += '</tbody></table>'
                }

                if ((image['exif_gm']) && (Object.keys(image['exif_gm']).length != 0)) {
                    htmltext += '<h4>EXIF original : </h4><table class="table table-sm">' +
                        '<tbody><tr><td style="text-align: right;width: 30%"><strong>filename</strong></td>' +
                        '<td style="width: 70%">' + image['filename'] + '</td></tr>' +
                        '<tr><td style="text-align: right;width: 30%"><strong>dirname</strong></td>' +
                        '<td style="width: 70%">' + image['path'] + '</td></tr>' +
                        '<tr><td style="text-align: right;width: 30%"><strong>filesize</strong></td>' +
                        '<td style="width: 70%">' + image['filesize'] + '</td></tr>' +
                        '<tr><td style="text-align: right;width: 30%"><strong>imagesize</strong></td>' +
                        '<td style="width: 70%">' + image['size']['width'] + ' x ' + image['size']['height'] + '</td></tr>'
                    for (let key in image['exif_gm']) {
                        if (Object.prototype.hasOwnProperty.call(image['exif_gm'], key)) {
                            for (exif_key in iptc_exif) {
                                if ((image['exif_gm'][key] != null) && (key == iptc_exif[exif_key]['code'])) {
                                    htmltext += '<tr>'
                                    htmltext += '<td style="text-align: right;width: 30%">' +
                                        '<strong>' + iptc_exif[exif_key]['label'] + '</strong></td>' +
                                        '<td style="width: 70%">' + image['exif_gm'][key] + '</td>'
                                    htmltext += '</tr>'
                                }
                            }
                        }
                    }
                    htmltext += '</tbody></table>'
                }

                if ((!image['exif_gm']) && (image['iptc_gm'])) {
                    htmltext += 'No more Info !'
                }
                $('#divinfoimage_' + id).html(htmltext)
                $('#exifiptcB').html('Hide')
            })
    }
}


function testinputs() {
    let t=true;

    $('input[required]').each(function(i){
        if ( $(this).val()=="" ) {
            t=false;
            return false;
        }
    })

    if (t) $(':submit').prop( "disabled", false )
    else    $(':submit').prop( "disabled", true )

}

function validateCorpusCrowdsourcing(lang) {

    console.log(lang)
    let msg1_fr = 'Une ou plusieurs dates sont dans le futur ...'
    let msg1_en = 'One or more dates are in the  futur ...'

    let msg2_fr = 'La plus ancienne date possible doit être antérieure à la plus récente date possible'
    let msg2_en = 'The earliest possible date must be prior to the latest possible date'


    let msg1 = '',
        msg2 = ''

    if (lang === 'fr') {
        msg1 = msg1_fr
        msg2 = msg2_fr
    } else if (lang == 'en') {
        msg1 = msg1_en
        msg2 = msg2_en
    }



    let d1 = new Date(document.forms['depotDemand'].elements.metadata_251.value)
    let d2 = new Date(document.forms['depotDemand'].elements.metadata_252.value)
    let today = new Date()
    if ((d2 > today ) || (d1 > today)) {
        alert(msg1)
        return false
    } else {
        if ((d2 - d1 > 0) || (d2 - d1 == 0))
            return true
        else {
            alert(msg2)
            return false
        }
    }
}

function exploreObj(root, folderId,lng,user) {

    $('#tabloid').empty()
    $('.alert').remove()

    let model = 'virtualObject'
    let type = 'object'
    let html = ''
    $.ajax({
        url: '/assets/../exploreObj/' + root + ',' + folderId
    })
        .done(function(data) {
            console.log(data.data_length)
            if (data.data_length === 0 ) {
                html += '0 Object'

                html += '<br><a href="#"onclick="getDepotDetails(' + user + ',' + folderId + ',0,\'' + lng + '\')" title="Back">' +
                        '<i class="fas fa-arrow-circle-left fa-2x"></i></a>'

                $('#tabloid').append(html)
            } else {
                if (typeof data[0] != 'undefined') {
                    html += '<div class="d-flex justify-content-between" id="topexplore">' +
                        '<span style="font-size: 1.6em">' + data[0].depotName + '</span></div>'
                    html += '<br><div class="listTabloid">'
                    for (i = 0; i < data.data_length; i++) {
                        html += '<div class="card"><h5 class="card-title text-center">' + data[i]['name'] + '</h5>' +
                            '<a href="/3drepository/' + folderId + '/virtual_' + data[i]['id_obj'] + '" title="' + data[i]['name'] + '" target="_blank">' +
                            '<img class="card-img mx-auto d-block" src="/media/image?fid=' + folderId + '&id=' + data[i]['id'] + '&format=' + data[i]['srcImgThumb'] + '&type=thumb&root=' + root + '" alt="' + data[i]['name'] + '"></a>'

                        html += '<div class="card-body">' +
                            '<div class="card-text">' +
                            '<div class="d-flex justify-content-between"> ' +
                            '<span id="' + root + '_' + data[i]['id_obj'] + '"></span>' +
                            //'<a href="/3drepository/' + folderId + '/virtual_' + data[i]['id_obj'] + '" target="_blank" title="Ouvrir dans un nouvel onglet">' +
                            //'<i class="fas fa-external-link-alt"></i></a>' +
                            '<a href="/edit,' + root + ',' + model + ',' + type + ',' + data[i]['id_obj'] + ',' + folderId + '" title="Indexer" >' +
                            '<i class="fas fa-edit" style="margin: 0 0.5em"></i></a>' +
                            '</div></div></div></div>'
                    }
                    html += '</div></div>'
                    html += '<ul class="nav explore-scroll-top">' +
                        '<li><a href="#topexplore" title="Top">' +
                        '<i class="fas fa-chevron-circle-up fa-2x"></i>' +
                        '</a>' +
                        '</li></ul>'

                    html += '<br><a href="#"onclick="getDepotDetails(' + user + ',' + folderId + ',0,\'' + lng + '\')" >' +
                        '<i class="fas fa-arrow-circle-left fa-2x"></i></a>'

                    $('#tabloid').append(html)

                } else {
                    console.log('KO')
                }
            }
        })
}

function delete_thespactols_item(root, id_item, item_type, id_thesaurus, id_thes_thesaurus , thesaurus ){
//function delete_thespactols_item(root, id_item, item_type, thes_path, thesaurus ){

    $.ajax({
        url: '/assets/../deleteThesaurusPactolsItem/' + root + ',' + id_item +','+ item_type+','+ id_thesaurus+','+ id_thes_thesaurus+','+ thesaurus
        //url: '/assets/../deleteThesaurusPactolsItem/' + root + ',' + id_item +','+ item_type+','+ thes_path+','+ thesaurus
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })

}

function delete_thespactolsgeo_item(root, id_item, item_type, id_thesaurus ){

    $.ajax({
        url: '/assets/../deleteThesaurusPactolsGeoItem/' + root + ',' + id_item +','+ item_type+','+ id_thesaurus
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })

}

function delete_thesmulti_item(root, id_item, item_type, thes_path , thesaurus ){

    $.ajax({
        url: '/assets/../deleteThesaurusMultiItem/' + root + ',' + id_item +','+ item_type+','+ thes_path+','+ thesaurus
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })

}

function delete_thesmulti_item_qual(root, id_item, item_type, thes_path , thesaurus, qualifier ){

    $.ajax({
        url: '/assets/../deleteThesaurusMultiItemQualifier/' + root + ',' + id_item +','+ item_type+','+ thes_path+','+ thesaurus+','+qualifier
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })

}

function delete_thes_item(root, id_item, item_type, thes_path , thesaurus ){

    $.ajax({
        url: '/assets/../deleteThesaurusItem/' + root + ',' + id_item +','+ item_type+','+ thes_path+','+ thesaurus
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })

}
function delete_thes_item_qual(root, id_item, item_type, id_thes , thesaurus, qualifier ){

    $.ajax({
        url: '/assets/../deleteThesaurusItemQualifier/' + root + ',' + id_item +','+ item_type+','+ id_thes+','+ thesaurus+','+qualifier
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })

}

function delete_tag_item(root, id_item, item_type, id_tag ){

    $.ajax({
        url: '/assets/../deleteTagItem/' + root + ',' + id_item +','+ item_type+','+ id_tag
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })

}

function delete_siteProject(id_overallProject, id_siteProject) {
    $.ajax({
        url: '/assets/../deleteSiteProject/' + id_overallProject +','+ id_siteProject
    })
        .done(function(data) {
            alert('Deleted !')
            location.reload();
        })
}


function generate_doi_xml(element, folderId) {

    // 1/ récupérer tous les fichiers d'un projet (un tableau de id ?)
    $.ajax({
        url: '/doiToRunXmlGeneration/'+folderId
    })
        .done(function(data) {
            //console.log(data)
            // pour chaque dépot générer le xml (boucle for)
            alert(data +' xml files will be generated !')
            location.reload();
        })
}

function generate_doi(itemType, idItem) {

    if (itemType === 'folder') {
        // On récupère le projet
        $.ajax({
            url: '/xmlForDOIProject/' + idItem
        })
            .done(function (data) {
                // pour chaque dépot générer le xml (boucle for)
                alert(' xml file will be generated for project  !')
                location.reload();
            })
    } else if (itemType === 'file') {
        $.ajax({
            url: '/xmlForDOI/' + idItem
        })
            .done(function (data) {
                // pour chaque dépot générer le xml (boucle for)
                alert(' xml file will be generated for the file  !')
                location.reload();
            })
    }
}

function validate_doi(type, idItem) {

    // mettre à jour dans la table des doi l'url
    $.ajax({
        url: '/assets/../validateDOI/'+type+','+idItem
    })
        .done(function(data) {
            console.log(data)
            if (data === 200) {
                alert(' DOI Validate OK ')
            } else {
                alert('Bad Validation for DOI ')
            }
            location.reload();
        })
}

function getComments(branche, id, userId, lng, nbComment){

    let val='Valider',
        rej = 'Rejeter',
        del = 'Supprimer',
        comment = 'Commentaire ',
        owner = 'Enregistré par ',
        behalf = 'Pour le compte de ',
        dateOn = 'Le ',
        dateFor = 'pour la date du '

    if (lng !== 'fr') {
        val = 'Validate'
        rej = 'Reject'
        del = 'Delete'
        comment = 'Comment'
        owner = 'Recorded by'
        behalf = 'On behalf of'
        dateOn = 'On'
        dateFor = 'For the date'
    }
    let html = ''
    let rights = 1
    let user_connected = parseInt(userId)

    let status = ''
    $('#edit-button').hide();
    $('#explore-div').removeAttr('style').removeClass('d-none hidden').hide();
    $('#menuCentre').removeAttr('style').removeClass('d-none hidden').show();
    $('#tabloid').removeAttr('style').removeClass('d-none hidden').empty().show();
    history.replaceState({}, '', window.location.origin + window.location.pathname);

    // Signal that the main content area is no longer in a specific "explore" state
    if (typeof explorePage !== 'undefined') {
        explorePage.id = 0; // Reset to a non-specific ID
        explorePage.url = null; // Nullify the URL to indicate no active explore type
    }

    // par rapport au user_id on récupère son status (accessible dans la variable req.user)
    $.ajax({
        url: '/statusRights/' + userId
    })
        .done(function (dat) {
            status = dat.status

            console.log(status)
            $.ajax({
                url: '/exploreComments/' + branche + ',' + id
            })
                .done(function (data) {
                    html += '<br>'
                    for (let i = 0; i < data.length; i++) {
                        // D'abord on affiche chaque item et ensuite on boucle sur les commentaires
                        // status admin ou scribe : on montre tous les commentaires validés ou en attente
                        // Les commentaire à false ne sont plus remontés par la requete
                        // status user et guest : on ne montre que les commentaires validés
                        if (status === 'user' || status === 'guest') {
                            let validComment = 0
                            for (let c in data[i]['comments']) {
                                if (data[i]['comments'][c]['validated'] !== 'pending') validComment++
                            }
                            console.log(validComment)
                            // si tous les commentaires sont en attente pour cet item, on ne l'affiche pas si on est user ou quest
                            if (!validComment) continue
                        }
                        if (!data[i]['comments']) continue
                        //html += '<div class="card mb-3" style="max-width: 540px;">' +
                        html += '<div class="card mb-3"><div class="row no-gutter">' +
                            '<div class="col-md-4" style="text-align: center!important;">'
                        if (data[i]['type'] === 'file') {
                            let context = 'v';

                            html += '<a href="/visionneuse,' + data[i]['id'] + '-' + data[i]['fid'] + ','+branche+'-' + context + ',' + rights + '" target="_blank">'
                            html += '<img class="card-img mx-auto d-block" src="/thumb/' + data[i]['fid'] + '_' + data[i]['id'] +'" alt="' + data[i]['filename'] + '">'
                            html += '</a>'
                        } else if (data[i]['type'] === 'object') {
                            let repre = data[i]['object_file_repre'] ? data[i]['object_file_repre'] : 0 ;
                            let context = 'v';
                            
                            html += '<a href="/visionneuseObj,' + data[i]['id'] + ',' + repre + '-' + data[i]['fid'] + ','+branche+'-' + context + ',' + rights + '" target="_blank">'
                            if (!data[i]['object_file_repre']) {
                                html += '<img class="card-img mx-auto d-block" src="/assets/images/default_repre_image_object.png" alt="' + data[i]['filename'] + '">'
                            } else {
                                html += '<img class="card-img mx-auto d-block" src="/thumb/' + data[i]['fid'] + '_' + data[i]['object_file_repre'] + '" alt="' + data[i]['filename'] + '">'
                            }
                            html += '</a>'
                                //'<img class="card-img mx-auto d-block" src="/media/image?fid=' + data[i]['fid'] +
                                //'&id=' + data[i]['id'] + '&format=' + data[i]['srcImgThumb'] + '&type=thumb&root=' + branche + '" alt="' + data[i]['filename'] + '"></a>'

                        }
                        if (data[i]['filename']) {
                            html += ''
                            if (data[i]['type'] === 'file') {
                                if (data[i]['filename'].length > 17) html += '<h5 class="card-title">' + data[i]['filename'].substring(0, 17) + '...</h5>'
                                else html += '<h5 class="card-title">' + data[i]['filename'] + '</h5> '
                            } else if (data[i]['type'] === 'object') {
                                if (data[i]['filename'].length > 17) html += '<h5 class="card-title">Object '+ data[i]['filename'].substring(0, 17) + '...</h5>'
                                else html += '<h5 class="card-title">Object ' + data[i]['filename'] + '</h5> '
                            }
                        } else html += '<h5></h5> '
                        html +='</div><div class="col-md-8"><div class="card-body" style="height:100%;width:100%;"><p class="card-text">'
                        if (data[i]['comments']) {
                            for (let c = 0; c < data[i]['comments'].length; c++) {
                                if (data[i]['comments'][c]['validated'] === 'pending') { // Ne pas afficher
                                    if (status === 'guest' || status === 'user')   continue
                                }
                                html += '<br>'+ comment+': '
                                html += '<br>'
                                html += escapeHtml(data[i]['comments'][c]['content']).replace(/\r\n/g, '<br>').trim() + '<br>' +
                                    '<small class="text-muted">'+owner+': ' + data[i]['comments'][c]['signature'] + '</small><br>'
                                // if (data[i]['comments'][c]['author'].length > 0) {
                                //     html += '<small class="text-muted">'+behalf+': ' + data[i]['comments'][c]['author'] + '</small><br>'
                                // }
                                html += '<small class="text-muted">'+dateOn+': ' + data[i]['comments'][c]['ladate'] + '</small><br>'
                                // if (data[i]['comments'][c]['other_date'] && (data[i]['comments'][c]['other_date'] !== '')) {
                                //     html += '<small class="text-muted">'+dateFor+': ' + escapeHtml(data[i]['comments'][c]['other_date']) + '</small><br>'
                                // }
                                // affichage des infos de model de données
                                if (data[i]['comments'][c]['description_thematisee_ND']) {
                                    for (let m in data[i]['comments'][c]['description_thematisee_ND']) {
                                        let taba = data[i]['comments'][c]['description_thematisee_ND'][m]['value']
                                        html += data[i]['comments'][c]['description_thematisee_ND'][m]['label'] + ' : ' + taba[0]
                                    }
                                    html += '<br>'
                                }

                                if (status === 'admin' || status === 'scribe') {
                                    if (data[i]['comments'][c]['validated'] === 'pending') {
                                        html += '<a href="#" class="btn btn-sm btn-success" title="' + val + '" onclick="patchComment(' + id + ', \'' + branche + '\', \'true\', ' + data[i]['comments'][c]['comment_id'] + ',\'' + status + '\',\'' + lng + '\', ' + nbComment + ')">V</a>' +
                                            ' <a href="#" class="btn btn-sm btn-danger" title="' + rej + '" onclick="patchComment(' + id + ', \'' + branche + '\', \'false\', ' + data[i]['comments'][c]['comment_id'] + ',\'' + status + '\',\'' + lng + '\', ' + nbComment + ')">X</a>'
                                        '<br>'
                                    }
                                    if (data[i]['comments'][c]['validated'] !== 'pending') {
                                        html += '<a href="#" onclick="deleteComment(' + id + ', \'' + branche + '\', \'true\', ' + data[i]['comments'][c]['comment_id'] + ',\'' + status + '\',\'' + lng + '\', ' + nbComment + ');" title="' + del + '" ><i class="far fa-trash-alt"></i></a>'
                                    }
                                } else { // pour le user qui a mis un commentaire :  il doit pouvoir l'enlever !
                                    if (data[i]['comments'][c]['id_user'] === user_connected) {
                                        html += '<a href="#" onclick="deleteComment(' + id + ', \'' + branche + '\', \'true\', ' + data[i]['comments'][c]['comment_id'] + ',\'' + status + '\',\'' + lng + '\', ' + nbComment + ');" title="' + del + '" ><i class="far fa-trash-alt"></i></a>'
                                    }
                                }

                            }

                        }
                        html += '</p></div></div>'
                        html += '</div></div>'

                    }

                    $('#tabloid').append(html)
                })
        })

}

function patchComment(idMainFolder, branche, status, commentId, userStatus, lng, nbComment){
    let dataget = {
        status: status,
        commentId: commentId
    }
    let val = 'Commentaire Validé',
        rej = 'Commentaire Rejeté'

    if (lng === 'en') {
        val = 'Comment validated'
        rej = 'Comment rejected'
    }
    $.ajax({
        data: dataget,
        url: '/patchComment/'+branche+','+idMainFolder,
        success: function(data){
            if (status === 'true') {
                alert(val)
                getComments(branche, idMainFolder, userStatus, lng, nbComment)
            } else if (status === 'false') {
                alert(rej)
                if ((nbComment-1) === 0){
                    location.reload()
                } else {
                    getComments(branche, idMainFolder, userStatus, lng, (nbComment-1))
                }
            }

        }
    })

}

function deleteComment(idMainFolder, branche, status, commentId, userStatus, lng, nbComment){
    let datadel = {
        status: status,
        commentId: commentId
    }
    let mess = "Commentaire supprimé !"
    if (lng === 'en') mess = "Comment deleted !"
    $.ajax({
        data: datadel,
        url: '/deleteComment/'+branche+','+idMainFolder,
        success: function(data){
            alert(mess)
            if ((nbComment-1) === 0){
                location.reload()
            } else {
                getComments(branche, idMainFolder, userStatus, lng, (nbComment-1))
            }


        }
    })
}


//https://stackoverflow.com/questions/1787322/htmlspecialchars-equivalent-in-javascript
function escapeHtml(text) {
    if (!text) return '';
    else {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };

        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
}


// Handle image loading errors to prevent retries
function handleImageErrors() {
    // Set to keep track of failed image URLs to prevent retries
    const failedImageUrls = new Set();

    // Function to handle image load errors
    function handleImageError(event) {
        const img = event.target;

        // If this URL has already failed, don't try to load it again
        if (failedImageUrls.has(img.src)) {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }

        // Add the failed URL to our set
        failedImageUrls.add(img.src);

        // Log the error (can be removed in production)
        console.log('Image failed to load:', img.src);

        // Replace with default image
        img.src = '/assets/images/novisuel.jpg';

        // Prevent the default error behavior (which might include retries)
        event.preventDefault();
        event.stopPropagation();
        return false;
    }

    // Add error handler to all existing images
    document.querySelectorAll('img').forEach(img => {
        img.addEventListener('error', handleImageError);
    });

    // Use MutationObserver to handle images added to the DOM after page load
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    // Check if the added node is an image
                    if (node.nodeName === 'IMG') {
                        node.addEventListener('error', handleImageError);
                    }

                    // Check for images within the added node
                    if (node.querySelectorAll) {
                        node.querySelectorAll('img').forEach(img => {
                            img.addEventListener('error', handleImageError);
                        });
                    }
                });
            }
        });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
}

// Initialize image error handling when the DOM is ready
$(function() {
    // Initialize image error handling
    handleImageErrors();

    // Initialize autocomplete
    $('#theso_chrono_value').autocomplete({
        minLength: 3,
        autofocus: true,
        source: function (request, response) {
            $.ajax({
                url: '/thesaurusPactolsName,conservatoire3d,chrono,' + request.term,
                dataType: "json",
                success: function (data) {
                    response(data)
                }
            });
        },
        select: function (event, ui) {
            //event.preventDefault();
            $('#theso_chrono_id').val(ui.item.id);
        }
    });
})

function convertStatus(userStatus, write)
{
    let userRight = 0
    // if userRight = 'notconnected' : on laisse 0
    if (userStatus === 'guest')
    {
        userRight = 1
    }
    else if (userStatus === 'user')
    {
        if ((write) && (write === -1))
        {
            userRight = 2
        }  else
        {
            userRight = 3
        }
    }
    else if (userStatus === 'scribe')
    {
        userRight = 4
    } else if (userStatus === 'admin')
    {
        userRight = 5
    }
    return userRight;
}


/*  menu contextuel */
/*  https://www.scriptol.fr/css/menu-contextuel.php */
if (typeof xMousePosition === 'undefined') {
    var xMousePosition = 0;
}
if (typeof yMousePosition === 'undefined') {
    var yMousePosition = 0;
}
document.onmousemove = function(e)
{
  xMousePosition = e.clientX + window.pageXOffset;
  yMousePosition = e.clientY + window.pageYOffset;
};


function addVirtualFolder(element, idFolder, lng)
{
    const msg_virtualFolder = lng === 'fr' ? 'Nom pour le dossier virtuel' : 'Name of the virtual folder'
    const msg_emptyName = lng === 'fr' ? 'Vous n\'avez entré aucun nom. Veuillez entrer un nom complet' : 'Please Enter non empty string'
    const msg_badChar = lng === 'fr' ? 'Ne pas utiliser de caractères spéciaux' : 'Do not use special char'

    let name = prompt(msg_virtualFolder)
    if (name === null || name === "") {
        alert(msg_emptyName)
    } else if(/^[.$,]*$/.test(name) === true){
        alert(msg_badChar)
    } else {
        $.ajax({
                url: '/createVirtualFolder,' + name +','+idFolder
            })
                .done(function(data) {
                    // Log the response data to help with debugging
                    console.log('Create virtual folder response:', data);

                    // Store the newly created folder ID in localStorage
                    // The server returns the folder ID in data.code.id
                    if (data && data.code && data.code.id) {
                        console.log('Found folder ID in response:', data.code.id);
                        // Navigate to the new folder
                        const newFolderId = data.code.id;
                        const baseUrl = window.location.pathname.split('?')[0];
                        window.location.href = baseUrl + '?folder=' + newFolderId;
                    } else {
                        // Fallback to simple reload if we don't get an ID
                        console.error('Failed to get folder ID from response:', data);
                        location.reload();
                    }
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('AJAX request failed:', textStatus, errorThrown);
                    location.reload();
                })
    }
}

function deleteVirtualFolder(element, idProject, nameProject, lng) {
    const msg_virtualFolderDelete = lng === 'fr' ? ' supprimé !' : ' deleted !';
    $.ajax({
        url: '/deleteVirtualFolder/' + idProject
    })
    .done(function(data){
        if (!data['code']) {
            alert(data['texte']);
        } else {
            $('#explore-div').empty();

            // Check if the deleted folder is currently selected
            const urlParams = new URLSearchParams(window.location.search);
            const currentFolder = urlParams.get('folder');
            if ((currentFolder && currentFolder == idProject) || (typeof explorePage !== 'undefined' && explorePage.id == idProject)) {
                // Redirect to main project page (remove ?folder=...)
                window.location.href = window.location.pathname.split('?')[0];
            } else {
                location.reload();
            }
        }
    });
}

function renameFolder(element, idFolder, nameFolder, lng) {

    // Renommer un folder:
    // ça fonctionne mais c'est lourd !
    //let newname = prompt("Ancien nom: "+nameFolder + " \nDonnez un nouveau nom ", nameFolder)
    const msg_virtualFolder = lng === 'fr' ? 'Nouveau Nom' : 'New name'
    const msg_emptyName = lng === 'fr' ? 'Vous n\'avez entré aucun nom. Veuillez entrer un nom complet' : 'Please Enter non empty string'
    const msg_badChar = lng === 'fr' ? 'Ne pas utiliser de caractères spéciaux' : 'Do not use special char'
    const msg_error = lng === 'fr' ? 'Impossible de renommer' : 'Not possible to rename'


    let newname = prompt(msg_virtualFolder, nameFolder)
    if (newname === null || newname === "") {
        alert(msg_emptyName)
    } else if(/^[.$,]*$/.test(newname) === true){
        alert(msg_badChar)
    } else {

        $.ajax({
            url: '/renameFolder/' + idFolder + ','+ nameFolder+','+ newname
        })
            .done(function(data) {
                if (!data['code']) {
                    // alert('OK')
                    // window.location.reload()
                    // alert('Folder renamed successfully. Reloading tree...');
                }
                else {
                    // alert('KO')
                    // window.location.reload()
                    // alert('Error renaming folder.');
                }
                location.reload(); // TODO: Ideally, just reload foldersTreeVitrine.
            })
    }
}

function downloadFile(element, idFolder, isVirtual, folderName, lng = "fr")
{
    // construct a modal to download files
    let html = ''
    //$('#explore-div').empty()
    const msg_download = lng === 'fr' ? 'Taille du dossier à télécharger : ' : 'Size of folder to download: '
    // on ajax success after get all files to download : show the modal to "activate" the download part
    if (isVirtual) {
        $.ajax({
            url: '/getVirtualSizeFolder,' + idFolder
        })
            .done(function (dataT) {
                if (confirm(msg_download + dataT['size'])) {
                    html+= '<div><a href="/projectv/'+dataT['rootF'] + '?folder='+ idFolder+'" class="btn btn-hand btn-secondary" title="Retour" style="color: #242943;"><i class="fa fa-chevron-left fa-2x"></i></a>   ' +
                        '  <a href="/downloadFileCl/'+idFolder+','+folderName+',1" class="btn btn-hand btn-secondary" title="download"><i class="fas fa-download fa-2x"></i> '+folderName+'.zip</a></div>'
                    $('#explore-div').empty()
                    $('#explore-div').append(html)
                }
            })

    } else {

        $.ajax({
            url: '/getSizeFolder,' + idFolder
        })
            .done(function (data) {
                if (confirm(msg_download + data)) {
                    html+= '<div><a href="/downloadFileCl/'+idFolder+','+folderName+',0" class="btn btn-hand btn-secondary" title="download"><i class="fas fa-download fa-2x"></i> '+folderName+'.zip</a></h4></div>'
                    $('#explore-div').empty()
                    $('#explore-div').append(html)
                }
            })
    }
}

function annoter(element)
{
    alert('...')
}

function createObject(element, idFolder, lng )
{
    // Create object
    const msg_createObj = lng === 'fr' ? 'Nom pour l\'objet' : 'Name of the object'
    const msgnull_obj = lng === 'fr' ? 'Vous n\'avez entré aucun nom, veuillez saisir un nom' : 'Please enter object name. Do not leave empty'
    let name = prompt(msg_createObj)
    if (name === null || name === "") {
        alert(msgnull_obj)
    } else {

        $.ajax({
                url: '/createObject,' + name +','+idFolder
            })
                .done(function(data) {
                    // location.reload()
                    // alert('Object created. Reloading content and tree...');
                    location.reload(); // TODO: Ideally, reload explore-results and foldersTreeVitrine.
                })
    }
}

function monmenu(element, isVirtual, isDownloadable, idProject, nameProject, userStatus, userWrite, quality, lng, branch, model, model_type, nbItems)
{
    //quality add for distinction between master and slave folder of a project  : do not create object in a master folder
    let userRight = convertStatus(userStatus, userWrite)

    // Get the menu element that was already created in folders-tree-vitrine.js
    let d = document.getElementById('ctxmenu1');

    // Change lng in menu if needed
    const msg_download = lng === 'fr' ? 'Télécharger' : 'Download'
    const msg_annotate = lng === 'fr' ? 'Annoter' : 'Annotate'
    const msg_createObj = lng === 'fr' ? 'Créer un objet dans ce dossier' : 'Create object in this folder'
    const msg_delete = lng === 'fr' ? 'Supprimer' : 'Delete'
    const msg_createPID = lng === 'fr' ? 'Créer DOI pour les fichiers de ce dossier' : 'Create Digital Identifier for items in this folder'
    const msg_rename = lng === 'fr' ? 'Renommer le dossier' : 'Rename folder'
    const msg_expand_recursively = lng === 'fr' ? 'Déplier récursivement' : 'Expand recursively'
    const msg_close_recursively = lng === 'fr' ? 'Replier récursivement' : 'Close recursively'

    // New messages for batch operations
    const msg_add_metadata_all_folder_items = lng === 'fr' ? 'Ajouter métadonnées à tous les éléments' : 'Add metadata to all items'
    const msg_add_tag_all_folder_items = lng === 'fr' ? 'Ajouter un tag à tous les éléments' : 'Add tag to all items'
    const msg_delete_all_folder_items = lng === 'fr' ? 'Supprimer tous les éléments du dossier' : 'Delete all items in folder'
    const confirm_delete_all_virtual_folder_items_fr = `Êtes-vous sûr de vouloir supprimer tous les éléments du dossier virtuel "${nameProject}" ? Cette action est irréversible.`
    const confirm_delete_all_virtual_folder_items_en = `Are you sure you want to delete all items in the virtual folder "${nameProject}"? This action is irreversible.`
    const confirm_delete_all_folder_items_fr = `Êtes-vous sûr de vouloir supprimer tous les éléments du dossier "${nameProject}" ? Cette action est irréversible.`
    const confirm_delete_all_folder_items_en = `Are you sure you want to delete all items in the folder "${nameProject}"? This action is irreversible.`


    // Une partie du menu ne regarde pas les userWrite mais seulement si le dossier est téléchargeable.
    if (userRight > 3) { // pour le moment on affiche le menu seulement pour admin
        d.onmouseover = function () {
            this.style.cursor = 'pointer';
        }
        // Check if this folder has children (has a folder-parent element)
        const hasChildren = element.closest('li').querySelector('.folder-parent') !== null;

        // Add "Expand recursively" or "Close recursively" option if the folder has children
        if (hasChildren) {
            // Check if the folder is already expanded
            const $li = $(element.closest('li'));
            const $folderParent = $li.find('> .d-flex > .folder-parent');
            const isExpanded = $folderParent.hasClass('folder-parent-down');

            let pExpand = document.createElement('p');
            d.appendChild(pExpand);

            if (isExpanded) {
                // If expanded, show "Close recursively" option
                pExpand.onclick = function () {
                    if (typeof window.closeFolderRecursively === 'function') {
                        window.closeFolderRecursively($li);
                        if (d && d.parentNode) { d.parentNode.removeChild(d); }
                    }
                };
                pExpand.setAttribute('class', 'ctxline');
                pExpand.innerHTML = msg_close_recursively;
            } else {
                // If collapsed, show "Expand recursively" option
                pExpand.onclick = function () {
                    if (typeof window.expandFolderRecursively === 'function') {
                        window.expandFolderRecursively($li);
                        if (d && d.parentNode) { d.parentNode.removeChild(d); }
                    }
                };
                pExpand.setAttribute('class', 'ctxline');
                pExpand.innerHTML = msg_expand_recursively;
            }
        }

        if (isDownloadable) { // on ne télécharge que si on est admin/scribe pour les tests pour le moment
            // dans tous les cas si on crée le menu on crée également ce premier item
            let p2 = document.createElement('p');
            d.appendChild(p2);
            p2.onclick = function () {
                downloadFile(element, idProject, isVirtual, nameProject, lng)
            };
            p2.setAttribute('class', 'ctxline');
            p2.innerHTML = msg_download + " \"" + nameProject + "\"";
        }

        // 1. Renommer un dossier
        let pr = document.createElement('p');
        d.appendChild(pr);
        pr.onclick = function () {
            renameFolder(element, idProject, nameProject, lng)
        };
        pr.setAttribute('class', 'ctxline');
        pr.innerHTML = msg_rename;

        // 2. Créer un objet
        if ((userRight > 3) && (quality === 'slave')) {
            let pO = document.createElement('p');
            d.appendChild(pO);
            pO.onclick = function () {
                createObject(element, idProject, nameProject)
            };
            pO.setAttribute('class', 'ctxline');
            pO.innerHTML = msg_createObj + " \"" + nameProject + "\"";
        }

        // 3. Créer DOI
        if (userRight > 4) {
            let p5 = document.createElement('p');
            d.appendChild(p5);
            p5.onclick = function () {
                generate_doi_xml(element, idProject)
            };
            p5.setAttribute('class', 'ctxline');
            p5.innerHTML = msg_createPID + " \"" + nameProject + "\"";
        }

        if (nbItems > 0) {
            // 4. Add metadata to all items
            let pAddMeta = document.createElement('p');
            d.appendChild(pAddMeta);
            pAddMeta.onclick = function () {
                handleAddMetadataToAllFolderItems(idProject, nameProject, lng, branch, model, model_type);
                if (d && d.parentNode) { d.parentNode.removeChild(d); }
            };
            pAddMeta.setAttribute('class', 'ctxline');
            pAddMeta.innerHTML = msg_add_metadata_all_folder_items + " \"" + nameProject + "\"";

            // 5. Add tag to all items
            let pAddTag = document.createElement('p');
            d.appendChild(pAddTag);
            pAddTag.onclick = function () {
                handleAddTagToAllFolderItems(idProject, nameProject, lng, branch);
                if (d && d.parentNode) { d.parentNode.removeChild(d); }
            };
            pAddTag.setAttribute('class', 'ctxline');
            pAddTag.innerHTML = msg_add_tag_all_folder_items + " \"" + nameProject + "\"";

            // 6. Delete all items in the folder - only for virtual folders
            if (isVirtual) {
                let pDeleteAll = document.createElement('p');
                d.appendChild(pDeleteAll);
                pDeleteAll.onclick = function () {
                    const confirmMessage = lng === 'fr' ? confirm_delete_all_virtual_folder_items_fr : confirm_delete_all_virtual_folder_items_en;
                    if (confirm(confirmMessage)) {
                        handleDeleteAllFolderItems(idProject, nameProject, lng, isVirtual);
                    }
                    if (d && d.parentNode) { d.parentNode.removeChild(d); }
                };
                pDeleteAll.setAttribute('class', 'ctxline');
                pDeleteAll.innerHTML = msg_delete_all_folder_items + " \"" + nameProject + "\"";
            }
        }

        // 7. Delete virtual folder
        if (isVirtual) {
            let p4 = document.createElement('p');
            d.appendChild(p4);
            p4.onclick = function () {
                deleteVirtualFolder(element, idProject, nameProject, lng)
            };
            p4.setAttribute('class', 'ctxline');
            p4.innerHTML = msg_delete + " \"" + nameProject + "\"";
        }

        if (userRight > 5) { // Personne ! quand se sera développé
            let p3 = document.createElement('p');
            d.appendChild(p3);
            p3.onclick = function () {
                annoter(element)
            };
            p3.setAttribute('class', 'ctxline');
            p3.innerHTML = msg_annotate + " \"" + nameProject + "\"";
        }

    } else if (userRight === 3) { // pour un user de type user avec des droits en écriture sur le dossier
        d.onmouseover = function () {
            this.style.cursor = 'pointer';
        }

        // Check if this folder has children (has a folder-parent element)
        const hasChildren = element.closest('li').querySelector('.folder-parent') !== null;

        // Add "Expand recursively" or "Close recursively" option if the folder has children
        if (hasChildren) {
            // Check if the folder is already expanded
            const $li = $(element.closest('li'));
            const $folderParent = $li.find('> .d-flex > .folder-parent');
            const isExpanded = $folderParent.hasClass('folder-parent-down');

            let pExpand = document.createElement('p');
            d.appendChild(pExpand);

            if (isExpanded) {
                // If expanded, show "Close recursively" option
                pExpand.onclick = function () {
                    if (typeof window.closeFolderRecursively === 'function') {
                        window.closeFolderRecursively($li);
                        if (d && d.parentNode) { d.parentNode.removeChild(d); }
                    }
                };
                pExpand.setAttribute('class', 'ctxline');
                pExpand.innerHTML = msg_close_recursively;
            } else {
                // If collapsed, show "Expand recursively" option
                pExpand.onclick = function () {
                    if (typeof window.expandFolderRecursively === 'function') {
                        window.expandFolderRecursively($li);
                        if (d && d.parentNode) { d.parentNode.removeChild(d); }
                    }
                };
                pExpand.setAttribute('class', 'ctxline');
                pExpand.innerHTML = msg_expand_recursively;
            }
        }

        if (isDownloadable) { // on ne télécharge que si on est admin/scribe pour les tests pour le moment
            // dans tous les cas si on crée le menu on crée également ce premier item
            let p2 = document.createElement('p');
            d.appendChild(p2);
            p2.onclick = function () {
                downloadFile(element, idProject, isVirtual, nameProject, lng)
            };
            p2.setAttribute('class', 'ctxline');
            p2.innerHTML = msg_download + " \"" + nameProject + "\"";
        }

        // 1. Renommer un dossier
        let pr = document.createElement('p');
        d.appendChild(pr);
        pr.onclick = function () {
            renameFolder(element, idProject, nameProject, lng)
        };
        pr.setAttribute('class', 'ctxline');
        pr.innerHTML = msg_rename;

        // 2. Créer un objet - seulement sur les dossiers virtuels pour les users
        if (isVirtual) {
            let pO = document.createElement('p');
            d.appendChild(pO);
            pO.onclick = function () {
                createObject(element, idProject, nameProject)
            };
            pO.setAttribute('class', 'ctxline');
            pO.innerHTML = msg_createObj + " \"" + nameProject + "\"";
        }

        // 7. Delete virtual folder
        if (isVirtual) {
            let p4 = document.createElement('p');
            d.appendChild(p4);
            p4.onclick = function () {
                deleteVirtualFolder(element, idProject, nameProject, lng)
            };
            p4.setAttribute('class', 'ctxline');
            p4.innerHTML = msg_delete + " \"" + nameProject + "\"";
        }

    } else if (userRight < 3) {
        if (isDownloadable) {
            // TODO : si le folder est public alors on doit pouvoir le télécharger
            d.onmouseover = function () {
                this.style.cursor = 'pointer';
            }

            // Check if this folder has children (has a folder-parent element)
            const hasChildren = element.closest('li').querySelector('.folder-parent') !== null;

            // Add "Expand recursively" or "Close recursively" option if the folder has children
            if (hasChildren) {
                // Check if the folder is already expanded
                const $li = $(element.closest('li'));
                const $folderParent = $li.find('> .d-flex > .folder-parent');
                const isExpanded = $folderParent.hasClass('folder-parent-down');

                let pExpand = document.createElement('p');
                d.appendChild(pExpand);

                if (isExpanded) {
                    // If expanded, show "Close recursively" option
                    pExpand.onclick = function () {
                        if (typeof window.closeFolderRecursively === 'function') {
                            window.closeFolderRecursively($li);
                            if (d && d.parentNode) { d.parentNode.removeChild(d); }
                        }
                    };
                    pExpand.setAttribute('class', 'ctxline');
                    pExpand.innerHTML = msg_close_recursively;
                } else {
                    // If collapsed, show "Expand recursively" option
                    pExpand.onclick = function () {
                        if (typeof window.expandFolderRecursively === 'function') {
                            window.expandFolderRecursively($li);
                            if (d && d.parentNode) { d.parentNode.removeChild(d); }
                        }
                    };
                    pExpand.setAttribute('class', 'ctxline');
                    pExpand.innerHTML = msg_expand_recursively;
                }
            }

            let p = document.createElement('p');
            d.appendChild(p);
            p.onclick = function () {
                downloadFile(element, idProject, isVirtual, nameProject, lng)
            };
            p.setAttribute('class', 'ctxline');
            p.innerHTML = msg_download + " \"" + nameProject + "\"";
        }
    }

    return false;
}

function handleAddMetadataToAllFolderItems(idProject, nameProject, lng, branch, model, model_type) {
    if (!branch || !model || !model_type) {
        const errorMsg = lng === 'fr' ? "Informations manquantes (branche, modèle ou type de modèle) pour ajouter des métadonnées à tous les éléments."
                                      : "Missing information (branch, model, or model type) to add metadata to all items.";
        alert(errorMsg);
        console.error("handleAddMetadataToAllFolderItems: Missing branch, model, or model_type. Action aborted.");
        return;
    }
    window.location.href = `/edit,${branch},${model},${model_type},0,${idProject}`;
}

function handleAddTagToAllFolderItems(idProject, nameProject, lng, branch) {
    if (!branch) {
        const errorMsg = lng === 'fr' ? "Information de la branche manquante. Impossible d'ajouter des tags à tous les éléments."
                                      : "Branch information is missing. Cannot add tags to all items.";
        alert(errorMsg);
        console.error("handleAddTagToAllFolderItems: Branch information is missing. Action aborted.");
        return;
    }
    window.location.href = `/keyword,${branch},0,0,${idProject}`;
}

function handleDeleteAllFolderItems(idProject, nameProject, lng, isVirtual) {
    if (typeof deleteAll === 'function') {
        deleteAll(idProject, lng);
        location.reload();
    } else {
        const errorMsg = lng === 'fr' ? `Erreur: La fonction 'deleteAll' n'est pas définie. Impossible de supprimer les éléments du dossier: ${nameProject}.`
                                      : `Error: The function 'deleteAll' is not defined. Cannot delete items for folder: ${nameProject}.`;
        alert(errorMsg);
        console.error(`'deleteAll' function not found for folder ${nameProject} (ID: ${idProject}). Please ensure it's loaded and available globally.`);
    }
}

async function copyMetadataItem(branch, item_type, item_id){
    const copy_url = new URL('/copyMetadataItem', window.location.origin);
    copy_url.searchParams.set('branch', branch);
    copy_url.searchParams.set('item_type', item_type);
    copy_url.searchParams.set('item_id', item_id);
    const result = await fetch(copy_url)
        .then(response => response.json())
        .catch(() => ({status: 'error', details: [{type: 'network', success: false, error: 'Erreur réseau'}]}));

    showMetadataCopyPopup(result, item_id, 'fr');
}

// Affiche le résultat de la copie de métadonnées dans une popup modale
function showMetadataCopyPopup(result, item_id, lng) {
    // Supprimer l'ancienne popup si présente
    const old = document.getElementById('metadata-copy-popup');
    if(old) old.remove();

    // Déterminer le titre
    let title = lng === 'fr' ? 'Résultat de la sélection de métadonnées' : 'Metadata copy selection result';

    // Construire le contenu détaillé avec classes CSS
    let html = `<div class="metadata-copy-popup-title">${title}</div>`;
    if(result.details && Array.isArray(result.details)){
        html += '<ul class="metadata-copy-popup-list">';
        for(const d of result.details){
            let iconClass = d.success ? 'fa fa-check-circle' : 'fa fa-times-circle';
            let label = d.label && d.label.trim() ? d.label : d.type;
            html += `<li><i class='${iconClass}' style='margin-right:6px;'></i><span class="metadata-copy-popup-label">${label}</span>`;
            if(d.error) html += ` <span class="metadata-copy-popup-${d.success ? 'success' : 'error'}">(${d.error})</span>`;
            html += `</li>`;
        }
        html += '</ul>';
    }

    // Créer l'élément
    const div = document.createElement('div');
    div.id = 'metadata-copy-popup';
    div.className = 'metadata-copy-popup';
    div.innerHTML = html +
        '<button id="close-metadata-popup" class="metadata-copy-popup-close" title="Fermer">✖</button>';

    // Placement à côté de la visionneuse si elle existe
    const viewer_text = document.querySelector(`#text_${item_id} .modal-dialog`);
    if(viewer_text) {
        // Récupérer la position et la taille de la visionneuse
        const rect = viewer_text.getBoundingClientRect();
        const offsetLeft = viewer_text.offsetLeft;
        div.style.top = rect.top + 'px';
        div.style.left = '16px';
        div.style.right = `${offsetLeft + rect.width + 16}px`;
    }

    // Fade in
    div.classList.add('fade-in');

    // Custom close button with fade-out
    div.querySelector('#close-metadata-popup').onclick = () => {
        div.classList.remove('fade-in');
        div.classList.add('fade-out');
        div.addEventListener('animationend', () => div.remove(), { once: true });
    };

    document.body.appendChild(div);

    // Auto close après 5s avec fade-out
    setTimeout(() => {
        if(document.getElementById('metadata-copy-popup')) {
            div.classList.remove('fade-in');
            div.classList.add('fade-out');
            div.addEventListener('animationend', () => div.remove(), { once: true });
        }
    }, 2500);
}

async function pasteMetadataItem(branch, item_type, item_id, folder_id, lng){
    const paste_url = new URL('/pasteMetadataItem', window.location.origin);
    paste_url.searchParams.set('branch', branch);
    paste_url.searchParams.set('item_type', item_type);
    paste_url.searchParams.set('item_id', item_id);
    const result = await fetch(paste_url)
        .then(response => response.json())
        .catch(() => ({status: 'error', details: [{type: 'network', success: false, error: 'Erreur réseau'}]}));

    // Si succès, rafraîchir la visionneuse
    if(result.status === 'success'){
        promiseSwitchModalMulti('text',item_type,item_id,folder_id,branch,item_id).then(() => {
            showMetadataPastePopup(result, item_id, lng);
        })  
    // Si erreur, afficher la popup
    }else{
        showMetadataPastePopup(result, item_id, lng);
    }
}

function showMetadataPastePopup(result, item_id, lng) {
    // Supprimer l'ancienne popup si présente
    const old = document.getElementById('metadata-copy-popup');
    if(old) old.remove();

    // Déterminer l'icône et le titre (icône n'est plus utilisé, mais gardé pour compatibilité)
    let title = lng === 'fr' ? 'Résultat de la copie de métadonnées' : 'Metadata copy result';

    // Construire le contenu détaillé avec classes CSS
    let html = `<div class="metadata-copy-popup-title">${title}</div>`;
    if(result.details && Array.isArray(result.details)){
        html += '<ul class="metadata-copy-popup-list">';
        for(const d of result.details){
            // Use Font Awesome icons for status
            let iconClass = d.success ? 'fa fa-check-circle' : 'fa fa-times-circle';
            // Display label if present, otherwise fallback to type
            let label = d.label && d.label.trim() ? d.label : d.type;
            html += `<li><i class='${iconClass}' style='margin-right:6px;'></i><span class="metadata-copy-popup-label">${label}</span>`;
            if(d.error) html += ` <span class="metadata-copy-popup-error">(${d.error})</span>`;
            html += `</li>`;
        }
        html += '</ul>';
    }

    // Créer l'élément
    const div = document.createElement('div');
    div.id = 'metadata-copy-popup';
    div.className = 'metadata-copy-popup';
    div.innerHTML = html +
        '<button id="close-metadata-popup" class="metadata-copy-popup-close" title="Fermer">✖</button>';

    // Placement à côté de la visionneuse si elle existe
    const viewer_text = document.querySelector(`#text_${item_id} .modal-dialog`);
    if(viewer_text) {
        // Récupérer la position et la taille de la visionneuse
        const rect = viewer_text.getBoundingClientRect();
        const offsetLeft = viewer_text.offsetLeft;
        div.style.top = rect.top + 'px';
        div.style.left = '16px';
        div.style.right = `${offsetLeft + rect.width + 16}px`;
    }

    // Fade in
    div.classList.add('fade-in');

    // Custom close button with fade-out
    div.querySelector('#close-metadata-popup').onclick = () => {
        div.classList.remove('fade-in');
        div.classList.add('fade-out');
        div.addEventListener('animationend', () => div.remove(), { once: true });
    };

    const observer = new MutationObserver(() => {
        if (!viewer_text.classList.contains('show')) {
            // Fade out and remove the popup
            div.classList.remove('fade-in');
            div.classList.add('fade-out');
            div.addEventListener('animationend', () => div.remove(), { once: true });
            observer.disconnect();
        }
    });

    observer.observe(viewer_text.parentElement, { attributes: true, attributeFilter: ['class']});

    document.body.appendChild(div);
}