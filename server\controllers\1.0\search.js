const db = require("../../helpers/db").default;
const responseHelper = require("../../helpers/response");

/**
 * Crée un string à partir d'une liste de strings et d'un séparateur inséré entre chaque élément
 * @param {Array.<String>} values Liste de strings
 * @param {String} sep Séparateur
 * @returns {String}
 */
function separateValues(values, sep) {
  let res = "",
    resfin = "";
  for (let i = 0; i < values.length; i++) {
    if (i > 0) res += sep;
    res += buildSearchPattern(values[i]);
  }
  // si la chaine de caractère contient un single quote ' cela ne peut fonctionner avec un  ~* '....'
  // cela peut fonctionner avec un ~* '.......'
  resfin = res.replace("'", ".");
  return resfin;
}

/**
 * Crée le texte d'une requête de recherche faisant des jointures entre différentes requêtes
 * @param {Array.<String>} values Liste de requêtes
 * @param {String} sep Séparateur (UNION, UNION ALL, INTERSECT, EXCEPT)
 * @returns {String}
 */
function separateQueries(values, sep) {
  let res = "";
  for (const v in values) {
    if (v > 0) res += `${sep}\n`;
    res += `(\n${values[v]})\n`;
  }
  return res;
}

/**
 * Crée le texte d'une requête de recherche sur champs multiples en faisant l'UNION de plusieurs requêtes
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} tags Liste de valeurs à rechercher
 * @returns {String}
 */
function createMultipleFieldsQuery(branch, item, tags) {
  let queries = [];
  queries.push(createPassportQuery(branch, item, tags));
  queries.push(createThesaurusQuery(branch, item, tags));
  queries.push(createThesaurusMultiQuery(branch, item, tags));
  queries.push(createThesaurusPeriodoQuery(branch, item, tags));
  queries.push(createThesaurusPactolsQuery(branch, item, tags));
  queries.push(createNameQuery(item, tags));

  return separateQueries(queries, "UNION");
}

/**
 * Crée le texte d'une requête de recherche sur la globalité du passport
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} tags Liste de valeurs à rechercher
 * @returns {String}
 */
function createPassportQuery(branch, item, tags) {
  let join = `INNER JOIN ${branch}_passport p ON p.id_item = ${item.id}.id AND p.item_type = '${
    item.type
  }' AND p.value::text ~* '${separateValues(tags, "|")}'\n`;
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus ou sur un thesaurus en particulier si précisé
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} tags Liste de valeurs à rechercher
 * @param {String} thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns {String}
 */
function createThesaurusQuery(branch, item, tags, thesaurus) {
  let join = `INNER JOIN ${branch}_thesaurus_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}'\n`;
  if (thesaurus === undefined) {
    // champs multiples
    join +=
      `INNER JOIN ${branch}_thesaurus t ON t.id = ti.id_thesaurus ` +
      `AND t.thesaurus_path <@ (SELECT ARRAY_AGG(thesaurus_path) FROM ${branch}_thesaurus WHERE name ~* '${separateValues(
        tags,
        "|"
      )}')\n` +
      ` AND t.thesaurus IN (SELECT thesaurus FROM  ${branch}_thesaurus WHERE name ~* '${separateValues(tags, "|")}')\n`;
  } else {
    // recherche thesaurus
    join +=
      `INNER JOIN ${branch}_thesaurus t ON t.id_thes = ti.id_thes_thesaurus ` +
      `AND t.thesaurus = '${thesaurus}' AND t.thesaurus = ti.thesaurus AND t.thesaurus_path @ '${separateValues(
        tags,
        "|"
      )}'\n`;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus multi ou sur un thesaurus multi en particulier si précisé
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} tags Liste de valeurs à rechercher
 * @param {String} thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns {String}
 */
function createThesaurusMultiQuery(branch, item, tags, thesaurus) {
  let join = `INNER JOIN ${branch}_thesaurus_multi_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}'\n`;
  if (thesaurus === undefined) {
    // champs multiples
    join +=
      `INNER JOIN ${branch}_thesaurus_multi t ON t.id = ti.id_thesaurus AND ` +
      `t.thesaurus_path <@ (SELECT ARRAY_AGG(thesaurus_path) FROM ${branch}_thesaurus_multi WHERE name ~* '${separateValues(
        tags,
        "|"
      )}')\n` +
      ` AND t.thesaurus IN (SELECT thesaurus FROM  ${branch}_thesaurus_multi WHERE name ~* '${separateValues(
        tags,
        "|"
      )}')\n`;
  } else {
    // recherche thesaurus
    join +=
      `INNER JOIN ${branch}_thesaurus_multi t ON t.id_thes = ti.id_thes_thesaurus ` +
      `AND t.thesaurus = '${thesaurus}' AND t.thesaurus = ti.thesaurus AND t.thesaurus_path @ '${separateValues(
        tags,
        "|"
      )}'\n`;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus pactols ou sur un thesaurus pactols en particulier si précisé
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} tags Liste de valeurs à rechercher
 * @param {String} thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns {String}
 */
function createThesaurusPactolsQuery(branch, item, tags, thesaurus) {
  let join = `INNER JOIN ${branch}_thesaurus_pactols_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}'\n`;
  if (thesaurus === undefined) {
    // champs multiples
    join +=
      `INNER JOIN ${branch}_thesaurus_pactols t ON t.id = ti.id_thesaurus AND ` +
      `t.thesaurus_path <@ (SELECT ARRAY_AGG(thesaurus_path) FROM ${branch}_thesaurus_pactols WHERE name ~* '${separateValues(
        tags,
        "|"
      )}')\n` +
      ` AND t.thesaurus IN (SELECT thesaurus FROM  ${branch}_thesaurus_pactols WHERE name ~* '${separateValues(
        tags,
        "|"
      )}')\n`;
  } else {
    // recherche thesaurus
    // TODO : ne pas jointer sur l'id absolu du thesaurus mais sur l'id interne d'un thesaurus ?
    join +=
      `INNER JOIN ${branch}_thesaurus_pactols t ON t.id_thes = ti.id_thes_thesaurus ` +
      `AND t.thesaurus_path @ '${separateValues(tags, "|")}'\n`;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus geo pactols ou sur un thesaurus pactols en particulier si précisé
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} tags Liste de valeurs à rechercher
 * @param {String} thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns {String}
 */
function createThesaurusPactolsGeoQuery(branch, item, tags, thesaurus) {
  let join = `INNER JOIN ${branch}_thesaurus_pactols_geo_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}'\n`;
  if (thesaurus === undefined) {
    // champs multiples
    join += `INNER JOIN ${branch}_thesaurus_pactols_geo t ON t.id = ti.id_thesaurus AND t.name ~* '${separateValues(
      tags,
      "|"
    )}'\n`;
  } else {
    // recherche thesaurus
    // TODO : ne pas jointer sur l'id absolu du thesaurus mais sur l'id interne d'un thesaurus ?
    join +=
      `INNER JOIN ${branch}_thesaurus_pactols_geo t ON t.id = ti.id_thesaurus ` +
      `AND t.id::text ~* '${separateValues(tags, "|")}'\n`;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus periodo ou sur un thesaurus periodo en particulier si précisé
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} tags Liste de valeurs à rechercher
 * @param {String} thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns {String}
 */
function createThesaurusPeriodoQuery(branch, item, tags, thesaurus) {
  let join = `INNER JOIN ${branch}_thesaurus_periodo_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}'\n`;
  if (thesaurus === undefined) {
    // champs multiples
    join += `INNER JOIN ${branch}_thesaurus_periodo t ON t.id = ti.id_periodo AND t.label ~* '${separateValues(
      tags,
      "|"
    )}'\n`;
  } else {
    // recherche thesaurus
    join += `INNER JOIN ${branch}_thesaurus_periodo t ON t.id = ti.id_periodo AND t.id_periodo ~* '${separateValues(
      tags,
      "|"
    )}'\n`;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur des métadonnées du passport données
 * @param {String} branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Object.<string, Array.<String>>} passport Contient l'identifiant de la métadonnée et les valeurs à rechercher
 * @returns {String}
 */
function createMetadataQuery(branch, item, passport) {
  let join = "";
  for (const p in passport) {
    let tags = passport[p];
    let alias = `p_${p}`;

    join +=
      `INNER JOIN ${branch}_passport ${alias} ON ${alias}.id_item = ${item.id}.id` +
      ` AND ${alias}.item_type = '${item.type}'` +
      ` AND ${alias}.id_metadata = ${p} AND ${alias}.value::text ~* '${separateValues(tags, "|")}'\n`;
  }

  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur le nom des items
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} names Liste des noms à rechercher
 * @returns {String}
 */
function createNameQuery(item, names) {
  let where = item.where != "" ? `${item.where} AND ` : "WHERE ";
  const patterns = names.map(function(name) {
    return `${item.id}.name ~* '${buildSearchPattern(name)}'`;
  });
  where += patterns.join(" OR ");
  return item.start + where;
}

/**
 * Crée le texte d'une requête de recherche sur les extensions des fichiers
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {Array.<String>} extensions Liste des extensions à rechercher
 * @returns {String}
 */
function createExtensionsQuery(item, extensions) {
  let where = item.where != "" ? `${item.where} AND ` : "WHERE ";
  if (separateValues(extensions, "|").includes("files")) {
    where += "fi.file_ext ~* '.'\n";
  } else {
    where += `fi.file_ext ~* '${separateValues(extensions, "|")}'\n`;
  }
  return item.start + where;
}

/**
 * Crée le texte d'une requête de recherche sur les fichiers dont l'utilisateur a accès
 * @param {Object} item Contient les informations sur le type d'item
 * @param {String} item.start Texte des débuts de requêtes
 * @param {String} item.where Texte de la commande WHERE
 * @param {String} item.type Le type d'item
 * @param {String} item.id Alias de la table du type d'item
 * @param {String} accessFolders Liste des répertoires accessibles
 * @returns {String}
 */
function createAccessFoldersQuery(item, accessFolders) {
  let where = item.where != "" ? `${item.where} AND ` : "WHERE ";
  where += `fo.id IN ${accessFolders}\n`;
  return item.start + where;
}

/**
 * Crée le texte d'une requête de recherche entière
 * @param {String} branch pft3d ou conservatoire3d
 * @param {Number} idFolderMaitre Id du projet
 * @param {Number} userId Id de l'utilisateur
 * @param {String} reqelem JSON de recherche encodé au format URI, chaine de caractère dans l'url
 * @param {Object} selects Contient le texte des commandes SELECT pour chaque type d'items
 * @returns {String}
 */
async function createQuery(branch, idFolderMaitre, userId, reqelem, selects) {
  const parameters = JSON.parse(decodeURIComponent(reqelem));
  let user_status;
  // USER STATUS
  try {
    const result = await db.oneOrNone(
      `SELECT COALESCE ((SELECT user_status FROM archeogrid_user WHERE id = ${userId}), 'guest') AS status`
    );
    user_status = result.status;
  } catch (e) {
    user_status = "guest";
  }
  // FOLDERS ACCESSIBLES
  let read = "";
  if (userId) {
    read = `SELECT get_access_from_rootfolder_gen( '${branch}', ${idFolderMaitre}, $1 ) as fold `;
  } else {
    read = `SELECT array_agg(id) as fold FROM ${branch}_folder WHERE  status = 'public' `;
  }

  let accessFolders = "";
  if ((branch === "pft3d" ) || (branch === "corpus")) {
    try {
      const userRead = await db.oneOrNone(read, userId);

      if (userRead.fold.length > 0) {
        accessFolders = JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")");
      }
    } catch (e) {
      responseHelper.sendError(500, "server_error in createQuery : userRead", e);
    }
  }

  // FOLDERS SELECTION
  let folders = "";
  if (parameters.folder) {
    folders = `fo.folder_path @ '${parameters.folder}'`; // répertoire et ses enfants
    // ALL FOLDERS
  } else {
    folders = `fo.folder_path <@ '${idFolderMaitre}'`;
  }

  // EXTENSIONS
  let searchFiles = false;
  let searchObjects = false;
  let searchFolders = false;
  let searchUnicos = false;
  let searchDeposits = false;
  let extensions = [];

  if (parameters.type) {
    if (parameters.type === "files" && ((branch === "pft3d" ) || (branch === "corpus"))) {
      searchFiles = true;
    }
    if (parameters.type === "unicos" && ((branch === "pft3d" ) || (branch === "corpus"))) {
      searchUnicos = true;
    } else if (parameters.type === "objects") {
      searchObjects = true;
    }
    // else if (parameters['type'] === 'folders') {
    //     searchFolders = true
    // }
    else if (parameters.type === "deposits" && branch === "conservatoire3d") {
      searchDeposits = true;
    } else {
      searchFiles = true;
      extensions.push(parameters.type);
    }
  } else {
    if ((branch === "pft3d" ) || (branch === "corpus")) {
      searchFiles = searchObjects = searchUnicos = true;
    }
    if (branch === "conservatoire3d") {
      searchObjects = searchDeposits = true;
    }
  }

  let items = {};
  if (searchFiles) {
    let startFile =
      `${selects.file}FROM ${branch}_file fi\n` + `INNER JOIN ${branch}_folder fo ON fo.id = fi.id_folder\n`;
    if ((branch === "pft3d") || (branch === 'corpus')) startFile += `AND ${folders}\n`;

    items.files = {
      start: startFile,
      where: "",
      type: "file",
      id: "fi",
    };
  }
  if (searchUnicos) {
    let startUnico =
      `${selects.unico}FROM ${branch}_unico u\n` +
      `INNER JOIN ${branch}_file i ON i.id = u.id_file\n` +
      `INNER JOIN ${branch}_folder fo ON fo.id = i.id_folder\n`;
    if ((branch === "pft3d") || (branch === 'corpus')) startUnico += `AND ${folders}\n`;

    items.unicos = {
      start: startUnico,
      where: "",
      type: "unico",
      id: "u",
    };
  }
  if (searchObjects) {
    let startObject =
      `${selects.object}FROM ${branch}_object ob\n` + `INNER JOIN ${branch}_folder fo ON fo.id = ob.id_folder\n`;
    if ((branch === "pft3d") || (branch === 'corpus')) startObject += `AND ${folders}\n`;
    startObject += `LEFT OUTER JOIN ${branch}_file fi ON fi.id = ob.id_file_representative\n`;
    if (branch === "conservatoire3d") {
      // pour le conservatoire, pour les objets, on peut stocker dans la metadata 146 une url pour une image de visualisation
      startObject += `LEFT OUTER JOIN ${branch}_passport pass_nakala ON pass_nakala.id_item = ob.id AND pass_nakala.id_metadata = 146\n`;
    }

    let where = branch === "conservatoire3d" && user_status !== "admin" ? "WHERE ob.doi IS NOT NULL\n" : "";

    items.objects = {
      start: startObject,
      where: where,
      type: "object",
      id: "ob",
    };
  }
  if (searchFolders) {
    let startFolder = `${selects.folder}FROM ${branch}_folder fo\n`;
    startFolder += `LEFT OUTER JOIN ${branch}_file fi ON fi.id = fo.id_representative_picture\n`;

    items.folders = {
      start: startFolder,
      where: "",
      type: "folder",
      id: "fo",
    };
  }
  if (searchDeposits) {
    let startDeposit = `${selects.deposit}FROM ${branch}_folder fo\n`;
    startDeposit +=
      `LEFT OUTER JOIN ${branch}_file fi ON fi.id = fo.id_representative_picture\n` +
      `LEFT OUTER JOIN ${branch}_passport pass_nom ON pass_nom.id_item = fo.id AND pass_nom.id_metadata = 97\n` +
      `LEFT OUTER JOIN ${branch}_passport pass_nakala ON pass_nakala.id_item = fo.id AND pass_nakala.id_metadata = 142\n`;

    let where = "WHERE fo.folder_passport = 'deposit' AND fo.visible = 'true'";
    where += user_status === "admin" ? "\n" : " AND fo.doi IS NOT NULL\n";

    items.deposit = {
      start: startDeposit,
      where: where,
      type: "folder",
      id: "fo",
    };
  }

  /**
   * Crée le texte d'une requête de recherche sur un type d'item en particulier
   * @param {Object} item Contient les informations sur le type d'item
   * @param {String} item.start Texte des débuts de requêtes
   * @param {String} item.where Texte de la commande WHERE
   * @param {String} item.type Le type d'item
   * @param {String} item.id Alias de la table du type d'item
   * @returns {String}
   */
  function createItemQuery(item) {
    let queries = [];

    if (parameters.all) {
      let multipleFieldsQueries = [];
      for (const searchBar in parameters.all) {
        const tags = parameters.all[searchBar];
        multipleFieldsQueries.push(createMultipleFieldsQuery(branch, item, tags));
      }
      queries.push(separateQueries(multipleFieldsQueries, "INTERSECT"));
    }

    if (parameters.thesaurus) {
      let thesaurusQueries = [];
      for (const thesaurus in parameters.thesaurus) {
        const tags = parameters.thesaurus[thesaurus];
        thesaurusQueries.push(createThesaurusQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.multi) {
      let allThesaurusMultiQueries = [];
      for (const thesaurus in parameters.multi) {
        const searchBars = parameters.multi[thesaurus];

        let thesaurusQueries = [];
        for (const searchBar in searchBars) {
          const tags = searchBars[searchBar];
          thesaurusQueries.push(createThesaurusMultiQuery(branch, item, tags, thesaurus));
        }
        allThesaurusMultiQueries.push(separateQueries(thesaurusQueries, "INTERSECT"));
      }
      queries.push(separateQueries(allThesaurusMultiQueries, "INTERSECT"));
    }

    if (parameters.periodo) {
      let thesaurusQueries = [];
      for (const thesaurus in parameters.periodo) {
        const tags = parameters.periodo[thesaurus];
        thesaurusQueries.push(createThesaurusPeriodoQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.pactols) {
      let thesaurusQueries = [];
      for (const thesaurus in parameters.pactols) {
        const tags = parameters.pactols[thesaurus];
        thesaurusQueries.push(createThesaurusPactolsQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.geopactols) {
      let thesaurusQueries = [];
      for (const thesaurus in parameters.geopactols) {
        const tags = parameters.geopactols[thesaurus];
        thesaurusQueries.push(createThesaurusPactolsGeoQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.passport) {
      queries.push(createMetadataQuery(branch, item, parameters.passport));
    }

    if (extensions != "") {
      queries.push(createExtensionsQuery(item, extensions));
    }

    if (accessFolders.length > 0) {
      queries.push(createAccessFoldersQuery(item, accessFolders));
    }

    if (queries.length > 0) {
      return separateQueries(queries, "INTERSECT");
    }
    return item.start + item.where;
  }

  let queries = [];
  for (const i in items) {
    queries.push(createItemQuery(items[i]));
  }

  return separateQueries(queries, "UNION");
}

// get url endpoint /searchNb/:branch,:idFolder
// pour récupérer le nombre de résultats totaux de la recherche
exports.searchNb = async function (req, res) {
  let { branch } = req.params;
  let idFolderMaitre = parseInt(req.params.idFolder);
  let userId = parseInt(req.query.userId);
  let reqelem = req.query.search;

  const selects = {
    file: "SELECT DISTINCT fi.id AS id, 'file' AS item_type, fi.id_folder\n",
    object: "SELECT DISTINCT ob.id AS id, 'object' AS item_type, ob.id_folder AS id_folder\n",
    folder: "SELECT DISTINCT fo.id AS id, 'folder' AS item_type, fo.id_parent AS id_folder\n",
    deposit: "SELECT DISTINCT fo.id AS id, 'deposit' AS item_type, fo.id_parent AS id_folder\n",
    unico: "SELECT DISTINCT u.id AS id, 'unico' AS item_type, i.id_folder\n",
  };

  let start = "SELECT count(id) as nb, item_type, id_folder\n" + "FROM\n" + "(\n";

  let end = ") I\n" + "GROUP BY item_type, id_folder ";

  // Première requete : construction de la requete
  try {
    const query = start + (await createQuery(branch, idFolderMaitre, userId, reqelem, selects)) + end;

    // Deuxieme requete : requete précédemment construite
    try {
      const search = await db.any(query, idFolderMaitre);
      res.status(200).sendData(search);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getSearchNb", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getSearchNb query creation", e, req, res);
  }
};

// get url endpoint /search/:branch,:idFolder
// pour récupérer les résultats de la recherche
exports.search = async function (req, res) {
  let { body } = req;
  let { branch } = req.params;
  let idFolderMaitre = parseInt(req.params.idFolder);
  let userId = parseInt(body.userId);
  let reqelem = body.search;

  let offset = (parseInt(body.page) - 1) * body.pagination;
  let limit = body.pagination;

  let selects = {};

  if ((branch === "pft3d") || (branch === "corpus")) {
    selects = {
      file:
        "SELECT DISTINCT fi.id AS id_item, 'file' AS item_type, fi.id_folder AS id_folder, fi.name AS name,\n" +
        "fi.path AS path, fi.file_ext AS extension, fi.id AS idfile, fi.id_folder AS idfolder, 0 as x, 0 as y , 0 as width, 0 as height\n",
      object:
        "SELECT DISTINCT ob.id AS id_item, 'object' AS item_type, ob.id_folder AS id_folder, ob.name AS name,\n" +
        "fi.path AS path, fi.file_ext AS extension, ob.id_file_representative AS idfile, COALESCE(ob.id_folder, fi.id_folder) AS idfolder, 0 as x, 0 as y , 0 as width, 0 as height\n",
      unico:
        "SELECT DISTINCT u.id AS id_item, 'unico' AS item_type, i.id_folder AS id_folder, u.name AS name,\n" +
        "i.path AS path, i.file_ext AS extension, u.id_file AS idfile, i.id_folder AS idfolder, u.x, u.y, u.width, u.height\n",
      // folder: 'SELECT DISTINCT fo.id AS id_item, \'folder\' AS item_type, fo.id_parent AS id_folder, fo.name AS name,\n' +
      //     'fi.path AS path, fi.file_ext AS extension, fo.id_representative_picture AS idfile, fi.id_folder AS idfolder\n'
    };
  }
  if (branch === "conservatoire3d") {
    //  récupérer l'affichage de la miniature nakala si elle existe pour les objets
    selects = {
      // file: 'SELECT DISTINCT fi.id AS id_item, \'file\' AS item_type, fi.id_folder AS id_folder, fi.name AS name,\n' +
      //     'fi.path AS path, fi.file_ext AS extension, fi.id AS idfile, fi.id_folder AS idfolder, null AS doi, null AS nakala\n',
      object:
        "SELECT DISTINCT ob.id AS id_item, 'object' AS item_type, ob.id_folder AS id_folder, ob.name AS name,\n" +
        "fi.path AS path, fi.file_ext AS extension, ob.id_file_representative AS idfile, COALESCE(ob.id_folder, fi.id_folder) AS idfolder, ob.doi AS doi, " +
        "CASE WHEN ob.id_nakala IS NOT NULL THEN ob.id_nakala  " +
        "WHEN ob.id_nakala IS NULL THEN pass_nakala.value[1] END AS nakala\n",
      // folder: 'SELECT DISTINCT fo.id AS id_item, \'folder\' AS item_type, fo.id_parent AS id_folder, fo.name AS name,\n' +
      //     'fi.path AS path, fi.file_ext AS extension, fo.id_representative_picture AS idfile, fi.id_folder AS idfolder, null AS doi, null AS nakala\n',
      deposit:
        "SELECT DISTINCT fo.id AS id_item, 'deposit' AS item_type, fo.id_parent AS id_folder, COALESCE (pass_nom.value[1], fo.name) AS name,\n" +
        "fi.path AS path, fi.file_ext AS extension, fo.id_representative_picture AS idfile, fi.id_folder AS idfolder, fo.doi, pass_nakala.value[1] AS nakala\n",
    };
  }

  let end = "ORDER BY item_type, id_item ";
  if (limit > 0) end += `OFFSET ${offset} LIMIT ${limit}`;

  // Première requete : construction de la requete
  try {
    let query = (await createQuery(branch, idFolderMaitre, userId, reqelem, selects)) + end;

    // Deuxieme requete : requete précédemment construite
    try {
      const search = await db.any(query, idFolderMaitre);
      res.status(200).sendData(search);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getSearch request", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getSearch query creation", e, req, res);
  }
};

// get url endpoint /saveSearch/:branch,:projectId,:userId,:name,:search
// pour que l'utilisateur puisse enregistrer sa recherche
exports.saveSearch = async function (req, res) {
  let { branch } = req.params;
  let projectId = parseInt(req.params.projectId);
  let { userId } = req.params;

  let { name } = req.params;
  let search = decodeURIComponent(req.params.search);

  let query;
  if ((branch === "pft3d" ) || (branch === "corpus")) {
    query = `INSERT INTO ${branch}_saved_search VALUES ( nextval('${branch}_saved_search_id_seq'), ${userId}, ${projectId}, '${name}', CURRENT_TIMESTAMP, '${search}'::json)`;
  }
  if (branch === "conservatoire3d") {
    query = `INSERT INTO ${branch}_saved_search VALUES ( nextval('${branch}_saved_search_id_seq'), ${userId}, '${name}', CURRENT_TIMESTAMP, '${search}'::json)`;
  }

  await db
    .none(query)
    .then(() => {
      res.status(200).sendData({});
    })
    .catch((e) => {
      responseHelper.sendError(500, "server_error in saveSearch request", e, req, res);
    });
};

// get url endpoint /getUserSavedSearches/:branch,:userId/:projectId?
// pour que l'utilisateur puisse récupérer ses recherches enregistrées
exports.getUserSavedSearches = async function (req, res) {
  let { branch } = req.params;
  let { userId } = req.params;
  let query;

  if ((branch === "pft3d" ) || (branch === "corpus")) {
    query = `SELECT json_build_object('id', s.id_project, 'name', p.value) as project, s.id, s.name, s.date, s.search
        FROM ${branch}_saved_search s
        INNER JOIN ${branch}_passport p ON item_type = 'folder' AND id_metadata = 47 AND id_item = s.id_project
        WHERE s.id_user = ${userId}
        GROUP BY s.id, s.id_project, p.value`;
  }
  if (branch === "conservatoire3d") {
    query = `SELECT s.id, s.name, s.date, s.search
        FROM ${branch}_saved_search s
        WHERE s.id_user = ${userId}
        GROUP BY s.id`;
  }

  await db
    .any(query)
    .then((data) => {
      res.status(200).sendData(data);
    })
    .catch((e) => {
      responseHelper.sendError(500, "server_error in getUserSavedSearches request", e, req, res);
    });
};

// get url endpoint /removeUserSavedSearch/:branch,:userId/:searchId?
// pour que l'utilisateur puisse effacer une recherche enregistrée
exports.removeUserSavedSearch = async function (req, res) {
  let { branch } = req.params;
  let { userId } = req.params;

  let query = `DELETE FROM ${branch}_saved_search WHERE id_user = ${userId}`;

  if (req.params.searchId) {
    query += ` AND id = '${req.params.searchId}'`;
  }

  await db
    .any(query)
    .then((data) => {
      res.status(200).sendData(data);
    })
    .catch((e) => {
      responseHelper.sendError(500, "server_error in removeUserSavedSearch request", e, req, res);
    });
};

function isQuoted(term) {
  return typeof term === "string" && term.length > 1 &&
    ((term.startsWith('"') && term.endsWith('"')) ||
     (term.startsWith('\\"') && term.endsWith('\\"')));
}

function stripQuotes(term) {
  if (term.startsWith('\\"') && term.endsWith('\\"')) {
    return term.substring(2, term.length - 2);
  }
  return term.substring(1, term.length - 1);
}

function buildSearchPattern(term) {
  if (isQuoted(term)) {
    // Use (^|\\W)term($|\\W) for robust whole-word match
    const exact = stripQuotes(term).replace(/'/g, "''");
    return '(^|\\W)' + exact + '($|\\W)';
  } else {
    // Default: substring match
    return String(term).replace(/'/g, "''");
  }
}