import { z } from "zod";
import https from "https";
import { httpsRequestPromise } from "~/helpers/tools";

import { t } from "~/trpcContext";
import { branch, id, language, thesaurus } from "~/types/schemas";
import {
  exploreThesaurusItemPageOutput,
  findOutput,
  getMultiOutput,
  getPactolsOutput,
  getTreePactolsOutput,
  itemNbOutput,
  item_type,
  multiItemOutput,
  multiOriginOutput,
  originFolderOutput,
  pactolsOriginOutput,
  thesaurusNbOutput,
  thesaurus_data,
  periodO,
  treeMultiOutput,
  treeOutput,
} from "~/types/schemas/thesaurus";
import { Console, log } from "console";

export default t.router({
  listSimple: t.procedure.input(z.object({ branch, language })).query(async ({ ctx, input }) => {
    const localized_name = input.language === "fr" ? "name" : `name_${input.language}`;
    const data = await ctx.database.manyOrNone(
      `SELECT id::int, thesaurus, COALESCE(${localized_name}, name_en, name) AS name 
        FROM ${input.branch}_thesaurus 
        WHERE nlevel(thesaurus_path) = 1`,
    );

    return z.object({ id, thesaurus: z.string(), name: z.string() }).array().parse(data);
  }),
  listMulti: t.procedure.input(z.object({ branch, language })).query(async ({ ctx, input }) => {
    const localized_name = input.language === "fr" ? "name" : `name_${input.language}`;
    const data = await ctx.database.manyOrNone(
      `SELECT id::int, thesaurus, COALESCE(${localized_name}, name_en, name) as name 
      FROM ${input.branch}_thesaurus_multi 
      WHERE nlevel(thesaurus_path) = 1`,
    );

    return z.object({ id, thesaurus: z.string(), name: z.string() }).array().parse(data);
  }),
  getMulti: t.procedure
    .input(z.object({ branch, thesaurus: z.string() }))
    .output(getMultiOutput)
    .query(async ({ ctx, input }) => {
      const data = await ctx.database.any(
        `SELECT id::integer, id_thes::integer, thesaurus, short_name, thesaurus_path, ${input.branch}_get_real_thes_path_multi_unique_id(id_thes, $1) as name 
        FROM ${input.branch}_thesaurus_multi 
        WHERE thesaurus = $1 
        ORDER BY global_rank `,
        input.thesaurus,
      );

      return getMultiOutput.parse(data);
    }),

  getPactols: t.procedure
    .input(z.object({ branch, thesaurus: z.string(), filter: z.number().nullish() }))
    .output(getPactolsOutput)
    .query(async ({ ctx, input }) => {
      let query = "";
      let input_param: string | number = "";
      // TODO A refaire: stocker l'id thes point de départ du parcours dans la partie "function" de la table _metadata
      // TODO pour toujours garder l'information "thesaurus" dans la colonne "list" de _metadata
      //if (!isNaN(parseInt(input.thesaurus))) { // on récupère seulement une partie du thesaurus Cela suppose qu'on a un seul thesaurus 'sujet'
      if (input.filter) {
        // on récupère seulement une partie du thesaurus Cela suppose qu'on a un seul thesaurus 'sujet'
        input_param = input.filter;
        query = `SELECT id::integer, id_thes::integer, thesaurus, name as short_name, thesaurus_path, ${input.branch}_get_real_thes_path_pactols_v2(id_thes, '${input.thesaurus}') as name 
        FROM ${input.branch}_thesaurus_pactols 
        WHERE thesaurus_path <@ 
        (SELECT thesaurus_path 
          FROM ${input.branch}_thesaurus_pactols 
          WHERE thesaurus = '${input.thesaurus}' AND id_thes = $1)::ltree 
        ORDER BY thesaurus_path `;
      } else {
        input_param = input.thesaurus;
        query = `SELECT id::integer, id_thes::integer, thesaurus, name as short_name, thesaurus_path, ${input.branch}_get_real_thes_path_pactols_v2(id_thes, $1) as name 
          FROM ${input.branch}_thesaurus_pactols 
          WHERE thesaurus = $1 
          ORDER BY thesaurus_path`;
      }
      console.log(query);

      const data = await ctx.database.any(query, input_param);

      return getPactolsOutput.parse(data);
    }),
  addMulti: t.procedure
    .input(
      z.object({
        branch,
        thesaurus: z.string(),
        item_id: id,
        id: z.string(),
        type: item_type,
        user_id: id,
        qualifier:   z.string().nullish(), // can be null or undefined
        id_metadata: z.number().nullish()  // can be null or undefined
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // TODO : incrémenter le nb_item (automatiquement avec un trigger de la table thesaurus_pactols_item

      // on récupère le id_thesaurus et le id_thes séparé d'un _ (voir la fonction qui récupère les info)
      const thesaurus_id = Number.parseInt(input.id.split("_")[0]);
      const id_thes_thesaurus = Number.parseInt(input.id.split("_")[1]);

      if (input.qualifier) {
        console.log("cas avec qualifier on peut quand même indexer sur un autre qualifier");
      } else {
        console.log("cas sans qualifier");
        const { count } = await ctx.database.one(
          `SELECT count(*) FROM ${input.branch}_thesaurus_multi_item WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 AND id_thes_thesaurus = $4 `,
          [input.thesaurus, input.item_id, input.type, id_thes_thesaurus],
        );
        if (count !== "0") {
          console.log("count pas egal a zero on sort");
          return false;
        }
        console.log("on peut indexer");
      }

      await ctx.thesaurus.insertThesaurusItem(
        input.branch,
        'multi', // thesaurus_type
        thesaurus_id,
        id_thes_thesaurus,
        input.thesaurus,
        input.user_id,
        {
          id_item: input.item_id,
          item_type: input.type,
          qualifier: input.qualifier, // can be null/undefined
          id_metadata: input.id_metadata, // can be null/undefined
          collection: null
        }
      );
      return true;
    }),

  addSimple: t.procedure
    .input(
      z.object({
        branch,
        thesaurus: z.string(),
        item_id: id,
        id_thesaurus: id,
        id_thes_thesaurus: id,
        type: item_type,
        user_id: id,
        qualifier: z.string().nullish(),  // can be null or undefined
        id_metadata: z.number().nullish() // can be null or undefined
      }),
    )
    .mutation(async ({ ctx, input }) => {

      await ctx.thesaurus.insertThesaurusItem(
        input.branch,
        'simple', // thesaurus_type
        input.id_thesaurus,
        input.id_thes_thesaurus,
        input.thesaurus,
        input.user_id,
        {
          id_item: input.item_id,
          item_type: input.type,
          qualifier: input.qualifier, // can be null/undefined
          id_metadata: input.id_metadata, // can be null/undefined
          collection: null
        }
      );
    }),

  deleteLinkOfItem: t.procedure
    .input(
      z.object({
        branch,
        type: z.enum(["simple", "multi"]),
        thesaurus: z.string(),
        item_id: id
      }))
    .mutation(async ({ ctx, input }) => {
      await ctx.database.none(`DELETE FROM ${input.branch}_thesaurus${input.type === "multi" ? "_multi" : ""}_item WHERE thesaurus = $1 AND id_item = $2`, [input.thesaurus, input.item_id]);
    }),

  updateMulti: t.procedure
    .input(
      z.object({
        branch,
        type: item_type,
        thesaurus: z.string(),
        thesaurus_id: id,
        item_id: id,
        qualifier: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const rankNull = await ctx.database.one(
        `UPDATE ${input.branch}_thesaurus_multi_item SET qualifier = $1 WHERE thesaurus = $2 AND id_thes_thesaurus = $3 AND id_item = $4 AND item_type = $5 RETURNING id`,
        [input.qualifier, input.thesaurus, input.thesaurus_id, input.item_id, input.type],
      );

      return rankNull;
    }),
  multiName: t.procedure
    .input(z.object({ branch, name: z.string(), thesaurus: z.string() }))
    .output(
      z
        .object({
          id: z.string(),
          name: z.string(),
          value: z.string(),
          label: z.string(),
          thes_path: z.string(),
          thesaurus: z.string(),
        })
        .array(),
    )
    .query(async ({ input, ctx }) => {
      // pour pouvoir mettre des ' dans les noms des collections
      const strong_name = input.name.replace(/singlequote/g, "''");

      return await ctx.database.any(
        `SELECT name as name, ${input.branch}_get_real_thes_path_multi_unique_id(id_thes, $1) as value, concat(id, '_', id_thes) as id, ${input.branch}_get_real_thes_path_multi_unique_id(id_thes, $1) as label, thesaurus_path as thes_path, thesaurus 
        FROM ${input.branch}_thesaurus_multi 
        WHERE thesaurus = $1 AND name ILIKE '%${strong_name}%' 
        ORDER BY label `,
        input.thesaurus,
      );
    }),
  multiItem: t.procedure
    .input(z.object({ branch, type: item_type, item_id: id, language }))
    .output(multiItemOutput)
    .query(async ({ input, ctx }) => {
      const localized_name = input.language === "fr" ? "name" : `name_${input.language}`;
      // Donner tous les mots-clés de tous les thesaurus de thesaurus multi
      const pactagmultiList = await ctx.database.manyOrNone(
        `SELECT tm.id::integer, tm.id_thes, COALESCE(tm.${localized_name}, tm.name_en, tm.name) as name, tm.short_name, tm.thesaurus, mm.name as thesaurus_name, it.thes_path, tm.identifier, it.qualifier 
        FROM ${input.branch}_thesaurus_multi tm 
        INNER JOIN ${input.branch}_thesaurus_multi_item it ON it.id_thes_thesaurus = tm.id_thes AND it.thesaurus = tm.thesaurus 
        INNER JOIN ${input.branch}_thesaurus_multi mm ON mm.thesaurus = tm.thesaurus 
        WHERE id_item = $1 AND item_type='${input.type}' AND nlevel(mm.thesaurus_path)=1 `,
        input.item_id,
      );

      const list_thes = z
        .object({ name: z.string() })
        .array()
        .parse(
          await ctx.database.manyOrNone(
            `SELECT distinct name FROM ${input.branch}_thesaurus_multi mm WHERE nlevel(mm.thesaurus_path) = 1`,
          ),
        );

      return multiItemOutput.parse({
        tagthes: pactagmultiList,
        thesaurusName: list_thes.map((el) => el.name),
      });
    }),
  find: t.procedure
    .input(
      z.object({
        branch,
        thesaurus: z.string(),
        subset: z.string().optional(),
        type: z.enum(["simple", "multi"]).optional()
      }),
    )
    .output(findOutput)
    .query(async ({ input, ctx }) => {
      let leaf;
      switch(input.type){
        case "multi":
          leaf = "thesaurus_multi";
          break;
        // add case for other thesaurus type if needed (modified the zod input type)
        case "simple":
        default:
          leaf = "thesaurus";
          break;
      }

      const thesaurus_table = `${input.branch}_${leaf}`;

      const subsetFilter = input.subset ? `AND global_rank like '${input.subset}%'` : "";

      const result = await ctx.database.manyOrNone(`
        SELECT id::integer, id_thes, short_name, thesaurus_path, name as name_fr, name_en, identifier
        FROM ${thesaurus_table}
        WHERE thesaurus = $1
        ${subsetFilter}
        AND nlevel(thesaurus_path) > 1 
        ORDER BY global_rank`, [input.thesaurus]
      );

      if(!result){
        throw new Error("THESAURUS CONTROLLER ERROR : No result found");
      }

      return findOutput.parse(result);
    }),
  getTree: t.procedure
    .input(z.object({ branch, language, thesaurus: z.string(), thesaurus_path: z.string() }))
    .query(async ({ ctx, input }) => {
      const oldThesaurusZero = ["rome_navone", "LEVALLOIS_PERRET_CLEMENT_BAYARD", "2C3D_AUDRIX"];

      const thesaurus_id =
        input.branch === "pft3d" && oldThesaurusZero.includes(input.thesaurus) ? "0" : input.thesaurus_path;

      const name = input.language === "fr" ? "name" : `name_${input.language}`;

      const query = `SELECT id_thes as id, COALESCE(NULLIF(${name}, ''), NULLIF(name_en, ''), NULLIF(name, '')) as short_name, 
        COALESCE(NULLIF(${name}, ''), NULLIF(name_en, ''), NULLIF(name, '')) as name, nb_item::int, 
        get_children_thesaurus_${input.branch}(id_thes, $1) as get_children, 
        nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::int , thesaurus_path as path 
        FROM ${input.branch}_thesaurus 
        WHERE thesaurus = $1 AND thesaurus_path <@ $2::ltree 
        ORDER BY global_rank  `;

      const data = await ctx.database.any(query, [input.thesaurus, thesaurus_id]);

      return treeOutput.parse(data);
    }),
  getTreeMulti: t.procedure
    .input(z.object({ branch, language, thesaurus: z.string(), thesaurus_path: z.string() }))
    .query(async ({ ctx, input }) => {
      let order = "global_rank";

      const thesaurus_path = input.thesaurus === "notredame" ? "1.28123.1000" : input.thesaurus_path;

      if (input.thesaurus === "deposant") {
        order = "name";
      }

      const name = input.language === "fr" ? "name" : `name_${input.language}`;
      let query = `SELECT id_thes as id, id::integer as unique_id, thesaurus_path as path, COALESCE(NULLIF(${name}, ''), NULLIF(name_en, ''), name) as name, short_name,
        nb_item::integer, haschild as get_children, nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::integer, global_rank
        FROM ${input.branch}_thesaurus_multi
        WHERE thesaurus = $1`;

      if (input.branch === "conservatoire3d" && input.thesaurus === "deposant") {
        query += " AND nlevel(thesaurus_path) > 1 ";
      }

      // Exclude rows where id_parent = 0 or global_rank = '001'
      query += ` AND global_rank <> '001'`;
      // query += ` AND id_parent <> 0 `;

      query += ` AND thesaurus_path <@ $2::ltree ORDER BY ${order}`;

      const data = await ctx.database.manyOrNone(query, [input.thesaurus, thesaurus_path]);

      return treeMultiOutput.parse(data);
    }),
  getMultiFromName: t.procedure.input(z.object({ branch, thesaurus: z.string() })).query(async ({ ctx, input }) => {
    const data = await ctx.database.any(
      `SELECT id_thes::int, thesaurus FROM ${input.branch}_thesaurus_multi WHERE name = $1`,
      input.thesaurus,
    );
    return z.object({ id_thes: id, thesaurus: z.string() }).array().parse(data);
  }),
  multiByShortName: t.procedure.input(z.object({ branch, thesaurus })).query(async ({ input, ctx }) => {
    return await ctx.database.manyOrNone(
      `SELECT * FROM ${input.branch}_thesaurus_multi tm WHERE tm.thesaurus = $1 ORDER BY tm.short_name`,
      [input.thesaurus],
    );
  }),
  simpleOrigin: t.procedure.input(z.object({ branch, project_id: id })).query(async ({ input, ctx }) => {
    // nouvelle table folder_thesaurus_multi fait le lien entre les thesaurus et un projet
    const query = `SELECT t.id::integer, t.id_thes, t.name, t.short_name, l.label, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer, get_first_metadata( f.id, '${input.branch}', 'folder', 'project') AS project_name 
        FROM ${input.branch}_thesaurus t 
        INNER JOIN ${input.branch}_folder_thesaurus ft ON ft.thesaurus = t.thesaurus AND ft.id_thes = t.id_thes 
        INNER JOIN ${input.branch}_folder f ON f.id = ft.id_folder 
        LEFT OUTER JOIN ${input.branch}_metadata m ON m.list = t.thesaurus AND m.status = 'thesaurus' 
        LEFT OUTER JOIN ${input.branch}_metadata_label l ON l.id_metadata = m.id AND l.language  = 'en' 
        WHERE t.global_rank = '001' AND ft.id_folder = $1 AND order_thes IS NOT NULL 
        ORDER BY order_thes`;

    let data: unknown;

    if (input.branch === "pft3d") {
      if (input.project_id) {
        data = await ctx.database.manyOrNone(query, input.project_id);
      } else {
        data = await ctx.database.manyOrNone(
          `SELECT t.id_thes as id, t.name,t.short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer
                    FROM ${input.branch}_thesaurus_multi t
                    WHERE nlevel(thesaurus_path) = 1`,
        );
      }
    }

    if (input.branch === "conservatoire3d") {
      data = await ctx.database.manyOrNone(
        `SELECT t.id_thes as id, t.name, t.short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer 
          FROM ${input.branch}_thesaurus_multi t 
          WHERE nlevel(thesaurus_path) = 1`,
      );
    }

    if (input.branch === "corpus") {
      if (input.project_id) {
        data = await ctx.database.manyOrNone(query, input.project_id);
      } else {
        data = await ctx.database.manyOrNone(
          `SELECT t.id_thes as id, t.name,t.short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer
                    FROM ${input.branch}_thesaurus_multi t
                    WHERE nlevel(thesaurus_path) = 1`,
        );
      }
    }

    return thesaurus_data.array().parse(data);
  }),
  originFolder: t.procedure
    .input(z.object({ branch, language, folder_id: id }))
    .output(originFolderOutput)
    .query(async ({ input, ctx }) => {
      const name_column = input.language === "fr" ? "name" : `name_${input.language}`;
      const query = `SELECT distinct t.id::integer, t.id_thes::integer, ft.visible, COALESCE(t.${name_column}, t.name_en, t.name) as name, t.short_name,
        CASE WHEN l.label IS NOT NULL THEN l.label ELSE t.short_name END as label, ft.visible, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer,
        get_first_metadata(f.id, '${input.branch}', 'folder', 'project') AS project_name
        FROM ${input.branch}_thesaurus t
        INNER JOIN ${input.branch}_folder_thesaurus ft ON ft.thesaurus = t.thesaurus AND ft.id_thes = t.id_thes
        INNER JOIN ${input.branch}_folder f ON f.id = ft.id_folder
        LEFT OUTER JOIN ${input.branch}_metadata m ON m.list = t.thesaurus AND m.status = 'thesaurus'
        LEFT OUTER JOIN ${input.branch}_metadata_label l ON l.id_metadata = m.id AND l.language  = '${input.language}'
        WHERE t.global_rank = '001' AND ft.id_folder = $1 AND order_thes IS NOT NULL
        AND ft.visible = '1'  `;

      if ((input.branch === "pft3d") || ( input.branch === "corpus") ) {
        return originFolderOutput.parse(await ctx.database.manyOrNone(query, input.folder_id));
      }

      if (input.branch === "conservatoire3d") {
        return originFolderOutput.parse(
          await ctx.database.manyOrNone(
            `SELECT t.id_thes::integer as id, t.name,t.short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer 
            FROM ${input.branch}_thesaurus t 
            WHERE nlevel(thesaurus_path) = 1`,
          ),
        );
      }

      return [];
    }),
  multiOrigin: t.procedure
    .input(z.object({ branch, language, project_id: id.optional() }))
    .output(multiOriginOutput)
    .query(async ({ input, ctx }) => {
      const name_column = input.language === "fr" ? "name" : `name_${input.language}`;
      // nouvelle table folder_thesaurus_multi fait le lien entre les thesaurus et un projet
      // jointure externe maintenant pour récupérer les thesaurus et leurs indexations même s'ils ne font pas partie d'un schéma)
      const query = `SELECT distinct t.id::integer, t.id_thes::integer, ft.visible, COALESCE(t.${name_column}, t.name_en, t.name) as name, t.short_name,CASE WHEN l.label IS NOT NULL THEN l.label ELSE t.short_name END as label, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer ,
      get_first_metadata(f.id, '${input.branch}', 'folder', 'project') AS project_name
     FROM ${input.branch}_thesaurus_multi t
     INNER JOIN ${input.branch}_folder_thesaurus_multi ft ON ft.thesaurus = t.thesaurus AND ft.id_thes = t.id_thes
     INNER JOIN ${input.branch}_folder f ON f.id = ft.id_folder
     LEFT OUTER JOIN ${input.branch}_metadata m ON m.list = t.thesaurus AND m.status = 'multi'
     LEFT OUTER JOIN ${input.branch}_metadata_label l ON l.id_metadata = m.id AND l.language = $2
     WHERE t.global_rank = '001' AND ft.id_folder = $1 AND order_thes IS NOT NULL
     AND ft.visible = '1' `;

      if ((input.branch === "pft3d")||(input.branch === "corpus")) {
        if (input.project_id) {
          const data = await ctx.database.manyOrNone(query, [input.project_id, input.language]);
          return multiOriginOutput.parse(data);
        }
        const data = await ctx.database.manyOrNone(
          `SELECT t.id_thes::integer as id, t.${name_column}, t.short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer 
          FROM ${input.branch}_thesaurus_multi t 
          WHERE nlevel(thesaurus_path) = 1  `,
        );

        return multiOriginOutput.parse(data);
      }

      if (input.branch === "conservatoire3d") {
        const data = await ctx.database.manyOrNone(
          `SELECT t.id_thes::integer as id, t.${name_column}, t.short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer 
          FROM ${input.branch}_thesaurus_multi t 
          WHERE nlevel(thesaurus_path) = 1  `,
        );

        return multiOriginOutput.parse(data);
      }

      let queryCorpus = `SELECT t.id_thes::integer as id, t.${name_column} as name, t.${name_column} as short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer
        FROM ${input.branch}_thesaurus_multi t 
        WHERE nlevel(thesaurus_path) = 1  `;

      const data = await ctx.database.manyOrNone(queryCorpus,
      );

      return multiOriginOutput.parse(data);
    }),
  pactolsOrigin: t.procedure
    .input(z.object({ branch, language, project_id: z.number() }))
    .output(pactolsOriginOutput)
    .query(async ({ input, ctx }) => {
      const name = input.language === "fr" ? "t.name" : `t.name_${input.language}`;

      const query = `SELECT t.id_thes as id, COALESCE(${name}, t.name_en, t.name) as name, COALESCE(${name}, t.name_en, t.name) as short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer 
        FROM ${input.branch}_thesaurus_pactols t 
        WHERE nlevel(thesaurus_path) = 1`;

      if ((input.branch === "pft3d") || (input.branch === "corpus")) {
        const thesPactolsOrig = await ctx.database.manyOrNone(query, input.project_id);
        return pactolsOriginOutput.parse(thesPactolsOrig);
      }

      if (input.branch === "conservatoire3d") {
        const thesPactolsOrig = await ctx.database.manyOrNone(
          `SELECT t.id_thes as id, t.name, t.name as short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer 
          FROM ${input.branch}_thesaurus_pactols t 
          WHERE nlevel(thesaurus_path) = 1`,
        );
        return pactolsOriginOutput.parse(thesPactolsOrig);
      }

      const thesPactolsOrig = await ctx.database.manyOrNone(
        `SELECT t.id_thes as id, t.name, t.name as short_name, t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer 
        FROM ${input.branch}_thesaurus_pactols t 
        WHERE nlevel(thesaurus_path) = 1`,
      );
      return pactolsOriginOutput.parse(thesPactolsOrig);
    }),
  getTreePactols: t.procedure
    .input(
      z.object({
        branch,
        language,
        thesaurus: z.string(),
        thesaurus_path: z.string(),
      }),
    )
    .output(getTreePactolsOutput)
    .query(async ({ ctx, input }) => {
      const name = input.language === "fr" ? "name" : `name_${input.language}`;
      let query = "";
      const query1 = `SELECT id_thes as id, id::integer as unique_id, thesaurus_path as path, 
        COALESCE(NULLIF(${name}, ''), NULLIF(name_en,''), NULLIF(name, '')) as name, nb_item::integer, 
        CASE 
        WHEN haschild = 1 THEN haschild 
        ELSE null 
        END as get_children, nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::integer 
        FROM ${input.branch}_thesaurus_pactols 
        WHERE thesaurus = $1 `;

      const query3 = "AND nlevel(thesaurus_path) > 1 ";
      const query4 = "AND thesaurus_path <@ $2::ltree " + "ORDER BY thesaurus_path ";

      if (input.branch === "conservatoire3d" && input.thesaurus === "deposant") {
        query = query1 + query3 + query4;
      } else {
        query = query1 + query4;
      }

      const thesaurustreePactols = await ctx.database.manyOrNone(query, [input.thesaurus, input.thesaurus_path]);

      return getTreePactolsOutput.parse(thesaurustreePactols);
    }),
  getAllThesaurusForProject: t.procedure
    .input(z.object({ branch, language, project_id: id }))
    .query(async ({ ctx, input }) => {
      // Dans les tables thesaurus, on stocke dans name le nom en français du concept, dans short_name le nom en anglais
      const multi_name = input.language === "fr" ? "name" : `name_${input.language}`;
      const cnd3d_period = input.language === "fr" ? "Période" : "Period of time";
      const cnd3d_pactols = input.language === "fr" ? "Mots-clés Pactols" : "Pactols Keywords";
      //const { lng } = req.params;
      const lng = input.language === "fr" ? "fr" : input.language;
      const name = input.language === "fr" ? "name" : `name_${input.language}`;

      let query = "";
      if (input.branch === "conservatoire3d") {
        query = `SELECT st.thesaurus, COALESCE(t.${multi_name}, t.name_en, t.name) as name, 'multi' as status, l.description
          FROM conservatoire3d_thesaurus_multi t
          INNER JOIN conservatoire3d_search_thesaurus st ON st.thesaurus = t.thesaurus AND st.id_thes = t.id_thes
          LEFT OUTER JOIN conservatoire3d_metadata_label l ON l.label = t.name AND l.language = '${lng}'
          WHERE nlevel(thesaurus_path) = 1 AND st.visible = true AND st.type = 'multi'
          UNION ALL
          SELECT distinct t.thesaurus, COALESCE(t.${name}, t.name_en, t.name) as name, 'thesaurus' as status, l.description
          FROM conservatoire3d_thesaurus t
          INNER JOIN conservatoire3d_search_thesaurus st ON st.thesaurus = t.thesaurus AND st.id_thes = t.id_thes
          LEFT OUTER JOIN conservatoire3d_metadata_label l ON l.label = t.${name} AND l.language = '${lng}'
          WHERE nlevel(thesaurus_path) = 1 AND st.visible = true AND st.type = 'thesaurus'
          UNION ALL
          SELECT 'periodo', '${cnd3d_period}' as name, 'periodo', 'periodo'
          UNION ALL
          SELECT 'pactols', '${cnd3d_pactols}', 'pactols', 'pactols' `;
      } else {
        query = `SELECT distinct fm.thesaurus, COALESCE(m.${multi_name}, m.name_en, m.name) as name, 'multi' as status,
          COALESCE(m.name, m.name_en, m.name) as description
          -- CASE WHEN description IS NOT NULL THEN description ELSE '' END as description
          FROM ${input.branch}_thesaurus_multi m
          INNER JOIN ${input.branch}_folder_thesaurus_multi fm ON fm.thesaurus= m.thesaurus AND fm.id_thes = m.id_thes
          -- LEFT OUTER JOIN ${input.branch}_metadata_label l ON l.label = m.name
          WHERE fm.id_folder = ${input.project_id} AND visible = '1'
          UNION ALL
          SELECT distinct ft.thesaurus, COALESCE(t.${name}, t.name_en, t.name) as name, 'thesaurus' as status ,
          COALESCE(t.${name}, t.name_en, t.name) as description
          -- CASE WHEN description IS NOT NULL THEN description ELSE '' END as description
          FROM ${input.branch}_thesaurus t
          INNER JOIN ${input.branch}_folder_thesaurus ft ON ft.thesaurus= t.thesaurus AND ft.id_thes = t.id_thes
          -- LEFT OUTER JOIN ${input.branch}_metadata_label l ON l.label = t.name
          WHERE ft.id_folder = ${input.project_id} AND visible = '1'
          UNION ALL
          SELECT thesaurus, ${lng}_name as name  ,  'pactols' as status , ${lng}_description  as description
          FROM ${input.branch}_folder_thesaurus_pactols fp
          WHERE id_folder = ${input.project_id} AND thesaurus = 'sujet' AND visible = '1'
          UNION ALL
          SELECT thesaurus, ${lng}_name as name , 'geopactols' , ${lng}_description  as description
          FROM ${input.branch}_folder_thesaurus_pactols fp
          WHERE id_folder = ${input.project_id} AND thesaurus = 'lieux' AND visible = '1'
          `;
      }
      // TODO: A la création d'un nouveau projet, renseigner la table _folder_thesaurus_pactols par défaut
      // TODO: dans le module admin : pouvoir cocher ou décocher les thesaurus pactols (sujet et chrono) pour gérer les mots-clefs
      const thesaurus = await ctx.database.manyOrNone(query);

      return z
        .object({
          thesaurus: z.string(),
          name: z.string().nullable(),
          status: z.string(),
          description: z.string().nullable(),
        })
        .array()
        .parse(thesaurus);
    }),
  exploreThesaurusItemPage: t.procedure
    .input(
      z.object({
        branch,
        thesaurus: z.string(),
        thesaurus_id: id,
        thesaurus_path: z.string().optional(),
        type: z.union([z.literal("multi"), z.literal("simple"), z.literal("pactols")]),
        user_id: z.number().optional(),
        page: z.number(),
        limit: z.number(),
      }),
    )
    .output(exploreThesaurusItemPageOutput)
    .query(async ({ ctx, input }) => {
      let nakala = 0;
      const nakalaObject = 0;
      let codeNom = 0;
      const model = input.branch === "pft3d" ? "project" : "deposit";
      const offset = (input.page - 1) * input.limit;
      let read = "";
      let in_query = "";
      if (input.user_id) {
        read = `SELECT get_access_gen( $1, '${input.branch}') as fold `;
      } else {
        read = `SELECT array_agg(id)  as fold FROM ${input.branch}_folder WHERE  status = 'public' AND $1 = $1 `;
      }

      // Première requete : récupèrer la liste des folders autorisés par le user :
      // TODO : ils doivent être dans le projet !!!
      // récupérer l'id projet avec la table pft3d_folder_thesaurus_multi ??? ou le passer en parametre dans le body
      const userRead = await ctx.database.oneOrNone(read, input.user_id);

      if (userRead.fold.length === 0) in_query = "";
      else {
        if (input.branch === "conservatoire3d") {
          // on ne trie pas les infos, tout est accessible
        } else {
          in_query = ` AND i.id_item IN ( SELECT id FROM ${input.branch}_file WHERE id_folder in ${JSON.stringify(
            userRead.fold,
          )
            .replace("[", "(")
            .replace("]", ")")} ) OR  i.id_item IN ( SELECT id FROM ${
              input.branch
          }_object WHERE id_folder in ${JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")")}  ) )`;
        }
      }
      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await ctx.database.any(
        `SELECT id, code, rank from ${input.branch}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${input.branch}_metadata_model where name = $1) `,
        model,
      );
      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      // pour les item de type folder, l'info Nakala de leur image est présente dans la métadonnées "nakala" du model "deposit"

      // pour les item de type object : l'info Nakala de leur image est présente dans la métadonnées "Nakala" du model "VirtualObject"
      // ou bien, et aussi maintenant dans la table object directement avec le champ id_nakala

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code === "nakala") nakala = startQ[i].id;
      }

      if (input.type === "multi") {
        let where = "";
        if (input.thesaurus === "_" && input.thesaurus_path) {
          where = ` WHERE t.id_thes = ${input.thesaurus_id} `;
        } else {
          where = ` WHERE t.thesaurus_path = '${input.thesaurus_path}' AND t.thesaurus = '${input.thesaurus}' `;
        }
        const query = `SELECT distinct i.item_type, t.short_name, ff.folder_name,
                                          CASE
                                              WHEN array_to_string(pnakala.value, '') IS NOT NULL AND
                                                   i.item_type = 'folder' THEN array_to_string(pnakala.value, '')
                                              WHEN array_to_string(pnakalaObj.value, '') IS NOT NULL AND
                                                   i.item_type = 'object' THEN array_to_string(pnakalaObj.value, '')
                                              WHEN o.id_nakala IS NOT NULL AND i.item_type = 'object' THEN o.id_nakala
                                              WHEN i.item_type = 'file' THEN '' END                   AS nakala,
                                          COALESCE(array_to_string(pnom.value, ''), '')               AS depot_name,
                                          COALESCE(ffo.id::integer, o.id::integer, fi.id::integer, 0) AS id,
                                          COALESCE(ffo.path, fo.path, fi.path, '')                    AS path,
                                          COALESCE(ffo.name, fo.name, fi.name, '')                    AS filename,
                                          COALESCE(ff.id, fo.id_folder, fi.id_folder, o.id_folder, 0) AS idfolder,
                                          COALESCE(fi.file_ext, ffo.file_ext, '')                     AS extension,
                                          o.name                                                      AS object_name,
                                          ff.nb_objects,
                                          COALESCE(ff.doi, o.doi, foo.doi)                            AS doi,
                                          COALESCE(ffo.id, fi.id, fo.id)                              AS id_file,
                                          CASE
                                            WHEN i.item_type = 'folder'
                                            THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                                              WHERE id_user = ${input.user_id} AND id_folder = ff.id
                                              AND id_item = ff.id AND item_type = 'folder')
                                            WHEN i.item_type = 'object'
                                            THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                                              WHERE id_user = ${input.user_id} AND id_folder = o.id_folder
                                              AND id_item = o.id AND item_type = 'object')
                                            WHEN i.item_type = 'file'
                                            THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                                              WHERE id_user = ${input.user_id} AND id_folder = fi.id_folder
                                              AND id_item = fi.id AND item_type = 'file')
                                          END favorite
                                   FROM ${input.branch}_thesaurus_multi t
                                            INNER JOIN ${input.branch}_thesaurus_multi_item i
                                                       ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus
                                            LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                                            LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture
                                            LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                                            LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object'
                                            LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative
                                            LEFT OUTER JOIN ${input.branch}_folder foo ON foo.id = o.id_folder
                                            LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND
                                                                                           pnakala.item_type =
                                                                                           'folder' AND
                                                                                           pnakala.id_metadata = ${nakala}
                                            LEFT OUTER JOIN ${input.branch}_passport pnakalaObj
                                                            ON pnakalaObj.id_item = o.id AND
                                                               pnakalaObj.item_type = 'object' AND
                                                               pnakalaObj.id_metadata = ${nakalaObject}
                                            LEFT OUTER JOIN ${input.branch}_passport pnom
                                                            ON pnom.id_item = i.id_item AND i.item_type = 'folder' AND
                                                               pnom.id_metadata = ${codeNom} ${where}
                                   ORDER BY filename
                                   OFFSET ${offset} LIMIT ${input.limit} `;
        const data = await ctx.database.manyOrNone(query);
        return exploreThesaurusItemPageOutput.parse(data);
      }
      if (input.type === "simple") {
        let where = "";
        where = ` WHERE t.id_thes = ${input.thesaurus_id} AND t.thesaurus = '${input.thesaurus}' `;

        const query = `SELECT i.item_type, t.short_name, ff.folder_name,
                          CASE WHEN array_to_string(pnakala.value, '') IS NOT NULL AND i.item_type = 'folder' THEN array_to_string(pnakala.value, '')
                              WHEN array_to_string(pnakalaObj.value, '') IS NOT NULL AND i.item_type = 'object' THEN array_to_string(pnakalaObj.value, '')
                              WHEN o.id_nakala IS NOT NULL AND i.item_type = 'object' THEN o.id_nakala
                              WHEN i.item_type = 'file' THEN '' END AS nakala,
                          COALESCE(array_to_string(pnom.value, ''), '') AS depot_name,
                          COALESCE(ffo.id::integer, o.id::integer, fi.id::integer, 0) AS id,
                          COALESCE(ffo.path, fo.path, fi.path, '') AS path,
                          COALESCE(ffo.name, fo.name, fi.name, '') AS filename, 
                          COALESCE(ff.id, fo.id_folder, fi.id_folder, o.id_folder, 0) AS idfolder,
                          COALESCE(fi.file_ext, ffo.file_ext, '') AS extension,
                          o.name AS object_name, 
                          ff.nb_objects, 
                          COALESCE(ff.doi, o.doi, foo.doi ) AS doi,
                          COALESCE(ffo.id, fi.id, fo.id, 0) AS id_file,
                          CASE
                              WHEN i.item_type = 'folder'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = ff.id
                              AND id_item = ff.id AND item_type = 'folder')
                              WHEN i.item_type = 'object'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = o.id_folder
                              AND id_item = o.id AND item_type = 'object')
                              WHEN i.item_type = 'file'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = fi.id_folder
                              AND id_item = fi.id AND item_type = 'file')
                          END favorite
                          FROM ${input.branch}_thesaurus t
                          INNER JOIN ${input.branch}_thesaurus_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus
                          LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                          LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture
                          LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                          LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object'
                          LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative
                          LEFT OUTER JOIN ${input.branch}_folder foo ON foo.id = o.id_folder
                          LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.item_type = 'folder' AND pnakala.id_metadata = ${nakala}
                          LEFT OUTER JOIN ${input.branch}_passport pnakalaObj ON pnakalaObj.id_item = o.id AND pnakalaObj.item_type = 'object' AND pnakalaObj.id_metadata = ${nakalaObject}
                          LEFT OUTER JOIN ${input.branch}_passport pnom ON pnom.id_item = i.id_item AND i.item_type = 'folder' AND pnom.id_metadata = ${codeNom}
                          ${where}
                          ORDER BY ff.folder_name
                          OFFSET ${offset} LIMIT ${input.limit} `;
        const data = await ctx.database.manyOrNone(query);

        return exploreThesaurusItemPageOutput.parse(data);
      }
      if (input.type === "pactols") {
        const query = `SELECT i.item_type, t.name AS short_name,  ff.folder_name,
                          CASE WHEN o.id_nakala is not null THEN o.id_nakala
                          ELSE COALESCE(array_to_string(pnakala.value,''), '') END AS nakala,
                          COALESCE(array_to_string(pnom.value, ''), '') AS depot_name,
                          COALESCE(ffo.id::integer, o.id::integer, fi.id::integer, 0) AS id,
                          COALESCE(ffo.path, fo.path, fi.path, '') AS path,
                          COALESCE(ffo.name, fo.name, fi.name, '') AS filename,
                          COALESCE(ff.id, fo.id_folder, fi.id_folder, 0) AS idfolder,
                          COALESCE(fi.file_ext, ffo.file_ext, '') AS extension,
                          o.name AS object_name,
                          ff.nb_objects,
                          COALESCE(ff.doi, o.doi) AS doi,
                          COALESCE(ffo.id, fi.id, fo.id) AS id_file,
                          CASE
                              WHEN i.item_type = 'folder'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = ff.id
                              AND id_item = ff.id AND item_type = 'folder')
                              WHEN i.item_type = 'object'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = o.id_folder
                              AND id_item = o.id AND item_type = 'object')
                              WHEN i.item_type = 'file'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = fi.id_folder
                              AND id_item = fi.id AND item_type = 'file')
                          END favorite
                          FROM ${input.branch}_thesaurus_pactols t
                          INNER JOIN ${input.branch}_thesaurus_pactols_item i ON i.id_thes_thesaurus = t.id_thes
                          LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                          LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture
                          LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                          LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object'
                          LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative
                          LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = ${nakala}
                          LEFT OUTER JOIN ${input.branch}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = ${codeNom}
                          WHERE t.id_thes = ${input.thesaurus_id}
                          ORDER BY ff.folder_name
                          OFFSET ${offset} LIMIT ${input.limit} `;
        const data = await ctx.database.manyOrNone(query);
        return exploreThesaurusItemPageOutput.parse(data);
      }
      if (input.thesaurus === "periodo") {
        const query = `SELECT i.item_type, t.label AS short_name,  ff.folder_name,
                          COALESCE(array_to_string(pnakala.value,''), '') AS nakala,
                          COALESCE(array_to_string(pnom.value, ''), '') AS depot_name,
                          COALESCE(ffo.id::integer, o.id::integer, fi.id::integer, 0) AS id,
                          COALESCE(ffo.path, fo.path, fi.path, '') AS path,
                          COALESCE(ffo.name, fo.name, fi.name, '') AS filename,
                          COALESCE(ff.id, fo.id_folder, fi.id_folder, 0) AS idfolder,
                          COALESCE(fi.file_ext, ffo.file_ext, '') AS extension,
                          o.name AS object_name,
                          ff.nb_objects,
                          COALESCE(ff.doi, o.doi) AS doi, 
                          COALESCE(ffo.id, fi.id, fo.id) AS id_file,
                          CASE
                              WHEN i.item_type = 'folder'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = ff.id
                              AND id_item = ff.id AND item_type = 'folder')
                              WHEN i.item_type = 'object'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = o.id_folder
                              AND id_item = o.id AND item_type = 'object')
                              WHEN i.item_type = 'file'
                              THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item
                              WHERE id_user = ${input.user_id} AND id_folder = fi.id_folder
                              AND id_item = fi.id AND item_type = 'file')
                          END favorite
                          FROM ${input.branch}_thesaurus_periodo t
                          INNER JOIN ${input.branch}_thesaurus_periodo_item i ON i.id_thes_periodo = t.id_periodo
                          LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                          LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture
                          LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                          LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object'
                          LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative
                          LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = ${nakala}
                          LEFT OUTER JOIN ${input.branch}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = ${codeNom}
                          WHERE t.id = ${input.thesaurus_id}
                          ORDER BY ff.folder_name
                          OFFSET ${offset} LIMIT ${input.limit} `;
        const data = await ctx.database.manyOrNone(query);
        return exploreThesaurusItemPageOutput.parse(data);
      }
      //simple ?
      const debut = `SELECT i.item_type,  t.short_name,  ff.folder_name, 
        COALESCE(array_to_string(pnakala.value,''), o.id_nakala,  '') AS nakala, 
        COALESCE(array_to_string(pnom.value, ''), '') AS depot_name, 
        CASE 
        WHEN (fi.id IS NULL) AND (o.id IS NULL) THEN ffo.id 
        WHEN (ff.id IS NULL) AND (o.id IS NULL) THEN fi.id 
        WHEN (ff.id IS NULL) AND (fi.id IS NULL) 
        THEN o.id::integer 
        ELSE 0 
        END as id, 
        CASE 
        WHEN (fi.path IS NULL) AND (fo.path IS NULL) THEN ffo.path 
        WHEN (ffo.path IS NULL) AND (fo.path IS NULL) THEN fi.path 
        WHEN (ffo.path IS NULL) AND (fi.path IS NULL) THEN fo.path 
        ELSE '' 
        END as path, 
        COALESCE(ffo.name, fo.name, fi.name, '') AS filename, 
        CASE 
        WHEN ff.id IS NOT NULL THEN ff.id 
        WHEN fi.id_folder IS NULL THEN fo.id_folder 
        ELSE fi.id_folder 
        END as idfolder, 
        CASE 
        WHEN fi.file_ext IS NOT NULL THEN fi.file_ext 
        WHEN ffo.file_ext IS NOT NULL THEN ffo.file_ext 
        END as extension, o.name as object_name, ff.nb_objects, 
        COALESCE(ff.doi, o.doi) AS doi, 
        COALESCE(ffo.id, fi.id, fo.id) as id_file, 
        CASE 
        WHEN i.item_type = 'folder' THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item WHERE id_user = ${input.user_id} AND id_folder = ff.id AND id_item = ff.id AND item_type = 'folder') 
        WHEN i.item_type = 'object' THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item WHERE id_user = ${input.user_id} AND id_folder = o.id_folder AND id_item = o.id AND item_type = 'object') 
        WHEN i.item_type = 'file' THEN EXISTS(SELECT * FROM ${input.branch}_favorite_item WHERE id_user = ${input.user_id} AND id_folder = fi.id_folder AND id_item = fi.id AND item_type = 'file') 
        END favorite 
        FROM ${input.branch}_thesaurus t 
        INNER JOIN ${input.branch}_thesaurus_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus AND i.thes_path = t.thesaurus_path 
        LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' 
        LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture 
        LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file' 
        LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object' 
        LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative 
        LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 
        LEFT OUTER JOIN ${input.branch}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 `;

      // On ne recupère que l'id thes pour un thesaurus particulier
      // On récupère tous les id_thes enfants de l'id_thes demandé pour tous les thesaurus (pas pris en compte pour le moment)
      const general = " WHERE t.id_thes = $3 ";
      const suite = " AND t.thesaurus = $4 ";
      const milieu = in_query;
      const fin = ` ORDER BY ff.folder_name OFFSET ${offset} LIMIT ${input.limit}`;

      let query = "";

      if (input.thesaurus === "_") {
        // general
        // polyhiérarchie : on prend tous les path d'un id_thes
        // A ce jour, même pour un concept général, on ne va chercher que le terme et non tous ses enfants
        // on ne récupère que les thesaurus concernés par le projet ?
        const query = debut + general + milieu + fin;
        return await ctx.database.manyOrNone(query, [nakala, codeNom, input.thesaurus_id]);
      }
      // un thesaurus en particulier
      query = debut;
      if (input.thesaurus_path) {
        // TODO polyhierarchie
        query += ` WHERE t.thesaurus_path = $3 ${suite} ${milieu} ${fin}`;
        const data = await ctx.database.any(query, [nakala, codeNom, input.thesaurus_path, input.thesaurus]);

        return exploreThesaurusItemPageOutput.parse(data);
      }

      query += suite + milieu + fin;

      const data = await ctx.database.any(query, [nakala, codeNom, input.thesaurus]);
      return exploreThesaurusItemPageOutput.parse(data);
    }),
  exploreThesaurusMultiItemPage: t.procedure
    .input(
      z.object({
        branch,
        language,
        thesaurus: z.string(),
        thesaurus_id: id,
        user_id: z.number(),
        page: z.number(),
        limit: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const model = "project";
      let nakala = 0;
      let codeNom = 0;

      // DONE : Mettre le id_user dans the body et envoyer la requete seulement sur les id_item autorisées par le id_user :
      const offset = (input.page - 1) * input.limit;
      const { limit } = input;

      // Check if user is admin to bypass folder restrictions
      let isAdmin = false;
      if (input.user_id) {
        const userStatus = await ctx.database.oneOrNone(
          `SELECT user_status FROM archeogrid_user WHERE id = $1`,
          input.user_id
        );
        isAdmin = userStatus?.user_status === 'admin';
      }

      // gestion de la polyhierarchie
      const read = input.user_id
        ? `SELECT get_access_gen($1, '${input.branch}') as fold `
        : `SELECT array_agg(id) as fold FROM ${input.branch}_folder WHERE status = 'public' AND $1 = $1 `;

      const name = input.language === "fr" ? "name" : `name_${input.language}`;

      // Première requete : récupèrer la liste des folders autorisés par le user :
      // TODO : ils doivent être dans le projet !!!
      // récupérer l'id projet avec la table pft3d_folder_thesaurus_multi ??? ou le passer en parametre dans le body
      const userRead = await ctx.database.oneOrNone(read, input.user_id);

      let in_query = "";
      // For admin users, skip folder restrictions entirely
      if (!isAdmin && userRead.fold.length) {
        in_query = ` AND (i.id_item IN (SELECT id FROM ${input.branch}_file WHERE id_folder in ${JSON.stringify(
          userRead.fold,
        )
          .replace("[", "(")
          .replace("]", ")")}  ) OR  i.id_item IN ( SELECT id FROM ${
          input.branch
        }_object WHERE id_folder in ${JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")")}  ) )`;
      }

      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await ctx.database.any(
        `SELECT id, code , rank from ${input.branch}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${input.branch}_metadata_model where name = $1) `,
        model,
      );

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code === "nakala") nakala = startQ[i].id;
      }

      // on supprime le lien avec la table _folder_thesaurus_multi (on est sûr de parcourir depuis la bonne page projet
      const debut = `SELECT distinct i.item_type, COALESCE(t.${name}, t.name_en, t.name) as short_name, ff.folder_name,
        COALESCE(array_to_string(pnakala.value,''), '') AS nakala, 
        COALESCE(array_to_string(pnom.value,''), '') AS depot_name, 
        COALESCE(ffo.id, fi.id, o.id, u.id, 0) AS id, 
        COALESCE(ffo.path, fo.path, fi.path, fu.path, '') AS path, 
        COALESCE(ffo.name, fo.name, fi.name, u.name, '') AS filename, 
        COALESCE(ff.id, fo.id_folder, fi.id_folder, fu.id_folder, 0) AS idfolder, 
        COALESCE(ff.id, o.id_folder, fi.id_folder, fu.id_folder, 0) AS idfolder_item, 
        COALESCE(ffo.id, fo.id, fi.id, fu.id, 0) AS idfile, 
        COALESCE(fi.file_ext, ffo.file_ext, fu.file_ext, '') AS extension, 
        COALESCE(u.x, 0) AS x, 
        COALESCE(u.y, 0) AS y, 
        COALESCE(u.width, 0) AS width, 
        COALESCE(u.height, 0) AS height, u.type, u.polygon, o.name as object_name, ff.nb_objects,
        COALESCE(ffi.file_passport, foo.folder_passport, ff.folder_passport, '0') AS model
        FROM ${input.branch}_thesaurus_multi t 
        INNER JOIN ${input.branch}_thesaurus_multi_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus 
        LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file' 
        LEFT OUTER JOIN ${input.branch}_folder ffi ON ffi.id = fi.id_folder
        LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' 
        LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture
        LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object' 
        LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative
        LEFT OUTER JOIN ${input.branch}_folder foo ON o.id_folder = foo.id
        LEFT OUTER JOIN ${input.branch}_unico u ON u.id = i.id_item AND i.item_type = 'unico' 
        LEFT OUTER JOIN ${input.branch}_file fu ON fu.id = u.id_file
        LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 
        LEFT OUTER JOIN ${input.branch}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 `;

      // On ne recupère que l'id thes pour un thesaurus particulier
      // TODO polyhierarchie
      const particulier = " WHERE t.id_thes = $3 ";
      // On récupère tous les id_thes enfants de l'id_thes demandé pour tous les thesaurus (pas pris en compte pour le moment)
      const general = "WHERE t.id_thes = $3";
      const suite = " AND t.thesaurus = $4 ";
      const milieu = in_query;
      const fin = ` ORDER BY filename OFFSET ${offset} LIMIT ${limit}`;

      if (input.thesaurus === "_") {
        // general
        // polyhiérarchie : on prend tous les path d'un id_thes
        // A ce jour, même pour un concept général, on ne va chercher que le terme et non tous ses enfants
        // on ne récupère que les thesaurus concernés par le projet ?
        const query = debut + general + milieu + fin;
        return await ctx.database.manyOrNone(query, [nakala, codeNom, input.thesaurus_id]);
      }

      // un thesaurus en particulier
      const query = debut + particulier + suite + milieu + fin;
      return await ctx.database.manyOrNone(query, [nakala, codeNom, input.thesaurus_id, input.thesaurus]);
    }),
  exploreThesaurusItemNB: t.procedure
    .input(z.object({ branch, language, thesaurus: z.string(), thesaurus_id: id }))
    .output(itemNbOutput.nullable())
    .query(async ({ input, ctx }) => {
      // 1 on récupère tous les folders concernés et le nombre d'item
      // TODO : completer la requete pour les items de type folder ou objets
      // TODO : Ajouter la notion de cloisonnement par projet !
      const query = `SELECT json_agg(x) as folders_info 
        FROM 
        (SELECT id_folder, count(*) as nb_file, 0 as nb_object, 0 as nb_unico 
          FROM ${input.branch}_file 
          WHERE id IN 
          (SELECT id_item 
            FROM ${input.branch}_thesaurus_item 
            WHERE thesaurus = $1 AND item_type = 'file' AND id_thes_thesaurus = $2) 
          GROUP BY id_folder 
          UNION ALL 
          SELECT id_folder, 0 as nb_file, count(*) as nb_object, 0 as nb_unico 
          FROM ${input.branch}_object 
          WHERE id IN 
          (SELECT id_item 
            FROM ${input.branch}_thesaurus_item 
            WHERE thesaurus = $1 AND item_type = 'object' AND id_thes_thesaurus = $2) 
          GROUP BY id_folder 
          UNION ALL 
          SELECT id_folder, 0 as nb_file, 0 as nb_object, count(*) as nb_unico 
          FROM ${input.branch}_unico u 
          INNER JOIN ${input.branch}_file f ON f.id = u.id_file 
          WHERE u.id IN 
          (SELECT id_item 
            FROM ${input.branch}_thesaurus_item 
            WHERE thesaurus = $1 AND item_type = 'unico' AND id_thes_thesaurus = $2) 
          GROUP BY id_folder) AS x`;

      const thesaurus_item_folders_count = await ctx.database.oneOrNone(query, [input.thesaurus, input.thesaurus_id]);

      if (!thesaurus_item_folders_count) return null;

      // 2 on récupère le nom complet du concept en jeu
      // REVISION de la requete pour traiter la polyhierarchie : on interroge non pas sur le id_thes mais avec l'id
      const thesaurus_data = await ctx.database.oneOrNone(
        `SELECT nb_item::integer, nb_item::integer as total_count, ${input.branch}_get_real_thes_path_path_id_${input.language}(thesaurus_path::TEXT, $1) as thesaurus_name ` +
          `FROM ${input.branch}_thesaurus WHERE thesaurus =  $1 AND id_thes = $2 `,
        [input.thesaurus, input.thesaurus_id],
      );

      if (!thesaurus_data) return null;

      return itemNbOutput.parse({
        ...thesaurus_data,
        ...thesaurus_item_folders_count,
      });
    }),
  exploreThesaurusMultiItemNB: t.procedure
    .input(
      z.object({
        branch,
        language,
        thesaurus: z.string(),
        thesaurus_id: id,
      }),
    )
    .output(thesaurusNbOutput.nullable())
    .query(async ({ input, ctx }) => {
      // 1 on récupère tous les folders concernés et le nombre d'item
      // DONE : completer la requete pour les items de type folder ou objets
      // On revient au tag par id (et non pas par thes_path moins cohérent)
      const query = `SELECT json_agg(x) as folders_info 
        FROM 
        (SELECT id_folder, count(*) as nb_file, 0 as nb_object, 0 as nb_unico 
          FROM ${input.branch}_file 
          WHERE id IN 
          (SELECT id_item 
            FROM ${input.branch}_thesaurus_multi_item 
            WHERE thesaurus = $1 AND item_type = 'file' AND id_thes_thesaurus = $2) 
        GROUP BY id_folder 
        UNION ALL 
        SELECT id_folder, 0 as nb_file, count(*) as nb_object, 0 as nb_unico 
        FROM ${input.branch}_object 
        WHERE id IN 
        (SELECT id_item 
          FROM ${input.branch}_thesaurus_multi_item 
          WHERE thesaurus = $1 AND item_type = 'object' AND id_thes_thesaurus = $2) 
        GROUP BY id_folder 
        UNION ALL 
        SELECT id_folder, 0 as nb_file, 0 as nb_object, count(*) as nb_unico 
        FROM ${input.branch}_unico u 
        INNER JOIN ${input.branch}_file f ON f.id = u.id_file 
        WHERE u.id IN 
        (SELECT id_item 
          FROM ${input.branch}_thesaurus_multi_item 
          WHERE thesaurus = $1 AND item_type = 'unico' AND id_thes_thesaurus = $2) 
        GROUP BY id_folder) AS x`;

      const thesaurus_item_folders_count = await ctx.database.oneOrNone(query, [input.thesaurus, input.thesaurus_id]);

      const name = input.language === "fr" ? "name" : `name_${input.language}`;

      // 2 on récupère le nom complet du concept en jeu et le total count
      // REVISION de la requete pour traiter la polyhierarchie : on interroge non pas sur le id_thes mais avec l'id
      // Nouvelle revision - 06/12/2022 on regarde le id_thes (dans le path)
      // on ne prend que le premier item, tous sont sensés avoir le même name
      const data = await ctx.database.oneOrNone(
        `SELECT COALESCE(${name}, name_en, name) as thesaurus_name, nb_item::integer as total_count 
        FROM ${input.branch}_thesaurus_multi 
        WHERE thesaurus = $1 AND id_thes = $2 limit 1`,
        [input.thesaurus, input.thesaurus_id],
      );

      if (!data) return null;

      const { thesaurus_name, total_count } = data;

      return thesaurusNbOutput.parse({
        thesaurus_name,
        total_count,
        ...thesaurus_item_folders_count,
      });
    }),
  exploreThesaurusPactolsItemPage: t.procedure
    .input(
      z.object({
        branch,
        thesaurus: z.string(),
        thesaurus_id: id,
        thesaurus_path: z.string(),
        user_id: z.number().optional(),
        page: z.number(),
        limit: z.number(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const model = "project";
      let nakala = 0;
      let codeNom = 0;
      // DONE : Mettre le id_user dans le body et envoyer la requete seulement sur les id_item autorisées par le id_user :
      const offset = (input.page - 1) * input.limit;

      let read = "";
      let in_query = "";
      if (input.user_id) {
        read = `SELECT get_access_gen( $1, '${input.branch}') as fold`;
      } else {
        read = `SELECT array_agg(id) as fold FROM ${input.branch}_folder WHERE  status = 'public'  AND $1 = $1 `;
      }

      // Première requete : récupèrer la liste des folders autorisés par le user :
      // TODO : ils doivent être dans le projet !!!
      // récupérer l'id projet avec la table pft3d_folder_thesaurus_multi ??? ou le passer en parametre dans le body
      const userRead = await ctx.database.oneOrNone(read, input.user_id);

      if (userRead.fold.length === 0) in_query = "";
      else
        in_query = ` AND i.id_item IN 
      (SELECT id::integer 
        FROM ${input.branch}_file 
        WHERE id_folder in ${JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")")} 
        UNION ALL
        SELECT id::integer 
        FROM ${input.branch}_object 
        WHERE id_folder in ${JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")")} 
        UNION ALL 
        SELECT id::integer 
        FROM ${input.branch}_unico 
        WHERE id_file in 
        (SELECT id 
          FROM ${input.branch}_file 
          WHERE id_folder in ${JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")")}))`;

      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await ctx.database.any(
        `SELECT id::integer, code, rank from ${input.branch}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${input.branch}_metadata_model where name = $1) `,
        model,
      );

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code === "nakala") nakala = startQ[i].id;
      }

      const debut = `SELECT i.item_type, t.name, ff.folder_name, 
      COALESCE(array_to_string(pnakala.value,''), '') AS nakala, 
      COALESCE(array_to_string(pnom.value,''), '') AS depot_name, 
      COALESCE(ffo.id::integer, fi.id::integer, o.id::integer, u.id::integer, 0) AS id, 
      COALESCE(ffo.path, fo.path, fi.path, fu.path, '') AS path, 
      COALESCE(ffo.name, fo.name, fi.name, u.name, '') AS filename, 
      COALESCE(ff.id, fo.id_folder, fi.id_folder, fu.id_folder, o.id_folder, 0) AS idfolder, 
      COALESCE(ffo.id::integer, fo.id::integer, fi.id::integer, fu.id::integer, 0) AS idfile, 
      COALESCE(fi.file_ext, ffo.file_ext, fu.file_ext, '') AS extension, o.name as object_name, ff.nb_objects, u.x, u.y, u.width, u.height, u.type, u.polygon 
      FROM ${input.branch}_thesaurus_pactols t 
      INNER JOIN ${input.branch}_thesaurus_pactols_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus 
      LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file' 
      LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' 
      LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture 
      LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object' 
      LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative 
      LEFT OUTER JOIN ${input.branch}_unico u ON u.id = i.id_item AND i.item_type = 'unico' 
      LEFT OUTER JOIN ${input.branch}_file fu ON fu.id = u.id_file 
      LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 
      LEFT OUTER JOIN ${input.branch}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 `;

      // On ne recupère que l'id thes pour un thesaurus particulier
      // TODO polyhierarchie
      const particulier = " WHERE t.thesaurus_path = $3 ";
      // On récupère tous les id_thes enfants de l'id_thes demandé pour tous les thesaurus (pas pris en compte pour le moment)
      const general = "WHERE t.id_thes = $3";

      const suite = " AND t.thesaurus = $4 ";
      const milieu = in_query;

      const fin = ` ORDER BY ff.folder_name OFFSET ${offset} LIMIT ${input.limit}`;

      let query = "";

      if (input.thesaurus === "_") {
        // general
        // polyhiérarchie : on prend tous les path d'un id_thes
        //query = debut + general + milieu + fin // A ce jour, même pour un concept général, on ne va chercher que le terme et non tous ses enfants
        query = debut + general + milieu + fin;
        const data = await ctx.database.manyOrNone(query, [nakala, codeNom, id]);
        return data;
      }

      // un thesaurus en particulier
      query = debut + particulier + suite + milieu + fin;
      const data = await ctx.database.manyOrNone(query, [nakala, codeNom, input.thesaurus_path, input.thesaurus]);

      return data;
    }),
  exploreThesaurusSimpleItemPage: t.procedure
    .input(
      z.object({
        branch,
        language,
        thesaurus: z.string(),
        thesaurus_id: id,
        user_id: z.number(),
        page: z.number(),
        limit: z.number(),
        projectId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const model = "project";
      let nakala = 0;
      let codeNom = 0;

      // DONE : Mettre le id_user dans le body et envoyer la requete seulement sur les id_item autorisées par le id_user :
      const offset = (input.page - 1) * input.limit;
      const { limit } = input;

      // Check if user is admin to bypass folder restrictions
      let isAdmin = false;
      if (input.user_id) {
        const userStatus = await ctx.database.oneOrNone(
          `SELECT user_status FROM archeogrid_user WHERE id = $1`,
          input.user_id
        );
        isAdmin = userStatus?.user_status === 'admin';
      }

      // gestion de la polyhierarchie
      const read = input.user_id
        ? `SELECT get_access_gen($1, '${input.branch}') as fold `
        : `SELECT array_agg(id) as fold FROM ${input.branch}_folder WHERE status = 'public' AND $1 = $1 `;

      const name = input.language === "fr" ? "name" : `name_${input.language}`;

      // Première requete : récupèrer la liste des folders autorisés par le user :
      // TODO : ils doivent être dans le projet !!!
      // récupérer l'id projet avec la table pft3d_folder_thesaurus ??? ou le passer en parametre dans le body
      const userRead = await ctx.database.oneOrNone(read, input.user_id);

      let in_query = "";
      // For admin users, skip folder restrictions entirely
      if (!isAdmin && userRead.fold.length) {
        in_query = ` AND (i.id_item IN (SELECT id FROM ${input.branch}_file WHERE id_folder in ${JSON.stringify(
          userRead.fold,
        )
          .replace("[", "(")
          .replace("]", ")")}  ) OR  i.id_item IN ( SELECT id FROM ${
          input.branch
        }_object WHERE id_folder in ${JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")")}  ) )`;
      }

      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await ctx.database.any(
        `SELECT id, code , rank from ${input.branch}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${input.branch}_metadata_model where name = $1) `,
        model,
      );

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code === "nakala") nakala = startQ[i].id;
      }

      const debut = `SELECT i.item_type, COALESCE(t.${name}, t.name_en, t.name) as short_name, ff.folder_name, 
        COALESCE(array_to_string(pnakala.value,''), '') AS nakala, 
        COALESCE(array_to_string(pnom.value,''), '') AS depot_name, 
        COALESCE(ffo.id, fi.id, o.id, u.id, 0) AS id, 
        COALESCE(ffo.path, fo.path, fi.path, fu.path, '') AS path, 
        COALESCE(ffo.name, fo.name, fi.name, u.name, '') AS filename, 
        COALESCE(ff.id, fo.id_folder, fi.id_folder, fu.id_folder, 0) AS idfolder, 
        COALESCE(ff.id, o.id_folder, fi.id_folder, fu.id_folder, 0) AS idfolder_item, 
        COALESCE(ffo.id, fo.id, fi.id, fu.id, 0) AS idfile, 
        COALESCE(fi.file_ext, ffo.file_ext, fu.file_ext, '') AS extension, 
        COALESCE(u.x, 0) AS x, 
        COALESCE(u.y, 0) AS y, 
        COALESCE(u.width, 0) AS width, 
        COALESCE(u.height, 0) AS height, u.type, u.polygon, o.name as object_name, ff.nb_objects 
        FROM ${input.branch}_thesaurus t 
        INNER JOIN ${input.branch}_thesaurus_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus 
        INNER JOIN ${input.branch}_folder_thesaurus fm ON fm.thesaurus = t.thesaurus AND fm.id_folder = ${input.projectId}::INT 
        LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = i.id_item AND i.item_type = 'file' 
        LEFT OUTER JOIN ${input.branch}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' 
        LEFT OUTER JOIN ${input.branch}_file ffo ON ffo.id = ff.id_representative_picture 
        LEFT OUTER JOIN ${input.branch}_object o ON o.id = i.id_item AND i.item_type = 'object' 
        LEFT OUTER JOIN ${input.branch}_file fo ON fo.id = o.id_file_representative 
        LEFT OUTER JOIN ${input.branch}_unico u ON u.id = i.id_item AND i.item_type = 'unico' 
        LEFT OUTER JOIN ${input.branch}_file fu ON fu.id = u.id_file 
        LEFT OUTER JOIN ${input.branch}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 
        LEFT OUTER JOIN ${input.branch}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 `;

      // On ne recupère que l'id thes pour un thesaurus particulier
      // TODO polyhierarchie
      const particulier = " WHERE t.id_thes = $3 ";
      // On récupère tous les id_thes enfants de l'id_thes demandé pour tous les thesaurus (pas pris en compte pour le moment)
      const general = "WHERE t.id_thes = $3";
      const suite = " AND t.thesaurus = $4 ";
      const milieu = in_query;
      const fin = ` ORDER BY ff.folder_name OFFSET ${offset} LIMIT ${limit}`;

      if (input.thesaurus === "_") {
        // general
        // polyhiérarchie : on prend tous les path d'un id_thes
        // A ce jour, même pour un concept général, on ne va chercher que le terme et non tous ses enfants
        // on ne récupère que les thesaurus concernés par le projet ?
        const query = debut + general + milieu + fin;
        return await ctx.database.manyOrNone(query, [nakala, codeNom, input.thesaurus_id]);
      }

      // un thesaurus en particulier
      const query = debut + particulier + suite + milieu + fin;
      return await ctx.database.manyOrNone(query, [nakala, codeNom, input.thesaurus_id, input.thesaurus]);
    }),
  linkProject: t.procedure
    .input(
      z.object({
        branch,
        type: z.literal("simple").or(z.literal("multi")),
        project_id: id,
        thesaurus: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (input.type === "simple") {
        const exists = await ctx.database.manyOrNone(
          `SELECT * FROM ${input.branch}_folder_thesaurus WHERE id_folder = $1 AND thesaurus = $2`,
          [input.project_id, input.thesaurus],
        );
        if (exists.length) return;

        const { max_order } = await ctx.database.one(
          `SELECT max(order_thes) as max_order FROM ${input.branch}_folder_thesaurus WHERE id_folder = $1`,
          input.project_id,
        );
        const { first_value } = await ctx.database.one(
          `SELECT min(id_thes) as first_value FROM ${input.branch}_thesaurus WHERE thesaurus = $1`,
          input.thesaurus,
        );
        await ctx.database.none(
          `INSERT INTO ${input.branch}_folder_thesaurus (id_folder, id_thes, thesaurus, order_thes, visible) 
          VALUES ($1, $2, $3, $4, 1)`,
          [input.project_id, first_value, input.thesaurus, max_order + 1],
        );
      } else {
        const exists = await ctx.database.manyOrNone(
          `SELECT * FROM ${input.branch}_folder_thesaurus_multi WHERE id_folder = $1 AND thesaurus = $2`,
          [input.project_id, input.thesaurus],
        );
        if (exists.length) return;

        const { max_order } = await ctx.database.one(
          `SELECT max(order_thes) as max_order FROM ${input.branch}_folder_thesaurus_multi WHERE id_folder = $1`,
          input.project_id,
        );
        const { first_value } = await ctx.database.one(
          `SELECT min(id_thes) as first_value FROM ${input.branch}_thesaurus_multi WHERE thesaurus = $1`,
          input.thesaurus,
        );
        await ctx.database.none(
          `INSERT INTO ${input.branch}_folder_thesaurus_multi (id_folder, id_thes, thesaurus, order_thes, visible) 
          VALUES ($1, $2, $3, $4, 1)`,
          [input.project_id, first_value, input.thesaurus, max_order + 1],
        );
      }
    }),
  unlinkProject: t.procedure
    .input(
      z.object({
        branch,
        type: z.literal("simple").or(z.literal("multi")),
        project_id: id,
        thesaurus: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (input.type === "simple") {
        await ctx.database.none(
          `DELETE FROM ${input.branch}_folder_thesaurus 
          WHERE id_folder = $1 AND thesaurus = $2`,
          [input.project_id, input.thesaurus],
        );
      } else {
        await ctx.database.none(
          `DELETE FROM ${input.branch}_folder_thesaurus_multi 
          WHERE id_folder = $1 AND thesaurus = $2`,
          [input.project_id, input.thesaurus],
        );
      }
    }),

    thesaurusPeriodO: t.procedure
    .input(z.object({branch: z.string()}))
    .output(periodO.array())
    .query(async ({input, ctx}) => {
      const result = await ctx.database.manyOrNone(
        `SELECT id, language, id_periodo, label, localized_labels, start_date, stop_date, spatial_coverage_desc  
         FROM ${input.branch}_thesaurus_periodo  
         order by language, start_date::int `)

      return periodO.array().parse(result)
    }),

    thesaurusGeonames: t.procedure
    .input(z.object({branch: z.string()}))
    .output(z.object({id, id_geonames: id, label: z.string()}).array())
    .query(async ({input, ctx}) => {
      const result = await ctx.database.many(
        `SELECT id::int, id_thes::int as id_geonames, name as label 
         FROM ${input.branch}_thesaurus_multi
         WHERE thesaurus = 'geo'
         AND nlevel(thesaurus_path) > 1
         order by label
         `)

      return z.object({id, id_geonames: id, label: z.string()}).array().parse(result)
    }),

    getThesaurusPactolsCollection: t.procedure
    .input(z.object({branch: z.string(), collection: z.string().nullable()}))
    .output(z.object({id, id_thes: id, name_fr: z.string(), name_en: z.string()}).array())
    .query(async ({input, ctx}) => {
      let filter = '';
      // collection peut maintenant être égale à "sujet" auquel cas, on récupère toute la table
      // A ce jour, toute la table des pactols ne contient que le thesaurus "sujet" et non le thesaurus "geo"
      if (input.collection)
          filter = (input.collection === 'sujet') ? '' : ' WHERE collection = \''+input.collection+'\' ';

      const result = await ctx.database.manyOrNone(
        `SELECT id::int, id_thes, name as name_fr, name_en 
         FROM ${input.branch}_thesaurus_pactols
         ${filter}
         ORDER BY name
         `)

      return z.object({id, id_thes: id, name_fr: z.string(), name_en: z.string()}).array().parse(result)
    }),

    getThesaurusPactolsCollectionNames: t.procedure
    .input(z.object({branch: z.string()}))
    .output(z.object({name: z.string(), code: z.string(), thesaurus_path: z.string()}).array())
    .query(async ({input, ctx}) => {
      const result = await ctx.database.manyOrNone(
        `SELECT name, collection as code, thesaurus_path
         FROM ${input.branch}_thesaurus_pactols
         WHERE nlevel(thesaurus_path) = 1 AND collection IS NOT NULL
         ORDER BY collection
         `)
        
      return z.object({name: z.string(), code: z.string(), thesaurus_path: z.string()}).array().parse(result)
    }),

    getThesaurusPactolsByName : t.procedure
    .input(z.object({branch: z.string(), name: z.string()}))
    .output(z.object({id, id_thes: id, thesaurus: z.string(), name_fr: z.string(), name_en: z.string(), identifier: z.string(), collection: z.string()}).nullable())
    .query(async ({input, ctx}) => {
      const result = await ctx.database.oneOrNone(
        `SELECT id::int, id_thes, thesaurus, name as name_fr, name_en, identifier, collection
         FROM ${input.branch}_thesaurus_pactols
         WHERE LOWER(name) = LOWER('${input.name}')
         OR LOWER(name_en) = LOWER('${input.name}')
         `)
        
      return z.object({id, id_thes: id, thesaurus: z.string(), name_fr: z.string(), name_en: z.string(), identifier: z.string(), collection: z.string()}).nullable().parse(result)
    }),

    addThesaurusPactolsItem: t.procedure
    .input(z.object({
      branch, 
      id_pactols: z.string(), 
      thesaurus: z.string(), 
      id_item: id, 
      item_type: z.string(), 
      id_user: id, 
      collection: z.string().optional(),
      qualifier: z.string().optional().nullable(),
      id_metadata: z.string().optional().nullable()
    }))
    .mutation(async ({input, ctx}) => {
      const [id_thes, id_thes_thesaurus] = input.id_pactols.split("_");
      
      const exists = await ctx.database.oneOrNone(`
        SELECT id_thesaurus
        FROM ${input.branch}_thesaurus_pactols_item
        WHERE id_thes_thesaurus = ${id_thes_thesaurus}
        AND id_thesaurus = ${id_thes}
        AND thesaurus = '${input.thesaurus}'
        AND id_item = ${input.id_item}
        AND item_type='${input.item_type}' 
        LIMIT 1`
      )

      if (!exists) {
        await ctx.thesaurus.insertThesaurusItem(
          input.branch,
          'pactols',
          Number.parseInt(id_thes),
          Number.parseInt(id_thes_thesaurus),
          input.thesaurus,
          input.id_user,
          {
            id_item: input.id_item,
            item_type: input.item_type,
            qualifier: input.qualifier,
            id_metadata: (id => isNaN(id) ? null : id)(parseInt(input.id_metadata ?? "")),
            collection: input.collection
          }
        )
      }
      return true;
    }),

    createTag: t.procedure
    .input(z.object({branch, tag_name: z.string()}))
    .output(id)
    .mutation(async ({input, ctx}) => {
      const exists = await ctx.database.oneOrNone(`
        SELECT id
        FROM ${input.branch}_tag
        WHERE name = $1
        LIMIT 1`,
        [input.tag_name]
      )

      if (exists) return id.parse(exists.id)
      
      const tag = await ctx.database.one(
        `INSERT INTO ${input.branch}_tag(name)
        VALUES ($1)
        RETURNING id`,
        [input.tag_name]
      )

      return id.parse(tag.id)
    }),

    createTagMetadataLink : t.procedure
    .input(z.object({branch, id_tag: id, id_item: id, item_type: z.string(), id_metadata: id}))
    .mutation(async ({input, ctx}) => {
      const exists = await ctx.database.oneOrNone(`
        SELECT id_tag
        FROM ${input.branch}_item_tag
        WHERE id_tag=${input.id_tag} 
        AND id_item=${input.id_item}
        AND item_type='${input.item_type}'
        AND id_metadata=${input.id_metadata}
        LIMIT 1`
      )

      if (!exists) {
        await ctx.database.none(
          `INSERT INTO ${input.branch}_item_tag(id_tag, id_item, item_type, date_tagged, id_metadata)
          VALUES ($1, $2, $3, now(), $4)`,
          [input.id_tag, input.id_item, input.item_type, input.id_metadata]
        )
      }
    }),

    deteteTagMetadataLink : t.procedure
    .input(z.object({branch, id_tag: id, id_item: id, item_type: z.string(), id_metadata: id}))
    .mutation(async ({input, ctx}) => {
      await ctx.database.none(
        `DELETE FROM ${input.branch}_item_tag
        WHERE id_tag=$1
        AND id_item=$2
        AND item_type=$3
        AND id_metadata=$4`,
        [input.id_tag, input.id_item, input.item_type, input.id_metadata]
      );
    }),

    getIdThesaurusByName: t.procedure
    .input(z.object({ branch, name: z.string(), thesaurus: z.string(), thesaurus_type: z.enum(["single", "multi"]) }))
    .query(async ({ input, ctx }) => {
        // cas des chaînes de caractères avec des '
        const name = input.name.replace(/''/g, "'")
        const result = await ctx.database.oneOrNone(
        `SELECT id::int, id_thes::int
        FROM ${input.branch}_thesaurus${input.thesaurus_type === "multi" ? "_multi" : ""}
        WHERE thesaurus = $1
        AND LOWER(name) = LOWER($2)
        LIMIT 1`, [input.thesaurus, name]
      );
      return z.object({ id, id_thes: z.number() }).nullable().parse(result);
    }),

    getDeposantPrincipal: t.procedure
    .input(z.object({ branch, type: z.enum(["single", "multi"]), id_folder: z.number() }))
    .output(z.object({ id_user: z.number() }).nullable())
    .query(async ({ input, ctx }) => {
      const result = await ctx.database.oneOrNone(
        `SELECT id_user
        FROM ${input.branch}_thesaurus${input.type === "multi" ? "_multi" : ""}_item
        WHERE id_item = ${input.id_folder}
        AND thesaurus = 'deposant'
        AND qualifier = 'deposant_principal'`
      );
      return z.object({ id_user: z.number() }).nullable().parse(result);
    }),

    /**
     * Create a new entry in the geonames thesaurus with the dedicated API
     * If the country is not found in the geonames thesaurus, it will be created
     * It return the name, id, id_thes and latlng of the created location (not the country)
     */
    createGeonamesWithAPI: t.procedure
    .input(z.object({ branch, id_geonames: z.string() }))
    .output(z.object({ name: z.string(),id: z.number(), id_thes: z.number(), latlng: z.string()}))
    .mutation(async ({ input, ctx }) => {
      const exists = await ctx.database.oneOrNone(
        `SELECT name, id::integer, id_thes::integer, latlng
        FROM ${input.branch}_thesaurus_multi
        WHERE id_thes = ${input.id_geonames}
        AND thesaurus = 'geo'
        LIMIT 1`
      );

      if(exists){
        return z.object({ name: z.string(),id: z.number(), id_thes: z.number(), latlng: z.string() }).parse(exists);
      }

      let identifierPrefix = "https://sws.geonames.org/";
      let options = {
        //host: 'api.geonames.org',
        host: "secure.geonames.org",
        path: `/getJSON?geonameId=${input.id_geonames}&username=archeovision`,
        method: "POST",
      };

      const location_info: any = await httpsRequestPromise(options);
      const countryId = location_info.countryId;

      if(!countryId){
        throw new Error(`Unable to retrieved countryId from geonames API for id ${input.id_geonames}`);
      }

      const country_geonames = await ctx.database.oneOrNone(`
        SELECT *
        FROM ${input.branch}_thesaurus_multi
        WHERE thesaurus = 'geo'
        AND id_thes = ${countryId}
        LIMIT 1`)
        
      let country_rank;
      if(!country_geonames) {
        let options = {
          //host: 'api.geonames.org',
          host: "secure.geonames.org",
          path: `/getJSON?geonameId=${countryId}&username=archeovision`,
          method: "POST",
        };
        const country_info: any = await httpsRequestPromise(options);
        const {max_rank} = await ctx.database.one(`
          SELECT max(rank) as max_rank
          FROM ${input.branch}_thesaurus_multi 
          WHERE nlevel(thesaurus_path) = 2
          AND thesaurus = 'geo'`);

        country_rank = max_rank + 1;

        const name_fr_country = country_info.alternateNames.find((alt: {lang: string}) => alt.lang == "fr")?.name || country_info.name;
        const name_en_country = country_info.alternateNames.find((alt: {lang: string}) => alt.lang == "en")?.name || name_fr_country;
        
          await ctx.database.none(`
          INSERT INTO ${input.branch}_thesaurus_multi(thesaurus, id_thes, latlng, name, name_en, short_name, nb_item, thesaurus_path, id_parent, rank, global_rank, nb_tot_item, identifier, haschild)
          VALUES ('geo', $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)`,
          [
            countryId,
            `${country_info.lat},${country_info.lng}`,
            name_fr_country,
            name_en_country,
            name_fr_country,
            0,
            `1.${countryId}`,
            1,
            country_rank,
            `001.${country_rank.toString().padStart(3, '0')}`,
            0,
            identifierPrefix + countryId,
            1
          ]
        );
      }else{
        country_rank = country_geonames.rank;
      }

      const {max_rank_location} = await ctx.database.one(`
        SELECT MAX(rank) as max_rank_location
        FROM conservatoire3d_thesaurus_multi 
        WHERE thesaurus='geo' 
        AND NLEVEL(thesaurus_path) = 3
      `);

      const name_fr_location = location_info.alternateNames.find((alt: {lang: string}) => alt.lang == "fr")?.name || location_info.name;
      const name_en_location = location_info.alternateNames.find((alt: {lang: string}) => alt.lang == "en")?.name || name_fr_location;

      // TODO INSERT LOCATION  IN THESO GEO
      const new_geo = await ctx.database.one(`
        INSERT INTO ${input.branch}_thesaurus_multi(thesaurus, id_thes, latlng, name, name_en, short_name, nb_item, thesaurus_path, id_parent, rank, global_rank, nb_tot_item, identifier, haschild)
        VALUES ('geo', $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING name, id::integer, id_thes::integer, latlng`,
        [
          input.id_geonames,
          `${location_info.lat},${location_info.lng}`,
          name_fr_location,
          name_en_location,
          name_fr_location,
          0,
          `1.${countryId}.${input.id_geonames}`,
          countryId,
          max_rank_location + 1,
          `001.${country_rank.toString().padStart(3, '0')}.${(max_rank_location + 1).toString().padStart(3, '0')}`,
          0,
          identifierPrefix + input.id_geonames,
          1
        ]
      ); 

      return z.object({name: z.string(), id: z.number(), id_thes: z.number(), latlng: z.string() }).parse(new_geo);
    }),

    getThesaurusPactolsWithItemAndCollection: t.procedure
    .input(z.object({branch, id_item: z.number(), item_type: z.string(), collection: z.string().nullable()}))
    .query(async ({input, ctx}) => {
      const result = await ctx.database.manyOrNone(`
        SELECT tp.*
        FROM ${input.branch}_thesaurus_pactols tp
        INNER JOIN ${input.branch}_thesaurus_pactols_item tpi ON tpi.id_thesaurus = tp.id
        WHERE tpi.id_item = ${input.id_item}
        AND tpi.item_type = '${input.item_type}'
        AND tpi.collection ${input.collection ? ` = '${input.collection}'` : "IS NULL"}`  
      );

      return result;
    }),
    
    getThesaurusPactolsMetadataLink: t.procedure
    .input(z.object({branch, id_item: z.number(), item_type: z.string(), model: z.string()}))
    .output(z.object({ id_thesaurus: z.number(), id_thes_thesaurus: z.number(), collection: z.string().nullable() }).array())
    .query(async ({input, ctx}) => {
      const results = await ctx.database.manyOrNone(`
        SELECT id_thesaurus::integer, id_thes_thesaurus, collection
        FROM ${input.branch}_thesaurus_pactols_item
        WHERE id_item = ${input.id_item}
        AND item_type = '${input.item_type}'`
      );

      return z.object({ id_thesaurus: z.number(), id_thes_thesaurus: z.number(), collection: z.string().nullable() }).array().parse(results ?? []);
    }),

    deleteThesaurusPactolsMetadataLink: t.procedure
    .input(z.object({branch, id_item: z.number(), item_type: z.string(), collection: z.string().nullable(), id_thesaurus: z.number(), id_thes_thesaurus: z.number()}))
    .mutation(async ({input, ctx}) => {
      await ctx.database.none(`
        DELETE FROM ${input.branch}_thesaurus_pactols_item
        WHERE id_item = $1
        AND item_type = $2
        ${input.collection ? "AND collection = $3" : "AND collection IS NULL"}
        AND id_thesaurus = $4
        AND id_thes_thesaurus = $5`,
        [input.id_item, input.item_type, input.collection, input.id_thesaurus, input.id_thes_thesaurus]
      );
    }),

    createThesaurusPactolsMetadataLink: t.procedure
    .input(z.object({branch, id_item: z.number(), item_type: z.string(), id_metadata: z.number(), id_thesaurus: z.number(), id_thes_thesaurus: z.number()}))
    .mutation(async ({input, ctx}) => {
      const collection = await ctx.database.oneOrNone(`
        SELECT list
        FROM ${input.branch}_metadata
        WHERE id = $1`,
        [input.id_metadata]
      );

      const exists = await ctx.database.oneOrNone(`
        SELECT *
        FROM ${input.branch}_thesaurus_pactols_item
        WHERE id_item = $1
        AND item_type = $2
        ${collection ? `AND collection = '${collection.list}'` : "AND collection IS NULL"}
        AND id_thesaurus = $3
        AND id_thes_thesaurus = $4
        LIMIT 1`,
        [input.id_item, input.item_type, input.id_thesaurus, input.id_thes_thesaurus]
      );

      if(exists) {
        return true;
      }

      await ctx.database.none(`
        INSERT INTO ${input.branch}_thesaurus_pactols_item (id_item, item_type, collection, id_thesaurus, id_thes_thesaurus, thesaurus)
        VALUES ($1, $2, $3, $4, $5, 'sujet')`,
        [input.id_item, input.item_type, collection ? collection.list : 'NULL', input.id_thesaurus, input.id_thes_thesaurus]
      );
    }),

    getThesaurusTagsWithMetadataAndItem: t.procedure
    .input(z.object({branch, id_item: z.number(), item_type: z.string(), id_metadata: z.number()}))
    .query(async ({input, ctx}) => {
      const result = await ctx.database.manyOrNone(`
        SELECT t.*
        FROM ${input.branch}_tag t
        INNER JOIN ${input.branch}_item_tag it ON it.id_tag = t.id
        WHERE it.id_item = ${input.id_item}
        AND it.item_type = '${input.item_type}'
        AND it.id_metadata = ${input.id_metadata}`
      );

      if(!result && input.id_metadata){
        console.log(`NO TAGS FOUND FOR ${input.item_type} n°${input.id_item} USING METADATA n°${input.id_metadata}. TRYING WITH NO METADATA ID FILTER.`);

        const result_no_metadata_id = await ctx.database.manyOrNone(`
          SELECT t.*
          FROM ${input.branch}_tag t
          INNER JOIN ${input.branch}_item_tag it ON it.id_tag = t.id
          WHERE it.id_item = ${input.id_item}
          AND it.item_type = '${input.item_type}'`
        );

        if(result_no_metadata_id){
          console.log(`${result_no_metadata_id.length} TAGS FOUND FOR ${input.item_type} n°${input.id_item} USING NO METADATA ID FILTER.`);
          return result_no_metadata_id;
        }else{
          console.log(`NO TAGS FOUND FOR ${input.item_type} n°${input.id_item} USING NO METADATA ID FILTER.`);
        }
      };

      return result ?? [];
    }),

    getTagMetadataLink: t.procedure
    .input(z.object({branch, id_item: z.number(), item_type: z.string()}))
    .output(z.object({ id_tag: z.number(), id_metadata: z.number() }).array())
    .query(async ({input, ctx}) => {
      const result = await ctx.database.manyOrNone(`
        SELECT id_tag, id_metadata
        FROM ${input.branch}_item_tag
        WHERE id_item = ${input.id_item}
        AND item_type = '${input.item_type}'`
      );

      return z.object({ id_tag: z.number(), id_metadata: z.number() }).array().parse(result);
    }),

    getSubThesaurus: t.procedure
    .input(z.object({branch, thesaurus: z.string(), project_id: z.number().optional()}))
    .query(async ({input, ctx}) => {

      let leaf = 'thesaurus';
      switch(input.thesaurus){
        case 'multi':
          leaf = 'thesaurus_multi';
          break;
        case 'pactols':
          leaf = 'thesaurus_pactols';
          break;
      }

      let where = '';
      if(input.project_id){
        where = `
           INNER JOIN ${input.branch}_folder_${leaf} ft ON ft.thesaurus = t.thesaurus
           WHERE ft.id_folder = ${input.project_id}`;
      }

      const result = (await ctx.database.manyOrNone(`
        SELECT distinct t.thesaurus
        FROM ${input.branch}_${leaf} t
        ${where}
        ORDER BY t.thesaurus`
      ))?.map((r) => r.thesaurus);

      return result;
  }),

    getIdThesaurusByNameAndId: t.procedure
        .input(z.object({ branch, name: z.string(), thesaurus: z.string(), thesaurus_type: z.enum(["single", "multi"]), id_thes: id }))
        .query(async ({ input, ctx }) => {
            const result = await ctx.database.oneOrNone(
                `SELECT id::int, id_thes::int
                    FROM ${input.branch}_thesaurus${input.thesaurus_type === "multi" ? "_multi" : ""}
                    WHERE thesaurus = $1
                    AND LOWER(name) = LOWER($2)
                    AND id_thes = ${input.id_thes}
                    LIMIT 1`, [ input.thesaurus, input.name]
                );
            return z.object({ id, id_thes: z.number() }).nullable().parse(result);
        }),

    addMultiConcept: t.procedure
        .input(
            z.object({
                branch,
                thesaurus: z.string(),
                id_thes: id,
                type: z.string(),
                id_parent: id,
                name: z.string(),
                identifier: z.string().nullish()
            }),
        )
        .mutation(async ({ ctx, input }) => {
        // chercher le parent
            const ty = input.type === 'multi' ? '_multi' : ''
            let query = `SELECT id_thes as parent
                FROM ${input.branch}_thesaurus${ty}
                WHERE thesaurus = '${input.thesaurus}'
                AND id_thes = ${input.id_parent}`
            console.log(query)
            const parent = await ctx.database.oneOrNone(`
                SELECT id_thes as parent
                FROM ${input.branch}_thesaurus${ty}
                WHERE thesaurus = '${input.thesaurus}'
                AND id_thes = ${input.id_parent}`
              );

            console.log(typeof parent)
            if (!parent) {
                console.log(parent)
                console.log('KO parent')
                return false;
            } else {
                console.log('OK parent')
                const concept = await ctx.database.manyOrNone(`
                    SELECT id_thes, name
                    FROM ${input.branch}_thesaurus${ty}
                    WHERE thesaurus = '${input.thesaurus}'
                      AND id_thes = ${input.id_thes}`
                );
                console.log(typeof concept)
                if (concept && concept.length) {
                    return true;
                } else {
                    const ident = input.identifier ? input.identifier : '';
                    const newid = await ctx.database.oneOrNone(`
                        INSERT INTO ${input.branch}_thesaurus${ty}(thesaurus, id_thes, name, short_name, name_en, id_parent, identifier)
                        SELECT $1, ${input.id_thes}, $2, $2, $2, ${input.id_parent}, $3
                           RETURNING id `, [input.thesaurus, input.name, ident]
                    );
                    if (newid) { // 1 / mettre à jour le thesaurus_path puis le rank, puis le global rank
                        let {thesaurus_path} = await ctx.database.oneOrNone(`
                            UPDATE ${input.branch}_thesaurus${ty}
                            SET thesaurus_path = ${input.branch}_get_thes_path${ty}(id_thes, '${input.thesaurus}')::ltree,
                        nb_tot_item = 0
                            WHERE thesaurus = '${input.thesaurus}'
                              AND id_thes = ${input.id_thes}
                              AND thesaurus_path IS NULL
                                RETURNING thesaurus_path`)

                        // on met à jour tous les rang concerné par l'ajout (
                        // à partr de l'id parent on revoit tout pour classeer par nom en prenant en compte le nouvel élément
                        // nouvelle fonction postgres qui fait ça et met à jour les global_rank également
                        let {rank} = await ctx.database.oneOrNone(`
                          SELECT update_${input.branch}_all_rank_thes${ty}_after_insert(${input.id_parent},'${input.thesaurus}') as rank`
                        );
                        // on met à jour le parent
                        let updatechild = await ctx.database.oneOrNone(`
                            UPDATE ${input.branch}_thesaurus${ty}
                            SET haschild = 1
                            WHERE thesaurus = '${input.thesaurus}'
                              AND id_thes = ${input.id_parent}`);
                        return true;

                    } else {
                        return false;
                    }
                }
            }

        }),

    addKeywordsFromMetadataPassport: t.procedure
      .input(z.object({
        branch,
        id_item: z.number().int().nonnegative(),
        item_type: z.string(),
        id_user: id,
        in_metadata_id: id,
        delimiter: z.string(),
        thesaurus_type: z.string(),
        thesaurus_collection: z.string().nullable(),
        should_create_tags: z.boolean(),
        out_metadata_id: id.optional(),
      })).mutation(async ({input, ctx}) => {
        const inMetadataValue = await ctx.database.oneOrNone(`
          SELECT p.value
          FROM ${input.branch}_passport p
          WHERE p.id_item = $1
          AND p.item_type = $2
          AND p.id_metadata = $3`, 
        [input.id_item, input.item_type, input.in_metadata_id]);

        if(!inMetadataValue || !inMetadataValue.value)
          return {status: 'ko', message: "The item has no value for the metadata."};

        const allInMetadataValue: string = inMetadataValue.value.join(input.delimiter);

        let leaf = '';
        switch(input.thesaurus_type){
          case 'simple':
            leaf = 'thesaurus';
            break;
          case 'multi':
            leaf = 'thesaurus_multi';
            break;
          case 'pactols':
            leaf = 'thesaurus_pactols';
            break;
          default:
            return {status: 'ko', message: "The thesaurus type is not valid."};
        }

        let foundThes:any[] = [], foundFreeTags:any[] = [], notFoundTags:any[] = [];
        for(const keyword of allInMetadataValue.split(input.delimiter).map(k => k.trim()).filter(k => k != '')){
          const thes_item = await ctx.database.oneOrNone(`
            SELECT *
            FROM ${input.branch}_${leaf}
            WHERE (name ILIKE $1 OR name_en ILIKE $1)
            ${input.thesaurus_collection ? ' AND thesaurus = $2' : ''}
            LIMIT 1`,
            [keyword, input.thesaurus_collection]
          );

          if(thes_item){
            foundThes.push(thes_item);
            continue;
          }

          const free_tag = await ctx.database.oneOrNone(`
            SELECT *
            FROM ${input.branch}_tag
            WHERE name ILIKE $1
            LIMIT 1`,
            [keyword]
          );

          if(free_tag){
            foundFreeTags.push(free_tag);
          }else{
            notFoundTags.push(keyword);
          }
        }

        let status = 'ok';
        let errorMessage = '';
        const promises = [];

        let p1 = ctx.database.tx(async (trx) => {
          await trx.batch(foundThes.map(thes => {
            return trx.none(`
              INSERT INTO ${input.branch}_${leaf}_item (id_thesaurus, id_thes_thesaurus,${leaf === 'thesaurus_pactols' ? '' : 'thes_path, '} id_item, item_type, thesaurus, id_user, qualifier, id_metadata, date_modified) 
              VALUES ($1, $2, ${leaf === 'thesaurus_pactols' ? '' : '$3, '} $4, $5, $6, $7, (SELECT code FROM ${input.branch}_metadata WHERE id = $8), $8, NOW())
              ON CONFLICT DO NOTHING`, // Use to prevent double insertion
              [thes.id, thes.id_thes, thes.thesaurus_path, input.id_item, input.item_type, input.thesaurus_collection ?? 'NULL', input.id_user, input.out_metadata_id == -1 ? 'NULL' : input.out_metadata_id]
            );
          }));
        }).then(() => {
          console.log(`THESAURUS CONTROLLER INFO : Link ${foundThes.length} thesaurus from metadata passport.`);
        }).catch((err) => {
          console.log(`THESAURUS CONTROLLER ERROR : Error while linking thesaurus from metadata passport!`);
          console.log(err);
          status = 'partial';
          errorMessage += `Error while linking thesaurus from metadata passport! `;
          foundThes = [];
        });

        promises.push(p1);

        let p2 = ctx.database.tx(async (trx) => {
          await trx.batch(foundFreeTags.map(free_tag => {
            return trx.none(`
              INSERT INTO ${input.branch}_item_tag (id_tag, id_item, item_type, date_tagged, id_metadata)
              VALUES ($1, $2, $3, NOW(), $4)
              ON CONFLICT DO NOTHING`, // Use to prevent double insertion
              [free_tag.id, input.id_item, input.item_type, input.out_metadata_id == -1 ? 'NULL' : input.out_metadata_id]
            );
          }));
        }).then(() => {
          console.log(`THESAURUS CONTROLLER INFO : Link ${foundFreeTags.length} free tags from metadata passport.`);
        }).catch((err) => {
          console.log(`THESAURUS CONTROLLER ERROR : Error while linking free tags from metadata passport!`);
          console.log(err);
          status = 'partial';
          errorMessage += `Error while linking free tags from metadata passport! `;
          foundFreeTags = [];
        });

        promises.push(p2);

        if(input.should_create_tags){
          let p3 = ctx.database.tx(async (trx) => {
            await trx.batch(notFoundTags.map(free_tag_name => {
              return trx.one(`INSERT INTO ${input.branch}_tag (name) VALUES ($1) RETURNING id`, [free_tag_name])
                        .then((tag_id) => trx.none(`
                            INSERT INTO ${input.branch}_item_tag (id_tag, id_item, item_type, date_tagged, id_metadata) 
                            VALUES ($1, $2, $3, NOW(), $4)
                            ON CONFLICT DO NOTHING`, // Use to prevent double insertion
                            [tag_id.id, input.id_item, input.item_type, input.out_metadata_id == -1 ? 'NULL' : input.out_metadata_id]));
            }));
          }).then(() => {
            console.log(`THESAURUS CONTROLLER INFO : Create and link ${notFoundTags.length} free tags from metadata passport.`);
          }).catch((err) => {
            console.log(`THESAURUS CONTROLLER ERROR : Error while creating and/or linking free tags from metadata passport!`);
            console.log(err);
            status = 'partial';
            errorMessage += `Error while creating and/or linking free tags from metadata passport! `;
            notFoundTags = [];
          });

          promises.push(p3);
        }

        return await Promise.all(promises).then(() => { 
          const result: any = {
            status,
            message: errorMessage,
            foundThes: foundThes.map(thes => thes.name),
            foundFreeTags: foundFreeTags.map(tag => tag.name),
            createdTags: [],
            notFoundTags: []
          }

          if(input.should_create_tags){
            result.createdTags = notFoundTags;
          }else{
            result.notFoundTags = notFoundTags;
          }

          return result;
      })
    })
});
