---
sidebar_position: 2
---

# ListRecords

Afin de créer les enregistrements OAI-PMH, le CND3D utilise les tables ***conservatoire3d_moissonnage*** et ***conservatoire3d_oai_records***. L'objectif est de rassembler des informations provenant de plusieurs tables dans une seule (***conservatoire3d_oai_records***), afin de faciliter la création des enregistrements OAI-PMH.
Dans l'application CND3D, la route **oai/listOfRecords** (accessible [ici](https://3d.humanities.science/oai?verb=ListRecords&metadataPrefix=oai_dc)) est utilisée pour générer le fichier XML qui compile toutes les informations/métadonnées des objets de la base de données à partir de la table conservatoire3d_oai_records.

Le ***modèle de record*** utilisé est basé sur le modèle openArchives disponible [ici](https://www.openarchives.org/OAI/2.0/oai_dc.xsd). Voici ce à quoi un ***record*** ressemble dans le fichier XML de sortie:

```xml
    <record>
        <header>
            <identifier></identifier>
            <datestamp></datestamp>
            <setSpec></setSpec>
        </header>
        <metadata>
            <oai_dc:dc>
                <dc:identifier></dc:identifier>
                <dc:title></dc:title>
                <dc:creator></dc:creator>
                <dc:constributor></dc:constributor>
                <dc:publisher></dc:publisher>
                <dc:subject></dc:subject>
                <dc:description></dc:description>
                <dc:date></dc:date>
                <dc:type></dc:type>
                <dc:format></dc:format>
                <dc:source></dc:source>
                <dc:language></dc:language>
                <dc:coverage></dc:coverage>
                <dc:relation></dc:relation>
                <dc:rights></dc:rights>
            </oai_dc:dc>
        </metadata>
    </record>
```

## Mapping

Voici le mapping utilisé pour alimenter la table ***conservatoire3d_oai_records*** afin de générer la liste des enregistrements. Ce mapping a été modifié début 2025 en raison du nouveau modèle de dépôt mis en place dans CND3D (passage de la version V1 à la V2).

### Object V1 (old deposit model)

La table ***conservatoire3d_moissonnage*** est alimentée par le script ***feed_conservatoire3d_moissonnage.sql***. La table ***conservatoire3d_oai_records*** est alimentée par le script ***feed_conservatoire3d_oai_records.sql***. Dans cette version du modèle de métadonnées de dépôt, aucune distinction n’est faite entre l’objet virtuel et l’objet physique.

#### identifier

`Référence non ambiguë à la ressource dans un contexte donné.`

Directement recuperé depuis la table ***conservatoire3d_object*** avec le champ ***doi***.

```xml
    <identifier>https://doi.org/10.34969/CND3D/207944.o.2023</identifier>
```
---

#### datestamp

`Date et heure de la dernière modification apportée à la ressource.`

Directement recuperé depuis la table ***conservatoire3d_object*** avec le champ ***date_integration***.

```xml
    <datestamp>2025-01-01T00:00:00Z</datestamp>
```
---

#### setSpec

`L'ensemble auquel la ressource appartient.`

Toujours 'oai_dc:cnd3d_set0'.

```xml
    <setSpec>oai_dc:cnd3d_set0</setSpec>
```
---

#### dc:identifier

`Une référence non ambiguë à la ressource dans un contexte donné.`

Récupération directe dans la table ***conservatoire3d_object*** avec le champ ***doi***.

```xml
    <dc:identifier>https://doi.org/10.34969/CND3D/207944.o.2023</dc:identifier>
```
---

#### dc:title

`Titre de la ressource.`

Directement recuperé depuis la table ***conservatoire3d_object*** avec le champ ***title***. Les retours de ligne ou les sauts de ligne sont remplacé par un espace.

```xml
    <dc:title>Vase_31</dc:title>
```
---

#### dc:creator

`Créateurs de la ressource.`

Récupéré à l'aide de la fonction SQL ***get_creators_from_object_for_DOI()*** avec l'***id*** de l'objet. Cette fonction utilise la métadonnée ***créateur*** de l'objet et récupère sa valeur dans la table ***conservatoire3d_passport***.

```xml
    <dc:creator>Pauline VANDENBROUCK</dc:creator>
```
---

#### dc:contributor

`Contributeurs de la ressource.`

Récupéré à l'aide la fonction SQL ***get_contributors_from_object()*** avec l' ***id*** de l'objet. Cette fonction va chercher la valeur de la métadonnée ***contributeur*** de l'objet virtuel dans la table ***conservatoire3d_passport***.

```xml
    <dc:contributor>Théa Poullain</dc:contributor>
```
---

#### dc:publisher

`L'entité responsable de la mise à disposition de la ressource.`

Toujours 'Archeovision'.

```xml
    <dc:publisher>Archeovision</dc:publisher>
```
---

#### dc:subject

`Thématique de la ressource.` 

Récupéré à l'aide de deux fonctions SQL ***conservatoire3d_get_pactols_tag_from_object()*** et ***conservatoire3d_get_tags_from_object()*** avec l'***id*** de l'objet. Ces fonctions utilisent les tables ***conservatoire3d_pactol*** et ***conservatoire3d_tag***.

```xml
    <dc:subject>expérimentation</dc:subject>
    <dc:subject>poterie</dc:subject>
    <dc:subject>Vase</dc:subject>
```
---

#### dc:description

`Un compte rendu de la ressource.`

Récupéré depuis les métadonnées ***description*** et ***note***de l'objet, en utilisant la table ***conservatoire3d_passport***. Tout retour chariot ou caractère de saut de ligne est remplacé par un espace.

```xml
    <dc:description>Modele 3D de l'eglise Saint Jean Abbetot et de sa crypte (interieur/exterieur)</dc:description>
```
---

#### dc:date

`Point ou période de temps associé à un événement dans le cycle de vie de la ressource.`

Récupéré depuis la metadonnée ***date3D*** de l'objet, en utilisant la table ***conservatoire3d_passport***.

```xml
    <dc:date>2025-01-01T00:00:00Z</dc:date>
```
---

#### dc:type

`Narure ou genre de la ressource.`

Toujours '3D object'.

```xml
    <dc:type>3D object</dc:type>
```
---

#### dc:format

`Format de fichier, support physique ou dimensions de la ressource.`

Récupéré depuis la métadonnée ***structureDocument*** du dépôt, en utilisant la table ***conservatoire3d_passport***.

```xml
    <dc:format>1 fichier 3D (ply), 3 fichiers image (jpg)</dc:format>
```
---

#### dc:source

`Ressource connexe dont la ressource décrite est dérivée.`

Récupéré depuis la table ***conservatoire3d_doi*** en utilisant l'***id*** du dépôt. C'est le DOI du dépôt.

```xml
    <dc:source>https://doi.org/10.34969/CND3D/324720.d.2023</dc:source>
```
---

#### dc:language

`Langue de la ressource.`

Toujours 'fr'.

```xml
    <dc:language>fr</dc:language>
```
---

#### dc:coverage

`Thème spatial et/ou temporel de la ressource, sa zone d’application géographique ou la juridiction à laquelle elle se rapporte.`

Ce champ est une combinaison de 4 autres champs insérés dans la table ***conservatoire3d_moissonnage*** :
- ***coverage_da*** : la métadonnée 'dateArcheologique' de l'objet en utilisant la table ***conservatoire3d_passport***.
- ***coverage_p*** : un thésaurus 'periodo' lié à l'objet à l'aide de la table ***conservatoire3d_periodo***.
- ***coverage_latlng*** et ***coverage_loc_name*** : un thésaurus 'geo' lié à l'objet en utilisant les champs « latlng » et « name » de la table ***conservatoire3d_thesaurus_multi***. 

Ces quatre champs sont ensuite agrégés dans la table ***conservatoire3d_oai_records***.

```xml
    <dc:coverage>49.49822,0.38533</dc:coverage>
    <dc:coverage>Médiaval</dc:coverage>
    <dc:coverage>Moyen Âge</dc:coverage>
    <dc:coverage>Saint-Jean-d'Abbetot</dc:coverage>
```

#### dc:relation

`Ressource connexe dont la ressource décrite est dérivée.`

La table ***conservatoire3d_thesaurus_multi_item*** permet de récupérer l'***id*** de la collection liée à l'objet. Le nom de la collection est ensuite extrait de la table ***conservatoire3d_thesaurus_multi***. Le résultat est le début de l'URL de la page de la collection et le nom de la collection.

```xml
    <dc:relation>https://3d.humanities.science/collection/Eglise de Saint-Jean-d'Abbetot</dc:relation>
```

> **Note**: Nous supposons qu'un objet ne peut appartenir qu'à une seule collection.
---

#### dc:rights

`Informations sur les droits détenus dans et sur la ressource.`

Récupérer depuis la métadonnée ***proprietaireObjet*** de l'objet, via la table ***conservatoire3d_passport***.

```xml
    <dc:rights>Commune de la Cerlangue</dc:rights>
```

### Object V2 (new deposit model)

La table ***conservatoire3d_moissonnageV2*** est alimentée par le script ***feed_conservatoire3d_moissonnageV2.sql***. La table ***conservatoire3d_oai_records*** est alimentée par le script ***feed_conservatoire3d_oai_records.sql***.

La documentation du modèle de métadonnées utilisé dans le CND3D est disponible [ici] (https://3d.humanities.science/docs/).


#### identifier

`Référence non ambiguë à la ressource dans un contexte donné.`

Directement récupéré depuis la table ***conservatoire3d_object*** avec le champ ***doi***.

```xml
    <identifier>https://doi.org/10.34969/CND3D/207944.o.2023</identifier>
```
---

#### datestamp

`Date et heure de la dernière modification apportée à la ressource.`

Directement récupéré depuis la table ***conservatoire3d_object*** avec le champ ***date_integration***.

```xml
    <datestamp>2025-01-01T00:00:00Z</datestamp>
```
---

#### setSpec

`L'ensemble auquel la ressource appartient.`

Toujours 'oai_dc:cnd3d_set0'.

```xml
    <setSpec>oai_dc:cnd3d_set0</setSpec>
```
---

#### dc:identifier

`Une référence non ambiguë à la ressource dans un contexte donné.`

Directement récupéré depuis la table ***conservatoire3d_object*** avec le champ ***doi***.

```xml
    <dc:identifier>https://doi.org/10.34969/CND3D/207944.o.2023</dc:identifier>
```
---

#### dc:title

`Titre de la ressource.`

Il s'agit de la valeur de la métadonnée ***object_name*** de l'objet virtuel avec la chaîne '(objet 3D) à la fin. 

```xml
    <dc:title>Lingot bipyramidal (3D object)</dc:title>
```
---

#### dc:creator

`Créateurs de la ressource.`

Récupéré depuis la table ***conservatoire3d_actor***. Il s'agit de l'agrégation du nom complet des acteurs contenues dans la métadonnée ***object_creator*** de l'objet virtuel. 

```xml
    <dc:creator>Pauline VANDENBROUCK</dc:creator>
    <dc:creator>Barrier, Sylvie</dc:creator>
```
---

#### dc:constributor

`Contributeurs de la ressource.`

Récupéré depuis la table ***conservatoire3d_actor***. Il s'agit de l'agrégation du nom complet des acteurs contenues dans la métadonnée ***object_contributor*** de l'objet virtuel. 

```xml
   <dc:contributor>Durost, Sébastien</dc:contributor>
    <dc:contributor>Guillaume Reich</dc:contributor>
```
---

#### dc:publisher

`L'entité responsable de la mise à disposition de la ressource.`

Cette information est extraite de la table ***conservatoire3d_actor***. L'information est accessible en accédant au dépot de l'objet virtuel sur les métadonnées ***dm_responsibleEntity***. La plupart du temps, il n'y a qu'une seule entité responsable, mais pour plus de sécurité, l'agrégation de tous les noms d'entités responsables est effectuée. 

```xml
    <dc:publisher>Bibracte</dc:publisher>
```
---
 
#### dc:subject

`Thématique de la ressource.`

Cette information est extraite de 3 tables de thésaurus ***conservatoire3d_thesaurus***, ***conservatoire3d_thesaurus_multi***, ***conservatoire3d_thesaurus_pactols*** et de la table de tags ***conservatoire3d_tag***. Ces informations peuvent être liées à une métadonnée ou non. 

```xml
    <dc:subject>poterie</dc:subject>
    <dc:subject>terre cuite</dc:subject>
    <dc:subject>ACQUISITION</dc:subject>
    <dc:subject>Artefact</dc:subject>
    <dc:subject>coupe</dc:subject>
    <dc:subject>Bibracte, Mont-Beuvray. La nécropole de la Croix du Rebout</dc:subject>
```

> **Note**: Les métadonnées ***dm_responsibleEntity*** et ***dm_rights*** liées au dépôt de l'objet virtuel ne sont pas récupérées ici, même si elles sont liées à un thésaurus. La licence peut être trouvée sur la balise rights et l'entité responsable sur la balise publisher.
---

#### dc:description

`Un compte rendu de la ressource.`

Récupéré depuis la metadonnée ***object_description*** de l'objet virtuel.

```xml
    <dc:description>
        Bas-relief en forme d'ogive. 
        Dans un cadre un ange auréolé à gauche avec épée et bouclier en combat un autre tombé à droite sans ailes.
    </dc:description>
```
---

#### dc:date

`Point ou période de temps associé à un événement dans le cycle de vie de la ressource.`

Extrait depuis la métadonnée ***object_documentedDate*** de l'objet virtuel. Si la métadonnée ***object_documentedDate*** est null, c'est la valeur de la métadonnée ***dp_date*** du dépôt qui est récupérée. Cette date respecte la norme ISO 8601 (https://en.wikipedia.org/wiki/ISO_8601).

```xml
    <dc:date>2024</dc:date>
```
---

#### dc:type

`Nature ou le genre de la ressource.`

Toujours '3D object'.

```xml
    <dc:type>3D object</dc:type>
```
---

#### dc:format

`Format de fichier, support physique ou dimensions de la ressource.`

Récupéré depuis la metadonnée ***ds_content*** du dépôt lié à l'objet virtuel.

```xml
    <dc:format>1 fichier 3D (ply) 1 fichier image (png)</dc:format>
```
---

#### dc:source

`Ressource connexe dont la ressource décrite est dérivée.`

Récupéré depuis la metadonnée ***po_name*** de l'object physique. Si l'objet virtuel n'a pas d'objet physique, c'est le DOI du dépôt qui est récupéré.

```xml
    <dc:source>Tonnelet (Inv. 998.9.5262.58)</dc:source>
```
---

#### dc:language

`Langue de la ressource`

Toujours 'fr'.

```xml
    <dc:language>fr</dc:language>
```
---

#### dc:coverage

`Thème spatial et/ou temporel de la ressource, sa zone d’application géographique ou la juridiction à laquelle elle se rapporte.`

Extrait de la table ***conservatoire3d_datation*** à l'aide des métadonnées ***po_discovery_date*** et ***po_creation_datation*** de l'objet physique. Il regroupe les ***date_min***, ***date_max*** et ***date_litteral*** de chaque datations. 

Il extrait également des données de la table ***conservatoire3d_location*** en utilisant les métadonnées ***po_discovery_location***, ***po_creation_location*** et ***po_retention_location*** de l'objet physique. Il agrège les ***name*** et ***longlat*** de chaque localisation.

Les valeurs sont filtrées pour ne conserver que les valeurs distinctes.

Un lien vers la table ***conservatoire3d_thesaurus_periodo*** est établi à l'aide de la table ***conservatoire3d_datation***.

```xml
    <dc:coverage>-0100-01-01</dc:coverage>
    <dc:coverage>1994-01-01</dc:coverage>
    <dc:coverage>1er siècle avant notre ère</dc:coverage>
    <dc:coverage>1998</dc:coverage>
    <dc:coverage>Ie siècle AEC</dc:coverage>
    <dc:coverage>46.93228,4.0489</dc:coverage>
    <dc:coverage>46.92726,4.03717</dc:coverage>
    <dc:coverage>Bibracte - Site et musée</dc:coverage>
    <dc:coverage>Bibracte, Mont-Beuvray. Îlot des Grandes Forges</dc:coverage>
```
---

#### dc:relation

`Ressource connexe dont la ressource décrite est dérivée.`

Extrait depuis la metadonnée ***object_collection*** de l'objet virtuel. La valeur du thesaurus est utilisé pour construire l'URL amenant à la page du CND3D de la collection.

```xml
    <dc:relation>https://3d.humanities.science/collection/Bazoches-sur-Vesles</dc:relation>
```

> **Note**: Nous supposons qu'un objet virtuel ne peut appartenir qu'à une seule collection.
---

#### dc:rights

`Informations sur les droits détenus dans et sur la ressource.`

Récupéré depuis la métadonnée ***ds_rights*** du dépôt lié à l'objet virtuel.

```xml
    <dc:rights>CC BY SA</dc:rights>
```

