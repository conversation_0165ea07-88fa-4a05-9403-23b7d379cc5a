---
sidebar_position: 3
---

# Ingestion du CSV

Cette fonctionnalité vous permet d'ingérer les données du fichier CSV dans la table et/ou le modèle de métadonnées. Vous pouvez voir le tableau de vue d'ensemble avec chaque 'csv-metadata' en cours de traitement ou déjà existant dans la base de données. En cliquant sur une ligne, vous avez accès à trois possibilités :
- C<PERSON>er ou Mettre à jour la table
- Créer ou Mettre à jour le modèle de métadonnées
- Ingerer les données du CSV dans la table et/ou le modèle de métadonnées

Vous pouvez décider d'ingérer les données uniquement dans la table ou uniquement dans le modèle en cliquant sur '...' du bouton d'ingestion.

![](ingestCSV-FR.png)

Pour l'ingestion du modèle, seuls les types de base sont entièrement pris en charge tels que *boolean*, *char*, *choice*, *choico*, *date*, *int*, *list*, *map* ou *text*. Les autres types tels que *actor*, *datation*, *doc*, *inventory*, *link*, *location*, *nomenclature*, *thesaurus*, *multi*, *pactols* ou *periodo* ne sont pas entièrement pris en charge ce qui signifie qu'ils seront stockés sous forme de texte. Une implémentation ultérieure permettra de les intégrer pleinnement dans la base de données.

Après l'ingestion, un message de succès s'affichera et le tableau pour le contenu de la table et du modèle sera affiché (en fonction de votre choix d'ingestion: dans la table et/ou dans le modèle). Vous pouvez vérifier si les données ingérées dans la table et/ou le modèle sont correctes.

![](ingestCSV-CT-FR.png)
![](ingestCSV-CM-FR.png)

Le résultat final peut être vu directement sur les pages de visualisation des éléments lors de l'affichage des métadonnées. Dans notre exemple avec un claveau du projet N-Dame, voici le résultat : 

![](finalGoal-FR.png)
