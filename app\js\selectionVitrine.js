// Selection Vitrine: Managing three distinct selection states
// 1. currentSelections: Items selected from other pages (read-only, from localStorage)
// 2. registeredSelections: Items saved/registered into the selection page (read-only, from server)
// 3. vitrineSelections: Items selected within this page for applying actions (interactive)

let currentSelections = new Set(); // From localStorage (read-only)
let registeredSelections = new Set(); // From server data (read-only)
let vitrineSelections = new Set(); // Page-specific selections for actions

// Global cached variables for event listeners
let cachedUserId = null;
let cachedProjectId = null;
let cachedLng = null;

/**
 * Helper function to clear visual selection state from all items
 */
function clearVisualSelectionState() {
    const selectedItems = document.querySelectorAll('.checked, .see-select');
    selectedItems.forEach(item => {
        item.classList.remove('checked', 'see-select');
        const checkbox = item.querySelector('.form-check-input');
        if (checkbox && checkbox.checked) {
            checkbox.checked = false;
        }
    });
}

const updateButtonsState = (function() {
    let timeout;
    let lastCount = -1;

    function updateUI(count) {
        if (count === lastCount) return;
        lastCount = count;

        const isDisabled = count === 0;
        const isIllustrateEnabled = count === 1;

        const virtualFolderForms = document.querySelectorAll('.virtual-folder-container form');
        const illustrateForm = document.querySelector('form[action*="/addSelectionToIllustrateObject/"]');

        requestAnimationFrame(() => {
            virtualFolderForms.forEach(form => {
                const selects = form.querySelectorAll('select');
                const buttons = form.querySelectorAll('input[type="submit"]');

                if (form === illustrateForm) return;

                selects.forEach(select => {
                    select.disabled = isDisabled;
                    if (isDisabled) {
                        select.classList.add('disabled-select');
                    } else {
                        select.classList.remove('disabled-select');
                    }
                });

                buttons.forEach(button => {
                    button.disabled = isDisabled;
                    if (isDisabled) {
                        button.classList.add('disabled-button');
                        const wrapper = button.closest('.button-wrapper');
                        if (wrapper) {
                            wrapper.classList.add('disabled');
                            if (!wrapper.hasAttribute('data-bs-toggle')) {
                                const selectAtLeastOneElement = document.getElementById('select-at-least-one-text');
                                const tooltipText = selectAtLeastOneElement ? selectAtLeastOneElement.value : 'Select at least one item';

                                wrapper.setAttribute('data-bs-toggle', 'tooltip');
                                wrapper.setAttribute('data-bs-title', tooltipText);

                                if (!bootstrap.Tooltip.getInstance(wrapper)) {
                                    new bootstrap.Tooltip(wrapper);
                                }
                            }
                        }
                    } else {
                        button.classList.remove('disabled-button');

                        const wrapper = button.closest('.button-wrapper');
                        if (wrapper) {
                            wrapper.classList.remove('disabled');

                            const tooltipInstance = bootstrap.Tooltip.getInstance(wrapper);
                            if (tooltipInstance) {
                                tooltipInstance.dispose();
                                wrapper.removeAttribute('data-bs-toggle');
                                wrapper.removeAttribute('data-bs-title');
                            }
                        }
                    }
                });
            });

            // Handle illustrate form separately
            if (illustrateForm) {
                const illustrateButton = illustrateForm.querySelector('input[type="submit"]');
                const illustrateSelect = illustrateForm.querySelector('select');

                if (illustrateButton && illustrateSelect) {
                    let canIllustrate = isIllustrateEnabled;

                    if (canIllustrate) {
                        const vitrineArray = Array.from(vitrineSelections);

                        if (vitrineArray.length === 1) {
                            const selectedItem = vitrineArray[0];
                            canIllustrate = selectedItem.startsWith('i');

                            if (canIllustrate) {
                                const idMatch = selectedItem.match(/^i(\d+)_/);
                                if (idMatch && idMatch[1]) {
                                    const fileId = idMatch[1];
                                    const idFileInput = illustrateForm.querySelector('input[name="idFile"]');
                                    if (idFileInput) {
                                        idFileInput.value = fileId;
                                    } else {
                                        canIllustrate = false;
                                    }
                                } else {
                                    canIllustrate = false;
                                }
                            }
                        } else {
                            canIllustrate = false;
                        }
                    }

                    illustrateButton.disabled = !canIllustrate;
                    illustrateSelect.disabled = !canIllustrate;

                    if (!canIllustrate) {
                        illustrateButton.classList.add('disabled-button');
                        illustrateSelect.classList.add('disabled-select');

                        const wrapper = illustrateButton.closest('.button-wrapper');
                        if (wrapper) {
                            wrapper.classList.add('disabled');

                            if (!wrapper.hasAttribute('data-bs-toggle')) {
                                let tooltipText;
                                if (count === 0) {
                                    const selectAtLeastOneElement = document.getElementById('select-at-least-one-text');
                                    tooltipText = selectAtLeastOneElement ? selectAtLeastOneElement.value : 'Select at least one item';
                                } else {
                                    const selectOnlyOneElement = document.getElementById('select-only-one-text');
                                    tooltipText = selectOnlyOneElement ? selectOnlyOneElement.value : 'Select only one item';
                                }

                                wrapper.setAttribute('data-bs-toggle', 'tooltip');
                                wrapper.setAttribute('data-bs-title', tooltipText);

                                if (!bootstrap.Tooltip.getInstance(wrapper)) {
                                    new bootstrap.Tooltip(wrapper);
                                }
                            }
                        }
                    } else {
                        illustrateButton.classList.remove('disabled-button');
                        illustrateSelect.classList.remove('disabled-select');

                        const wrapper = illustrateButton.closest('.button-wrapper');
                        if (wrapper) {
                            wrapper.classList.remove('disabled');

                            const tooltipInstance = bootstrap.Tooltip.getInstance(wrapper);
                            if (tooltipInstance) {
                                tooltipInstance.dispose();
                                wrapper.removeAttribute('data-bs-toggle');
                                wrapper.removeAttribute('data-bs-title');
                            }
                        }
                    }
                }
            }
        });
    }

    return function(count) {
        clearTimeout(timeout);
        timeout = setTimeout(() => updateUI(count), 50);
    };
})();

// Override the global updateItemSelection function for vitrine page selections
window.updateItemSelection = function updateItemSelection(idItem, itemType, idfolder) {
    if (window.justFinishedDragSelection) {
        return;
    }

    let type = "";
    let selected_item = "";
    if (itemType === "object") {
        type = "o";
        selected_item = "o" + idItem + "_" + idfolder;
    } else if (itemType === "file") {
        type = "i";
        selected_item = "i" + idItem + "_" + idfolder;
    } else if (itemType === "folder") {
        type = "d";
        selected_item = "d" + idItem + "_" + idfolder;
    } else if (itemType === "unico") {
        type = "u";
        selected_item = "u" + idItem + "_" + idfolder;
    }

    const el = document.getElementById("item-" + type + "-" + idItem + "_" + idfolder);
    if (!el) {
        return;
    }

    const checkbox = el.querySelector(".form-check-input");
    const arrayItem = selected_item;

    requestAnimationFrame(() => {
        if (!vitrineSelections.has(arrayItem)) {
            el.classList.add("checked");
            el.classList.add("see-select");
            vitrineSelections.add(arrayItem);
            if (checkbox && !checkbox.checked) {
                checkbox.checked = true;
            }
        } else {
            el.classList.remove("checked");
            el.classList.remove("see-select");
            vitrineSelections.delete(arrayItem);
            if (checkbox?.checked) {
                checkbox.checked = false;
            }
        }

        const nbSelectedElement = document.getElementById("nb-selected");
        if (nbSelectedElement) {
            nbSelectedElement.innerText = vitrineSelections.size.toString();
        }

        updateButtonsState(vitrineSelections.size);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    
    const navigationEntries = window.performance.getEntriesByType("navigation");
    if (navigationEntries.length > 0 && navigationEntries[0].type === 'reload') {
        localStorage.setItem('selection', '[]');
    }
    
    // Prevent default context menu on items to enable right-click selection
    document.addEventListener('contextmenu', function(e) {
        if (e.target.closest('.check_item, .update-item-selector')) {
            e.preventDefault();
        }
    });
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    }
    if (typeof $ !== 'undefined' && typeof $.fn.hide === 'function') {
        $('.loader').hide();
    }

    // Initialize the different selection sets
    // 1. Load current selections from localStorage (read-only)
    const storedCurrentSelections = localStorage.getItem("selection") || "[]";
    try {
        const parsedCurrentSelections = JSON.parse(storedCurrentSelections);
        currentSelections = new Set(Array.isArray(parsedCurrentSelections) ? parsedCurrentSelections : []);
    } catch (e) {
        console.error("Error parsing current selections from localStorage:", e);
        currentSelections = new Set();
    }

    // 2. Load registered selections from server data (read-only)
    const selectionDataElement = document.getElementById('selection-data');
    if (selectionDataElement) {
        try {
            const selectionData = JSON.parse(selectionDataElement.value || '[]');
            registeredSelections = new Set();
            if (selectionData && selectionData.length > 0) {
                selectionData.forEach((item) => {
                    if (item.fav) {
                        registeredSelections.add(item.fav);
                    }
                });
            }
        } catch (e) {
            console.error('Error processing selectionDataElement:', e);
            registeredSelections = new Set();
        }
    }

    // 3. Initialize empty vitrine selections
    vitrineSelections = new Set();

    if (typeof updateButtonsState === 'function') {
        updateButtonsState(vitrineSelections.size);
    }
    if (typeof $ !== 'undefined') {
        document.querySelectorAll('.button-wrapper.disabled').forEach(wrapper => {
            if (wrapper.hasAttribute('data-bs-toggle') && wrapper.getAttribute('data-bs-toggle') === 'tooltip') {
                if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                    new bootstrap.Tooltip(wrapper);
                }
            }
        });
    }

    if (typeof adjustVirtualFolderHeight === 'function') {
        adjustVirtualFolderHeight();
        window.addEventListener('resize', adjustVirtualFolderHeight);
    }
    if (typeof switchDisplay === 'function') { switchDisplay('grid'); }

    const selectionCount = document.getElementById("nb-selected");
    if (selectionCount) {
        selectionCount.innerText = vitrineSelections.size.toString();
    }

    const projectIdElement = document.getElementById('project-id');

    if (!projectIdElement) {
        return;
    }

    const userIdElement = document.getElementById('user-id');
    const languageElement = document.getElementById('language');

    let setupHasIssues = false;
    
    // Set global cached variables
    cachedProjectId = projectIdElement.value;

    if (!userIdElement) {
        console.warn("SelectionVitrine Init: 'user-id' element is MISSING. User-specific actions (save/remove selection) will be unavailable.");
    } else {
        cachedUserId = userIdElement.value;
        if (!cachedUserId) {
            console.warn("SelectionVitrine Init: 'user-id' element is present but has no value. User-specific actions will be unavailable.");
        }
    }

    if (!cachedProjectId) {
        console.error("SelectionVitrine Init: 'project-id' element is present but has NO VALUE. Core vitrine functionality will be disabled.");
        setupHasIssues = true;
    }

    if (languageElement) {
        cachedLng = languageElement.value;
    } else {
        console.warn("SelectionVitrine Init: 'language' element is MISSING. Localizations might be affected.");
    }

    // Event listeners and exploreSelection call
    if (!setupHasIssues) {
        // Try to attach save selection listeners immediately
        attachSaveSelectionListeners();
        
        // Also try again after a short delay in case elements are loaded dynamically
        setTimeout(attachSaveSelectionListeners, 500);
        setTimeout(attachSaveSelectionListeners, 1000);

        const removeSelectionBtn = document.getElementById('remove-selection-btn');
        if (removeSelectionBtn) {
            removeSelectionBtn.addEventListener('click', function() {
                if (!cachedUserId || !cachedProjectId) {
                    console.error("Remove selection click: Critical error - Cached User ID or Project ID is null/empty.");
                    alert(window.i18n && window.i18n.errorOccured ? window.i18n.errorOccured : 'An unexpected error occurred.');
                    return;
                }
                // Clear vitrine selections as well when removing registered selections
                vitrineSelections.clear();
                updateButtonsState(0);
                const nbSelectedElement = document.getElementById("nb-selected");
                if (nbSelectedElement) {
                    nbSelectedElement.innerText = "0";
                }
                
                // Remove visual selection state from all selected items
                clearVisualSelectionState();
                
                removeSelection(cachedUserId, cachedProjectId, cachedLng);
            });
        }

        if (typeof exploreSelection === 'function') {
            exploreSelection(cachedProjectId);
        } else {
            console.warn("SelectionVitrine Init: exploreSelection function not found.");
        }

    } else {
        console.warn("SelectionVitrine Init: Core vitrine-specific event listeners (save/remove) and explore functionality were NOT initialized due to missing Project ID value or other setup issues.");
    }
    
    // Mark registered selections visually on page load
    setTimeout(function() { 
        markRegisteredSelections();
    }, 500);

    // Use a MutationObserver to wait for items to be loaded into the DOM
    const exploreResults = document.getElementById('explore-results');
    if (exploreResults) {
        const observer = new MutationObserver((mutationsList, obs) => {
            // Look for additions of nodes
            for(const mutation of mutationsList) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Try to attach save selection listeners when new content is loaded
                    setTimeout(attachSaveSelectionListeners, 100);
                    
                    // Now that content is loaded, override the selection function
                    window.updateItemSelection = function updateItemSelection(idItem, itemType, idfolder) {
                        if (window.justFinishedDragSelection) {
                            return;
                        }

                        let type = "";
                        let selected_item = "";
                        if (itemType === "object") {
                            type = "o";
                            selected_item = "o" + idItem + "_" + idfolder;
                        } else if (itemType === "file") {
                            type = "i";
                            selected_item = "i" + idItem + "_" + idfolder;
                        } else if (itemType === "folder") {
                            type = "d";
                            selected_item = "d" + idItem + "_" + idfolder;
                        } else if (itemType === "unico") {
                            type = "u";
                            selected_item = "u" + idItem + "_" + idfolder;
                        }

                        const el = document.getElementById("item-" + type + "-" + idItem + "_" + idfolder);
                        if (!el) {
                            return;
                        }

                        const checkbox = el.querySelector(".form-check-input");
                        const arrayItem = selected_item;

                        requestAnimationFrame(() => {
                            if (!vitrineSelections.has(arrayItem)) {
                                el.classList.add("checked");
                                el.classList.add("see-select");
                                vitrineSelections.add(arrayItem);
                                if (checkbox && !checkbox.checked) {
                                    checkbox.checked = true;
                                }
                            } else {
                                el.classList.remove("checked");
                                el.classList.remove("see-select");
                                vitrineSelections.delete(arrayItem);
                                if (checkbox?.checked) {
                                    checkbox.checked = false;
                                }
                            }

                            const nbSelectedElement = document.getElementById("nb-selected");
                            if (nbSelectedElement) {
                                nbSelectedElement.innerText = vitrineSelections.size.toString();
                            }

                            updateButtonsState(vitrineSelections.size);
                        });
                    }

                    // We can disconnect the observer now that we've done our setup
                    obs.disconnect();
                }
            }
        });

        observer.observe(exploreResults, { childList: true, subtree: true });
    }
});

// Modified registerSelection function to use current selections
function registerSelectionVitrine(idUser, idFolder, lng, currentSelection) {
    const ajaxData = { selection: currentSelection };
    const ajaxUrl = "/registerSelection/" + idUser + "," + idFolder;
    
    $.ajax({
        url: ajaxUrl,
        type: "POST",
        data: ajaxData,
    }).done(data => {
        // Try to show toast notification
        let toastShown = false;
        
        // First try with global variables
        if (window.toastContent && window.projectToast) {
            window.toastContent.innerText = data;
            window.projectToast.show();
            toastShown = true;
        } else {
            // Fallback: try to find and use the toast element directly
            const toastElement = document.getElementById("thesaurus-toast");
            if (toastElement) {
                const toastBody = toastElement.querySelector(".toast-body");
                if (toastBody) {
                    toastBody.innerText = data;
                    const toastInstance = bootstrap.Toast.getOrCreateInstance(toastElement);
                    toastInstance.show();
                    toastShown = true;
                }
            }
        }
        
        if (!toastShown) {
            // Show a simple alert as last resort
            alert(data);
        }

        // Clear current selections
        localStorage.setItem("selection", "[]");
        currentSelections.clear();
        
        // Clear vitrine selections and update UI
        vitrineSelections.clear();
        const nbSelectedElement = document.getElementById("nb-selected");
        if (nbSelectedElement) {
            nbSelectedElement.innerText = "0";
        }
        
        // Remove visual selection state from all selected items
        clearVisualSelectionState();
        
        updateButtonsState(0);
    }).fail(error => {
        // Try to show more helpful error message
        let errorMessage = 'Server error occurred';
        if (error.responseText) {
            try {
                const errorData = JSON.parse(error.responseText);
                errorMessage = errorData.message || errorData.error || error.responseText;
            } catch (e) {
                errorMessage = error.responseText;
            }
        }
        
        alert('Error saving selection: ' + errorMessage);
    });
}

// Update form submissions to use vitrine selections
document.addEventListener('DOMContentLoaded', function() {
    // Remove setTimeout to prevent race conditions
    function attachFormListeners() {
        const virtualFolderForms = document.querySelectorAll('.virtual-folder-container form');
        virtualFolderForms.forEach(form => {
            // Check if listener already attached to prevent duplicates
            if (form.hasAttribute('data-vitrine-listener')) {
                return;
            }
            form.setAttribute('data-vitrine-listener', 'true');
            
            form.addEventListener('submit', function(e) {
                if (vitrineSelections.size === 0) {
                    e.preventDefault();
                    alert(getNoItemsSelectedText());
                    return;
                }
                
                // Remove any existing vitrineSelection inputs to prevent duplicates
                const existingInput = form.querySelector('input[name="vitrineSelection"]');
                if (existingInput) {
                    existingInput.remove();
                }
                
                // Add vitrine selections as hidden input
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'vitrineSelection';
                hiddenInput.value = JSON.stringify(Array.from(vitrineSelections));
                form.appendChild(hiddenInput);
                
                console.log('[Form Submit] Adding vitrineSelection data:', Array.from(vitrineSelections));
            });
        });
    }
    
    // Attach immediately and also retry after a short delay in case DOM is still loading
    attachFormListeners();
    setTimeout(attachFormListeners, 100);
});

/**
 * Helper function to get the localized 'items' text
 * @returns {string} The localized text for 'items'
 */
function getItemsText() {
    const itemsTextElement = document.getElementById('items-text');
    return itemsTextElement ? itemsTextElement.value : 'items';
}

/**
 * Helper function to get the localized 'no items selected' text
 * @returns {string} The localized text for 'no items selected'
 */
function getNoItemsSelectedText() {
    const noItemsSelectedTextElement = document.getElementById('no-items-selected-text');
    return noItemsSelectedTextElement ? noItemsSelectedTextElement.value : 'No items selected';
}

/**
 * Function to update the selection count display
 */
function updateSelectionCount() {
    const countElement = document.getElementById('selection-count');
    const nbSelectedElement = document.getElementById('nb-selected');

    if (countElement) {
        const count = nbSelectedElement ? parseInt(nbSelectedElement.innerText) || 0 :
                     (vitrineSelections ? vitrineSelections.size : 0);
        countElement.innerText = count + ' ' + getItemsText();
    }
}

/**
 * Function to mark registered selections visually
 */
function markRegisteredSelections() {
    for (const id of registeredSelections) {
        const _index = id.lastIndexOf("_");
        const item_id = `item-${id.charAt(0)}-${id.substring(1, _index)}_${id.substring(_index + 1)}`;
        const htmlElement = document.getElementById(item_id);

        if (htmlElement) {
            // Mark as registered selection with a different visual style
            htmlElement.classList.add("registered-selection");
        }
    }
}

/**
 * Function to check all items from localStorage (for vitrine selections only)
 */
function checkAllFromStorage() {
    const countElement = document.getElementById('nb-selected');

    if (!countElement) {
        updateButtonsState(0);
        return;
    }

    // Only handle vitrine selections here
    const totalSelectionCount = vitrineSelections.size;
    countElement.innerText = totalSelectionCount.toString();

    const selectionCountElement = document.getElementById('selection-count');
    if (selectionCountElement) {
        selectionCountElement.innerText = totalSelectionCount + ' ' + getItemsText();
    }

    updateButtonsState(totalSelectionCount);
}

// Function to attach save selection event listeners
function attachSaveSelectionListeners() {
    const saveSelectionDropdown = document.getElementById('save-selection');
    if (saveSelectionDropdown && !saveSelectionDropdown.hasAttribute('data-listener-attached')) {
        
        // Mark as having listeners attached to prevent duplicates
        saveSelectionDropdown.setAttribute('data-listener-attached', 'true');
        
        // Attach event listener to the dropdown item itself and its child elements
        saveSelectionDropdown.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            
            if (!cachedUserId || !cachedProjectId) {
                alert(window.i18n && window.i18n.errorOccured ? window.i18n.errorOccured : 'An unexpected error occurred.');
                return;
            }
            // Use vitrine selections (items selected on this page) instead of currentSelections
            const currentSelection = Array.from(vitrineSelections);
            if (currentSelection.length === 0) {
                 alert(getNoItemsSelectedText());
                 return;
            }
            
            registerSelectionVitrine(cachedUserId, cachedProjectId, cachedLng, currentSelection);
        });
        
        // Also attach to the anchor element inside for better compatibility
        const saveAnchor = saveSelectionDropdown.querySelector('a.dropdown-item');
        if (saveAnchor) {
            saveAnchor.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                
                if (!cachedUserId || !cachedProjectId) {
                    alert(window.i18n && window.i18n.errorOccured ? window.i18n.errorOccured : 'An unexpected error occurred.');
                    return;
                }
                // Use vitrine selections (items selected on this page) instead of currentSelections
                const currentSelection = Array.from(vitrineSelections);
                
                if (currentSelection.length === 0) {
                     alert(getNoItemsSelectedText());
                     return;
                }
                
                registerSelectionVitrine(cachedUserId, cachedProjectId, cachedLng, currentSelection);
            });
        }
    }
}
