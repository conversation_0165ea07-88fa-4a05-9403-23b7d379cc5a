/* Fade in effect for lazy loaded images */
.lazy-image {
    transition: opacity 0.3s;
    opacity: 0.5;
    min-height: 80px;
}

.lazy-image[src^="/thumb/"],
.lazy-image[src^="/small/"],
.lazy-image[src^="/crop/"],
.lazy-image[src^="/hhdd/"],
.lazy-image[src^="http"] {
    opacity: 1;
}

/* Style for placeholder images */
img[src="/assets/images/placeholder.png"] {
    max-height: 80px;
    object-fit: contain;
    opacity: 0.3;
}

/* .card-link, .list-link {
    background-color: transparent;
} */

.card-body, .list-vitrine-content {
    background-color: #f8f8f8;
}

/* Lazy image container styles */
.lazy-image-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80px;
    width: 100%;
    background-color: #f8f8f8;
    overflow: hidden;
}

/* Ensure all lazy-load-card elements have consistent styling */
.lazy-load-card {
    transition: all 0.2s ease;
    border: none !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    background-color: #f8f9fa !important;
    overflow: hidden !important;
}
