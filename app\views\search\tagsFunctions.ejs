<script>
  // Fonctions
  function escapeHtml(text) {
    if (!text) return '';
    return text.replace(/[&<>]/g, function(m) {
      switch (m) {
        case '&': return '&amp;';
        case '<': return '&lt;';
        case '>': return '&gt;';
        default: return m;
      }
    });
  }

  function escapeAttr(text) {
    if (!text) return '';
    return text.replace(/"/g, '&quot;');
  }

  function createTag(value, id) {
    if (id === undefined) id = value
    // value attribute is double-quote escaped, inner text is HTML-escaped
    return '<span class="pb-1"><span class="tag-or px-1"><%=__("or")%></span><span class="tag p-1"><span class="tag-value pr-1" value="' + escapeAttr(id) + '">' + escapeHtml(value) + '</span><i class="tag-erase fas fa-times btn-hand"></i></span></span>'
  }

  function addTag(input, value, id) {
    if (value.length > 0)
      input.before(createTag(value, id))
  }

  function emptyTags() {
    $('.tag').parent('span').remove()
  }

  // Events
  // Ajoute un tag quand on appuie sur Entrée
  $(document).on('keydown', '.tag-list input, .tag-list-actor input', function(e) {
    if (e.originalEvent.keyCode === 13 && $(this).val().length > 0) {
      let value = $(this).val().trim()
      $(this).val('')
      addTag($(this), value)
      // newSearch()
    }
  })

  // Supprime le tag quand on clique sur la croix
  $(document).on('click', '.tag-erase', function (e) {
    $(this).parent('.tag').parent('span').remove()
    // newSearch()
  })
</script>