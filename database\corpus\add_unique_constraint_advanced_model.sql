ALTER TABLE corpus_actor ADD CONSTRAINT corpus_unique_actor UNIQUE (actor_type, name, identifier);
ALTER TABLE corpus_actor_item ADD CONSTRAINT corpus_unique_actor_item UNIQUE (id_actor, id_metadata, id_item, item_type);

ALTER TABLE corpus_datation ADD CONSTRAINT corpus_unique_datation UNIQUE (date_min, date_max, date_literal, id_periodo);
ALTER TABLE corpus_datation_item ADD CONSTRAINT corpus_unique_datation_item UNIQUE (id_datation, id_metadata, id_item, item_type);

ALTER TABLE corpus_location ADD CONSTRAINT corpus_unique_location UNIQUE (name, uri_geonames, longlat);
ALTER TABLE corpus_location_item ADD CONSTRAINT corpus_unique_location_item UNIQUE (id_location, id_metadata, id_item, item_type);