import { z } from "zod";
import { t } from "~/trpcContext";
import { branch, id, language } from "~/types/schemas";
import {
  overall_full_output,
  overall_projects,
  projects_card_output,
  projects_from_overall_output,
  projects_full_output,
  projects_map_output,
} from "~/types/schemas/projects";

const projects_root_list_output = z.object({ id, name: z.string(), folder_name: z.string(), path: z.string() }).array();

const limited_project_root_output = z
  .object({ id, name: z.string(), folder_name: z.string(), path: z.string() })
  .array();

const items_list_output = z
    .object({ id, type:z.string(), name: z.string(), id_folder: z.number() })
    .array();

const items_list_outputNB = z
    //.object({ nb_files: z.number(), nb_objects: z.number(), nb_unicos: z.number(), nb_folders: z.number(),  })
    .object({ nb_files: z.string(), nb_objects: z.string(), nb_unicos: z.string(), nb_folders: z.string(),  })
    .array();

export default t.router({
  /**
   * Get the full info from a project
   * @param branch The affected branch
   * @param language The project's display name language
   * @param project_id The id of the project
   * @returns The project info
   */
  projectsFull: t.procedure.input(z.object({ branch, language, project_id: id })).query(async ({ input, ctx }) => {
    // sur les thesaurus maison = nomenclature
    let   querynomen = '', querymulti = '', query = '';
    if (input.branch === 'pft3d') {
        querynomen = `SELECT get_projectfull_nomen2('${input.language}', id) AS nomeninfo
        FROM ${input.branch}_folder
        WHERE folder_passport = 'project' AND visible = 'true' AND id = $1`;
        querymulti = `SELECT get_info_multi_gen5('${input.language}', id, '${input.branch}', 'folder') as thesmultiinfo
        FROM ${input.branch}_folder
        WHERE (folder_passport = 'project' OR folder_passport = 'overall') AND visible = 'true' AND id = $1`;

        query =
            `SELECT get_projectfull('${input.language}', id) AS project ` +
            `FROM ${input.branch}_folder WHERE (folder_passport = 'project' OR folder_passport = 'overall') AND id = $1 limit 1`;
    }
    else if (input.branch === 'corpus') { // on utilise get_info_multi_gen4 qui regarde aussi code (metadata table) = identifier (multi item table)
        querynomen = `SELECT get_projectfull_nomen2('${input.language}', id) AS nomeninfo
        FROM ${input.branch}_folder
        WHERE id_parent IS NULL AND visible = 'true' AND id = $1`;
        querymulti = `SELECT get_info_multi_gen5('${input.language}', id, '${input.branch}', 'folder') as thesmultiinfo
        FROM ${input.branch}_folder
        WHERE id_parent IS NULL AND visible = 'true' AND id = $1`;

        query =
            `SELECT get_projectfull_corpus('${input.language}', id) AS project ` +
            `FROM ${input.branch}_folder WHERE (id_parent IS NULL OR folder_passport = 'overall') AND id = $1 limit 1`;
    }

    const data = await ctx.database.oneOrNone(query, input.project_id);
    if (!data) return null;
    const { project } = data;
    const nomenclature = await ctx.database.any(querynomen, input.project_id);
    const multi = await ctx.database.any(querymulti, input.project_id);

    const project_image = await ctx.database.oneOrNone(
      ` SELECT image_path FROM ${input.branch}_project_image WHERE id = $1 ` ,
      input.project_id,
    );

    const image = project_image?.image_path ?? null;

    return projects_full_output.parse({
      data: project,
      nomenclature: nomenclature.map((el) => el.nomeninfo),
      multi: multi.map((el) => el.thesmultiinfo),
      image,
    });
  }),
  /**
   * Get the full info from an overall project
   * @param branch The affected branch
   * @param language The project's display name language
   * @param project_id The id of the overall project
   * @returns The overall project info
   */
  overallFull: t.procedure
    .input(
      z.object({
        branch,
        language,
        project_id: id,
      }),
    )
    .output(overall_full_output)
    .query(async ({ input, ctx }) => {
      const data = await ctx.database.oneOrNone("SELECT get_overallprojectfull($1, $2) AS project", [
        input.language,
        input.project_id,
      ]);

      return overall_full_output.parse(data.project);
    }),
  /**
   * Get the list of all the overall projects
   * @param branch The affected branch
   * @returns The overall projects list
   */
  overallProjects: t.procedure
    .input(z.object({ branch }))
    .output(overall_projects)
    .query(async ({ input, ctx }) => {
      const projects = await ctx.database.manyOrNone(
        `SELECT id, name, id_parent, folder_path, passport, date_creation, rank, global_rank, status, id_site, visible, uploadable, commentable, id_representative_picture, folder_passport, file_passport, nb_tot_images_gen(id, $1) as nb_images, get_loc_from_id_gen(id, $1) AS location, folder_name, date_update 
        FROM ${input.branch}_folder
        WHERE folder_passport = 'overall' AND id_parent IS NULL AND visible = 'true'
        ORDER BY name `,
        input.branch,
      );
      return overall_projects.parse(projects);
    }),
  /**
   * Get the list of all the projects within an overall project
   * @param branch The affected branch
   * @param id The overall project id
   * @returns The overall projects list
   */
  projectsIdFromOverall: t.procedure
    .meta({ description: "Get the list of all the projects within an overall project" })
    .input(z.object({ branch, project_id: id }))
    .output(projects_from_overall_output)
    .query(async ({ input, ctx }) => {
      const query = `SELECT id, name, id_parent, folder_path, folder_name, passport, date_creation,
        rank::integer, global_rank::integer, status, id_site::integer, visible, uploadable, commentable,
        id_representative_picture, folder_passport, nb_images::integer, date_update, natsort::integer,
        nb_objects::integer, nb_tot_images::integer, nb_tot_objects::integer, doi, nb_unicos::integer, nb_tot_unicos::integer
        FROM ${input.branch}_folder f
        INNER JOIN ${input.branch}_folder_overall fo ON fo.id_folder = f.id
        WHERE fo.id_overall = ${input.project_id} AND f.folder_passport = 'project'
        ORDER BY name`;

      const data = await ctx.database.manyOrNone(query);

      return projects_from_overall_output.parse(data);
    }),
  /**
   * Get the list of all the projects within displayable on card page with map info
   * @param branch The affected branch
   * @returns The overall projects list
     */
  projectsCard: t.procedure
    .meta({
      openapi: {
        tags: ["projects"],
        method: "GET",
        path: "/projects.projectsCard",
        summary: "Get all the projects for cards listing",
      },
    })
    .input(z.object({ branch }))
    .output(projects_card_output)
    .query(async ({ input, ctx }) => {
        const query =
        ` SELECT f.id, f.name,f.folder_name, f.date_creation, f.id_representative_picture, f.rank,
        nb_tot_images_gen(f.id, $1) AS nbimages, p.value[1] as loc, s.${input.branch}_url as url,
         get_first_metadata( f.id, $1, 'folder', 'project') AS project_name
        , concat('https://www.archeogrid.fr/projectv/', f.id) as urlProject
        , folder3d.nb_tot_images as tot_images_3d, folder3d.nb_images as images_3d, folder3d.id as id_3d, folder3d.status as status_3d
        FROM ${input.branch}_folder f INNER JOIN ${input.branch}_passport p ON p.id_item = f.id
        INNER JOIN ${input.branch}_metadata m ON m.id = p.id_metadata
        INNER JOIN ${input.branch}_metadata_label l ON l.id_metadata = m.id
        INNER JOIN ${input.branch}_site s ON s.id = f.id_site
        LEFT OUTER JOIN ${input.branch}_folder folder3d ON folder3d.folder_path <@ f.folder_path  AND folder3d.folder_name = '3DOnline'
        AND folder3d.status = 'public' AND folder3d.nb_images != 0
        WHERE l.language = 'fr' AND l.label = 'Localisation' AND item_type = 'folder'
        AND f.id_parent IS NULL AND f.visible = 'true' ORDER BY rank` ;
      const data = await ctx.database.manyOrNone(query, input.branch);
      return projects_card_output.parse(data);
    }),
  projectsMap: t.procedure
    .meta({
      openapi: {
        tags: ["projects"],
        method: "GET",
        path: "/projects.projectsMap",
        summary: "Get all the projects visible on the global map",
      },
    })
    .input(z.object({ branch }))
    .output(projects_map_output.array())
    .query(async ({ input, ctx }) => {
      const data = await ctx.database.any(
        `SELECT p.id, name,folder_name, date_creation, id_representative_picture, rank,
         nb_tot_images_gen(p.id, $1) AS nbimages, ${input.branch}_url as url,
         get_loc_from_id_pass_gen(p.id, $1) AS loc,
         get_first_metadata( p.id, $1, 'folder', 'project') AS project_name
         FROM ${input.branch}_folder p
         INNER JOIN ${input.branch}_site s ON s.id = p.id_site
         WHERE id_parent IS NULL
         AND folder_passport  = 'project'
         AND visible = 'true' ORDER BY rank `, input.branch
      );
      return projects_map_output.array().parse(data);
    }),
  projectsRootList: t.procedure
    .input(z.object({ branch, user_id: id }))
    .output(projects_root_list_output)
    .query(async ({ input, ctx }) => {
      if (input.branch === "pft3d") {
        const data = await ctx.database.manyOrNone(
          `SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
          FROM ${input.branch}_folder
          WHERE folder_name IS NOT NULL AND id_parent IS NULL
          ORDER BY folder_name`,
        );
        return projects_root_list_output.parse(data);
      }

      if (input.branch === "conservatoire3d") {
        const data = await ctx.database.manyOrNone(
          `SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
          FROM ${input.branch}_folder
          WHERE folder_name IS NOT NULL AND folder_passport = 'deposit'
          ORDER BY folder_name`,
        );
        return projects_root_list_output.parse(data);
      }

      const data = await ctx.database.manyOrNone(
        `SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
        FROM ${input.branch}_folder
        WHERE folder_name IS NOT NULL AND id_parent IS NULL
        ORDER BY folder_name`,
      );
      return projects_root_list_output.parse(data);
    }),
  limitedProjectsRoot: t.procedure
    .input(z.object({ branch, user_id: id }))
    .output(limited_project_root_output)
    .query(async ({ input, ctx }) => {
      switch(input.branch) {
        case "pft3d":
          const data_pft3d = await ctx.database.manyOrNone(
            `SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
            FROM ${input.branch}_folder fo
            INNER JOIN ${input.branch}_user_folder uf ON uf.id_folder = fo.id AND uf.id_user = $1
            WHERE folder_name IS NOT NULL AND id_parent IS NULL AND folder_passport = 'project'
            ORDER BY folder_name`,
            input.user_id,
          );
          return limited_project_root_output.parse(data_pft3d);
        case "conservatoire3d":
          const data_cnd3d = await ctx.database.manyOrNone(
            `SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
            FROM ${input.branch}_folder
            WHERE folder_name IS NOT NULL AND folder_passport = 'deposit'
            ORDER BY folder_name`,
          );
          return limited_project_root_output.parse(data_cnd3d);
        case "corpus":
          const data_corpus = await ctx.database.manyOrNone(
            `SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
            FROM ${input.branch}_folder f
            INNER JOIN ${input.branch}_user_folder uf ON uf.id_folder = f.id AND uf.id_user = $1
            WHERE folder_name IS NOT NULL AND id_parent IS NULL AND folder_passport = 'project'`,
            input.user_id
          );
          return limited_project_root_output.parse(data_corpus);
        default:
          console.log(`PROJECTS CONTROLLER ERROR: UNKNOWN BRANCH '${input.branch}'!`);
          const data = await ctx.database.manyOrNone(
            `SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
            FROM ${input.branch}_folder
            WHERE folder_name IS NOT NULL AND id_parent IS NULL
            ORDER BY folder_name`,
          );
          return limited_project_root_output.parse(data);
      }
    }),
  getProjectId: t.procedure.input(z.object({ branch, file_id: id })).query(async ({ ctx, input }) => {
    const query = `SELECT subltree(fo.folder_path, 0, 1) as project_id
        FROM ${input.branch}_folder fo
        INNER JOIN ${input.branch}_file fi ON fi.id_folder = fo.id
        WHERE fi.id = ${input.file_id} `;

    const result = await ctx.database.oneOrNone(query);

    return id.nullable().parse(Number.parseInt(result?.project_id) || null);
  }),
  itemsProjectList: t.procedure
        .input(z.object({branch, project_id: id, loadAll: z.boolean().optional(), nb_items: z.number().optional(), offset: z.number().optional()}))
        .output(items_list_output)
        .query(async ({input, ctx}) => {
          const baseQuery = `
            SELECT * FROM (
              SELECT id::int, 'file' as type, name, id_folder
              FROM ${input.branch}_file
              WHERE id_folder IN (SELECT id FROM ${input.branch}_folder WHERE folder_path <@ '${input.project_id}')
            UNION ALL
              SELECT distinct id::int, 'object' as type, name, id_folder
              FROM ${input.branch}_object
              WHERE id_folder IN (SELECT id FROM ${input.branch}_folder WHERE folder_path <@ '${input.project_id}')
            UNION ALL
              SELECT distinct u.id::int, 'unico' as type, u.name, f.id_folder
              FROM ${input.branch}_unico u
              INNER JOIN ${input.branch}_file f ON u.id_file = f.id
              WHERE id_file IN (
                SELECT fi.id
                FROM ${input.branch}_file fi
                WHERE id_folder IN (
                  SELECT fo.id
                  FROM ${input.branch}_folder fo
                  WHERE folder_path <@ '${input.project_id}'
                )
              )
            ORDER BY id_folder
            ) AS LISTING`;

          let querySuffix = "";
          if (!input.loadAll) {
            const limitPart = (typeof input.nb_items === 'number' && input.nb_items > 0)
              ? `LIMIT ${input.nb_items}`
              : '';
            const offsetPart = (typeof input.offset === 'number')
              ? `OFFSET ${input.offset}`
              : 'OFFSET 0'; // Default to OFFSET 0 if not specified and not loadAll

            querySuffix = ` ${limitPart} ${offsetPart}`.trim();
          }
          
          const finalQuery = `${baseQuery}${querySuffix}`;
          const data = await ctx.database.manyOrNone(finalQuery);

          return items_list_output.parse(data);
        }),
    itemsProjectListNB: t.procedure
        .input(z.object({branch, project_id: id}))
        .output(items_list_outputNB)
        .query(async ({input, ctx}) => {
            const data = await ctx.database.manyOrNone(`
            SELECT
              (SELECT COUNT(*) FROM ${input.branch}_file WHERE id_folder IN (SELECT id FROM ${input.branch}_folder WHERE folder_path <@ '${input.project_id}')) AS nb_files,
              (SELECT COUNT(*) FROM ${input.branch}_object WHERE id_folder IN (SELECT id FROM ${input.branch}_folder WHERE folder_path <@ '${input.project_id}')) AS nb_objects,
              (SELECT COUNT(*) FROM ${input.branch}_unico WHERE id_file IN
                     (SELECT id FROM ${input.branch}_file WHERE id_folder IN (SELECT id FROM ${input.branch}_folder WHERE folder_path <@ '${input.project_id}'))) AS nb_unicos,
              (SELECT COUNT(*) FROM ${input.branch}_folder WHERE folder_path <@ '${input.project_id}') AS nb_folders
            `
            );

            return items_list_outputNB.parse(data);
        }),

    getProjectFolders: t.procedure
      .input(z.object({branch, project_id: id}))
      .query(async ({ctx, input}) => {
        const rootFolder = await ctx.database.oneOrNone(`
          SELECT id, name, folder_name, ${input.branch}_get_real_path(id) as path
          FROM ${input.branch}_folder
          WHERE id = $1
        `, input.project_id);

        if(!rootFolder){
          return [];
        }

        const folderList: {id: number, name: string, folder_name: string, path: string}[] = [rootFolder];
        for await (const folder of ctx.folder.getChildFolders(input.branch, input.project_id)) {
          folderList.push(folder);
        }

        return folderList;
      }),

     getProjectIdByName: t.procedure
     .input(z.object({branch, name: z.string()}))
     .output(z.number().nullable())
     .query(async ({ctx, input}) => {
       const result = await ctx.database.oneOrNone(`
          SELECT id
          FROM ${input.branch}_folder
          WHERE folder_name = $1`
          , [input.name]
       );

        return z.number().nullable().parse(result?.id);
     }),
});
