/**
 * Mobile-specific functionality for project vitrine pages
 */
(function() {
    let isLoading = false;
    let isMobileListView = false;
    let allMobileItems = [];
    let msnry = null;
    let resizeTimeout;
    let isCurrentlyMobileView = false;
    let scrollToTopButton = null;

    function debounce(func, delay) {
        return function(...args) {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    document.addEventListener('DOMContentLoaded', initMobileVitrine);

    function initMobileVitrine() {
        setupMobileItemView();
        createScrollToTopButton();

        isCurrentlyMobileView = window.innerWidth <= 768;
        const footerElement = document.querySelector('footer');

        window.addEventListener('scroll', handleLazyLoadScroll);
        window.addEventListener('resize', debouncedHandleResize);

        if (isCurrentlyMobileView) {
            if (footerElement) footerElement.style.display = 'none';
            ensureMobileCoreUI();
            updateScrollToTopButtonVisibility();
            setupInfiniteScroll();
            const exploreResults = document.getElementById('explore-results');
            const gridView = document.getElementById('explore-grid');
            const listView = document.querySelector('.list-vitrine-container');
            const desktopGridVisible = gridView && window.getComputedStyle(gridView).display !== 'none' && gridView.children.length > 0;
            const desktopListVisible = listView && window.getComputedStyle(listView).display !== 'none' && listView.children.length > 0;

            if (exploreResults && (desktopGridVisible || desktopListVisible)) {
                exploreResults.style.opacity = '0';
                exploreResults.style.minHeight = '75vh';
                document.body.style.overflow = 'hidden';
                convertToMobileView();
            }
        } else {
            if (footerElement) footerElement.style.display = '';
            switchToDesktopView();
            const exploreResults = document.getElementById('explore-results');
            if (exploreResults) {
                 exploreResults.style.opacity = '1';
                 exploreResults.style.minHeight = '';

                 const gridView = document.getElementById('explore-grid');
                 const listView = document.querySelector('.list-vitrine-container');
                 if (typeof display === 'string') {
                    if (display === 'grid' && gridView) {
                        gridView.style.display = '';
                        if (listView) listView.style.display = 'none';
                    } else if (display === 'list' && listView) {
                        listView.style.display = '';
                        if (gridView) gridView.style.display = 'none';
                    } else {
                        if (gridView) gridView.style.display = ''; else if (listView) listView.style.display = '';
                        if (gridView && listView && gridView.style.display === '') listView.style.display = 'none';
                    }
                 } else {
                    if (gridView) gridView.style.display = '';
                    if (listView) listView.style.display = 'none';
                 }

                 const mobileItemsDisplayArea = document.getElementById('mobile-items-display-area');
                 if (mobileItemsDisplayArea) mobileItemsDisplayArea.innerHTML = '';
            }
        }
    }

    function setupInfiniteScroll() {
        const loader = document.createElement('div');
        loader.className = 'mobile-loader';
        loader.innerHTML = '<div class="mobile-loader-spinner"></div><span>Loading all items...</span>';

        const exploreResults = document.getElementById('explore-results');
        if (exploreResults) {
            exploreResults.appendChild(loader);
        }

        window.removeEventListener('scroll', handleLazyLoadScroll);
        
        window.addEventListener('scroll', function() {
            updateScrollToTopButtonVisibility();
            
            const hasItemsToReveal = allMobileItems.some(item => item.classList.contains('needs-lazy-reveal'));
            if (!hasItemsToReveal) return;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
            const scrollThreshold = scrollTop + viewportHeight + 200;

            const itemsToReveal = allMobileItems.filter(item => {
                if (!item.classList.contains('needs-lazy-reveal')) return false;
                const rect = item.getBoundingClientRect();
                return rect.top <= scrollThreshold;
            });

            if (itemsToReveal.length > 0) {
                revealVisibleMobileItems();
            }
        });
    }

    function handleLazyLoadScroll() {
        updateScrollToTopButtonVisibility();
        
        const hasItemsToReveal = allMobileItems.some(item => item.classList.contains('needs-lazy-reveal'));
        if (!hasItemsToReveal) return;

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
        const scrollThreshold = scrollTop + viewportHeight + 200;

        const itemsToReveal = allMobileItems.filter(item => {
            if (!item.classList.contains('needs-lazy-reveal')) return false;
            const rect = item.getBoundingClientRect();
            return rect.top <= scrollThreshold;
        });

        if (itemsToReveal.length > 0) {
            revealVisibleMobileItems();
        }
    }

    function revealVisibleMobileItems(onCompleteCallback) {
        if (isLoading || !allMobileItems.length) {
            if (typeof onCompleteCallback === 'function') onCompleteCallback();
            return;
        }

        let newlyVisibleItems = [];
        let processedCount = 0;
        let madeVisibleCount = 0;

        for (const item of allMobileItems) {
            processedCount++;
            if (item.classList.contains('needs-lazy-reveal')) {
                const isInViewport = isElementInViewport(item);

                if (isInViewport) {
                    madeVisibleCount++;
                    const img = item.querySelector('img[data-src]');
                    if (img) {
                        const dataSrc = img.getAttribute('data-src');
                        img.src = dataSrc;
                        img.removeAttribute('data-src');
                    }
                    item.classList.remove('needs-lazy-reveal');
                    newlyVisibleItems.push(item);
                }
            }
        }

        if (newlyVisibleItems.length > 0 && msnry) {
            imagesLoaded(newlyVisibleItems, function() {
                if (newlyVisibleItems.length > 0) {
                    msnry.layout();
                }
                if (typeof onCompleteCallback === 'function') onCompleteCallback();
            });
        } else {
            if (typeof onCompleteCallback === 'function') onCompleteCallback();
        }
    }

    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
        const viewportWidth = window.innerWidth || document.documentElement.clientWidth;

        const vertInView = rect.top < viewportHeight && rect.bottom > 0;
        const horInView = rect.left < viewportWidth && rect.right > 0;

        return vertInView && horInView;
    }

    function createMobileMenuButton() {
        if (document.getElementById('mobile-menu-button') && document.getElementById('mobile-view-toggle')) {
            return;
        }

        const menuButtonContainer = document.createElement('div');
        menuButtonContainer.className = 'mobile-menu-button-container';
        menuButtonContainer.classList.add('show-on-mobile');

        const leftContainer = document.createElement('div');
        leftContainer.className = 'mobile-menu-left';

        const menuButton = document.createElement('div');
        menuButton.id = 'mobile-menu-button';
        menuButton.innerHTML = '<i class="fas fa-folder"></i>';
        menuButton.title = 'Show Folders';
        menuButton.setAttribute('aria-label', 'Show Folders');
        menuButton.addEventListener('click', toggleMobileMenu);
        leftContainer.appendChild(menuButton);

        const menuLabel = document.createElement('div');
        menuLabel.className = 'mobile-menu-label';
        menuLabel.textContent = 'Folders';
        leftContainer.appendChild(menuLabel);

        menuButtonContainer.appendChild(leftContainer);

        const viewToggleButton = document.createElement('div');
        viewToggleButton.id = 'mobile-view-toggle';
        viewToggleButton.innerHTML = '<i class="fas fa-list-ul"></i>';
        viewToggleButton.title = 'Toggle View';
        viewToggleButton.setAttribute('aria-label', 'Toggle View');
        viewToggleButton.addEventListener('click', toggleMobileItemView);
        menuButtonContainer.appendChild(viewToggleButton);

        const grosContainer = document.getElementById('GROS');
        if (grosContainer) {
            grosContainer.parentNode.insertBefore(menuButtonContainer, grosContainer);
        } else {
            document.body.appendChild(menuButtonContainer);
        }
    }

    function createMobileOverlay() {
        if (document.getElementById('mobile-overlay')) {
            return;
        }

        const overlay = document.createElement('div');
        overlay.id = 'mobile-overlay';

        const closeIcon = document.createElement('div');
        closeIcon.innerHTML = '<i class="fas fa-times"></i>';
        closeIcon.addEventListener('click', closeMobileMenu);

        overlay.addEventListener('click', closeMobileMenu);

        overlay.appendChild(closeIcon);
        document.body.appendChild(overlay);
    }

    function createScrollToTopButton() {
        if (document.getElementById('mobile-scroll-to-top')) {
            return;
        }
        scrollToTopButton = document.createElement('button');
        scrollToTopButton.id = 'mobile-scroll-to-top';
        scrollToTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        scrollToTopButton.title = 'Scroll to top';
        scrollToTopButton.setAttribute('aria-label', 'Scroll to top');
        scrollToTopButton.style.display = 'none';
        scrollToTopButton.style.position = 'fixed';
        scrollToTopButton.style.bottom = '20px';
        scrollToTopButton.style.right = '20px';
        scrollToTopButton.style.zIndex = '1050';
        scrollToTopButton.style.padding = '10px 15px';
        scrollToTopButton.style.fontSize = '1.2em';
        scrollToTopButton.style.backgroundColor = '#f8f9fa';
        scrollToTopButton.style.border = '1px solid #dee2e6 !important';
        scrollToTopButton.style.borderRadius = '50%';
        scrollToTopButton.style.cursor = 'pointer';
        scrollToTopButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1) !important';
        scrollToTopButton.style.outline = 'none !important';

        scrollToTopButton.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        document.body.appendChild(scrollToTopButton);
    }

    function updateScrollToTopButtonVisibility() {
        if (!scrollToTopButton) return;

        if (window.pageYOffset > (window.innerHeight / 2)) {
            scrollToTopButton.style.display = 'block';
        } else {
            scrollToTopButton.style.display = 'none';
        }
    }

    function setupMobileFoldersContainer() {
        if (document.getElementById('mobile-folders-container')) {
            return;
        }

        const mobileFoldersContainer = document.createElement('div');
        mobileFoldersContainer.id = 'mobile-folders-container';

        const projectFoldersContainer = document.getElementById('project-folders-container');
        if (projectFoldersContainer) {
            const clonedContainer = projectFoldersContainer.cloneNode(true);

            clonedContainer.classList.remove('hide-on-mobile');

            const mobileProjectFolders = document.createElement('div');
            mobileProjectFolders.id = 'mobile-project-folders';
            mobileProjectFolders.appendChild(clonedContainer);

            mobileFoldersContainer.appendChild(mobileProjectFolders);

            const folderItems = mobileFoldersContainer.querySelectorAll('.folder-item');
            folderItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    const folderId = item.getAttribute('id') ||
                                    item.getAttribute('data-folder-id');

                    if (folderId) {
                        if (typeof window.exploreFolderVitrine === 'function') {
                            window.exploreFolderVitrine(folderId);
                        }

                        setTimeout(closeMobileMenu, 300);
                    }

                    e.preventDefault();
                    e.stopPropagation();
                });
            });

            const checkboxes = mobileFoldersContainer.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function(e) {
                    e.stopPropagation();
                });
            });
        }

        document.body.appendChild(mobileFoldersContainer);
    }

    function setupMobileItemView() {
        window.toggleMobileItemView = toggleMobileItemView;

        const originalExploreFolderVitrine = window.exploreFolderVitrine;

        window.exploreFolderVitrine = function(folderId) {
            currentFolderId = folderId;

            if (!isCurrentlyMobileView) {
                const exploreResults = document.getElementById('explore-results');
                if (exploreResults) {
                    exploreResults.style.opacity = '1';
                    exploreResults.style.minHeight = '';

                    const gridView = document.getElementById('explore-grid');
                    const listView = document.querySelector('.list-vitrine-container');
                    const mobileItemsDisplayArea = document.getElementById('mobile-items-display-area');
                    if (mobileItemsDisplayArea) mobileItemsDisplayArea.style.display = 'none';
                }
                originalExploreFolderVitrine(folderId);
                return;
            }

            isLoading = true;
            allMobileItems = [];

            const loader = document.querySelector('.mobile-loader');
            if (loader) {
                loader.classList.add('active');
                loader.innerHTML = '<div class="mobile-loader-spinner"></div><span>Loading all items...</span>';
            }
            
            const exploreResults = document.getElementById('explore-results');
            if (exploreResults) {
                 exploreResults.style.opacity = '0';
                 exploreResults.style.transition = 'opacity 0.3s ease-in-out';
                 exploreResults.style.minHeight = '75vh';

                 const gridView = document.getElementById('explore-grid');
                 const listView = document.querySelector('.list-vitrine-container');
                 if (gridView) gridView.style.display = 'none';
                 if (listView) listView.style.display = 'none';
                 const mobileItemsDisplayArea = document.getElementById('mobile-items-display-area');
                 if (mobileItemsDisplayArea) mobileItemsDisplayArea.innerHTML = '';
            }

            originalExploreFolderVitrine(folderId);
            setTimeout(() => {
                if (isCurrentlyMobileView) {
                    convertToMobileView();
                }
            }, 1500);
        };

        if (window.switchDisplay) {
            const originalSwitchDisplay = window.switchDisplay;

            window.switchDisplay = function(displayType) {
                originalSwitchDisplay(displayType);
                setTimeout(convertToMobileView, 300);
            };
        }

        setTimeout(() => {
            convertToMobileView();

            const folderElement = document.querySelector('.folder-selected');
            if (folderElement) {
                currentFolderId = folderElement.getAttribute('id') ||
                                 folderElement.getAttribute('data-folder-id');
            }
        }, 500);
    }

    function convertToMobileView() {
        if (window.innerWidth > 768 && !isCurrentlyMobileView) {
            const mobileContainer = document.getElementById('mobile-items-display-area');
            if (mobileContainer) {
                mobileContainer.innerHTML = '';
                mobileContainer.style.display = 'none';
            }

            const exploreResults = document.getElementById('explore-results');
            if (exploreResults) {
                exploreResults.style.opacity = '1';
                exploreResults.style.transition = '';
                exploreResults.style.minHeight = '';

                const gridView = document.getElementById('explore-grid');
                const listView = document.querySelector('.list-vitrine-container');

                if (typeof display === 'string') {
                    if (display === 'grid' && gridView) {
                        gridView.style.display = '';
                        if (listView) listView.style.display = 'none';
                    } else if (display === 'list' && listView) {
                        listView.style.display = '';
                        if (gridView) gridView.style.display = 'none';
                    } else {
                        if (gridView) gridView.style.display = ''; else if (listView) listView.style.display = '';
                        if (gridView && listView && gridView.style.display === '') listView.style.display = 'none';
                    }
                } else {
                    if (gridView) gridView.style.display = '';
                    if (listView) listView.style.display = 'none';
                }
            }
            isLoading = false;
            const loader = document.querySelector('.mobile-loader');
            if(loader) loader.classList.remove('active');
            document.body.style.overflow = '';
            return;
        }

        const exploreResults = document.getElementById('explore-results');
        if (!exploreResults) return;

        let mobileContainer = document.getElementById('mobile-items-display-area');
        const isInitialConversionFromDesktop = !mobileContainer || (mobileContainer.children.length === 0 && allMobileItems.length === 0 && (document.getElementById('explore-grid')?.children.length > 0 || document.querySelector('.list-vitrine-container')?.children.length > 0) );
        
        const isViewToggle = mobileContainer && mobileContainer.classList.contains('mobile-item-container') &&
                           ((isMobileListView && !mobileContainer.classList.contains('list-view')) ||
                            (!isMobileListView && mobileContainer.classList.contains('list-view')));
        
        if (!isInitialConversionFromDesktop && !isViewToggle && mobileContainer) {
            return;
        }

        if (mobileContainer) {
            mobileContainer.style.transition = 'none';
            mobileContainer.style.opacity = '0';
        } else {
            mobileContainer = document.createElement('div');
            mobileContainer.id = 'mobile-items-display-area';
            mobileContainer.style.transition = 'none';
            mobileContainer.style.opacity = '0';
            exploreResults.appendChild(mobileContainer);
        }

        if (isInitialConversionFromDesktop) {
            if (mobileContainer) mobileContainer.innerHTML = '';
            
            const gridView = document.getElementById('explore-grid');
            const listView = document.querySelector('.list-vitrine-container');
            if (gridView && exploreResults.contains(gridView)) gridView.style.display = 'none';
            if (listView && exploreResults.contains(listView)) listView.style.display = 'none';

            const sourceItemsDesktop = gridView
                ? Array.from(gridView.querySelectorAll('.card-link'))
                : (listView ? Array.from(listView.querySelectorAll('.list-link')) : []);

            allMobileItems = [];
            sourceItemsDesktop.forEach(item => {
                const mobileItem = createMobileItem(item);
                allMobileItems.push(mobileItem);
                mobileContainer.appendChild(mobileItem);
                mobileItem.classList.add('needs-lazy-reveal');
            });
        } else if (mobileContainer) {
            allMobileItems = Array.from(mobileContainer.children);
        } else {
            allMobileItems = [];
        }

        if (!mobileContainer) return;

        mobileContainer.className = isMobileListView ? 'mobile-item-container list-view' : 'mobile-item-container';

        if (!isMobileListView) {
            if (msnry) {
                msnry.destroy();
                msnry = null;
            }
            allMobileItems.forEach(item => {
                item.style.width = '';
                item.style.position = ''; item.style.left = ''; item.style.top = '';
                item.style.transform = ''; item.style.marginBottom = '';
            });

            if (allMobileItems.length > 0) {
                imagesLoaded(mobileContainer, function() {
                    void mobileContainer.offsetHeight;

                    const gutterWidth = 10;
                    const containerStyles = window.getComputedStyle(mobileContainer);
                    const paddingLeft = parseFloat(containerStyles.paddingLeft) || 0;
                    const paddingRight = parseFloat(containerStyles.paddingRight) || 0;

                    let effectiveContainerWidth = mobileContainer.clientWidth;
                    if (effectiveContainerWidth === 0 && exploreResults && window.getComputedStyle(exploreResults).display !== 'none') {
                        void exploreResults.offsetHeight;
                        effectiveContainerWidth = exploreResults.clientWidth;
                    }
                    if (effectiveContainerWidth === 0) {
                        effectiveContainerWidth = Math.min(window.innerWidth, 768) -
                                                  (parseFloat(window.getComputedStyle(document.body).marginLeft) || 0) -
                                                  (parseFloat(window.getComputedStyle(document.body).marginRight) || 0);
                    }

                    const containerContentWidth = effectiveContainerWidth - paddingLeft - paddingRight;
                    let itemOuterWidth = (containerContentWidth - gutterWidth) / 2;
                    if (itemOuterWidth <= 0) {
                        itemOuterWidth = Math.max(100, containerContentWidth > 0 ? containerContentWidth / 2 : effectiveContainerWidth / 2);
                    }
                    if (itemOuterWidth <= 0) itemOuterWidth = 150;

                    const cssItemWidth = Math.max(10, itemOuterWidth - 2);

                    allMobileItems.forEach(item => {
                        item.style.width = cssItemWidth + 'px';
                    });

                    msnry = new Masonry(mobileContainer, {
                        itemSelector: '.mobile-item',
                        columnWidth: cssItemWidth,
                        gutter: gutterWidth,
                        transitionDuration: '0.3s',
                        initLayout: true,
                        resize: false
                    });
                    
                    msnry.layout();

                    mobileContainer.style.transition = 'opacity 0.3s ease-in-out';
                    mobileContainer.style.opacity = '1';

                    revealVisibleMobileItems(() => {
                        const currentExploreResults = document.getElementById('explore-results');
                        const currentLoader = document.querySelector('.mobile-loader');
                        if (currentExploreResults) {
                            currentExploreResults.style.transition = 'opacity 0.25s ease-in-out';
                            currentExploreResults.style.opacity = '1';
                            currentExploreResults.style.minHeight = '';
                        }
                        if (currentLoader) currentLoader.classList.remove('active');
                        document.body.style.overflow = '';
                    });
                });
            } else {
                if (msnry) { msnry.destroy(); msnry = null; }
                
                if (mobileContainer) {
                    mobileContainer.style.transition = 'opacity 0.3s ease-in-out';
                    mobileContainer.style.opacity = '1';
                }
                
                revealVisibleMobileItems(() => {
                    const currentExploreResults = document.getElementById('explore-results');
                    const currentLoader = document.querySelector('.mobile-loader');
                    if (currentExploreResults) {
                        currentExploreResults.style.transition = 'opacity 0.25s ease-in-out';
                        currentExploreResults.style.opacity = '1';
                        currentExploreResults.style.minHeight = '';
                    }
                    if (currentLoader) currentLoader.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }
        } else {
            if (msnry) {
                msnry.destroy();
                msnry = null;
            }
            allMobileItems.forEach(item => {
                item.style.width = '';
                item.style.position = ''; item.style.left = ''; item.style.top = '';
                item.style.transform = ''; item.style.marginBottom = '';
            });
            setTimeout(() => {
                if (mobileContainer) {
                    mobileContainer.style.transition = 'opacity 0.3s ease-in-out';
                    mobileContainer.style.opacity = '1';
                }
                revealVisibleMobileItems(() => {
                    const currentExploreResults = document.getElementById('explore-results');
                    const currentLoader = document.querySelector('.mobile-loader');
                    if (currentExploreResults) {
                        currentExploreResults.style.transition = 'opacity 0.25s ease-in-out';
                        currentExploreResults.style.opacity = '1';
                        currentExploreResults.style.minHeight = '';
                    }
                    if (currentLoader) currentLoader.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }, 50);
        }

        isLoading = false;
    }

    function createMobileItem(originalItem) {
        const mobileItem = document.createElement('div');
        mobileItem.className = 'mobile-item needs-lazy-reveal';
        const href = originalItem.getAttribute('href');

        const originalImage = originalItem.querySelector('img');
        const imageContainer = document.createElement('div');
        imageContainer.className = 'mobile-item-image';

        if (originalImage) {
            const image = document.createElement('img');
            
            let baseUrl = originalImage.getAttribute('data-src') || originalImage.getAttribute('src');
            let imageUrl = baseUrl;

            if (baseUrl && baseUrl.includes('/thumb/')) {
                imageUrl = baseUrl.replace('/thumb/', '/small/');
            }
            
            image.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
            image.setAttribute('data-src', imageUrl);
            image.className = 'lazy-image';
            image.alt = originalImage.alt || '';

            image.onload = function() {
                this.classList.remove('lazy-image');
            };
            if (image.complete) {
                image.onload();
            }

            imageContainer.appendChild(image);
        }

        let title = '';
        const titleElement = originalItem.querySelector('.card-text') ||
                            originalItem.querySelector('.list-vitrine-title') ||
                            originalItem.querySelector('.vitrine-text');

        if (titleElement) {
            title = titleElement.textContent.trim();
        }

        const contentContainer = document.createElement('div');
        contentContainer.className = 'mobile-item-content';

        const titleElement2 = document.createElement('div');
        titleElement2.className = 'mobile-item-title';
        titleElement2.textContent = title || 'No title';
        contentContainer.appendChild(titleElement2);

        mobileItem.appendChild(imageContainer);
        mobileItem.appendChild(contentContainer);

        mobileItem.addEventListener('click', function() {
            if (href && href !== '#') {
                window.location.href = href;
            }
        });

        return mobileItem;
    }

    function toggleMobileMenu() {
        // Prevent opening mobile overlay in desktop mode
        if (window.innerWidth > 768) {
            closeMobileMenu();
            return;
        }
        const mobileFoldersContainer = document.getElementById('mobile-folders-container');
        const mobileOverlay = document.getElementById('mobile-overlay');

        if (mobileFoldersContainer.classList.contains('open')) {
            closeMobileMenu();
        } else {
            mobileFoldersContainer.classList.add('open');
            mobileOverlay.classList.add('open');
            document.body.style.overflow = 'hidden';
            setTimeout(revealVisibleMobileItems, 50);
        }
    }

    function closeMobileMenu() {
        const mobileFoldersContainer = document.getElementById('mobile-folders-container');
        const mobileOverlay = document.getElementById('mobile-overlay');

        if (mobileFoldersContainer) mobileFoldersContainer.classList.remove('open');
        if (mobileOverlay) mobileOverlay.classList.remove('open');
        document.body.style.overflow = '';
    }

    function toggleMobileItemView() {
        const mobileItemsContainer = document.getElementById('mobile-items-display-area');
        const viewToggleButton = document.getElementById('mobile-view-toggle');

        if (!mobileItemsContainer || !viewToggleButton) return;

        isMobileListView = !isMobileListView;

        if (isMobileListView) {
            viewToggleButton.innerHTML = '<i class="fas fa-th-large"></i>';
            viewToggleButton.title = 'Switch to Grid View';
        } else {
            viewToggleButton.innerHTML = '<i class="fas fa-list-ul"></i>';
            viewToggleButton.title = 'Switch to List View';
        }

        mobileItemsContainer.style.opacity = '0';

        convertToMobileView();
    }

    function _performResizeLogic() {
        const newIsMobile = window.innerWidth <= 768;
        const exploreResults = document.getElementById('explore-results');
        const footerElement = document.querySelector('footer');

        // Always hide overlay/folders if not mobile
        if (!newIsMobile) {
            const mobileOverlay = document.getElementById('mobile-overlay');
            const mobileFoldersContainer = document.getElementById('mobile-folders-container');
            if (mobileOverlay) {
                mobileOverlay.classList.remove('open');
                mobileOverlay.style.display = 'none';
            }
            if (mobileFoldersContainer) {
                mobileFoldersContainer.classList.remove('open');
                mobileFoldersContainer.style.display = 'none';
            }
        } else {
            // Restore display for mobile
            const mobileOverlay = document.getElementById('mobile-overlay');
            const mobileFoldersContainer = document.getElementById('mobile-folders-container');
            if (mobileOverlay) mobileOverlay.style.display = '';
            if (mobileFoldersContainer) mobileFoldersContainer.style.display = '';
        }

        if (newIsMobile === isCurrentlyMobileView) {
            if (newIsMobile) {
                convertToMobileView();
            }
            updateScrollToTopButtonVisibility();
        } else {
            if (exploreResults) {
                exploreResults.style.transition = 'opacity 0.1s ease-out';
                exploreResults.style.opacity = '0';
            }

            const switchingToMobile = newIsMobile;
            const switchingToDesktop = !newIsMobile;

            if (switchingToMobile) {
                if (footerElement) footerElement.style.display = 'none';
                ensureMobileCoreUI();
                convertToMobileView();
            } else if (switchingToDesktop) {
                switchToDesktopView();
            }

            if (exploreResults) {
                requestAnimationFrame(() => {
                    exploreResults.style.transition = 'opacity 0.25s 0.1s ease-in';
                    exploreResults.style.opacity = '1';
                    setTimeout(() => {
                        if (exploreResults) exploreResults.style.transition = '';
                    }, 350);
                });
            }
            isCurrentlyMobileView = newIsMobile;
        }
        updateScrollToTopButtonVisibility();
    }

    function ensureMobileCoreUI() {
        if (!document.getElementById('mobile-menu-button')) createMobileMenuButton();
        if (!document.getElementById('mobile-overlay')) createMobileOverlay();
        if (!document.getElementById('mobile-folders-container')) setupMobileFoldersContainer();
        if (!scrollToTopButton) createScrollToTopButton();

        document.querySelectorAll('.show-on-mobile').forEach(el => {
            if (el.classList.contains('mobile-menu-button-container')) {
                el.style.display = 'flex';
            } else {
                el.style.display = '';
            }
        });
        document.querySelectorAll('.hide-on-mobile').forEach(el => {
            el.style.display = 'none';
        });
    }

    function switchToDesktopView() {
        closeMobileMenu();
        // Also forcibly hide overlay and folders container if present
        const mobileOverlay = document.getElementById('mobile-overlay');
        const mobileFoldersContainer = document.getElementById('mobile-folders-container');
        if (mobileOverlay) {
            mobileOverlay.classList.remove('open');
            mobileOverlay.style.display = 'none';
        }
        if (mobileFoldersContainer) {
            mobileFoldersContainer.classList.remove('open');
            mobileFoldersContainer.style.display = 'none';
        }

        updateScrollToTopButtonVisibility();

        const mobileContainer = document.getElementById('mobile-items-display-area');
        if (mobileContainer) {
            mobileContainer.remove();
        }
        allMobileItems = [];

        if (msnry) {
            msnry.destroy();
            msnry = null;
        }

        const gridView = document.getElementById('explore-grid');
        const listView = document.querySelector('.list-vitrine-container');
        if (gridView) gridView.style.display = '';
        if (listView) listView.style.display = '';

        document.querySelectorAll('.show-on-mobile').forEach(el => {
            el.style.display = 'none';
        });

        document.querySelectorAll('.hide-on-mobile').forEach(el => {
            el.style.display = '';
        });

        const exploreResults = document.getElementById('explore-results');
        if (exploreResults && exploreResults.style.opacity === '0') {
             exploreResults.style.opacity = '1';
        }
        if (exploreResults) {
            exploreResults.style.minHeight = '';
        }

        const footerElement = document.querySelector('footer');
        if (footerElement) {
            footerElement.style.display = '';
        }
    }

    const debouncedHandleResize = debounce(_performResizeLogic, 200);
})();
