/* Search Page Specific Styles */

/* Searchable dropdown adjustments for search page */
#simple-search-bar .custom-select-container {
    flex: 1;
}

#simple-search-bar .custom-select-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

#simple-search-bar .input-group.no-gap {
    flex: 1;
}

#simple-search-bar .btn {
    margin-left: 0;
}

/* Ensure proper button spacing */
#simple-search-bar > .btn {
    margin-left: 0.5rem;
}

/* Ensure dropdown works in our GROS layout */
#GROS {
    position: relative;
}

#menuGauche .custom-select-container {
    position: relative;
    z-index: 1;
}

#menuGauche .custom-select-dropdown {
    position: absolute;
    z-index: 1060;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
}

/* Ensure dropdown is visible */
.custom-select-dropdown.show {
    display: block !important;
}

/* Make buttons inside flex-fill containers take full width */
.flex-fill button {
    width: 100%;
} 

.custom-autocomplete {
  position: relative;
}
.autocomplete-items {
  position: absolute;
  z-index: 99;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #ddd;
  max-height: 200px;
  overflow-y: auto;
  display: none;
  border-radius: 0 0 0.25rem 0.25rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.autocomplete-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 1rem;
  color: #333;
  background: #fff;
  transition: background 0.2s;
}
.autocomplete-item:hover, .autocomplete-item.active {
  background: #f0f0f0;
}
#searchSelectAdvancedInput.form-control.btn-vitrine-secondary.btn-hand {
  border-radius: 0.25rem 0 0 0.25rem;
} 

.btn-hand,
.btn-hand:hover,
.btn-hand:active,
.btn-hand:focus {
  background-color: white !important;
  border: 1px solid #d9d9d9 !important;
}