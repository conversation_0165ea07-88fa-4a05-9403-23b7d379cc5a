<script>
    function initMapGeoloc(name, lat, lng) {

        let mymap = L.map(name, { scrollWheelZoom: false }).setView([lat, lng], 4)
        L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>' +
                ' contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
            maxZoom: 18,
        }).addTo(mymap)
        let marker = L.marker([lat, lng]).addTo(mymap)

        //return marker

    }
    setTimeout(function () {
        window.dispatchEvent(new Event('resize'));
    }, 100);
</script>
<!-- Main -->

<div class="container-fluid" id="mainbandeau">

    <div class="row">
        <div class="col-md-3">
        </div>
        <div class="col-md-6">
            <p><a href="/projectv/9772"><img class="img_respARCHEOGRID" src="/assets/images/bandeau3dicons.jpg" /></a>
            </p>
        </div>
        <div class="col-md-3" id="menuDroit">
            <a href="/fr/3diconsview3d,<%= idItem %>"><button type="button" class="btn btn-sm">fr</button></a>
            <a href="/en/3diconsview3d,<%= idItem %>"><button type="button" class="btn btn-sm">en</button></a>

        </div>
    </div>
</div>

<div class="container">
    <a class="btn btn-sm btn-light mx-1" href="<%= collectionInfo['id'] %>" title="collection" target="_blank"
       style="border:2px solid #242943;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;"><i
           class="fas fa-book"></i> <%= collectionInfo['title']['_'] %></a>
    <div class="centre" id="col"><% if (carareType ==='generic') { %><a href="/hhdd/<%= idItem %>" target="_blank">
            <img src="/small/<%= folderId %>_<%= idItem %>" alt="<%= name %>" title="<%= name %>"></a>
        <% } else {  if (data3D.length > 0) { %><a
           href="/viewer3d,<%= data3D[0]['hash'] %>,<%= data3D[0]['id_folder'] %>,<%= branch %>" target="_blank"
           title="<%=__('3dmodel')%>"> <% } %>
            <img src="/small/<%= folderId %>_<%= idItem %>"><% if (data3D.length > 0) { %></a><% } }%>
    </div>
    <br>
    <ul class="nav nav-tabs">
        <li class="nav-item nav3dicons"><a class="nav-link nav3dicons active" href="#heritage"
               data-toggle="tab"><%=__('heritageAsset')%></a></li>
        <li class="nav-item nav3dicons"><a class="nav-link nav3dicons" href="#resource"
               data-toggle="tab"><%=__('the2')%> <%=__('resourceMaj')%></a></li>
        <% if (youTube !== '') { %><li class="nav-item nav3dicons"><a class="nav-link nav3dicons" href="#video"
               data-toggle="tab">Video</a></li><% } %>
        <% if (carareType === 'generic') { %><li class="nav-item nav3dicons"><a class="nav-link nav3dicons"
               href="#images" data-toggle="tab">Images</a></li>
        <% } else { if (resource.length > 0) { %><li class="nav-item nav3dicons"><a class="nav-link nav3dicons"
               href="#images" data-toggle="tab"><%=__('all')%> <%=__('digitalResources')%></a></li><% } }%>
        <% if (carare.hasOwnProperty('activity')) { %><li class="nav-item nav3dicon"><a class="nav-link nav3dicons"
               href="#activity" data-toggle="tab"><%=__('activity')%></a></li><% } %>
        <% if (collectionInfo['coverage']['spatial']['geometry']['quickpoint']) {%><li class="nav-item nav3dicons"><a
               class="nav-link nav3dicons" href="#location" data-toggle="tab"><%=__('location')%></a></li><% } %>
    </ul>
    <br>
    <!-- Tab panes -->
    <div class="tab-content">

        <%# HERITAGE %>
        <div class="tab-pane fade show active" id="heritage">
            <h3><%=__('heritageAsset')%>: <%= heritageAsset['appellation']['name']['_']%></h3>
            <% if (heritageAsset['description']) { %><br><%= heritageAsset['description']['_'] %><br><br><% } %>
            <% if (heritageAsset['characters']) { %>
            <% if (heritageAsset['characters']['heritageAssetType']) { %>
            <strong><%=__('heritageAssetType')%>:</strong>
            <%= heritageAsset['characters']['heritageAssetType']['_'] %><br><% } %>
            <% if ((heritageAsset['characters']['temporal']['timeSpan']['startDate']) && (heritageAsset['characters']['temporal']['timeSpan']['endDate']) &&
                    (heritageAsset['characters']['temporal']['periodName']) ) { %>
            <strong><%=__('period')%></strong>: from
            <%= heritageAsset['characters']['temporal']['timeSpan']['startDate'] %> to
            <%= heritageAsset['characters']['temporal']['timeSpan']['endDate'] %>
            - <%= heritageAsset['characters']['temporal']['periodName']['_'] %>
            <% } } %>
            <% if (heritageAsset['spatial']['locationSet']['namedLocation'] ) { %>
            <br><br><strong><%=__('location')%></strong>:
            <%= heritageAsset['spatial']['locationSet']['namedLocation']['_']%>
            <% } %>
            <% if (heritageAsset['recordInformation'].hasOwnProperty('creation')) { %>
            <% if (heritageAsset['recordInformation']['creation']['date']) { %>
            <br /><strong>Creation</strong>: <%= heritageAsset['recordInformation']['creation']['date']%>
            <% } %>
            <% } %>
        </div>
        <div class="tab-pane fade" id="resource">
            <% if (carareType === 'specific') { %>
            <% if (resource.length > 0) { for (let r in resource) { %>
            <% if (parseInt(resource[r]['recordInformation']['id'].replace('https://www.archeogrid.fr/3diconsview3d/', '')) === parseInt(idItem)) { %>
            <h3><%=__('digitalResource')%>: <%= resource[r]['appellation']['name']['_']%></h3>
            <% if (resource[r]['description']) { %>
            <strong><%=__('3dmodeldescription')%></strong>: <%= resource[r]['description']['_']%>
            <% } %>

            <% if (resource[r]['rights']) {%>
            <br><br><strong><%=__('rights')%></strong>:<ul>
                <li><% if (resource[r]['rights']['accessRights']) { %>
                    <%= resource[r]['rights']['accessRights']['_']%><% } else
                                if (resource[r]['rights']['copyrightCreditLine']) {%>
                    <strong><%=__('copyrights')%>:</strong>
                    <%= resource[r]['rights']['copyrightCreditLine']['_'] %><% } %>
                </li>
                <li>
                    <strong><%=__('EuropeanaRights')%>:</strong> <%= resource[r]['rights']['europeanaRights']%>
                </li>
            </ul>

            <% } %>
            <% } %>
            <% } } else { %>
            <h3><%=__('digitalResource')%>: <%= resource['appellation']['name']['_']%></h3>
            <% if (resource['description']) { %>
            <strong><%=__('3dmodeldescription')%></strong>: <%= resource['description']['_']%>
            <% } %>

            <% if (resource['rights']) {%>
            <br><br><strong><%=__('rights')%></strong>:<ul>
                <li><% if (resource['rights']['accessRights']) { %>
                    <%= resource['rights']['accessRights']['_']%><% } else
                                if (resource['rights']['copyrightCreditLine']) {%>
                    <strong><%=__('copyrights')%>:</strong> <%= resource['rights']['copyrightCreditLine']['_'] %><% } %>
                </li>
                <li>
                    <strong><%=__('EuropeanaRights')%>:</strong> <%= resource['rights']['europeanaRights']%>
                </li>
            </ul>

            <% } %>
            <% } %>
            <% } else { %><%# une des ressources generiques %>
            <% if (resource.length > 0) { for (let r in resource) { %>
            <% if (parseInt(resource[r]['link'].replace('https://www.archeogrid.fr/hhdd/', '')) === parseInt(idItem)) { %>
            <h4><%=__('digitalResource')%>: <%= resource[r]['appellation']['name']['_']%></h4>
            (id: <%= resource[r]['recordInformation']['id']%>)<br />
            Source: <%= resource[r]['recordInformation']['source']['_']%><br />
            <%=__('country')%>: <%= resource[r]['recordInformation']['country']['_']%>
            <% } %>
            <% } }%>
            <% } %>
        </div>

        <%# VIDEO %>
        <% if (youTube !== '') { %><div class="tab-pane fade" id="video"><br>
            <% if (youTube.indexOf('archeogrid.fr/viewer') !== -1) { %>
            <a href="<%= youTube %>" target="_blank"><% } else { %>
                <a href="http://www.youtube.com/watch?v=<%= youTube %>" target="_blank"><% } %>
                    <i class="fa fa-video fa-2x" aria-hidden="true"></i> Video available
                </a>
                <% } %>

                <%# IMAGES %>
                <div class="tab-pane fade" id="images"><br>
                    <% if (!imagesTab.length) { %>No image available<%} else { if (carareType === 'specific') { %>
                    <div class="listTabloid">
                        <% for (let i = 0; i <  imagesTab.length; i++) { %>
                        <div class="card text-center">
                            <a href="/3diconsview3d/<%= imagesTab[i]['id']%>" title="<%= imagesTab[i]['name']%>">
                                <img class="card-img mx-auto d-block"
                                     src="/thumb/<%= folderId %>_<%= imagesTab[i]['id'] %>" style="margin-top: 5px;">
                            </a>
                        </div>
                        <% } %>
                    </div>
                    <% } else { %><%# si c'est un carare generic, on ne montre pas toutes les images seulement les 5 première%>
                    <div class="listTabloid"><% for (let i = 0; i < 5 ; i++) { %><div class="card text-center">
                            <a href="/3diconsview3d/<%= imagesTab[i]['id']%>" title="<%= imagesTab[i]['name'] %>">
                                <img class="card-img mx-auto d-block"
                                     src="/thumb/<%= folderId %>_<%= imagesTab[i]['id'] %>"
                                     style="margin-top: 5px;" /></a>
                        </div>
                        <% } %></div>
                    <% } }  %>
                </div>

                <%# activity %>
                <% if (carare.hasOwnProperty('activity')) { %>
                <div class="tab-pane fade" id="activity"><%=__('see')%> <%=__('the3')%><%=__('event')%>:
                    <% if (carare['activity']['appellation'].hasOwnProperty('name')) { %>
                    <a
                       href="<%= carare['activity']['appellation']['id'] %>"><%= carare['activity']['appellation']['name']['_'] %></a>
                    <% } %>
                    <% if (carare['activity']['recordInformation'].hasOwnProperty('creation')) { %>
                    <% if (carare['activity']['recordInformation']['creation'].hasOwnProperty('date')) { %>
                    <br><strong>Creation date</strong>:
                    <%= carare['activity']['recordInformation']['creation']['date']%>
                    <% } %>
                    <% } %>
                </div>
                <% } %>

                <%# Location %>
                <%# mettre la classe active pour que la carte se charge correctement dès le début %>
                <div class="tab-pane fade active" id="location">
                    <div id="mapCarare" style="height:300px;"></div>
                </div>

        </div>
    </div>

    <script>
<% if (collectionInfo['coverage']['spatial']['geometry']['quickpoint']) {
            let name = 'mapCarare'
            let lat = collectionInfo['coverage']['spatial']['geometry']['quickpoint']['x']
            let lng = collectionInfo['coverage']['spatial']['geometry']['quickpoint']['y'] %>
                initMapGeoloc('<%= name %>', '<%= lat %>', '<%= lng %>');
<% } %>


    </script>




</div>