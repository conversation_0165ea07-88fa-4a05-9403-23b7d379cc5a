<script>
    window.onload = function () {
        exploreThesaurus(<%= projectId %>, '<%= thesaurus %>', <%= data['id_thes'] %>, '<%= data['thesaurus_path'] %>', 'simple')
    }
</script>

<!-- Main -->
<%- include('utils/title-content') %>

<hr>

<H3>Concept "<a href="<%=data['identifier']%>" class="fakelink"
       title="<%=__('see')%> <%=__('concept')%> <%=__('in')%> <%=__('thesaurus')%>"
       style="color: #0a6aa1"><%= conceptName %></a>"
    <%=__('for')%> <%= ('project')%> "<a href="/project/<%= rootF%>"><%= projectName[0]['value'][0]%></a>"</H3>
<div class="container-fluid">
    <div class="row">

        <div class="d-flex flex-column col-0 col-xl-1"></div>
        <div id="explore-div" class="d-flex flex-column col-0 col-xl-9" style="display: none !important;">
            <div class="d-flex flex-column mb-2">
                <span id="nb-results"></span>
                <div class="d-flex align-items-baseline justify-content-between">
                    <div id="display-menu" class="d-flex">
                        <div class="input-group group-borders">
                            <div class="input-group-addon">
                                <div class="input-group-text"><%=__('documentsDisplayed')%> :</div>
                            </div>

                            <select id="nb-results-page" class="btn-secondary btn-hand">
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>

                    <nav class="ml-auto">
                        <ul class="pagination m-0"></ul>
                    </nav>
                </div>
            </div>
            <div id="explore-results" class="d-flex flex-column mb-2"></div>
        </div>


    </div>
</div>