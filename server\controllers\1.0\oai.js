const { create } = require('xmlbuilder2')

const db = require("../../helpers/db").default;
const { isSafeFromSQLInjection, escapeSpecialXMLchars, pourVailderlaDate } = require('../../helpers/tools');

const maxRecords = 100
const maxIdentifiers = 200

const repositoryNameCorpus = 'Base de données des corpus Archeogrid - CNRS'
const repositoryNameArcheogrid = 'Base de données Archeogrid - CNRS'
const repositoryNameConservatoire = 'Base de données Conservatoire National des Données 3D SHS - CNRS'

const urlCorpus = 'https://corpus.archeogrid.fr/oai'
const urlArcheogrid = 'https://www.archeogrid.fr/oai'
const urlConservatoire = 'https://3d.humanities.science/oai'

const identify = {
    corpus: {
        'repositoryName': repositoryNameCorpus,
        'baseURL': urlCorpus,
        'protocolVersion': '2.0',
        'adminEmail': '<EMAIL>',
        'earliestDatestamp': '2010-01-26T00:00:00Z',
        'deletedRecord': 'transient',
        'granularity': 'YYYY-MM-DDT00:00:00Z',
        'compression': 'gzip'
    },
    pft3d: {
        'repositoryName': repositoryNameArcheogrid,
        'baseURL': urlArcheogrid,
        'protocolVersion': '2.0',
        'adminEmail': '<EMAIL>',
        'earliestDatestamp': '2010-01-26T00:00:00Z',
        'deletedRecord': 'transient',
        'granularity': 'YYYY-MM-DDT00:00:00Z',
        'compression': 'gzip'
    },
    conservatoire3d: {
        'repositoryName': repositoryNameConservatoire,
        'baseURL': urlConservatoire,
        'protocolVersion': '2.0',
        'adminEmail': '<EMAIL>',
        'earliestDatestamp': '2019-10-04T00:00:00Z',
        'deletedRecord': 'transient',
        'granularity': 'YYYY-MM-DDT00:00:00Z',
        'compression': 'gzip'
    }
}

const delimiter = `:`
const repositoryIdentifierCorpus = `corpus.archeogrid.fr`
const repositoryIdentifierArcheogrid = `www.archeogrid.fr`
const repositoryIdentifierConservatoire = `3d.humanities.science`
const repositoryIdentifier = `www.archeogrid.fr`
let oaiPrefix = `oai${delimiter}${repositoryIdentifier}${delimiter}`
const oaiPrefixArcheogrid = `oai${delimiter}${repositoryIdentifierArcheogrid}${delimiter}`
const oaiPrefixConservatoire = `oai${delimiter}${repositoryIdentifierConservatoire}${delimiter}`
const oaiPrefixCorpus = `oai${delimiter}${repositoryIdentifierCorpus}${delimiter}`

const metadataFormats = [
    {
        metadataPrefix: 'oai_dc',
        schema: 'https://www.openarchives.org/OAI/2.0/oai_dc.xsd',
        metadataNamespace: 'https://www.openarchives.org/OAI/2.0/oai_dc/',
        myhandler: 'record_dc.php',
        record_prefix: 'dc',
        record_namespace: 'http://purl.org/dc/elements/1.1/',
        metadataList: [
            'title',
            'creator',
            'subject',
            'description',
            'contributor',
            'publisher',
            'date',
            'type',
            'format',
            'identifier',
            'source',
            'language',
            'relation',
            'coverage',
            //'rights',
            //'coverage',
            'rights'
        ]
    },
    // {
    //     metadataPrefix: 'ap',
    //     schema: 'http://patrimoines.aquitaine.fr/ap/format-pivot/1.1/bnsa.xsd', // does not work
    //     metadataNamespace: 'http://patrimoines.aquitaine.fr/ap/format-pivot/1.1', // does not work
    //     myhandler: 'record_ap.php',
    //     record_prefix: 'ressource',
    //     record_namespace: 'http://patrimoines.aquitaine.fr/ap/format-pivot/1.1', // does not work
    //     metadataList: [
    //         'identifier',
    //         'title',
    //         'subject',
    //         'description',
    //         'publisher',
    //         'language',
    //         'source',
    //         'relation',
    //         'rights',
    //         'spatial',
    //         'mediaurl',
    //         'mediatypologie',
    //         'artiste',
    //         'datation',
    //         'periode',
    //         'collection',
    //         'metier',
    //         'etablissement',
    //         'locinseeCode',
    //         'typologieObjet'
    //     ]
    // }
]

const sets = [
    {
        setSpec: 'oai_dc:archeogridcorpuslot01',
        setName: `${repositoryNameCorpus} - LOT01 DUCLOT`,
        setDescription: ''
    }, {
        setSpec: 'oai_dc:archeogridcorpuslot02',
        setName: `${repositoryNameCorpus} - LOT02 PALADINI`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:archeogridcorpuslot03',
        setName: `${repositoryNameCorpus} - LOT03 PAU_MEDIATHEQUE`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:archeogridcorpuslot04',
        setName: `${repositoryNameCorpus} - LOT04 LASSERE`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:archeogridcorpuslot05',
        setName: `${repositoryNameCorpus} - LOT05 DENANT`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:archeogridcorpuslot06',
        setName: `${repositoryNameCorpus} - LOT06 PUJO`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:archeogridcorpuslot07',
        setName: `${repositoryNameCorpus} - LOT07 REYGONDEAU-LEMELE`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot08',
        setName: `${repositoryNameCorpus} - LOT08 CARRIER`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot09',
        setName: `${repositoryNameCorpus} - LOT09 GAYE`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot10',
        setName: `${repositoryNameCorpus} - LOT10 LE MENN`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot11',
        setName: `${repositoryNameCorpus} - LOT11 MARTIN`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot12',
        setName: `${repositoryNameCorpus} - LOT12 GAULTIER`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot13',
        setName: `${repositoryNameCorpus} - LOT13 DROUILLARD`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot14',
        setName: `${repositoryNameCorpus} - LOT14 DUMAIL`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot15',
        setName: `${repositoryNameCorpus} - LOT15 SAB`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot16',
        setName: `${repositoryNameCorpus} - LOT16 DE_DION_BOUTON`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot17',
        setName: `${repositoryNameCorpus} - LOT17 VERGNIEUX`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot18',
        setName: `${repositoryNameCorpus} - LOT18 BESSON`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot19',
        setName: `${repositoryNameCorpus} - LOT19 COULON`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot20',
        setName: `${repositoryNameCorpus} - LOT20 CLEM`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot21',
        setName: `${repositoryNameCorpus} - LOT21 DHALLUIN`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot22',
        setName: `${repositoryNameCorpus} - LOT22 CHALONS`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot23',
        setName: `${repositoryNameCorpus} - LOT23 WIEDEMANN`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot24',
        setName: `${repositoryNameCorpus} - LOT24 MAGENDIE`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot25',
        setName: `${repositoryNameArcheogrid} - LOT25 CESTAS`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot26',
        setName: `${repositoryNameCorpus} - LOT26 VICENTE`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot27',
        setName: `${repositoryNameArcheogrid} - LOT27 HOFFSUMMER`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot29',
        setName: `${repositoryNameCorpus} - LOT29 SAUDAX`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot30',
        setName: `${repositoryNameCorpus} - LOT30 ZAOUI`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot33',
        setName: `${repositoryNameCorpus} - LOT33 MAILLE`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot37',
        setName: `${repositoryNameCorpus} - LOT37 CALVELO`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot38',
        setName: `${repositoryNameCorpus} - LOT38 DUPIN`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:archeogridcorpuslot40',
        setName: `${repositoryNameCorpus} - LOT40 GUANO`,
        setDescription: ''
    },
    {
        setSpec: 'oai_dc:arhceogridcorpuslot41',
        setName: `${repositoryNameCorpus} - LOT41 BIDAULT`,
        setDescription: ''
    }
    // array('setSpec' => 'oai_dc:archeogrid', 'setName' => $repositoryName, 'setDescription'=> '') //,
    // array('setSpec'=>'math', 'setName'=>'Mathematics') ,
    // array('setSpec'=>'phys', 'setName'=>'Physics') 
]

const sets2 = [
    {
        setSpec: 'oai_dc:cnd3d_set01',
        setName: `${repositoryNameConservatoire} - CND3D`,
        setDescription: 'Objects 3D du Conservatoire National des données 3D SHS'
    }
]


function formatDate(date) {
    // TODO : passer en YYYY-MM-DDT00:00:00Z ?
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2)
        month = '0' + month;
    if (day.length < 2)
        day = '0' + day;

    return [year, month, day].join('-');
}

function xmlStart(args, branch) {
    let argsAtt = ''
    for (const arg in args) {
        argsAtt += ` ${arg}="${args[arg]}"`
    }
    return `<?xml version="1.0" encoding="UTF-8"?>
            <OAI-PMH xmlns='http://www.openarchives.org/OAI/2.0/' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xsi:schemaLocation='http://www.openarchives.org/OAI/2.0/ http://www.openarchives.org/OAI/2.0/OAI-PMH.xsd'>
            <responseDate>${new Date().toISOString().split('.')[0]+"Z" }</responseDate>
            <request ${argsAtt}>${branch === 'pft3d' ? urlArcheogrid : urlConservatoire}</request>
            </OAI-PMH>`
}

// Arguments : identifier (required), metadataPrefix (required)
async function getRecord(doc, args, branch) {
    if (branch === 'pft3d') {
        oaiPrefix = oaiPrefixArcheogrid
    } else if (branch === 'conservatoire3d') {
        oaiPrefix = oaiPrefixConservatoire
    } else if (branch === 'corpus') {
        oaiPrefix = oaiPrefixCorpus
    }
    if (args.metadataPrefix === undefined) {
        doc.root()
            .ele('error')
            .att('code', 'badArgument')
            .txt('Required argument metadataPrefix is missing.')
        return
    }
    if (args.identifier === undefined) {
        doc.root()
            .ele('error')
            .att('code', 'badArgument')
            .txt('Required argument identifier is missing.')
        return
    }

    const metadataPrefix = args.metadataPrefix
    const identifier = args.identifier.replace(oaiPrefix, '')

    let query = `SELECT *
    FROM ${branch}_oai_records
    WHERE oai_set ILIKE '${metadataPrefix}:%'
    AND oai_identifier = '${identifier}'`

    let metadata = false
    for (const m of metadataFormats) {
        if (m.metadataPrefix === metadataPrefix) {
            metadata = m
        }
    }
    if (!metadata) {
        doc.root()
            .ele('error')
            .att('code', 'cannotDisseminateFormat')
            .txt('The value of the metadataPrefix argument is not supported.')
        return
    }

    await db.any(query).then((records) => {
        if (records.length <= 0) {
            doc.root()
                .ele('error')
                .att('code', 'idDoesNotExist')
                .txt('This record does not exist.')
            return
        }
        const record = records[0]
        const mainEle = doc.root().ele(args.verb)
        const recordEle = mainEle.ele('record')
        const headerEle = recordEle.ele('header')
        if (record.deleted === 'true') {
            headerEle.att('status', 'deleted')
        }
        headerEle.ele('identifier').txt(oaiPrefix + record.oai_identifier).up()
            //.ele('datestamp').txt(formatDate(record.datestamp)).up()
            // corrige l'erreur qui fait planter la requête sur le GetRecord, le toISOString n'était pas correctement intégré
            .ele('datestamp').txt(record.enterdate.toISOString().split('.')[0]+"Z").up()
            .ele('setSpec').txt(record.oai_set).up()
            .up()
        const metadataEle = recordEle.ele('metadata')
        //const metadataChildEle = metadataEle.ele(`${metadata.record_prefix}:${metadata.record_prefix}`)
        // Correction pour moissonnage Isidore :
        // [Ticket#2022051938000102] moissonnage OAI-PMH.
        // Il reste une erreur dans GetRecord sur le namespace dc qui est aoi_dc et pas dc au niveau du fils de metadata.
        const metadataChildEle = metadataEle.ele(`${metadata.metadataPrefix}:${metadata.record_prefix}`)
            .att(`xmlns:${metadata.metadataPrefix}`, `${metadata.metadataNamespace}`)
            .att(`xmlns:${metadata.record_prefix}`, `${metadata.record_namespace}`)
            .att('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance')
            .att('xsi:schemaLocation', `${metadata.metadataNamespace} ${metadata.record_namespace}`)

        if (branch === 'pft3d') {
            for (const i of metadata.metadataList) {
                // pouvoir superposer les tags subjects pour n'avoir qu'un seul item par <>
                    if (i === 'subject') {
                        for (let v = 0; v < record['dc_subject'].split(',').length; v++) {
                            //supprimer aussi le premier charactère si c'est un espace, comme souvent après une virgule...
                            metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`].split(',')[v].replace(/^ /, "")).up()
                        }
                    } else {
                        if (record[`${metadata.record_prefix}_${i}`]) {
                            metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`]).up()
                        }
                    }
            }
        } else if (branch === 'conservatoire3d') {
            for (const i of metadata.metadataList) {
                // pouvoir superposer les tags subjects pour n'avoir qu'un seul item par <>
                if ((i === 'subject') || (i === 'creator') || (i === 'contributor') || (i === 'description') || (i === 'coverage')) {
                    //console.log(record['dc_subject'].length)
                    if (record[`${metadata.record_prefix}_${i}`]) {
                        for (let v = 0; v < record[`${metadata.record_prefix}_${i}`].length; v++) {
                            //supprimer aussi le premier charactère si c'est un espace, comme souvent après une virgule...
                            metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`][v].replace(/^ /, "")).up()
                        }
                    }// else il n'y a pas du cette info
                } else {
                    if (record[`${metadata.record_prefix}_${i}`]) {
                        metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`]).up()
                    }
                }
            }
        }
    }).catch(e => {
        doc.root()
            .ele('error')
            .att('code', 'idDoesNotExist')
            .txt('This record does not exist or is corrupted.')
    });
}

// Arguments : None
function identity(doc, args, branch) {
    const verbEle = doc.root().ele(args.verb)
    for (const i in identify[branch]) {
        verbEle.ele(i).txt(identify[branch][i])
    }
}

// Arguments : from (optional), until (optional), metadataPrefix (required), set (optional), resumptionToken (exclusive)
async function listIdentifiers(doc, args, branch) {
    if (branch === 'pft3d') {
        oaiPrefix = oaiPrefixArcheogrid
    } else if (branch === 'conservatoire3d') {
        oaiPrefix = oaiPrefixConservatoire
    }
    if (args.resumptionToken !== undefined) {
        try {
            //args = JSON.parse(decodeURIComponent(args.resumptionToken))
            args = JSON.parse(Buffer.from(args.resumptionToken, "base64").toString("utf-8"));
            if (args.offset === undefined) throw 'missing offset'
        } catch (err) {
            doc.root()
                .ele('error')
                .att('code', 'badResumptionToken')
                .txt('The value of the resumptionToken argument is invalid.')
            return
        }
    }
    if (args.metadataPrefix === undefined) {
        doc.root()
            .ele('error')
            .att('code', 'badArgument')
            .txt('metadataPrefix argument is missing.')
        return
    }

    const maxRows = maxIdentifiers
    const offset = args.offset ? args.offset : 0

    const metadataPrefix = args.metadataPrefix
    const set = args.set ? `AND oai_set = '${args.set}'` : ''
    const from = args.from ? `AND to_char(enterdate, 'YYYY-MM-DD') >= '${args.from}'` : ''
    const until = args.until ? `AND to_char(enterdate, 'YYYY-MM-DD') <= '${args.until}'` : ''

    // enterdate = date d'entrée de l'item dans le repository au format Timestamp
    let query = `SELECT serial, oai_identifier , oai_set,
        concat(to_char(enterdate, 'YYYY-MM-DD'), 'T', to_char(enterdate,'HH24:MI:SSZ')) as datestamp, deleted,
    count(*) OVER() AS completelistsize
    FROM ${branch}_oai_records
    WHERE oai_set ILIKE '${metadataPrefix}:%' 
    ${set}
    ${from}
    ${until}
    ORDER BY serial
    LIMIT ${maxRows}
    OFFSET ${offset}`

    await db.any(query).then((records) => {
        const completeListSize = records[0].completelistsize
        const deliveredRows = records.length
        const verbEle = doc.root().ele(args.verb)
        for (let r in records) {
            const record = records[r]
            verbEle.ele('header')
                .ele('identifier').txt(oaiPrefix + record.oai_identifier).up()
                .ele('datestamp').txt(record.datestamp).up()
                .ele('setSpec').txt(record.oai_set).up()
        }
        if (offset + deliveredRows < completeListSize) {
            args.offset = offset + maxRows
            delete args.resumptionToken
            // const token = encodeURIComponent(JSON.stringify(args)) ou encode en base 64
            const token = Buffer.from(JSON.stringify(args)).toString("base64")
            verbEle.ele('resumptionToken')
                .txt(token)
                .att('completeListSize', completeListSize)
                .att('cursor', offset)
        } else {
            verbEle.ele('resumptionToken')
                .att('completeListSize', completeListSize)
                .att('cursor', offset)
        }
    }).catch(e => {
        doc.root()
            .ele('error')
            .att('code', 'noRecordsMatch')
            .txt('The combination of the values of the from, until, set and metadataPrefix arguments results in an empty list.')
    });
}

// Arguments : identifier (optional)
function listMetadataFormats(doc, args, branch) {
    const verbEle = doc.root().ele(args.verb)
    for (const format of metadataFormats) {
        verbEle.ele('metadataFormat')
            .ele('metadataPrefix').txt(format.metadataPrefix).up()
            .ele('schema').txt(format.schema).up()
            .ele('metadataNamespace').txt(format.metadataNamespace).up()
    }
}

// Arguments : from, until, metadataPrefix, set, resumptionToken
async function listRecords(doc, args, branch) {
    if (branch === 'pft3d') {
        oaiPrefix = oaiPrefixArcheogrid
    } else if (branch === 'conservatoire3d') {
        oaiPrefix = oaiPrefixConservatoire
    }
    if (args.resumptionToken !== undefined) {
        try {
            //args = JSON.parse(decodeURIComponent(args.resumptionToken))
            args = JSON.parse(Buffer.from(args.resumptionToken, "base64").toString("utf-8"));
            if (args.offset === undefined) throw 'missing offset'
        } catch (err) {
            doc.root()
                .ele('error')
                .att('code', 'badResumptionToken')
                .txt('The value of the resumptionToken argument is invalid.')
            return
        }
    }
    if (args.metadataPrefix === undefined) {
        doc.root()
            .ele('error')
            .att('code', 'badArgument')
            .txt('metadataPrefix argument is missing.')
        return
    }

    if ((args.from) && (!pourVailderlaDate(args.from)) ) {
        doc.root()
            .ele('error')
            .att('code', 'badArgument')
            .txt('value for argument has illegal syntax.')
        return
    }

    if ((args.until) && (!pourVailderlaDate(args.until)) ) {
        doc.root()
            .ele('error')
            .att('code', 'badArgument')
            .txt('value for argument has illegal syntax.')
        return
    }

    const maxRows = maxRecords
    const offset = args.offset ? args.offset : 0

    const metadataPrefix = args.metadataPrefix
    const set = args.set ? `AND oai_set = '${args.set}'` : ''
    const from = args.from ? `AND to_char(enterdate, 'YYYY-MM-DD') >= '${args.from}'` : ''
    const until = args.until ? `AND to_char(enterdate, 'YYYY-MM-DD') <= '${args.until}'` : ''

    let query = `SELECT *, count(*) OVER() AS completelistsize
    FROM ${branch}_oai_records
    WHERE oai_set ILIKE '${metadataPrefix}:%'
    ${set}
    ${from}
    ${until}
    ORDER BY serial
    LIMIT ${maxRows}
    OFFSET ${offset}`

    let metadata = {}
    for (const m of metadataFormats) {
        if (m.metadataPrefix === metadataPrefix) {
            metadata = m
        }
    }

    await db.any(query).then((records) => {
        const completeListSize = records[0].completelistsize
        const verbEle = doc.root().ele(args.verb)
        const deliveredRows = records.length
        for (const record of records) {
            const recordEle = verbEle.ele('record')
            const headerEle = recordEle.ele('header')
            if (record.deleted === 'true') {
                headerEle.att('status', 'deleted')
            }
            headerEle.ele('identifier').txt(oaiPrefix + record.oai_identifier).up()
                //.ele('datestamp').txt(formatDate(record.datestamp)).up()
                .ele('datestamp').txt(record.enterdate.toISOString().split('.')[0]+"Z").up()
                .ele('setSpec').txt(record.oai_set).up()
                .up()
            const metadataEle = recordEle.ele('metadata')
            const metadataChildEle = metadataEle.ele(`${metadata.metadataPrefix}:${metadata.record_prefix}`)
                .att(`xmlns:${metadata.metadataPrefix}`, `${metadata.metadataNamespace}`)
                .att(`xmlns:${metadata.record_prefix}`, `${metadata.record_namespace}`)
                .att('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance')
                .att('xsi:schemaLocation', `${metadata.metadataNamespace} ${metadata.schema}`)

            if (branch === 'pft3d') {
                for (const i of metadata.metadataList) {
                    // pouvoir superposer les tags subjects pour n'avoir qu'un seul item par <>
                        if (i === 'subject') {
                            for (let v = 0; v < record['dc_subject'].split(',').length; v++) {
                                //supprimer aussi le premier charactère si c'est un espace, comme souvent après une virgule...
                                metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`].split(',')[v].replace(/^ /, "")).up()
                            }
                        } else {
                            metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`]).up()
                        }
                }
            } else if ((branch === 'conservatoire3d') || (branch === 'corpus')) {
                for (const i of metadata.metadataList) {
                    //console.log(i)
                    // pouvoir superposer les tags subjects pour n'avoir qu'un seul item par <>
                    if ((i === 'subject') || (i === 'creator')|| (i === 'contributor')|| (i === 'description')|| (i === 'coverage')){
                        //console.log(record['dc_subject'].length)
                        if (record[`${metadata.record_prefix}_${i}`]) {
                            for (let v = 0; v < record[`${metadata.record_prefix}_${i}`].length; v++) {
                                //supprimer aussi le premier charactère si c'est un espace, comme souvent après une virgule...
                                metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`][v].replace(/^ /, "")).up()
                            }
                        }// else il n'y a pas du cette info
                    } else {
                        if (record[`${metadata.record_prefix}_${i}`]) {
                            metadataChildEle.ele(`${metadata.record_prefix}:${i}`).txt(record[`${metadata.record_prefix}_${i}`]).up()
                        }
                    }
                }
            }
        }
        if (offset + deliveredRows < completeListSize) {
            args.offset = offset + maxRows
            delete args.resumptionToken
            //const token = encodeURIComponent(JSON.stringify(args))
            const token = Buffer.from(JSON.stringify(args)).toString("base64")
            verbEle.ele('resumptionToken')
                .txt(token)
                .att('completeListSize', completeListSize)
                .att('cursor', offset)
        } else {
            verbEle.ele('resumptionToken')
                .att('completeListSize', completeListSize)
                .att('cursor', offset)
        }
    }).catch(e => {
        doc.root()
            .ele('error')
            .att('code', 'noRecordsMatch')
            .txt('The combination of the values of the from, until, set and metadataPrefix arguments results in an empty list.')
    });
}

// Arguments : resumptionToken
function listSets(doc, args, branch) {
    if (branch === 'pft3d') {
        const setEle = doc.root().ele(args.verb)
        for (const set of sets) {
            setEle.ele('set')
                .ele('setSpec').txt(set.setSpec).up()
                .ele('setName').txt(set.setName).up()
        }
    } else if (branch === 'conservatoire3d') {
        const setEle = doc.root().ele(args.verb)
        for (const set of sets2) {
            setEle.ele('set')
                .ele('setSpec').txt(set.setSpec).up()
                .ele('setName').txt(set.setName).up()
                .ele('setDescription').txt(set.setDescription).up()
        }
    } else {
        doc.root()
            .ele('error')
            .att('code', 'noSetHierarchy')
            .txt('This repository does not support sets.')
    }
}

exports.oai = async function (req, res) {
    let args = req.query
    if (args.resumptionToken) {
        // PROVOKE UNE ERREUR d'encodage lors des tests avec le conservatoire, on laisse comme ça
        //args.resumptionToken = encodeURIComponent(args.resumptionToken)
        //const token = Buffer.from(JSON.stringify(args)).toString("base64")
        // On essaye autrement erreur toujours
        // args.resumptionToken = Buffer.from(JSON.stringify(args.resumptionToken)).toString("base64")
       //args = Buffer.from(JSON.stringify(args.resumptionToken)).toString("base64")
    } else {
        console.log('pas de token')
    }
    const branch = req.params.branch

    const doc = create(xmlStart(args, branch))

    switch (args.verb) {
        case 'Identify':
            identity(doc, args, branch)
            break;
        case 'ListMetadataFormats':
            listMetadataFormats(doc, args, branch)
            break;
        case 'ListSets':
            listSets(doc, args, branch)
            break;
        case 'ListIdentifiers':
            await listIdentifiers(doc, args, branch)
            break;
        case 'ListRecords':
            await listRecords(doc, args, branch)
            break;
        case 'GetRecord':
            if(!args.identifier){
                doc.root().ele('error').att('code', 'idDoesNotExist').txt('The identifier argument is missing.');
                break;
            }
            if(isSafeFromSQLInjection(args.identifier)){
                args.identifier = escapeSpecialXMLchars(args.identifier);
                await getRecord(doc, args, branch)
            }else{
                console.log("Suspected SQL Injection attempt with identifier: " + args.identifier);
                doc.root().ele('error').att('code', 'idDoesNotExist').txt(`The identifier provide ${args.identifier} is not valid.`)
            }
            break;
        default:
            doc.root().ele('error').att('code', 'badVerb').txt('Missing or illegal OAI verb.')
            break;
    }

    const xml = doc.end({prettyPrint: true})
    res.type('text/xml').status(200).send(xml)
}