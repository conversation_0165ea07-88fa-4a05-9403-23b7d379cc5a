<%- include('utils/title-content', { home: "home" }) %>

<div class="row">
    <div class="col-0 col-md-3 menuIndex">
        <div class="container">
            <p><%=__('browseThesaurus')%> <a href="/thesaurusPactols/<%= root %>,<%= projectId %>">PACTOLS</a></p>
        </div><%=__('or')%>
    </div>
</div>

<div class="row">
    <div class="col-0 col-md-3 menuIndex">
        <% if (info === 'yes') { %>
        <div class="container"><%=__('searchAllThesaurus')%></div>
        <div class="row" id="concept">
            <div class="input-group col-9">
                <input class="form-control" placeholder="<%=__('start2')%> <%=__('type')%>..." type="text"
                       name="0#openthesomulti_general_value" id="openthesomulti_general_value">
                <input class="form-control" type="hidden" name="0#openthesomulti_general_id"
                       id="openthesomulti_general_id">
                <button class="btn btn-secondary btn-hand" type="submit"
                        onclick="exploreThesConceptMultiPrepare( '<%= root %>', '<%= lang %>',<%= projectId %>,<%= user.id %>, 'multi', 'general', 'openthesomulti_general_value');">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div><%=__('or')%><br />
        <div class="container"><%= __('browseThesauri')%></div>
        <ul class="list-group list-group-flush justify-content-end" style="display: inline-block;">
            <% for (const t of thesaurus) { %>
            <% if (t.visible !== '0') { %>
            <% if (t.thesaurus === 'Periodo') { %>
            <a href="#" id="<%= t.thesaurus %>" onclick="getThesaurusTreePeriodo('<%= root %>');">
                <i class="far fa-clock fa-2x me-1" aria-hidden="true"></i><%= __('period') %>
                <span class="badge bg-warning badge-pill ml-auto" style="color: black;"><%= t.nb_tot_item %></span>
            </a>
            <% } else { %>
            <a class="list-group-item list-group-item-action MainPageThesaurus" href="#" id="<%= t.thesaurus %>"
               onclick="getThesaurusTreeMulti('<%= root %>',<%= projectId %>, '<%= t.thesaurus %>',<%= t.id_thes %>,<%= user.id %>);">
                <% if (t.thesaurus !== 'nd_th13') { %>
                <% if (t.thesaurus === 'lrmh') { // on met le logo %>
                <% if (lang === 'fr') { %>
                <img src="/assets/images/lrmh.png" style="height: 2em;">Collection
                <%= t.name %>
                <% } else { %>
                <img src="/assets/images/lrmh.png" style="height: 2em;"><%= t.short_name %>collection
                <% } %>
                <% } else { // toutes les autres collections %>
                <i class="fa fa-book me-1" aria-hidden="true"></i><%= t.name %>
                <% } %>
                <% } else if (t.thesaurus === 'nd_th13') { %>
                <% if (lang === 'fr') { %>
                <i class="fa fa-university fa-2x me-1" aria-hidden="true"></i><%= t.name %> (complet)
                <% } else { %>
                <i class="fa fa-university fa-2x me-1" aria-hidden="true"></i><%= t.label %> (complete)
                <% } %>
                <% } %>
                <span class="badge badge-pill bg-warning text-dark"><%= t.nb_tot_item %></span>
            </a>
            <% } %>
            <% } %>
            <% } %>
            <% for (const s of thesaurusSimple) { %>
            <a class="list-group-item list-group-item-action MainPageThesaurus" href="#" id="<%= s.thesaurus %>"
               onclick="getThesaurusTree('<%= root %>',<%= projectId %>, '<%= s.thesaurus %>',<%= s.id_thes %>,<%= user.id %>);">
                <% if (lang === 'fr') { %>
                <i class="fa fa-book me-1" aria-hidden="true"></i><%= s.name %>
                <% } else { %>
                <i class="fa fa-book me-1" aria-hidden="true"></i><%= s.label %>
                <% } %>
                <span class="badge badge-pill bg-warning text-dark"><%= s.nb_tot_item %></span>
            </a>
            <% } %>
        </ul>
        <% } else if (infothesaurus === 'yes') { %>
        <div class="container"><%=__('asearch')%> simple thesauri - <%= __('search')%> <%= __('a') %> Concept :</div>
        <div class="row" id="concept">
            <div class="input-group col-9">
                <input class="form-control" placeholder="<%=__('start2')%> <%=__('type')%>..." type="text"
                       name="0#openthesothesaurus_general_value" id="openthesothesaurus_general_value">
                <input class="form-control" type="hidden" name="0#openthesothesaurus_general_id"
                       id="openthesothesaurus_general_id">
                <button class="btn btn-secondary btn-hand" type="submit"
                        onclick="exploreThesConceptPrepare('<%= root %>', '<%= lang %>',<%= projectId %>,<%= user.id%>, 'thesaurus','general');"><i
                       class="fas fa-search"></i></button>
            </div>
        </div>
        <br>
        <div class="container"><%= __('browse')%> <%= __('thepluriel') %> thesaurus <%=__('by')%><%=__('branch')%></div>
        <ul class="list-group list-group-flush justify-content-end" style="display: inline-block;">
            <% for (let i=0; i < thesaurusSimple.length; i++ ) { %><% if (thesaurusSimple[i]['nb_tot_item']) { %>
            <% if (thesaurusSimple[i]['thesaurus'] ==='Periodo' ) { %>
            <a href="#" id="<%= thesaurusSimple[i]['thesaurus'] %>"
               onclick="getThesaurusTreePeriodo('<%= root %>');">
                <i class="far fa-clock fa-2x" aria-hidden="true"></i><%=__('period')%><span
                      class="badge bg-warning badge-pill ml-auto"
                      style="color: black;"><%= thesaurusSimple[i]['nb_tot_item']%></span>
            </a>
            <% } else { %>
            <a class="list-group-item list-group-item-action MainPageThesaurus" href="#"
               id="<%= thesaurusSimple[i]['thesaurus'] %>"
               onclick="getThesaurusTree('<%= root %>',<%= projectId %>, '<%= thesaurusSimple[i]['thesaurus']%>',<%= thesaurusSimple[i]['id_thes'] %>,<%= user['id']%>);">
                <% if (lang === 'fr') { %>
                <i class="fa fa-book me-1" aria-hidden="true"></i>Collection <%= thesaurusSimple[i]['name']%>
                <% } else { %>
                <i class="fa fa-book me-1" aria-hidden="true"></i><%= thesaurusSimple[i]['label']%>Collection
                <% } %>
                <span class="badge bg-warning badge-pill ml-auto"
                      style="color: black;"><%= thesaurusSimple[i]['nb_tot_item']%></span>
            </a>
            <% } %>
            <% } %>
            <% } %>

        </ul>
        <%} else {%>Pas d'info thesaurus à exprimer<% } %>
        <% if (infothesaurus === 'no') { %>

        <br>

        <ul class="list-group list-group-flush justify-content-end" style="display: inline-block;">
            <% for (let i=0; i < thesaurusSimple.length; i++ ) { %><%# if (thesaurusSimple[i]['nb_tot_item']) { %>
            <% if (thesaurusSimple[i]['thesaurus'] ==='Periodo' ) { %>
            <a href="#" id="<%= thesaurusSimple[i]['thesaurus'] %>"
               onclick="getThesaurusTreePeriodo('<%= root %>');">
                <i class="far fa-clock fa-2x me-1" aria-hidden="true"></i><%=__('period')%><span
                      class="badge bg-warning badge-pill ml-auto"
                      style="color: black;"><%= thesaurusSimple[i]['nb_tot_item']%></span>
            </a>
            <% } else { %>
            <a class="list-group-item list-group-item-action MainPageThesaurusSimple" href="#"
               id="<%= thesaurusSimple[i]['thesaurus'] %>"
               onclick="getThesaurusTree('<%= root %>',<%= projectId %>, '<%= thesaurusSimple[i]['thesaurus']%>',<%= thesaurusSimple[i]['id_thes'] %>,<%= user['id']%>);">
                <% if (lang === 'fr') { %>
                <i class="fa fa-book me-1" aria-hidden="true"></i> <%= thesaurusSimple[i]['name']%>
                <% } else { %>
                <i class="fa fa-book me-1" aria-hidden="true"></i><%= thesaurusSimple[i]['label']%>
                <%} %>
                <span class="badge bg-warning badge-pill ml-auto"
                      style="color: black;"><%= thesaurusSimple[i]['nb_tot_item']%></span>
            </a>
            <% } %>
        </ul>
        <% } } %>
        <div id="menuGauche" style="max-height: 36vh !important;">
        </div>
    </div>
    <%- include('explore/explore', { disableSelection: true }) %>
    <div class="loader"></div>
</div>

<script>
    window.onload = function () {
        $('#openthesomulti_general_value').val('')
    }
    $(function () {

        $('#openthesomulti_general_value').autocomplete({
            minLength: 3,
            autofocus: true,
            source: function (request, response) {
                $.ajax({
                    url: '/thesaurusMultiNameGeneral/<%= root %>,<%= projectId %>,' + encodeURI(request.term),
                    dataType: "json",
                    success: function (data) {
                        response(data)
                    }
                });
            },
            select: function (event, ui) {
                //event.preventDefault();
                $('#openthesomulti_general_id').val(ui.item.id + '##' + ui.item.thesaurus);
            }
        });

    })
</script>
