<% if (!project) var project = null; %>
<% if (!projectId) var projectId = -1; %>
<% if (!active) var active = ""; %>
<% if (!home) var home = ""; %>

<div class="row align-items-center">
    <div class="col col-xl-3 d-flex align-items-center gap-2">
        <% if (home === 'home') { %>
            <a href="/projectV/<%= projectId %>" title="<%=__('backprojectpage')%>" style="display: inline-flex; align-items: center;">
                <i class="fas fa-home fa-lg"></i>
            </a>
        <% } %>
        <% if (project?.image) { %>
            <img src="/assets/images/<%= project.image %>"
                 style="height: 3em; margin-left: 0.5em; margin-bottom: 0em; margin-top: 0em;" alt="Project image">
        <% } %>
        <% if (active == 'list' && branch === 'pft3d') { %><a href="/opL" title="<%=__('overallprojects')%>" style="color: #007bff !important;">
                <i class="fas fa-sitemap fa-2x"></i>
            </a>
        <% } %>
    </div>
    <div class="hide-on-mobile col-12 col-xl-6 d-flex justify-content-center text-center">
        <h2><a href="/projects<% if (branch === 'corpus') {%>L<% } %>"><% if (branch === 'corpus') {%><%= __('Corpus')%><% } else { %><%= __('extp3d')%><% } %></a></h2>
    </div>
    <div class="col col-xl-3 d-flex flex-wrap justify-content-between" id="menuDroitV" >
        <% if (branch === 'pft3d') {%><%- include('buttons-header', { active })  %><% } %>
        <%- include('language-switch') %>
        <%- include('login-header') %>
    </div>
</div>

<hr>