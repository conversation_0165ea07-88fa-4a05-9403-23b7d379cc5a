import { Router, type Request } from "express";
import async<PERSON><PERSON><PERSON> from "express-async-handler";
import fs from "node:fs";
import store from "store";

import {
  acceptedCantaloupeFormat,
  acceptedPrevisuFormat,
  branchConfig,
  paginer,
  performRequest,
  readAndParse3DFile,
} from "../tools/globals";
import * as helpers from "../tools/helpers_tools";

import type { Branch } from "../../server/types/api";
import { archeogrid_client } from "../tools/request";

const md5 = require("md5");

const explore = Router();

explore.get(
  "/exploreUnicosNb/:idFolder",
  asyncHandler(async (req, res) => {
    const idFolder = req.params.idFolder;
    const data = [];
    let nbRes = 0;
    const user = res.locals.user;

    const datasunico = await req.fetchApi(`/api/unicosPage/${branchConfig},${idFolder}`, {
      page: "0",
      limit: "0",
      userId: user.id.toString(),
    });

    if (datasunico.length) {
      for (let d = 0; d < datasunico.length; d++) {
        data.push({ id_folder: datasunico[d].idfolder, nb_unico: 1 });
      }
      nbRes = helpers.computeAccessItemUnico(data, user.read, user.user_status);
    }

    res.send({
      //name: 'Unicos',
      nbRes: nbRes,
      nbResUnico: nbRes,
    });
  }),
);

explore.get(
  "/exploreUnicosPage/:idFolder,:page",
  asyncHandler(async (req: Request<{ idFolder: string; page: string }>, res) => {
    const idFolder = req.params.idFolder;
    const page = req.params.page;
    const limit = req.query.limit ? (req.query.limit as string) : "10000";
    const user = res.locals.user;
    const Wrights =
      (user.user_status === "user" &&
        user.write.includes(Number.parseInt(req.params.idFolder))) ||
      (user.user_status === "scribe" &&
        user.read.includes(Number.parseInt(req.params.idFolder))) ||
      user.user_status === "admin";

    const datapost = {
      page: page,
      limit: limit,
      userId: user.id,
    };

    const data = await req.fetchApi(`/api/unicosPage/${branchConfig},${idFolder}`, datapost);
    let dataRes = [];
    if (user.user_status === "admin") {
      dataRes = data;
    } else {
      // on ne met dans les données finales que celles accessibles par le user (en read)
      for (let i = 0; i < user.read.length; i++) {
        for (let d = 0; d < data.length; d++) {
          if (data[d].idfolder === user.read[i]) {
            let elem = {};
            elem = data[d];
            dataRes.push(elem);
          }
        }
      }
    }

    let model ='0', model_type= 'unico';

    const renderEjs =
      req.query.display === "grid" ? "explore/explorevitrineGrid.ejs" : "explore/explorevitrineList.ejs";

    res.render(renderEjs, {
      exploreType: "folder",
      idFolder: idFolder,
      user: user,
      branch: branchConfig,
      WRights: Wrights, //done : affiner plus tard
      data: dataRes,
      model: model,
      model_type: model_type,
      layout: false,
      datapost: datapost,
      selection: true,
    });
  }),
);

explore.get(
  "/exploreFolderNb/:idFolder",
  asyncHandler(async (req, res) => {
    const branch = branchConfig;
    const language = res.locals.lang;
    let nbRes_tempo = 0, affichage = 0, nbRes = 0 ;
    let folder_id = Number.parseInt(req.params.idFolder);
    if (folder_id < 0) { // c'est le nouveau cas d'un exploreFolder random sur un projet => on supprime le - pour récupérer le "vrai id folder"
        affichage = 0
        if (req.params.idFolder.charAt(0) === '-') {
            folder_id = Number.parseInt(req.params.idFolder.slice(1))
        }
    } else affichage = 1;
    
    try {
      const folder = await archeogrid_client.folders.getFull.query({
        branch,
        language,
        folder_id,
      });
      
      if (!folder) {
        // Folder doesn't exist, return empty result instead of throwing error
        return res.send({
          name: `Folder ${folder_id} (not found)`,
          nbRes: 0,
          type: "folder",
        });
      }

      let foldername = ''
      nbRes_tempo  = folder.nbfolderItems ?? 0;
      if ((folder.fullname) && (folder.fullname.indexOf("/") !== -1)) {
          foldername = folder.fullname.substring(folder.fullname.indexOf("/")).substring(1)
      } else {
          foldername = folder.name
      }

      if (affichage === 1) { nbRes = nbRes_tempo } else { nbRes = -1 }

      res.send({
        name: foldername,
        nbRes: nbRes,
        type: "folder",
      });
    } catch (error) {
      // Handle any database or network errors gracefully
      console.error(`Error fetching folder ${folder_id}:`, error);
      res.send({
        name: `Folder ${folder_id} (error)`,
        nbRes: 0,
        type: "folder",
      });
    }
  }),
);

explore.get(
  "/exploreFolderPage/:idFolder,:page",
  asyncHandler(async (req: Request<{ idFolder: string; page: string }>, res) => {
    const idFolder = req.params.idFolder;
    const page = req.params.page;
    const limit = req.query.limit ? (req.query.limit as string) : "10000";
    const user = res.locals.user;

    const Wrights =
      (user.user_status === "user" &&
        user.write.includes(Number.parseInt(req.params.idFolder))) ||
      (user.user_status === "scribe" &&
        user.read.includes(Number.parseInt(req.params.idFolder))) ||
      user.user_status === "admin";

    const datapost = {
      page: page,
      limit: limit,
      userId: user.id,
    };

    const data = await req.fetchApi(`/api/explore/${idFolder},${branchConfig}`, datapost);

    const FullfolderStatus =
          await archeogrid_client.folders.getFull.query({ branch:branchConfig,language : 'fr', folder_id: parseInt(idFolder)});
    // récupérer un modèle par défault pour faire l'indexation massive sur le folder
    let model ='', model_type= '';
    if (FullfolderStatus) {
        if (FullfolderStatus.nbfolderImages) {
            model_type = 'file'
            model = FullfolderStatus.filePassport ?? '0'
        } else if (FullfolderStatus.nbfolderObjects) {
            model_type = 'object'
            model = FullfolderStatus.folderPassport ?? '0'
        } else if (FullfolderStatus.nbfolderUnicos) {
            model_type = 'unico'
            model = '0'
        } else {
            model_type = 'file'
            model = '0'
        }
    } else {
        model_type = 'file';
        model = '0';
    }

    for (let i = 0; i < data.length; i++) {
      //data[i]['name'] = data[i]['object_name'] ? data[i]['object_name'] : data[i]['filename']
      data[i].id_item = data[i].id;
      // Traiter à part le cas des objets : s'il n'y a pas de repre image, on envoie un idfile = 0 pour
      // afficher une image d'objet par défaut dans exploreGrid
      if (data[i].item_type === "object") {
        data[i].name = data[i].object_name;
        data[i].idfile = data[i].id_repr_image !== "0" ? data[i].id_repr_image : 0;
      } else if (data[i].item_type === "unico") {
        data[i].name = data[i].unico_name;
        data[i].idfile = data[i].id_repr_image;
      } else {
        // file
        data[i].name = data[i].filename;
        data[i].idfile = data[i].id_repr_image !== "0" ? data[i].id_repr_image : data[i].id;
      }
        if (data[i].item_type === "object") {
            data[i].idfolder = data[i].item_folder || data[i].fid;
        } else {
            data[i].idfolder = data[i].fid;
        }
    }

    const isVirtual = await req.fetchApi(`/api/isFolderVirtual/${branchConfig},${idFolder}`);

    const renderEjs =
      req.query.display === "grid" ? "explore/exploreGrid.ejs" : "explore/exploreList.ejs";
    res.render(renderEjs, {
      exploreType: "folder",
      user: user,
      branch: branchConfig,
      WRights: Wrights, //DONE  ! affiner l'affichage de la page en fonction des droits en écriture
      data: data,
      model: model,
      model_type: model_type,
      layout: false,
      datapost: datapost,
      virtual: isVirtual.virtual,
      idFolder: idFolder,
      selection: true,
    });
  }),
);

explore.get(
    "/exploreFolderVitrinePage/:idFolder,:page",
    asyncHandler(async (req: Request<{ idFolder: string; page: string }>, res) => {
        let idFolder = req.params.idFolder;
        const page = req.params.page;
        const limit = req.query.limit ? (req.query.limit as string) : "10000";
        const user = res.locals.user;

        const Wrights =
            (user.user_status === "user" &&
                user.write.includes(Number.parseInt(req.params.idFolder))) ||
            (user.user_status === "scribe" &&
                user.read.includes(Number.parseInt(req.params.idFolder))) ||
            user.user_status === "admin";

        const datapost = {
            page: page,
            limit: limit,
            userId: user.id,
        };

        const data = await req.fetchApi(`/api/explore/${idFolder},${branchConfig}`, datapost);

        if (idFolder.charAt(0) === '-') { // c'est le nouveau cas d'un exploreFolder random sur un projet => on supprime le - pour récupérer le "vrai id folder"
            idFolder = req.params.idFolder.slice(1);
        }
        const FullfolderStatus =
            await archeogrid_client.folders.getFull.query({ branch:branchConfig,language : 'fr', folder_id: parseInt(idFolder)});
        // récupérer un modèle par défault pour faire l'indexation massive sur le folder
        let model ='', model_type= '';
        if (FullfolderStatus) {
            if (FullfolderStatus.nbfolderImages) {
                model_type = 'file'
                model = FullfolderStatus.filePassport ?? '0'
            } else if (FullfolderStatus.nbfolderObjects) {
                model_type = 'object'
                model = FullfolderStatus.folderPassport ?? '0'
            } else if (FullfolderStatus.nbfolderUnicos) {
                model_type = 'unico'
                model = '0'
            } else {
                model_type = 'file'
                model = '0'
            }
        } else {
            model_type = 'file';
            model = '0';
        }

        for (let i = 0; i < data.length; i++) {
            //data[i]['name'] = data[i]['object_name'] ? data[i]['object_name'] : data[i]['filename']
            data[i].id_item = data[i].id;
            // Traiter à part le cas des objets : s'il n'y a pas de repre image, on envoie un idfile = 0 pour
            // afficher une image d'objet par défaut dans exploreGrid
            if (data[i].item_type === "object") {
                data[i].name = data[i].object_name;
                data[i].idfile = data[i].id_repr_image !== "0" ? data[i].id_repr_image : 0;
            } else if (data[i].item_type === "unico") {
                data[i].name = data[i].unico_name;
                data[i].idfile = data[i].id_repr_image;
            } else {
                // file
                data[i].name = data[i].filename;
                data[i].idfile = data[i].id_repr_image !== "0" ? data[i].id_repr_image : data[i].id;
            }
            data[i].idfolder = data[i].fid;
        }

        // Initialize title to empty for lazy metadata loading
        for (const item of data) {
          item.title = ''; // Will be populated by client-side lazy loading
        }

        const isVirtual = await req.fetchApi(`/api/isFolderVirtual/${branchConfig},${idFolder}`);

        const renderEjs =
            req.query.display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";
        res.render(renderEjs, {
            exploreType: "folder",
            user: user,
            branch: branchConfig,
            WRights: Wrights, //DONE  ! affiner l'affichage de la page en fonction des droits en écriture
            data: data,
            model: model,
            model_type: model_type,
            layout: false,
            datapost: datapost,
            virtual: isVirtual.virtual,
            idFolder: idFolder,
            selection: true,
        });
    }),
);

explore.get(
  "/exploreThesaurusNB/:thesaurus,:idThes",
  async (req: Request<{ thesaurus: string; idThes: string }>, res) => {
    const branch = branchConfig;

    const thesaurus_id = Number.parseInt(req.params.idThes);
    const thesaurus = req.params.thesaurus;
    const type = req.query.type;

    const language = res.locals.lang;
    const user: CustomUser = res.locals.user;

    const data =
      type === "multi"
        ? await archeogrid_client.thesaurus.exploreThesaurusMultiItemNB.query({
            branch,
            language,
            thesaurus,
            thesaurus_id,
          })
        : await archeogrid_client.thesaurus.exploreThesaurusItemNB.query({
            branch,
            language,
            thesaurus,
            thesaurus_id,
          });

    if (!data) throw new Error(`Thesaurus not found! (${{ thesaurus, thesaurus_id }})`);

    const nbResFile = helpers.computeAccessItemFile(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResObject = helpers.computeAccessItemObj(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResUnico = helpers.computeAccessItemUnico(
      data.folders_info,
      user.read,
      user.user_status,
    );

    const nbRes = nbResFile + nbResObject + nbResUnico;

    res.send({
      name: data.thesaurus_name,
      nbRes: nbRes,
      nbResObject: nbResObject,
      nbResUnico: nbResUnico,
      nbResFile: nbResFile,
    });

    // TODO donner la possibiliter d'utiliser des thesaurus autre que le thesaurus multi
  },
);

explore.get(
  "/exploreThesaurusPactolsNB/:thesaurus,:idThes",
  asyncHandler(async (req, res) => {
    const idThes = req.params.idThes;
    const thesaurus = req.params.thesaurus;

    const user = res.locals.user;

    const data = await req.fetchApi(
      `/api/explorethesPactolsNB/${branchConfig},${thesaurus},${idThes}`,
      req.query as Record<string, string>,
    );

    const nbResFile = helpers.computeAccessItemFile(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResObject = helpers.computeAccessItemObj(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResUnico = helpers.computeAccessItemUnico(
      data.folders_info,
      user.read,
      user.user_status,
    );

    const nbRes = nbResFile + nbResObject + nbResUnico;

    res.send({
      name: data.thesname,
      nbRes: nbRes,
      nbResObject: nbResObject,
      nbResFile: nbResFile,
      nbResUnico: nbResUnico,
    });
  }),
);

explore.get(
  "/exploreThesaurusPactolsGeoNB/:idThes",
  asyncHandler(async (req, res) => {
    const idThes = req.params.idThes;
    const user = res.locals.user;

    const data = await req.fetchApi(
      `/api/explorethesPactolsGeoNB/${branchConfig},${idThes}`,
      req.query as Record<string, string>,
    );

    const nbResFile = helpers.computeAccessItemFile(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResObject = helpers.computeAccessItemObj(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResUnico = helpers.computeAccessItemUnico(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbRes = nbResFile + nbResObject + nbResUnico;

    res.send({
      name: data.thesname,
      nbRes: nbRes,
      nbResObject: nbResObject,
      nbResFile: nbResFile,
      nbResUnico: nbResUnico,
    });
  }),
);

explore.get(
  "/exploreThesaurusPactolsPage/:thesaurus,:idThes,:page",
  asyncHandler(async (req, res) => {
    const request_data = {
      branch: branchConfig,
      thesaurus: req.params.thesaurus,
      thesaurus_id: Number.parseInt(req.params.idThes),
      thesaurus_path: req.query.thes_path as string,
      user_id: res.locals.user.id,
      page: Number.parseInt(req.params.page),
      limit: paginer,
    };

    // New logic for limit
    const parsedQueryLimitClient = Number.parseInt(req.query.limit as string);
    let queryLimit: number;
    if (req.query.limit === '0' || !req.query.limit) {
      queryLimit = Number.MAX_SAFE_INTEGER; // Use a large number for all items (default behavior)
    } else if (Number.isInteger(parsedQueryLimitClient) && parsedQueryLimitClient > 0) {
      queryLimit = parsedQueryLimitClient;
    } else {
      queryLimit = Number.MAX_SAFE_INTEGER; // Default to show all items if invalid limit provided
    }

    const final_request_data = {
      ...request_data, // Spread the original request_data
      limit: queryLimit, // Override the limit property
    };

    console.log("[explore.ts] /exploreThesaurusPactolsPage - Determined queryLimit:", queryLimit);
    console.log("[explore.ts] /exploreThesaurusPactolsPage - request_data:", JSON.stringify(final_request_data));

    const data =
      await archeogrid_client.thesaurus.exploreThesaurusPactolsItemPage.query(final_request_data);
    
    console.log("[explore.ts] /exploreThesaurusPactolsPage - data from archeogrid_client (length: " + (data ? data.length : 'null/undefined') + "):", JSON.stringify(data));

    if (data && data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        data[i].name = data[i].filename ? data[i].filename : "";
        data[i].id_item = data[i].id;
      }
    } else {
      console.warn("[explore.ts] /exploreThesaurusPactolsPage - No data items returned from archeogrid_client or data is empty.");
    }

    // Initialize title to empty for lazy metadata loading
    for (const item of data) {
      item.title = ''; // Will be populated by client-side lazy loading
    }

    const renderEjs =
      req.query.display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";
    
    console.log(`[explore.ts] /exploreThesaurusPactolsPage - Rendering with template: ${renderEjs}`);

    res.render(renderEjs, {
      exploreType: "thesaurus",
      user: res.locals.user,
      branch: branchConfig,
      WRights: 0, //TODO : affiner plus tard
      data: data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: request_data,
      virtual: false,
      selection: true,
    });
  }),
);

explore.get(
  "/exploreThesaurusPage/:thesaurus,:idThes,:page",
  asyncHandler(
    async (
      req: Request<
        { thesaurus: string; idThes: string; page: string },
        unknown,
        unknown,
        { display: string; thes_path: string; projectId: string; type: string; limit: string }
      >,
      res,
    ) => {
      const user = res.locals.user as CustomUser;

      const branch = branchConfig;
      const language = res.locals.lang;
      const thesaurus = req.params.thesaurus;
      const thesaurus_id = Number.parseInt(req.params.idThes);
      const thesaurus_path = req.query.thes_path;
      const user_id = user.id;
      const type = req.query.type;
      const page = Number.parseInt(req.params.page);
      const parsedQueryLimit = Number.parseInt(req.query.limit as string);

      // If limit is explicitly 0, pass 0 (or a very large number if 0 is not supported for "no limit" by the backend)
      // Otherwise, if it's a positive number, use it. Else, default to show all items.
      let limit: number;
      if (req.query.limit === '0' || !req.query.limit) {
        limit = Number.MAX_SAFE_INTEGER; // Use a large number for all items (default behavior)
      } else if (Number.isInteger(parsedQueryLimit) && parsedQueryLimit > 0) {
        limit = parsedQueryLimit;
      } else {
        limit = Number.MAX_SAFE_INTEGER; // Default to show all items if invalid limit provided
      }
      console.log("[explore.ts] /exploreThesaurusPage - Determined limit for query:", limit);

      if (type === "multi") {
        const data = await archeogrid_client.thesaurus.exploreThesaurusMultiItemPage.query({
          branch,
          language,
          user_id,
          thesaurus,
          thesaurus_id,
          page,
          limit,
        });

        for (let i = 0; i < data.length; i++) {
          data[i].name = data[i].filename ? data[i].filename : "";
          data[i].id_item = data[i].id;
          if (data[i].item_type === "object") {
            data[i].idfolder = data[i].idfolder_item ? data[i].idfolder_item : data[i].idfolder;
            data[i].idfile = data[i].idfile === "0" ? 0 : data[i].idfile;
          }
        }

        // Initialize title to empty for lazy metadata loading
        for (const item of data) {
          item.title = ''; // Will be populated by client-side lazy loading
        }

        const renderEjs =
          req.query.display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";

        res.render(renderEjs, {
          exploreType: "thesaurus",
          user: user,
          branch: branchConfig,
          WRights: 0, //TODO : affiner plus tard
          data: data,
          model: 'DublinCore',
          model_type: 'file',
          layout: false,
          datapost: { page },
          virtual: false,
          selection: true,
        });
      } else if (type === "simple") {
        const data = await archeogrid_client.thesaurus.exploreThesaurusItemPage.query({
          branch,
          thesaurus,
          thesaurus_id,
          type,
          user_id,
          page,
          limit,
        });

        type ExploreData = (typeof data)[number] &
          Partial<{
            name: string;
            id_item: number;
            idfile: number;
          }>;
        const explore_data: ExploreData[] = [];

        for (let i = 0; i < data.length; i++) {
          const explore: ExploreData = { ...data[i] };
          explore.name = data[i].filename ? data[i].filename : "";
          explore.id_item = data[i].id;
          if (data[i].item_type === "object") {
            explore.idfile = data[i].id_file === "0" ? 0 : Number.parseInt(data[i].id_file);
          } else if (data[i].item_type === "file") {
            explore.idfile = Number.parseInt(data[i].id_file);
          }
          explore_data.push(explore);
        }

        const renderEjs =
          req.query.display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";

        res.render(renderEjs, {
          exploreType: "thesaurus",
          user: user,
          branch: branchConfig,
          WRights: 0, //TODO : affiner plus tard
          data: explore_data,
          model: 'DublinCore',
          model_type: 'file',
          layout: false,
          datapost: { page },
          virtual: false,
          selection: true,
        });
      }
      // TODO: donner la possibilité d'utiliser d'autre thesaurus que le thesaurus multi
    },
  ),
);

explore.get(
  "/exploreThesaurusPactolsGeoPage/:idThes,:page",
  asyncHandler(async (req, res) => {
    const idThes = req.params.idThes;

    const user = res.locals.user;

    const datapost = {
      page: req.params.page,
      pagination: req.query.limit as string,
      userId: user.id,
      projectId: req.query.projectId as string,
    };

    const data = await req.fetchApi(
      `/api/explorethesPactolsGeoPage/${branchConfig},${idThes}`,
      datapost,
      "POST",
    );

    for (let i = 0; i < data.length; i++) {
      data[i].name = data[i].filename ? data[i].filename : "";
      data[i].id_item = data[i].id;
    }
    const renderEjs =
      req.query.display === "grid" ? "explore/exploreGrid.ejs" : "explore/exploreList.ejs";

    res.render(renderEjs, {
      exploreType: "thesaurus",
      user: user,
      branch: branchConfig,
      WRights: 0, //TODO : affiner plus tard
      data: data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: datapost,
      virtual: false,
      selection: false,
    });
  }),
);

explore.get(
  "/exploreSelectionNb/:idProject",
  asyncHandler(async (req, res) => {
    const idProject = req.params.idProject;
    const user = res.locals.user;

    const data = await req.fetchApi(`/api/favoritesItems/${branchConfig},${user.id},${idProject}`);

    res.send({
      name: "Selection",
      nbRes: data ? data.length : 0,
    });
  }),
);

/*
explore.get(
  "/exploreSelectionPage/:idProject,:page",
  asyncHandler(async (req, res) => {
    const idProject = req.params.idProject;
    const page = req.params.page;
    const limit = req.query.limit ? (req.query.limit as string) : "10000";
    const user = res.locals.user;

    const datapost = {
      page: page,
      limit: limit,
      userId: user.id,
    };

    const virtualFolders = await req.fetchApi(`/api/virtualFolder/${branchConfig},${idProject}`);
    const objectsFolders = await req.fetchApi(`/api/objectsFolder/${branchConfig},${idProject}`);
    const data = await req.fetchApi(
      `/api/favoritesItems/${branchConfig},${user.id},${idProject}`,
      datapost,
    );

    for (let i = 0; i < data.length; i++) {
      data[i].id_item = data[i].id;
      data[i].idfile = Number.parseInt(data[i].idfile);
      // pour que les user puissent visualiser les éléments sélectionnés, on suppose qu'à partir du moment où il les
      // a sélectionné c'est qu'il y avait accès
      // TODO : ajouter la notion de folder pour la selection
      //  en attendant :  on récupère le folder d'origine de l'item (id_folder) qu'on met dans idfolder pour l'affichage dans exploreGrid
      data[i].idfolder = data[i].id_folder;
    }
    const renderEjs =
      req.query.display === "grid" ? "explore/exploreGrid.ejs" : "explore/exploreList.ejs";

    res.render(renderEjs, {
      exploreType: "selection",
      user: user,
      branch: branchConfig,
      WRights: 0, //TODO : affiner plus tard
      data: data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: datapost,
      projectId: idProject,
      virtualFolders: virtualFolders,
      objectsFolders: objectsFolders,
      selection: true, // on dit true pour pouvoir affiner la sélection , c'est pratique !
    });
  }),
);
*/

// New route for selection vitrine page
explore.get(
  "/exploreSelectionVitrinePage/:idProject,:page",
  asyncHandler(async (req, res) => {
    const idProject = req.params.idProject;
    const page = req.params.page;
    const limit = req.query.limit ? (req.query.limit as string) : "10000";
    const user = res.locals.user;

    const datapost = {
      page: page,
      limit: limit,
      userId: user.id,
    };

    const virtualFolders = await req.fetchApi(`/api/virtualFolder/${branchConfig},${idProject}`);
    const objectsFolders = await req.fetchApi(`/api/objectsFolder/${branchConfig},${idProject}`);
    const data = await req.fetchApi(
      `/api/favoritesItems/${branchConfig},${user.id},${idProject}`,
      datapost,
    );

    for (let i = 0; i < data.length; i++) {
      data[i].id_item = data[i].id;
      data[i].idfile = Number.parseInt(data[i].idfile);
      // pour que les user puissent visualiser les éléments sélectionnés, on suppose qu'à partir du moment où il les
      // a sélectionné c'est qu'il y avait accès
      // TODO : ajouter la notion de folder pour la selection
      //  en attendant :  on récupère le folder d'origine de l'item (id_folder) qu'on met dans idfolder pour l'affichage dans exploreGrid
      data[i].idfolder = data[i].id_folder;
    }

    // Use the vitrine templates instead of the regular ones
    const renderEjs =
      req.query.display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";

    res.render(renderEjs, {
      exploreType: "selection",
      user: user,
      branch: branchConfig,
      WRights: 0, //TODO : affiner plus tard
      data: data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: datapost,
      projectId: idProject,
      virtualFolders: virtualFolders,
      objectsFolders: objectsFolders,
      selection: true, // on dit true pour pouvoir affiner la sélection , c'est pratique !
    });
  }),
);

explore.get(
  "/search/:projectId",
  asyncHandler(async (req, res) => {
    if (req.session) req.session.route = `/search/${req.params.projectId}`;
    res.locals.branch = branchConfig;
    const user = res.locals.user as CustomUser;
    const branch = branchConfig;
    const language = res.locals.lang;
    const project_id = Number.parseInt(req.params.projectId);

    console.log(req.query);
    const name = await req.fetchApi(`/api/projectName/${branchConfig},${req.params.projectId}`);

    const metadata = await req.fetchApi(
      `/api/metadataLabels/${branchConfig},${req.params.projectId},${res.locals.lang}`,
    );

    const projectFolders = await req.fetchApi(`/api/csstreeBOOL/${req.params.projectId},${branchConfig}`);

    const extensions = await req.fetchApi(
      `/api/projectExtensions/${branchConfig},${req.params.projectId},${user.id}`,
    );

    const thesaurus = await archeogrid_client.thesaurus.getAllThesaurusForProject.query({
      branch,
      language,
      project_id,
    });
    // const thesaurus = await req.fetchApi(`/api/thesaurusProject/${branchConfig},${res.locals.lang}`);

    const search = await req.fetchApi(
      `/api/thesaurusSearchNames/${branchConfig},${encodeURIComponent(req.query.search as string)}`,
    );

    const info = metadata.length > 0 ? "yes" : "no";

    let folders = [];
    if (user.user_status === "admin") {
      folders = projectFolders;
    } else {
      for (const i in projectFolders) {
        for (let j = 0; j < user.read.length; ++j) {
          if (projectFolders[i].id === user.read[j]) {
            folders.push(projectFolders[i]);
          }
        }
      }
    }

    const project = await archeogrid_client.projects.projectsFull.query({
        branch: branchConfig,
        language: res.locals.lang,
        project_id: Number.parseInt(req.params.projectId),
    });

    const tree = await req.fetchApi(`/api/csstreeBOOL/${req.params.projectId},${branchConfig}`);

    res.render("search/searchPage", {
      projectName: name,
      project: project,
      projectId: req.params.projectId,
      user: user,
      info: info,
      models: metadata,
      folders: folders,
      extensions: extensions,
      thesaurus: thesaurus,
      search: search,
      tree: tree,
      lng: res.locals.lang,
      selectedFolder: null,
    });
  }),
);

explore.get(
  ["/exploreSearchNb/:projectId", "/searchNb/:projectId"],
  asyncHandler(async (req, res) => {
    req.session.root = `search/${req.params.projectId}`;
    if (req.query.search !== "{}")
      req.session.root += `?search=${encodeURIComponent(req.query.search as string)}`; // pour que le login/out retourne sur cette page

    const user = res.locals.user;

    const dataSearch = {
      search: encodeURIComponent(req.query.search as string),
      userId: user.id,
    };

    const data = await req.fetchApi(`/api/searchNb/${branchConfig},${req.params.projectId}`, dataSearch);

    const nbResUnico = data
      ? helpers.computeAccessItemTypeUnico(data, user.read, user.user_status)
      : 0;
    const nbResFile = data
      ? helpers.computeAccessItemTypeFile(data, user.read, user.user_status)
      : 0;
    const nbResObject = data
      ? helpers.computeAccessItemTypeObject(data, user.read, user.user_status)
      : 0;
    const nbRes = nbResFile + nbResObject + nbResUnico;

    res.send({
      name: "Recherche",
      nbRes: nbRes,
      nbResObject: nbResObject,
      nbResFile: nbResFile,
      nbResUnico: nbResUnico,
    });
  }),
);

explore.get(
  ["/exploreSearchPage/:projectId,:page", "/searchExplore/:projectId,:page"],
  asyncHandler(async (req: Request<{ projectId: string; page: string }>, res) => {
    const user = res.locals.user;
    const branch = branchConfig;

    let effectiveLimitForQuery: number;
    let pageForQuery: number = Number.parseInt(req.params.page);

    if (req.query.limit === '0') {
      effectiveLimitForQuery = 10000; // actual limit of items that will load
      pageForQuery = 1;
    } else {
      const parsedLimit = Number.parseInt(req.query.limit as string);
      if (Number.isInteger(parsedLimit) && parsedLimit > 0) {
        effectiveLimitForQuery = parsedLimit;
      } else {
        effectiveLimitForQuery = paginer;
      }
    }

    const datapost = {
      page: req.params.page,
      pagination: effectiveLimitForQuery.toString(),
      search: encodeURIComponent(req.query.search as string),
      userId: user.id,
    };
    const folder_id = Number.parseInt(req.params.projectId);
    const search = encodeURIComponent(req.query.search as string);
    const user_id = user.id;

    const data = await archeogrid_client.search.postSearch.mutate({
      branch,
      folder_id,
      page: pageForQuery,
      pagination: effectiveLimitForQuery,
      search,
      user_id,
      limit: effectiveLimitForQuery,
    });

    let imgPrincipale: Partial<{
      srcImgThumb: string;
      srcImgSmall: string;
      srcImgHd: string;
      srcImg3d: string;
      urlTarget: string;
    }> &
      (typeof data)[number];

    type VisualizationData = (typeof data)[number] &
      Partial<{ object_name: string; iiif: number; write: boolean; HD: boolean }>;
    const visualization_data: VisualizationData[] = [];

    for (let i = 0; i < data.length; i++) {
      const search_data: VisualizationData = { ...data[i] };
      search_data.path = search_data.path ?? "/";
      search_data.extension = search_data.extension ?? "/";

      // Traiter à part le cas des objets : s'il n'y a pas de repre image, on envoie un idfile = 0 pour
      // afficher une image d'objet par défaut dans exploreGrid
      if (search_data.item_type === "object") {
        search_data.object_name = search_data.name;
        search_data.idfile = search_data.idfile || 0;
      }

      if (req.query.display === "grid") {
        imgPrincipale = data[i];
        const pathFile = search_data.path;
        const file = search_data.name;
        const path_tocourt = `${search_data.path.substring(
          0,
          search_data.path.lastIndexOf("/"),
        )}/`;

        if (acceptedCantaloupeFormat.includes(search_data.extension.toLowerCase())) {
          search_data.iiif = search_data.idfile ?? undefined;
        } else if (search_data.extension === "3d") {
          const srcImg3d = md5(pathFile);
          store.set(`image3d_${srcImg3d}`, pathFile);
          imgPrincipale.srcImg3d = srcImg3d;

          if (search_data.path) {
            const data3d = await readAndParse3DFile(search_data.path);
            if (data3d == null) {
              imgPrincipale.srcImgThumb = "";
              imgPrincipale.srcImgSmall = "";
            } else if (data3d.type === "3DHOP") {
              if (data3d.data["3dhop"].thumbnail) {
                const thumb_name = path_tocourt + data3d.data["3dhop"].thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                imgPrincipale.srcImgThumb = srcImgThumb;
                imgPrincipale.srcImgSmall = srcImgSmall;
              }
            } else {
              if (data3d.data.potree.thumbnail) {
                const thumb_name = path_tocourt + data3d.data.potree.thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                imgPrincipale.srcImgThumb = srcImgThumb;
                imgPrincipale.srcImgSmall = srcImgSmall;
              }
            }
          } else {
            req.flash("error", "Unable to get path for 3d file");
          }
        } else if (data[i].extension === "url") {
          imgPrincipale.srcImgSmall = helpers.setImageSmall(file, pathFile);
          imgPrincipale.srcImgThumb = helpers.setImageThumb(file, pathFile);
          if (data[i].path) {
            const contenuUrl = fs.readFileSync(data[i].path!, "utf-8");
            const urlFich = JSON.parse(contenuUrl);
            if (Object.hasOwn(urlFich, "url")) {
              imgPrincipale.urlTarget = urlFich.url;
              const target = md5(urlFich.url);
              store.set(`hd_${target}`, urlFich.url);
              imgPrincipale.srcImgHd = target;
            }
          }
        } else if (data[i].extension === "json") {
          imgPrincipale.srcImgThumb = "json";
          imgPrincipale.srcImgSmall = "json";
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else if (data[i].extension === "zip") {
          imgPrincipale.srcImgThumb = "zip";
          imgPrincipale.srcImgSmall = "zip";
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else {
          // il n'y a pas de prévisu
          imgPrincipale.srcImgThumb = "";
          imgPrincipale.srcImgSmall = "";
          imgPrincipale.srcImgHd = "";
          search_data.path = "";
        }

        search_data.write =
          user.user_status === "scribe" ||
          user.user_status === "admin" ||
          (user.user_status === "user" && user.write.includes(search_data.id_folder));

        search_data.HD = user.user_status === "guest" || user.user_status === "notconnected";
      }

      visualization_data.push(search_data);
    }

    // RENDER
    const renderEjs =
      req.query.display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";

    res.render(renderEjs, {
      exploreType: "search",
      user: user,
      branch: branch,
      WRights: 0, //TODO : affiner plus tard
      data: visualization_data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: datapost,
      projectId: req.params.projectId,
      selection: true,
    });
  }),
);

explore.get(
  "/exploreThesMultiNB,:root,:thesaurus,:idThes",
  asyncHandler(
    async (req: Request<{ root: Branch; thesaurus: string; idThes: string }>, res) => {
      // On récupère le nb item pour mettre en place la pagination en fonction du user et de ses droits !!!
      // { thes_path: '1.79342.28072.28087', projectId: '5535' }
      // on récupère un tableau avec tous les folders en jeu
      const branch = req.params.root;
      const thesaurus = req.params.thesaurus;
      const thesaurus_id = Number.parseInt(req.params.idThes);
      const language = res.locals.lang;

      const data = (await archeogrid_client.thesaurus.exploreThesaurusMultiItemNB.query({
        branch,
        language,
        thesaurus,
        thesaurus_id,
      })) ?? { thesaurus_name: null, folders_info: [] };

      // calculer le nb d'item réellement accessibles :
      const user = res.locals.user as CustomUser;
      // calculer le nb accessible d'item
      const nbResFile = helpers.computeAccessItemFile(
        data.folders_info,
        user.read,
        user.user_status,
      );
      const nbResObject = helpers.computeAccessItemObj(
        data.folders_info,
        user.read,
        user.user_status,
      );
      const nbResUnico = helpers.computeAccessItemUnico(
        data.folders_info,
        user.read,
        user.user_status,
      );
      const nbAccess = nbResFile + nbResObject + nbResUnico;
      // TODO : polyhierarchie : on a maintenant un tableau de thesname
      const dataSend = {
        thesname: data.thesaurus_name, // Pour afficher la hierarchie du concept
        nbAccess, // nb d'item accessible pour le user
        nb_item:
          data.folders_info[0].nb_file +
          data.folders_info[0].nb_object +
          data.folders_info[0].nb_unico, // nb total d'item du concept
        pagination: Math.ceil(nbAccess / paginer), // on fait la pagination en fonction du nom d'item accessible
      };

      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(dataSend));
    },
  ),
);

explore.get(
  "/exploreThesNB,:root,:thesaurus,:idThes",
  asyncHandler(
    async (req: Request<{ root: Branch; thesaurus: string; idThes: string }>, res) => {
      // On récupère le nb item pour mettre en place la pagination en fonction du user et de ses droits !!!
      const branch = req.params.root;
      const thesaurus = req.params.thesaurus;
      const thesaurus_id = Number.parseInt(req.params.idThes);

      const language = res.locals.lang;
      const user = res.locals.user as CustomUser;

      const data = await archeogrid_client.thesaurus.exploreThesaurusItemNB.query({
        branch,
        language,
        thesaurus,
        thesaurus_id,
      });

      if (!data) throw new Error("Thesaurus not found!");

      // TODO FIX DATA TYPE

      // calculer le nb d'item réellement accessibles :
      // calculer le nb accessible d'item
      const items = data.folders_info.map((f) => {
        return { id_folder: f.id_folder, nb: f.nb_file + f.nb_object + f.nb_unico };
      });
      const nbAccess = helpers.computeAccessItem(items, user.read, user.user_status);
      // TODO : polyhierarchie : on a maintenant un tableau de thesname
      const dataSend = {
        thesname: data.thesaurus_name, // Pour afficher la hierarchie du concept
        nb_item: data.nb_item, // nb total d'item du concept
        nbAccess: nbAccess, // nb d'item accessible pour le user
        pagination: Math.ceil(nbAccess / paginer), // on fait la pagination en fonction du nom d'item accessible
      };
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(dataSend));
    },
  ),
);

// on veut le concept, quelque soit son thesaurus
// ATTENTION ! un id_thes n'a pas du tout la même signification selon les thesaurus => premier tests avec les thesaurus frollo ok
// Mais pour les autres projets cela n'a pas de sens !! pour Karnak, une fois qu'on a récupéré un concept (son nom) on veut récupérer tous les items
// On recherche sun un nom de concept et non sur un id_thes ou alors, on ne récupère que l'id_thes et le thesaurus pour ne récupérer que le bon concept
explore.get(
  "/exploreThesMultiNBgeneral,:root,:idthesaurus",
  asyncHandler(async (req: Request<{ root: Branch; idthesaurus: string }>, res) => {
    // On récupère le nb item pour mettre en place la pagination en fonction du user et de ses droits !!!
    const user = res.locals.user as CustomUser;
    // on récupère un tableau avec tous les folders en jeu
    const data = await req.fetchApi(
      `/api/explorethesMultiNBGeneral/${req.params.root},${req.params.idthesaurus},${req.query.thesaurus}`,
      req.query as Record<string, string>,
    );

    let concept = "";
    // Polyhierarchie : il peut y voir plusieurs noms pour le même id_thes ....
    for (const i in data) {
      if (i.localeCompare("folders_info") === 0) {
        //do nothing
      } else {
        // on empile les différents noms donnés au concept id
        if (Number.parseInt(i) > 0) {
          concept += " / ";
        }
        concept += data[i].thesname;
      }
    }
    // calculer le nb accessible d'item
    const nbResFile = helpers.computeAccessItemFile(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResObject = helpers.computeAccessItemObj(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbResUnico = helpers.computeAccessItemUnico(
      data.folders_info,
      user.read,
      user.user_status,
    );
    const nbAccess = nbResFile + nbResObject + nbResUnico;
    const dataSend = { nb_item: 0, nbAccess: 0, thesname: "", pagination: 0 };

    if (data.folders_info) {
      dataSend.nb_item =
        data.folders_info[0].nb_file +
        data.folders_info[0].nb_object +
        data.folders_info[0].nb_unico; // nb total d'item du concept
    }

    dataSend.nbAccess = nbAccess; // nb d'item accessible pour le user
    dataSend.thesname = concept; // Pour afficher la hierarchie du concept
    dataSend.pagination = Math.ceil(nbAccess / paginer); // on fait la pagination en fonction du nom d'item accessible
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(dataSend));
  }),
);

// on veut le concept, quelque soit son thesaurus
explore.get(
  "/exploreThesNBgeneral,:root,:idthesaurus",
  asyncHandler(async (req, res) => {
    // On récupère le nb item pour mettre en place la pagination en fonction du user et de ses droits !!!
    const user = res.locals.user;
    // on récupère un tableau avec tous les folders en jeu
    const data = await req.fetchApi(
      `/api/explorethesNBGeneral/${req.params.root},${req.params.idthesaurus},${req.query.thesaurus}`,
      req.query as Record<string, string>,
    );
    // calculer le nb d'item réellement accessibles :
    let concept = "";
    // Polyhierarchie : il peut y voir plusieurs noms pour le même id_thes ....
    for (const i in data) {
      if (i.localeCompare("folders_info") === 0) {
        //do nothing
      } else {
        // on empile les différents noms donnés au concept id
        if (Number.parseInt(i) > 0) {
          concept += " / ";
        }
        concept += data[i].thesname;
      }
    }
    // calculer le nb accessible d'item
    const nbAccess = helpers.computeAccessItem(data.folders_info, user.read, user.user_status); // nb d'item accessible pour le user
    const thesname = concept; // Pour afficher la hierarchie du concept
    const pagination = Math.ceil(nbAccess / paginer); // on fait la pagination en fonction du nom d'item accessible
    const dataSend = { nbAccess, thesname, pagination };

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(dataSend));
  }),
);

explore.get(
  "/exploreThesMultiPage,:root,:thesaurus,:idthesaurus,:page",
  asyncHandler(
    async (
      req: Request<{ root: Branch; thesaurus: string; idthesaurus: string; page: string }>,
      res,
    ) => {
      // on ne récupère que une page de la requête (OFFSET (page-1) LIMIT paginer) ET
      // seulement les items autorisés (on passe le id_user dans le body
      const user = res.locals.user as CustomUser;
      const foldersArray: number[] = [];
      const foldersInfo: { id_folder: number; nb: number }[] = [];

      const branch = req.params.root;
      const language = res.locals.lang;
      const user_id = user.id;
      const thesaurus = req.params.thesaurus;
      const thesaurus_id = Number.parseInt(req.params.idthesaurus);
      const page = Number.parseInt(req.params.page);
      const parsedQueryLimit = Number.parseInt(req.query.limit as string);

      // If limit is explicitly 0, pass 0 (or a very large number if 0 is not supported for "no limit" by the backend)
      // Otherwise, if it's a positive number, use it. Else, default to paginer.
      let limit: number;
      if (req.query.limit === '0') {
        limit = 5000; // Change: Use a large number for all items
      } else if (Number.isInteger(parsedQueryLimit) && parsedQueryLimit > 0) {
        limit = parsedQueryLimit;
      } else {
        limit = paginer;
      }

      const data = await archeogrid_client.thesaurus.exploreThesaurusMultiItemPage.query({
        branch,
        language,
        user_id,
        thesaurus,
        thesaurus_id,
        page,
        limit,
      });

      let imgPrincipale = [];
      for (const i in data) {
        data[i].model = "0";
        // trier les items par folder pour ensuite afficher le nb d'items auquel on a access
        if (foldersArray.includes(data[i].idfolder)) {
          // le folder est déjà renseigné on ne fait rien de plus que ajouter l'item
          // récupérer le idfolder et on incrémente le nb à chaque fois
          for (const f in foldersInfo) {
            if (foldersInfo[f].id_folder === data[i].idfolder) {
              foldersInfo[f].nb++;
            }
          }
        } else {
          foldersArray.push(data[i].idfolder);
          // on initie le folderInfo
          foldersInfo.push({
            id_folder: data[i].idfolder,
            nb: 1,
          });
        }
        imgPrincipale = data[i];
        const pathFile = data[i].path;
        const file = data[i].filename;
        const path_tocourt = `${data[i].path.substring(0, data[i].path.lastIndexOf("/"))}/`;
        if (data[i].extension === "3d") {
          const srcImg3d = md5(pathFile);
          store.set(`image3d_${srcImg3d}`, pathFile);
          imgPrincipale.srcImg3d = srcImg3d;

          if (data[i].path) {
            const data3d = await readAndParse3DFile(data[i].path);
            if (data3d == null) {
              imgPrincipale.srcImgThumb = "";
              imgPrincipale.srcImgSmall = "";
            } else if (data3d.type === "3DHOP") {
              if (data3d.data["3dhop"].thumbnail) {
                const thumb_name = path_tocourt + data3d.data["3dhop"].thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                imgPrincipale.srcImgThumb = srcImgThumb;
                imgPrincipale.srcImgSmall = srcImgSmall;
              }
            } else {
              if (data3d.data.potree.thumbnail) {
                const thumb_name = path_tocourt + data3d.data.potree.thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                imgPrincipale.srcImgThumb = srcImgThumb;
                imgPrincipale.srcImgSmall = srcImgSmall;
              }
            }
          } else {
            req.flash("error", "Unable to get path for 3d file");
          }
        } else if (data[i].extension === "url") {
          imgPrincipale.srcImgSmall = helpers.setImageSmall(file, pathFile);
          imgPrincipale.srcImgThumb = helpers.setImageThumb(file, pathFile);
          if (data[i].path) {
            const contenuUrl = fs.readFileSync(data[i].path!, "utf-8");
            const urlFich = JSON.parse(contenuUrl);
            if (Object.hasOwn(urlFich, "url")) {
              imgPrincipale.urlTarget = urlFich.url;
              const target = md5(urlFich.url);
              store.set(`hd_${target}`, urlFich.url);
              imgPrincipale.srcImgHd = target;
            }
          }
        } else if (data[i].extension === "json") {
          imgPrincipale.srcImgThumb = "json";
          imgPrincipale.srcImgSmall = "json";
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else if (data[i].extension === "zip") {
          imgPrincipale.srcImgThumb = "zip";
          imgPrincipale.srcImgSmall = "zip";
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else if (data[i].extension === "png") {
          imgPrincipale.srcImgThumb = helpers.setImageThumbFake(file, pathFile);
          imgPrincipale.srcImgSmall = helpers.setImageSmallFake(file, pathFile);
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else if (acceptedPrevisuFormat.includes(data[i].extension.toLowerCase())) {
          // il y a une prévisu
          imgPrincipale.srcImgThumb = helpers.setImageThumb(file, pathFile);
          imgPrincipale.srcImgSmall = helpers.setImageSmall(file, pathFile);
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
          data[i].path = "";
        } else {
          // il n'y a pas de prévisu
          imgPrincipale.srcImgThumb = "";
          imgPrincipale.srcImgSmall = "";
          imgPrincipale.srcImgHd = "";
          data[i].path = "";
        }
      }

      // TODO : récupérer les infos sur les folders :
      // La liste des public folder ne changent pas , on peut la garder en mémoire ?
      // en fonction de qui est connecté , on connait ses droits sur les folders
      performRequest("/api/idPublicFolders/"+branchConfig, "GET", null, (total) => {
        res.render("exploreThes.ejs", {
          nbAccess: data.length,
          //nbAccess: data.nbAcess,
          userId: user.id,
          user: user,
          root: req.params.root,
          WRights: 0, //TODO : affiner plus tard
          data: data,
          layout: false,
          branch: req.params.root,
        });
      });
    },
  ),
);

explore.get(
  "/exploreThesPage,:root,:thesaurus,:idthesaurus,:page",
  asyncHandler(
    async (
      req: Request<{ root: Branch; thesaurus: string; idthesaurus: string; page: string }>,
      res,
    ) => {
      // on ne récupère que une page de la requête (OFFSET (page-1) LIMIT paginer) ET
      // seulement les items autorisés (on passe le id_user dans le body
      const user = res.locals.user as CustomUser;
      const datapost = {
        page: req.params.page,
        pagination: paginer,
        userId: user.id.toString(),
        thes_path: req.query.thes_path as string,
        projectId: req.query.projectId as string,
      };
      const foldersArray: number[] = [];
      const foldersInfo: { id_folder: number; nb: number }[] = [];

      const data = await archeogrid_client.thesaurus.exploreThesaurusSimpleItemPage.query({
        branch: req.params.root,
        thesaurus: req.params.thesaurus,
        thesaurus_id: Number.parseInt(req.params.idthesaurus),
        page: Number.parseInt(req.params.page),
        limit: paginer,
        user_id: user.id,
        projectId: req.query.projectId as string,
      });

      let imgPrincipale = [];
      for (const i in data) {
        data[i].model = "0";
        // trier les items par folder pour ensuite afficher le nb d'items auquel on a access
        if (foldersArray.includes(data[i].idfolder)) {
          // le folder est déjà renseigné on ne fait rien de plus que ajouter l'item
          // récupérer le idfolder et on incrémente le nb à chaque fois
          for (const f in foldersInfo) {
            if (foldersInfo[f].id_folder === data[i].idfolder) {
              foldersInfo[f].nb++;
            }
          }
        } else {
          foldersArray.push(data[i].idfolder);
          // on initie le folderInfo
          foldersInfo.push({
            id_folder: data[i].idfolder,
            nb: 1,
          });
        }
        imgPrincipale = data[i];
        const pathFile = data[i].path;
        const file = data[i].filename;
        const path_tocourt = `${data[i].path.substr(0, data[i].path.lastIndexOf("/"))}/`;
        if (data[i].extension === "3d") {
          const srcImg3d = md5(pathFile);
          store.set(`image3d_${srcImg3d}`, pathFile);
          imgPrincipale.srcImg3d = srcImg3d;

          if (data[i].path) {
            const data3d = await readAndParse3DFile(data[i].path);
            if (data3d == null) {
              imgPrincipale.srcImgThumb = "";
              imgPrincipale.srcImgSmall = "";
            } else if (data3d.type === "3DHOP") {
              if (data3d.data["3dhop"].thumbnail) {
                const thumb_name = path_tocourt + data3d.data["3dhop"].thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                imgPrincipale.srcImgThumb = srcImgThumb;
                imgPrincipale.srcImgSmall = srcImgSmall;
              }
            } else {
              if (data3d.data.potree.thumbnail) {
                const thumb_name = path_tocourt + data3d.data.potree.thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                imgPrincipale.srcImgThumb = srcImgThumb;
                imgPrincipale.srcImgSmall = srcImgSmall;
              }
            }
          } else {
            req.flash("error", "Unable to get path for 3d file");
          }
        } else if (data[i].extension === "url") {
          imgPrincipale.srcImgSmall = helpers.setImageSmall(file, pathFile);
          imgPrincipale.srcImgThumb = helpers.setImageThumb(file, pathFile);
          if (data[i].path) {
            const contenuUrl = fs.readFileSync(data[i].path!, "utf-8");
            const urlFich = JSON.parse(contenuUrl);
            if (Object.hasOwn(urlFich, "url")) {
              imgPrincipale.urlTarget = urlFich.url;
              const target = md5(urlFich.url);
              store.set(`hd_${target}`, urlFich.url);
              imgPrincipale.srcImgHd = target;
            }
          }
        } else if (data[i].extension === "json") {
          imgPrincipale.srcImgThumb = "json";
          imgPrincipale.srcImgSmall = "json";
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else if (data[i].extension === "zip") {
          imgPrincipale.srcImgThumb = "zip";
          imgPrincipale.srcImgSmall = "zip";
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else if (data[i].extension === "png") {
          imgPrincipale.srcImgThumb = helpers.setImageThumbFake(file, pathFile);
          imgPrincipale.srcImgSmall = helpers.setImageSmallFake(file, pathFile);
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
        } else if (acceptedPrevisuFormat.includes(data[i].extension.toLowerCase())) {
          // il y a une prévisu
          imgPrincipale.srcImgThumb = helpers.setImageThumb(file, pathFile);
          imgPrincipale.srcImgSmall = helpers.setImageSmall(file, pathFile);
          imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
          data[i].path = "";
        } else {
          // il n'y a pas de prévisu
          imgPrincipale.srcImgThumb = "";
          imgPrincipale.srcImgSmall = "";
          imgPrincipale.srcImgHd = "";
          data[i].path = "";
        }
      }
      // TODO : récupérer les infos sur les folders :
      // La liste des public folder ne changent pas , on peut la garder en mémoire ?
      // en fonction de qui est connecté , on connait ses droits sur les folders
      performRequest("/api/idPublicFolders/"+branchConfig, "GET", null, (total) => {
        res.render("exploreThes.ejs", {
          nbAccess: data.length,
          //nbAccess: data.nbAcess,
          userId: user.id,
          user: user,
          root: req.params.root,
          WRights: 0, //TODO : affiner plus tard
          data: data,
          layout: false,
          branch: req.params.root,
        });
      });
    },
  ),
);

explore.get(
  "/saveSearch/:projectId,:name,:search",
  (req: Request<{ projectId: string; name: string; search: string }>, res) => {
    let userId = 0;
    if (req.user) {
      userId = req.user.id;
    }
    const projectId = req.params.projectId;
    const name = req.params.name;
    const search = req.params.search;

    if (userId > 0) {
      performRequest(
        `/api/saveSearch/${branchConfig},${projectId},${userId},${encodeURIComponent(
          name,
        )},${encodeURIComponent(search)}`,
        "GET",
        null,
        (data, statusCode) => {
          res.sendStatus(statusCode);
        },
      );
    } else {
      res.sendStatus(401); // not connected
    }
  },
);

explore.get("/getSavedSearches/:projectId", (req, res) => {
  let userId = 0;
  if (req.user) {
    userId = req.user.id;
  }
  const projectId = req.params.projectId;

  if (userId > 0) {
    performRequest(
      `/api/getSavedSearches/${branchConfig},${projectId},${userId}`,
      "GET",
      null,
      (searches) => {
        res.send(searches);
      },
    );
  } else {
    res.sendStatus(401); // not connected
  }
});

explore.get("/removeSearch/:searchId?", (req, res) => {
  let userId = 0;
  if (req.user) {
    userId = req.user.id;
  }
  const searchId =
    req.params.searchId === undefined ? "" : encodeURIComponent(req.params.searchId);

  if (userId > 0) {
    performRequest(
      `/api/removeUserSavedSearch/${branchConfig},${userId}/${searchId}`,
      "GET",
      null,
      (data, statusCode) => {
        res.sendStatus(statusCode);
      },
    );
  } else {
    res.sendStatus(401); // not connected
  }
});

// Client-side metadata endpoint for lazy loading
explore.get(
  "/metadata/:branch/:item_type/:item_id",
  asyncHandler(async (req: Request<{ branch: string; item_type: string; item_id: string }>, res) => {
    try {
      // Extract optional model parameter from query
      const model = req.query.model as string | undefined;

      const metadata = await archeogrid_client.metadata.getFirstMetadata.query({
        branch: req.params.branch as Branch,
        item_id: Number.parseInt(req.params.item_id),
        item_type: req.params.item_type as any,
        model: model // Pass the model parameter to use the full priority logic
      });

      res.json({
        success: true,
        title: metadata?.title || '',
        metadata: metadata
      });
    } catch (error) {
      console.error(`Error fetching metadata for item ${req.params.item_id}:`, error);
      res.json({
        success: false,
        title: '',
        metadata: null
      });
    }
  }),
);

export default explore;
