import { Router } from "express";
import async<PERSON>and<PERSON> from "express-async-handler";
import nodemailer from "nodemailer";
import passport from "passport";
import { v1 as uuid_v1 } from "uuid";

import user_table from "../models/archeogrid_user";
import * as globals from "../tools/globals";
import {branchConfig, defaultLanguage, performRequest, validateEmail} from "../tools/globals";
import { archeogrid_client } from "../tools/request";

const auth = Router();

/**
 *___  _
/ ___|(_) __ _ _ __    _   _ _ __
\___ \| |/ _` | '_ \  | | | | '_ \
 ___) | | (_| | | | | | |_| | |_) |
|____/|_|\__, |_| |_|  \__,_| .__/
         |___/              |_|
*/
auth
  .route("/signupProject")
  .get((req, res) => {
    const root = req.session.root ?? "";
    res.locals.branch = branchConfig
    res.render("signup", {
      username: "",
      emailvalue: "",
      root,
      user: 0,
      email: 0,
      password: 0,
      success: 0,
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      const root = req.session.root ?? "";
      const username = req.body.username;
      const mail = req.body.mail;
      const password2 = req.body.password2;

      const user = await user_table.findOne({ where: { username: username } });

      if (user) {
        res.render("signup", {
          username: "",
          emailvalue: "",
          root,
          user: 1,
          email: 0,
          password: 0,
          success: 0,
        });
        return;
      }

      if (!validateEmail(mail)) {
        res.render("signup", {
          username: username,
          emailvalue: "",
          root: root,
          user: 0,
          email: 1,
          password: 0,
          success: 0,
        });
        return;
      }

      // check password
      if (password2 !== req.body.password) {
        res.render("signup", {
          username: username,
          emailvalue: req.body.mail,
          root: root,
          user: 0,
          email: 0,
          password: 1,
          entity: 0,
          success: 0,
        });
        return;
      }

      const tempUserInput = {
        username: req.body.username,
        password: req.body.password,
        email: req.body.mail,
        note: req.body.note,
        size: req.body.size,
        entity: req.body.depositor,
        language: res.locals.lang,
      };

      // Create temp_user
      const temp_user = await archeogrid_client.tempuser.createTempUser.mutate({
        ...tempUserInput,
      });

      // Create entity
      const entityInput = {};
      Object.assign(entityInput, temp_user);
      Object.assign(entityInput, tempUserInput);
      const entity = await archeogrid_client.entities.createPhysicalEntity.mutate({
        entity: req.body.depositor,
        contact: req.body.contact,
      });

      // Create link between temp_user and entity (update)
      const linkInput = { entity: entity.id.toString() };
      await req.fetchApiCode(`/api/tempuser/link/${temp_user.id}`, linkInput, "PATCH");
      // envoyer un <NAME_EMAIL>
      const transporter = nodemailer.createTransport({
        host: globals.emailHost,
        port: globals.emailPort,
        secure: globals.emailSecure,
      });
      // setup e-mail data with unicode symbols
      const mailOptions = {
        from: "Archeogrid <<EMAIL>>", // sender address
        to: `${globals.emailConfig}`,
        bcc: "<EMAIL>",
        subject: "[Archeogrid] Demande ouverture de compte au conservatoire (CND3D)",
        //text: '', // plaintext body
        html: `<p>Demande ouverture de compte pour ${req.body.mail}, </p><p></p><p>commentaire du demandeur : ${req.body.note}</p><p>volumétrie attendue  : ${req.body.size}</p><p>username : ${username} </p><hr /><p>Mail envoyé automatiquement par Archeogrid</p>`,
      };
      // send mail with defined transport object
      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          console.log(error);
        } else console.log(`Message sent: ${info.response}`);
      });

      res.render("signup", {
        username: username,
        emailvalue: req.body.mail,
        root: root,
        user: 0,
        email: 0,
        password: 0,
        success: 1,
      });
    }),
  );

/**
 *_             _
 | | ___   __ _(_)_ ___
 | |/ _ \ / _` | | '_  \
 | | (_) | (_| | | | | |
 |_|\___/ \__, |_|_| |_|
          |___/
 */
auth.use("/login_oidc", passport.authenticate("oidc"));

auth.use(
  "/auth/callback",
  passport.authenticate("oidc", { failureRedirect: "/error" }),
  (req, res) => {
    if (!req.user) throw new Error();

    const uuid = uuid_v1();
    const dataPost = {
      uuid,
      userId: req.user.id,
      duration: globals.sessionduration,
    };
    
    performRequest("/api/userSessionOIDC", "PUT", dataPost, () => {
      // Determine where to redirect after OIDC login
      let redirectUrl = "projects";
      
      // Check if we have a returnTo URL from the session
      if (req.session.returnTo && typeof req.session.returnTo === 'string') {
        redirectUrl = req.session.returnTo.startsWith('/') ? req.session.returnTo.substring(1) : req.session.returnTo;
        delete req.session.returnTo;
      }
      // Otherwise use session root if available
      else if (req.session.root) {
        redirectUrl = req.session.root;
      }
      
      // Check admin permissions
      if (redirectUrl === "admin" || redirectUrl.startsWith("admin/") || redirectUrl.startsWith("scribe/")) {
        if (req.user?.user_status === "admin" || req.user?.user_status === "scribe") {
          res.redirect(`/${redirectUrl}`);
        } else {
          res.redirect("/projects");
        }
      } else {
        res.redirect(`/${redirectUrl}`);
      }
    });
  },
);

auth
  .route("/login")
  .get((req, res) => {
    res.locals.branch = branchConfig
    
    // Store the page we want to return to after login
    let redirectUrl = "";
    
    // Priority 1: Use returnTo from session if available
    if (req.session.returnTo && typeof req.session.returnTo === 'string') {
      redirectUrl = req.session.returnTo.startsWith('/') ? req.session.returnTo.substring(1) : req.session.returnTo;
    }
    // Priority 2: Use referer header if it's not the login page itself
    else if (req.get('Referer')) {
      const referer = req.get('Referer');
      if (referer && !referer.includes('/login') && !referer.includes('/logout')) {
        try {
          const refererUrl = new URL(referer);
          if (refererUrl.origin === `${req.protocol}://${req.get('host')}`) {
            // Same origin, use the path
            const refererPath = refererUrl.pathname + refererUrl.search;
            redirectUrl = refererPath.startsWith('/') ? refererPath.substring(1) : refererPath;
          }
        } catch (e) {
          // Invalid referer URL, use default
          redirectUrl = "projects";
        }
      }
    }
    // Priority 3: Use session root or default
    else {
      redirectUrl = req.session.root || "projects";
    }
    
    // Clear returnTo to prevent reuse
    delete req.session.returnTo;
    
    res.render("login", {
      reqUrl: redirectUrl,
    });
  })
  .post(
    passport.authenticate("local-signin", { failureRedirect: "/error", failureFlash: true }),
    (req, res) => {
      if (!req.user) throw new Error();

      const uuid = uuid_v1();
      const dataPost = {
        uuid,
        userId: req.user.id,
        duration: globals.sessionduration,
      };
      
      // Get the redirect URL from the form
      const redirectUrl = req.body.reqUrl || "projects";
      
      performRequest("/api/userSession", "PUT", dataPost, () => {
        // Check if user has permission for admin routes
        if (redirectUrl === "admin" || redirectUrl.startsWith("admin/") || redirectUrl.startsWith("scribe/")) {
          if (req.user?.user_status === "admin" || req.user?.user_status === "scribe") {
            res.redirect(`/${redirectUrl}`);
          } else {
            req.flash("error", "not admin user");
            res.redirect("/projects");
          }
        } else {
          // For all other routes, redirect directly
          res.redirect(`/${redirectUrl}`);
        }
      });
    },
  );

auth.get("/connect", (req, res) => {
  res.locals.branch = branchConfig
  
  // Store the referring URL for redirect after login, similar to isLoggedIn middleware
  if (req.get('Referer')) {
    const referer = req.get('Referer');
    if (referer && !referer.includes('/login') && !referer.includes('/connect') && !referer.includes('/logout')) {
      try {
        const refererUrl = new URL(referer);
        if (refererUrl.origin === `${req.protocol}://${req.get('host')}`) {
          // Same origin, store the path for redirect after login
          req.session.returnTo = refererUrl.pathname + refererUrl.search;
        }
      } catch (e) {
        // Invalid referer URL, do nothing
      }
    }
  }
  
  if (!req.cookies.i18n) {
      req.setLocale(defaultLanguage);
  } else {
      req.setLocale(req.cookies.i18n);
  }
  res.render("connect");
});

auth.get("/monuser", (req, res) => {
  res.locals.branch = branchConfig
  res.render("user");
});

// route for user logout
auth.route("/logout").get((req, res, next) => {
  res.locals.branch = branchConfig
  
  // Determine where to redirect after logout
  let redirectUrl = "/";
  
  // Check if we have a redirect query parameter
  if (req.query.redirect && typeof req.query.redirect === 'string') {
    redirectUrl = req.query.redirect;
  }
  // Otherwise try to stay on the current page if possible
  else if (req.get('Referer')) {
    const referer = req.get('Referer');
    if (referer) {
      try {
        const refererUrl = new URL(referer);
        if (refererUrl.origin === `${req.protocol}://${req.get('host')}`) {
          const refererPath = refererUrl.pathname + refererUrl.search;
          
          // Check if the current page requires authentication
          // Pages that don't require auth can be stayed on after logout
          const publicPages = ['/', '/projects', '/search', '/connect'];
          const isPublicPage = publicPages.some(page => refererPath.startsWith(page));
          const isProjectView = /^\/project\/\d+$/.test(refererPath); // Allow staying on project view pages
          
          if (isPublicPage || isProjectView) {
            redirectUrl = refererPath;
          }
          // For protected pages like admin, redirect to home
          else {
            redirectUrl = "/";
          }
        }
      } catch (e) {
        // Invalid referer URL, use default
        redirectUrl = "/";
      }
    }
  }
  
  req.logout((err) => {
    if (err) {
      next(err);
      return;
    }
    res.redirect(redirectUrl);
  });
});

export default auth;
