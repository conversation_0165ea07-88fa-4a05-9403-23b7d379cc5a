<%- include('utils/title-content', { active: "map" }) %>
<div class="container">
    <% if (locals.flash && locals.flash.ok) { %>
    <div class="alert alert-success ">
        <%= locals.flash.ok %>
    </div>
    <% } %>
    <div style="display: none;">
        <% for (var i in data) { if ( data[i]['loc'] ) { %>
        <div class="card item js-marker-list"
             data-lat="<% if ( data[i]['loc'] ) { %><%= data[i]['loc']['latitude'] %><% } else { %>0<% }%>"
             data-lng="<% if ( data[i]['loc']) { %><%= data[i]['loc']['longitude'] %><% } else { %>0<% }%>"
             data-txt="..." style="margin: 0.5em;">

            <a href="/projectv/<%= data[i]['id']%>">
                <img class="card-img-top"
                     src="assets/<% if (data[i]['id_representative_picture'] !== 0) {%><%= data[i]['url'] %><%= data[i]['folder_name'] %>/<%= data[i]['folder_name'] %>.jpg"
                     <% } else { %>images/archeovision.jpg"<% } %> alt="<%=data[i]['name']%>"
                     title="<% if (data[i]['project_name']) { %><%= data[i]['project_name']%><%}%>">
            <div class="card-block"><% if (data[i]['project_name']) { %><%= data[i]['project_name']%><% } %>
                <h5 class=" card-title"><% if (data[i]['project_name']) { %><%= data[i]['project_name']%><% } %></h5>
                <% if (data[i]['nbimages'] != 0) { %>
                <small class="text-muted"><%=data[i]['nbimages']%> documents</small>
                <% }%>
        </div>
        </a>
    </div>
    <% } } %>
</div>
<div class="mapPal" id="map">

</div>
</div>


<script src="https://unpkg.com/leaflet@1.3.3/dist/leaflet.js"
        integrity="sha512-tAGcCfR4Sc5ZP5ZoVz0quoZDYX5aCtEm/eu1KhSLj2c9eFrylXZknQYmxUssFaVJKvvc0dJQixhGjG2yXWiV9Q=="
        crossorigin="">
        </script>
<script src="assets/dist/leaflet.markercluster.js"></script>