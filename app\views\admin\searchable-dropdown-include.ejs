<!-- Include searchable dropdown script -->
<script src="/js/searchable-dropdown.js"></script>

<!-- Initialize searchable dropdowns for admin pages -->
<script>
    // Apply searchable functionality to all select elements with form-select class in admin pages
    document.addEventListener('DOMContentLoaded', function() {
        // Get all select elements in the admin area
        const adminSelects = document.querySelectorAll('.form-select');

        // Make each select searchable if it has more than 5 options and is not a pagination or results per page dropdown
        adminSelects.forEach(select => {
            // Skip pagination, results per page, or dropdowns with few options
            const skipIds = ['nb-results-page', 'pagination-select'];
            const skipClasses = ['pagination-dropdown', 'results-per-page'];

            const shouldSkip =
                skipIds.includes(select.id) ||
                skipClasses.some(cls => select.classList.contains(cls)) ||
                select.options.length <= 5 ||
                select.closest('.pagination') !== null;

            if (!shouldSkip) {
                makeDropdownSearchable(select);
            }
        });
    });
</script>
