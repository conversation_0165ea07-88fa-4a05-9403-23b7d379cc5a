import { z } from "zod";

import { t } from "~/trpcContext";
import type { Branch } from "~/types/api";
import { branch, id, language } from "~/types/schemas";

const getUnicoOutput = z
  .object({
    id,
    id_user: id,
    id_file: id,
    creation_date: z.date(),
    name: z.string(),
    annotation: z.string().nullable(),
    x: z.number(),
    y: z.number(),
    width: z.number(),
    height: z.number(),
    type: z.string(),
    polygon: z.string().nullable(),
  })
  .nullable();

const unicoPageOutput = z
  .object({
    id_item: z.number(),
    item_type: z.string(),
    id_folder: z.number(),
    name: z.string(),
    path: z.string(),
    extension: z.string(),
    idfile: z.number(),
    idfolder: z.number(),
  })
  .array();

const getFileUnicosOutput = z
  .object({
    id,
    id_user: id,
    id_file: z.string().transform(Number),
    creation_date: z.date(),
    name: z.string(),
    annotation: z.string(),
    x: z.number(),
    y: z.number(),
    width: z.number(),
    height: z.number(),
    type: z.string(),
    polygon: z.string().nullable(),
    thesaurus: z
      .object({
        thesaurus: z.string(),
        name: z.string().nullable(),
        status: z.string(),
        tags: z.object({ name: z.string(), id: z.string() }).array(),
      })
      .array(),
  })
  .array();

export default t.router({
  getUnico: t.procedure
    .input(z.object({ branch, id }))
    .output(getUnicoOutput)
    .query(async ({ ctx, input }) => {
      const query = `SELECT id, id_user::int, id_file::int, creation_date, name, annotation, x, y, width, height, type, polygon 
        FROM ${input.branch}_unico 
        WHERE id = $1`;
      const unico = await ctx.database.oneOrNone(query, input.id);
      return getUnicoOutput.parse(unico);
    }),
  unicosPage: t.procedure
    .input(
      z.object({
        branch,
        folder_id: id,
        page: z.number().int().positive().optional(),
        limit: z.number().int().positive().optional(),
      }),
    )
    .output(unicoPageOutput)
    .query(async ({ input, ctx }) => {
      let query = `SELECT u.id AS id_item,
        'unico' AS item_type,
        0 AS id_folder,
        u.name AS name,
        fi.path AS path,
        fi.file_ext AS extension,
        fi.id::integer AS idfile,
        fo.id::integer AS idfolder,
        u.x, u.y, u.width, u.height, u.type, u.polygon
        FROM ${input.branch}_unico u
        INNER JOIN ${input.branch}_file fi ON fi.id = u.id_file
        INNER JOIN ${input.branch}_folder fo ON fo.id = fi.id_folder AND fo.folder_path <@ '${input.folder_id}'`;

      if (input.page && input.limit) {
        query += `OFFSET ${(input.page - 1) * input.limit} LIMIT ${input.limit}`;
      }

      return unicoPageOutput.parse(await ctx.database.any(query));
    }),
  getFileUnicos: t.procedure
    .input(z.object({ branch, language, project_id: id, file_id: id }))
    .query(async ({ ctx, input }) => {
      // si on est en français, on prend le nom du thesaurus dans le champ name,
      // sinon on prend le nom du thesaurus dans le champ short_name qui est en anglais
      const short_name_column = input.language === "fr" ? "name" : "short_name";
      const name_column = input.language === "fr" ? "name" : `name_${input.language}`;

      const query = `
      SELECT u.*, COALESCE(json_agg(thes.thesaurus) FILTER (WHERE thes IS NOT NULL), '[]') AS thesaurus
      FROM (
          (
              SELECT *
              FROM ${input.branch}_unico u
              WHERE u.id_file = $1
          ) AS u
          LEFT OUTER JOIN
          (
              (
                  SELECT id, json_build_object(
                      'thesaurus', thesaurus,
                      'name', thesname,
                      'status', status,
                      'tags', json_agg(tag)
                  ) AS thesaurus
                  FROM (
                      SELECT u.id, tname.${short_name_column} AS thesname, t.thesaurus AS thesaurus, 'thesaurus' AS status, json_build_object(
                          'name', t.name,
                          'id', CONCAT(t.id, '_', t.id_thes)
                      ) AS tag
                      FROM ${input.branch}_unico u
                      INNER JOIN ${input.branch}_thesaurus_item ti ON u.id = ti.id_item AND ti.item_type = 'unico'
                      INNER JOIN ${input.branch}_thesaurus t ON t.id_thes = ti.id_thes_thesaurus AND t.thesaurus = ti.thesaurus
                      LEFT OUTER JOIN ${input.branch}_thesaurus tname ON tname.thesaurus = t.thesaurus AND tname.id_parent IS NULL
                      WHERE u.id_file = $1
                  ) toto
                  GROUP BY id, thesaurus, thesname, status
              )
              UNION ALL
              (
                  SELECT id, json_build_object(
                      'thesaurus', thesaurus,
                      'name', thesname,
                      'status', status,
                      'tags', json_agg(tag)
                  ) AS thesaurus
                  FROM (
                      SELECT u.id, tname.${short_name_column} AS thesname, t.thesaurus AS thesaurus, 'multi' AS status, json_build_object(
                          'name', t.name,
                          'id', CONCAT(t.id, '_', t.id_thes)
                      ) AS tag
                      FROM ${input.branch}_unico u
                      INNER JOIN ${input.branch}_thesaurus_multi_item ti ON u.id = ti.id_item AND ti.item_type = 'unico'
                      INNER JOIN ${input.branch}_thesaurus_multi t ON t.id_thes = ti.id_thes_thesaurus AND t.thesaurus = ti.thesaurus
                      LEFT OUTER JOIN ${input.branch}_thesaurus_multi tname ON tname.thesaurus = t.thesaurus AND tname.id_parent IS NULL
                      WHERE u.id_file = $1
                  ) toto
                  GROUP BY id, thesaurus, thesname, status
              )
              UNION ALL
              (
                  SELECT id, json_build_object(
                      'thesaurus', thesaurus,
                      'name', thesname,
                      'status', status,
                      'tags', json_agg(tag)
                  ) AS thesaurus
                  FROM (
                      SELECT u.id, COALESCE(t.${name_column}, t.name_en) AS thesname, t.thesaurus AS thesaurus, 'pactols' AS status, json_build_object(
                          'name', t.${name_column} ,
                          'id', CONCAT(t.id, '_', t.id_thes)
                      ) AS tag
                      FROM ${input.branch}_unico u
                      INNER JOIN ${input.branch}_thesaurus_pactols_item ti ON u.id = ti.id_item AND ti.item_type = 'unico'
                      INNER JOIN ${input.branch}_thesaurus_pactols t ON t.id_thes = ti.id_thes_thesaurus AND t.thesaurus = ti.thesaurus
                      INNER JOIN ${input.branch}_folder_thesaurus_pactols tfolder ON tfolder.thesaurus = t.thesaurus AND tfolder.id_folder = $2
                      WHERE u.id_file = $1
                  ) toto
                  GROUP BY id, thesaurus, thesname, status
              )
              UNION ALL
              (
                  SELECT id, json_build_object(
                      'thesaurus', thesaurus,
                      'name', thesname,
                      'status', status,
                      'tags', json_agg(tag)
                  ) AS thesaurus
                  FROM (
                      SELECT u.id, COALESCE(t.${name_column}, t.name_en) AS thesname, 'lieux' AS thesaurus, 'geopactols' AS status, json_build_object(
                          'name', t.name,
                          'id', CONCAT(t.id, '_', t.identifier)
                      ) AS tag
                      FROM ${input.branch}_unico u
                      INNER JOIN ${input.branch}_thesaurus_pactols_geo_item ti ON u.id = ti.id_item AND ti.item_type = 'unico'
                      INNER JOIN ${input.branch}_thesaurus_pactols_geo t ON t.id = ti.id_thesaurus
                      INNER JOIN ${input.branch}_folder_thesaurus_pactols tfolder ON tfolder.thesaurus = 'lieux' AND tfolder.id_folder = $2
                      WHERE u.id_file = $1
                  ) toto
                  GROUP BY id, thesaurus, thesname, status
              )
          ) AS thes ON u.id = thes.id
      )
      GROUP BY u.id, u.id_user, u.id_file, u.creation_date, u.name, u.annotation, u.x, u.y, u.width, u.height, u.type, u.polygon;`;

      const unicos = await ctx.database.manyOrNone(query, [input.file_id, input.project_id]);

      return getFileUnicosOutput.parse(unicos);
    }),

  updateUnico: t.procedure
    .input(
      z.object({
        branch,
        user_id: id,
        unico_id: id,
        name: z.string(),
        thesaurus: z
          .object({
            ids: z.string().array().optional(),
            thesaurus: z.string(),
            status: z.union([
              z.literal("multi"),
              z.literal("thesaurus"),
              z.literal("pactols"),
              z.literal("chrono"),
              z.literal("geopactols"),
            ]),
          })
          .array(),
        annotation: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const annotation = input.annotation.replace(/'/g, "''");
      const name = input.name.replace(/'/g, "''");

      const query = `UPDATE ${input.branch}_unico SET name = $1, annotation = $2 WHERE id = $3`;

      await ctx.database.none(query, [name, annotation, input.unico_id]);

      const queryDelete = `DELETE FROM ${input.branch}_thesaurus_item
          WHERE id_item = $1 AND item_type = 'unico';
          DELETE FROM ${input.branch}_thesaurus_multi_item
          WHERE id_item = $1 AND item_type = 'unico'`;

      await ctx.database.none(queryDelete, input.unico_id);

      type ThesaurusData = {
        status: (typeof input.thesaurus)[number]["status"];
        thesaurus: string;
        ids: string[];
      };
      const affected_thesaurus = input.thesaurus.filter((t): t is ThesaurusData => Boolean(t.ids?.length));

      type ThesaurusType = "multi" | "simple" | "pactols" | "chrono" | "geopactols";
      type ThesaurusFunction = (
        branch: Branch,
        item: {
          id: number;
          type: string;
          thesaurus: string;
          thesaurus_id: number;
          id_thes: number;
        },
        id_user: number,
      ) => Promise<boolean>;
      type ThesaurusGeopactolsFunction = (
        branch: Branch,
        uri: string,
        name: string,
        id_item: number,
        item_type: string,
        id_user: number,
      ) => Promise<boolean>;

      type ThesaurusStatusBase = {
        type: ThesaurusType;
        table: string;
        second_table?: string;
        function: ThesaurusFunction | ThesaurusGeopactolsFunction;
      };

      type Multi = {
        type: "multi";
        table: "_thesaurus_multi_item";
        function: ThesaurusFunction;
      };
      type Simple = { type: "simple"; table: "_thesaurus_item"; function: ThesaurusFunction };
      type Pactols = {
        type: "pactols";
        table: "_thesaurus_pactols_item";
        function: ThesaurusFunction;
      };
      type Chrono = {
        type: "chrono";
        table: "_thesaurus_pactols_item";
        function: ThesaurusFunction;
      };
      type Geo = {
        type: "geopactols";
        second_table: "_thesaurus_pactols_geo";
        function: ThesaurusGeopactolsFunction;
      };

      type ThesaurusStatus = ThesaurusStatusBase & (Multi | Simple | Pactols | Chrono | Geo);

      for await (const thesaurus of affected_thesaurus) {
        let status: ThesaurusStatus;
        switch (thesaurus.status) {
          case "multi":
            status = {
              type: "multi",
              table: "_thesaurus_multi_item",
              function: ctx.thesaurus.insertMultiItem,
            };
            break;
          case "thesaurus":
            status = {
              type: "simple",
              table: "_thesaurus_item",
              function: ctx.thesaurus.insertItem,
            };
            break;
          case "pactols":
            status = {
              type: "pactols",
              table: "_thesaurus_pactols_item",
              function: ctx.thesaurus.insertPactolsItem,
            };
            break;
          case "chrono":
            status = {
              type: "chrono",
              table: "_thesaurus_pactols_item",
              function: ctx.thesaurus.insertPactolsItem,
            };
            break;
          case "geopactols":
            status = {
              type: "geopactols",
              table: "_thesaurus_pactols_geo_item",
              second_table: "_thesaurus_pactols_geo",
              function: ctx.thesaurus.insertPactolsGeoAndItem,
            };
            break;
          default:
            throw new Error("Wrong thesaurus type");
        }

        for await (const id of thesaurus.ids) {
          const [id_thesaurus, id_thes_thesaurus] = id.split("_");
          const thesQuery =
            thesaurus.thesaurus === "lieux"
              ? `SELECT count(*) FROM ${input.branch}${status.table} WHERE id_item = ${input.unico_id} AND item_type = 'unico' AND id_thesaurus = (SELECT id FROM ${input.branch}${status.second_table} WHERE identifier = '${id_thes_thesaurus}')`
              : `SELECT count(*) FROM ${input.branch}${status.table} WHERE thesaurus = '${thesaurus.thesaurus}' AND id_item = ${input.unico_id} AND item_type = 'unico' AND id_thesaurus = ${id_thesaurus};`;
          const { count } = await ctx.database.one(thesQuery);
          if (count === "0") {
            if (thesaurus.thesaurus !== "lieux" && status.type !== "geopactols") {
              await status.function(
                input.branch,
                {
                  id: input.unico_id,
                  type: "unico",
                  thesaurus: thesaurus.thesaurus,
                  thesaurus_id: Number.parseInt(id_thesaurus),
                  id_thes: Number.parseInt(id_thes_thesaurus),
                },
                input.user_id,
              );
            } else if (status.type === "geopactols") {
              // geo pactols 1 paramètre en moins et pas dans le même ordre
              await status.function(
                input.branch,
                id_thes_thesaurus,
                id_thesaurus,
                input.unico_id,
                "unico",
                input.user_id,
              );
            }
          }
        }
      }

      return true;
    }),

    getMetadata: t.procedure
    .input(z.object({branch, metadata_model: z.string(), language, id}))
    .query(async ({ctx, input}) => {
      let metadata_model = await ctx.database.manyOrNone(`
        SELECT m.*, ml.label, ml.description
        FROM ${input.branch}_metadata m
        INNER JOIN ${input.branch}_metadata_label ml ON ml.id_metadata = m.id
        INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
        WHERE mm.name = '${input.metadata_model}'
        AND ml.language = '${input.language}'`
      )

      return await ctx.metadata.retrieveMetadataValueOfModel(input.branch, metadata_model, input.id, "unico");
    })
});
