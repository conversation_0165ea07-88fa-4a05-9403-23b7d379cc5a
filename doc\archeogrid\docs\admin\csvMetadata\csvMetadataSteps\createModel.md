---
sidebar_position: 2
---

# Create a model

The creation off the model will create a new metadata model in the database. The matadata that compose the model are the columns of the CSV file. This features is divided in three steps. If you come from the menu you will start from the project selection, but if you come from another features you will start from the CSV file selection with information already displayed.

### Select the project

This step allows you to choose the project where your CSV file is located. You can use the search bar to find a specific project.

![](projectSelector-CM-EN.png)

> **Note**: Only the project that you have access to are displayed.

### Select the CSV file

This step allows you to choose the CSV file that you want to use. This requires to define the delimiter used in the CSV file, the wanted name of the model and the type of items that the CSV describes. The default name for the model is the name of the branch followed by the name of the CSV file. 

![](csvFileSelector-CM-EN.png)

> **Error**: An error will be displayed if the metadata folder doesn't exist and you'll be redirected to the menu. If there is no CSV file in the folder, a message will be displayed allowing you to go back to the project selection.

### Metadata model details

This step allows you to define more precisly the metadata model. You can add a description, and customize every metadata of the model. For each metadata you can define it's label, description, type, if it's unique and if it's required/mandatory. Default values are provided for each metadata. At the end of the form you can click on the Create button to create the model.

![](modelDetails-CM-EN.png)

> **Error**: An error will be displayed if the csv file doesn't exists, if there is no header in the file, if there is only one column in the file or if the model already exists.

### Result

If the model have been created, a success message will be displayed. You can either go create the corresponding table, ingest the data of the CSV into the model or go back to the menu. 

![](success-CM-EN.png)