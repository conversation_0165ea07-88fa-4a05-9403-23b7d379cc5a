.form-range {
    height: 1.5rem;
    padding: 0;
    align-items: center;
    justify-content: center;
    display: flex;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-range:focus {
    outline: none;
}

.form-range {
  accent-color: var(--slider-thumb-color, #666);
}

.form-range:hover {
  accent-color: var(--slider-thumb-hover-color, #444);
}

/* Responsive grid and list size variables */
:root {
    --grid-columns: 7;
    --list-size-level: 3; /* Default list size level (1-5) */
}

/* Update grid styles to use CSS variables */
.vitrine-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, 150px) !important;
    gap: 8px !important;
    flex: auto;
    justify-content: center !important;
}

.grid-size-active .vitrine-grid {
    grid-template-columns: repeat(var(--grid-columns), 1fr) !important;
}

.grid-size-active[data-columns="4"] .vitrine-grid {
    grid-template-columns: repeat(4, 1fr) !important;
}

.grid-vitrine-item {
    width: 150px !important;
}

.grid-size-active .grid-vitrine-item {
    width: 100% !important;
}

/* Keep original image container styling */
.grid-vitrine-img-link {
    margin: 0 !important;
    padding: 0 !important;
    display: block;
    width: 100%;
    height: auto;
    background-color: #f8f9fa !important;
    overflow: hidden;
    text-align: center;
}

.vitrine-img {
    max-height: 100px !important; /* Original max height */
    object-fit: contain;
    padding: 2px !important;
    margin-bottom: 0 !important;
    width: auto !important;
    max-width: 100% !important;
}

/* Scale image height proportionally when grid size changes */
.grid-size-active .vitrine-img {
    max-height: calc(100px * var(--scale-factor, 1)) !important;
}

/* Card body styling is maintained from original styles */

@media (max-width: 950px) {
    .vitrine-grid {
        grid-template-columns: repeat(auto-fill, 120px) !important;
    }

    .grid-vitrine-item {
        width: 120px !important;
    }

    .grid-size-active .vitrine-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .vitrine-img {
        max-height: 80px !important;
    }

    .grid-size-active .vitrine-img {
        max-height: calc(80px * var(--scale-factor, 1)) !important;
    }
    
    /* Responsive adjustments for list mode */
    .list-size-active .list-vitrine-item {
        height: calc((80px + (var(--list-size-level) - 1) * 20px) * 0.8) !important;
    }

    .list-size-active .list-vitrine-item .list-vitrine-image {
        width: calc((80px + (var(--list-size-level) - 1) * 20px) * 0.8) !important;
        height: calc((80px + (var(--list-size-level) - 1) * 20px) * 0.8) !important;
        flex: 0 0 calc((80px + (var(--list-size-level) - 1) * 20px) * 0.8) !important;
    }
    
    .list-size-active .list-vitrine-item .list-vitrine-image .list-img {
        max-height: 100% !important;
        max-width: 100% !important;
    }
}

/* List size slider styles - discrete levels like grid slider */
.list-size-active .list-vitrine-item {
    height: calc(100px + (var(--list-size-level) - 1) * 20px) !important;
    transition: none !important; /* Disable transition for instant resizing */
}

.list-size-active .list-vitrine-item .list-vitrine-image {
    height: calc(100px + (var(--list-size-level) - 1) * 20px) !important;
    width: calc(100px + (var(--list-size-level) - 1) * 20px) !important;
    flex: 0 0 calc(100px + (var(--list-size-level) - 1) * 20px) !important;
}

.list-size-active .list-vitrine-item .list-vitrine-image .list-img {
    max-height: 100% !important;
    max-width: 100% !important;
}

/* Compact View Styles */
.compact-list-view .list-vitrine-item {
    height: 50px !important;
    min-height: 50px !important;
}

.compact-list-view .list-vitrine-image {
    display: none !important;
}

.compact-list-view .list-vitrine-content {
    flex: 1 !important;
    padding: 0.5rem 1rem !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    height: 100% !important;
}

.compact-list-view .list-vitrine-title {
    font-weight: 500 !important;
    color: #333 !important;
    margin-bottom: 0 !important;
    display: flex !important;
    align-items: center !important;
    flex: 1 !important;
    margin-right: 1rem !important;
    line-height: 1.2 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.compact-list-view .list-vitrine-content > .d-flex {
    flex-shrink: 0 !important;
    margin-left: auto !important;
}

.compact-list-view .list-icon-btn {
    width: 32px !important;
    height: 32px !important;
    font-size: 0.8rem !important;
}

/* Compact view slider styling */
#compact-slider-container .slider-icon {
    font-size: 0.9rem;
    color: var(--primary-color);
    user-select: none;
    cursor: default;
    transition: color 0.2s ease;
}

#compact-slider {
    transition: all 0.2s ease;
}

/* Grid view slider styling */
#grid-slider-container .slider-icon {
    font-size: 0.9rem;
    color: var(--primary-color);
    user-select: none;
    cursor: default;
    transition: color 0.2s ease;
}

#grid-slider {
    transition: all 0.2s ease;
}

@media (max-width: 768px) {
    #compact-slider-container .slider-icon, #grid-slider-container .slider-icon {
        font-size: 0.8rem;
    }

    #compact-slider, #grid-slider {
        width: 50px;
    }
}

#grid-slider-tooltip {
    position: absolute;
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
    z-index: 1050;
}

.slider-wrapper:hover #grid-slider-tooltip,
.slider-wrapper:active #grid-slider-tooltip {
    opacity: 1;
}

