import { Router, type Request } from "express";
import async<PERSON>and<PERSON> from "express-async-handler";
import fs from "node:fs";
import store from "store";

import type { Branch, Requests } from "../../server/types/api";
import {
  acceptedPrevisuFormat,
  acceptedVideoFormats,
  branchConfig,
  isLoggedIn,
  readAndParse3DFile,
  viewerFormat,
} from "../tools/globals";
import * as helpers from "../tools/helpers_tools";
import { notNull } from "../tools/helpers_tools";
import { archeogrid_client } from "../tools/request";

const md5 = require("md5");

const defined = <TValue>(value: TValue | undefined): value is TValue => {
  return value !== undefined && value !== null;
};

const edit = Router();

/**
            _ _ _
    ___  __| (_) |_
  / _ \/ _`  | | ___|
 |  __/ (_|  | | |_
 \___|\__,_|_| \___|

 */
edit
  .route("/edit,:root,:model,:type,:idItem,:idFolder")
  .get(
    isLoggedIn,
    asyncHandler(
      async (
        req: Request<{
          root: Branch;
          model: string;
          type: string;
          idItem: string;
          idFolder: string;
        }>,
        res,
      ) => {
        const type = req.params.type;
        if (type !== "file" && type !== "folder" && type !== "object" && type !== "comment" && type !== "unico") {
          throw new Error(`Invalid type! (${type})`);
        }

        const branch = req.params.root;
        const item_id = Number.parseInt(req.params.idItem);
        const folder_id = Number.parseInt(req.params.idFolder);
        const language = res.locals.lang ?? "fr";
        let model = Number.parseInt(req.params.model) === 0 ? "DublinCore" : req.params.model;
        const user = res.locals.user;

        //unico
        if (req.params.type === 'unico') {
          model = Number.parseInt(req.params.model) === 0 ? "DublinCoreUnico" : req.params.model;
        }

        res.locals.myroot = branch === branchConfig ? "/projectsL" : `/${branch}`;

        if (!req.user) {
          req.session.returnTo = req.originalUrl;
          res.redirect("/login");
          return;
        }

        const overallFolders = await archeogrid_client.projects.overallProjects.query({
          branch,
        });

        const projectOverall = await req.fetchApi(`/api/projectWithOverall/${branchConfig},${item_id}`);

        const metadata = await archeogrid_client.metadata.getMetadataWithType.query({
          branch,
          language,
          type,
          model,
        });

        console.log("metadata =>", metadata)

        const tabnomen = metadata
          .filter((m) => m.status === "nomenclature")
          .map((m) => m.list)
          .filter(notNull);
        const tabmulti = metadata
          .filter((m) => m.status === "multi")
          .map((m) => m.list)
          .filter(notNull);
        const tabthes = metadata
          .filter((m) => m.status === "thesaurus")
          .map((m) => m.list)
          .filter(notNull);
        const tabpactols = metadata
          .filter((m) => m.status === "pactols")
          .filter((m): m is (typeof metadata)[number] & { list: string } => Boolean(m.list))
          .map((m) => {
            return { list: m.list, filter: m.function };
          })
          .filter(notNull);
        const thesinfo = metadata.some((m) => m.status === "multi");
        const tablist = metadata
          .filter((m) => m.status === "list" || m.status === "choico")
          .map((m) => Number.parseInt(m.list ?? ""));

        const uniqueTablist = helpers.getuniqueArray(tablist);

        const nomenclature = (
          await Promise.all(tabnomen.map((n) => archeogrid_client.thesaurus.find.query({ branch, thesaurus: n })))
        ).flat();

        const thesaurus: Record<string, any[]> = {};
        for (const thes of tabthes) {
          thesaurus[thes] = await archeogrid_client.thesaurus.find.query({ branch, thesaurus: thes });
        }

        console.log("thesaurus =>", thesaurus);

        const pactols: {
          filter: number;
          data: {
            id: number;
            name: string;
            id_thes: number;
            thesaurus: string;
            short_name: string;
            thesaurus_path: string;
          }[];
        }[] = [];

        for await (const p of tabpactols) {
          pactols.push({
            filter: p.filter,
            data: await archeogrid_client.thesaurus.getPactols.query({
              branch,
              thesaurus: p.list,
              filter: Number.parseInt(p.filter),
            }),
          });
        }

        const multi = (
          await Promise.all(tabmulti.map((m) => archeogrid_client.thesaurus.getMulti.query({ branch, thesaurus: m })))
        ).flat();

        const listeMetadata = await Promise.all(
          tablist.map((l) =>
            archeogrid_client.metadata.getMetadataListId.query({
              branch,
              list_id: l,
            }),
          ),
        );

        //on compte le nb de concept dans chaque thesaurus multi pour savoir ensuite si on l'affiche ou pas en select
        const multicounts = multi.reduce((acc, item) => {
          acc[item.thesaurus] = (acc[item.thesaurus] || 0) + 1;
          return acc;
        }, {});

        const mainfolder = await archeogrid_client.folders.mainFolder.query({
          branch,
          language,
          folder_id,
        });

        if (!mainfolder) throw new Error("Main folder not found!");
        if (branch === branchConfig) {
        // get image logo ?
        const project = await archeogrid_client.projects.projectsFull.query({
          branch: branch,
          language: res.locals.lang,
          project_id: Number.parseInt(mainfolder.mainfolder),
        });
        if (!project) throw new Error("Project does not exist!");
        mainfolder.image = project.image;
      }

        const allmetadata = await archeogrid_client.metadata.allMetadata.query({
          branch,
          language,
          type,
          id: item_id,
        });

        const list = await archeogrid_client.metadata.getMetadataList.query({ branch });

        let thesaurus_periodo;
        if(req.session.thesaurus_periodo){
          thesaurus_periodo = req.session.thesaurus_periodo;
        }else{
          thesaurus_periodo = await archeogrid_client.thesaurus.thesaurusPeriodO.query({branch: branch});
          req.session.thesaurus_periodo = thesaurus_periodo;
        }

        const base_params = {
          idFolder: req.params.idFolder,
          image: "",
          list,
          listemetadata: listeMetadata,
          mainFolder: mainfolder,
          metadata,
          multi,
          multicounts,
          pactols,
          nomenclature,
          thesaurus,
          overallFolders,
          projectOverall,
          redirect: res.locals.myroot,
          repre_image: mainfolder.id_representative_picture,
          root: branch,
          branch: branchConfig,
          tablist: uniqueTablist,
          tabmulti,
          tabpactols,
          tabnomen,
          tabthes,
          thesinfo,
          type: req.params.type,
          val: [],
          video: acceptedVideoFormats,
          viewerFormat,
          periodo: thesaurus_periodo,
          language
        };

        const is_authorized =
          res.locals.user_status === "admin" ||
          (res.locals.user_status === "scribe" && req.user.read.includes(folder_id)) ||
          (res.locals.user_status === "user" && req.user.write.includes(folder_id));

        if (!is_authorized) {
          if (req.params.type !== "folder") {
            req.flash("error", "Not authorized");
            return;
          }

          const user_access = await req.fetchApi(`/api/userAccessFolder/${user.id},${item_id},${branch}`);

          if (!user_access.rights) {
            // on est connecté mais on n'a pas les droits sur cet item
            req.flash("error", "Not authorized");
            return;
          }

          const data = await archeogrid_client.metadata.getMetadataModel.query({
            branch,
            language,
          });

          const same_type_data = data.filter((m) => m.metadata_type === type);
          const modellist = same_type_data.map((m) => m.name);
          const modeldesc = same_type_data.map((m) => m.description);

          for (let i = 0; i < data.length; i++) {
            if (data[i].metadata_type === req.params.type) {
              modellist.push(data[i].name);
              modeldesc.push(data[i].description);
            }
          }

          const val = allmetadata[model] ?? [];
          const folder = allmetadata.name;
          const thesaurusVal = allmetadata.thesaurus;
          const nomenVal = allmetadata.nomenclature;
          const multiVal = allmetadata.multi;
          const pactolsVal = allmetadata.pactols;

          res.render("edit", {
            ...base_params,
            idItem: 0,
            item: [],
            values: allmetadata,
            val: val,
            thesval: thesaurusVal,
            nomenval: nomenVal,
            multival: multiVal,
            pactolsval: pactolsVal,
            folder: folder,
            model: model,
            modelist: modellist,
            modeldesc: modeldesc,
            data: data
          });

          return;
        }

        let item, repre_image = null;
        switch (type) {
          case "object":
            item = await req.fetchApi(`/api/objectId/${branch},${item_id}`);
            repre_image = item.id_file_representative;
            break;
          case "unico":
            if (item_id) {
              item = await archeogrid_client.unico.getUnico.query({branch, id: item_id});
              if (!item) throw new Error("Unico not found!");
              repre_image = item.id_file;
            } else {
              console.log('indexation multiple unico')
            }
            break;
          default:
            repre_image = item_id;
            break;
        }

        const object_data = repre_image
          ? await archeogrid_client.files.prepareImage.query({ branch, file_id: repre_image })
          : null;

        let image:
          | (typeof object_data & {
              urlTarget: string;
              srcImgThumb: string;
              srcImg3d: string;
              srcImgSmall: string;
              srcImgHd: string;
            })
          | null = null;

        if (object_data) {
          // si c'est bien une image sinon le type est folder et n'affichera pas d'image
          image = {
            ...object_data,
            urlTarget: "",
            srcImgThumb: "",
            srcImg3d: "",
            srcImgSmall: "",
            srcImgHd: "",
          };
          const file = object_data.filename;
          const pathFile = object_data.path;
          const short_path = `${pathFile.substring(0, pathFile.lastIndexOf("/"))}/`;
          if (image.file_ext === "url") {
            const contenuUrl = fs.readFileSync(image.path, "utf-8");
            const urlFich = JSON.parse(contenuUrl);
            if (Object.hasOwn(urlFich, "url")) {
              image.urlTarget = urlFich.url;
            }
            image.srcImgThumb = helpers.setImageThumb(file, pathFile);
          } else if (image.file_ext === "3d") {
            const srcImg3d = md5(pathFile);
            store.set(`image3d_${srcImg3d}`, pathFile);
            image.srcImg3d = srcImg3d;
            if (image.path) {
              const data3d = await readAndParse3DFile(image.path);
              if (!data3d) {
                req.flash("error", "Unable to load .3d file");
                return;
              }
              if (data3d.type === "3DHOP") {
                if (data3d.data["3dhop"].thumbnail) {
                  const thumb_name = short_path + data3d.data["3dhop"].thumbnail;
                  const srcImgThumb = md5(thumb_name);
                  const srcImgSmall = srcImgThumb;
                  store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                  store.set(`small_${srcImgThumb}`, thumb_name);

                  image.srcImgThumb = srcImgThumb;
                  image.srcImgSmall = srcImgSmall;
                }
              } else {
                if (data3d.data.potree.thumbnail) {
                  const thumb_name = short_path + data3d.data.potree.thumbnail;
                  const srcImgThumb = md5(thumb_name);
                  const srcImgSmall = srcImgThumb;
                  store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                  store.set(`small_${srcImgThumb}`, thumb_name);

                  image.srcImgThumb = srcImgThumb;
                  image.srcImgSmall = srcImgSmall;
                }
              }
            } else {
              req.flash("error", "Unable to get path for 3d file");

              return;
            }
          } else if (image.file_ext === "json") {
            image.srcImgThumb = "json";
            image.srcImgSmall = "json";
            image.srcImgHd = helpers.setImageHd(file, pathFile);
          } else if (image.file_ext === "png") {
            image.srcImgThumb = helpers.setImageThumbFake(file, pathFile);
            image.srcImgSmall = helpers.setImageSmallFake(file, pathFile);
            image.srcImgHd = helpers.setImageHd(file, pathFile);
          } else if (acceptedPrevisuFormat.includes(image.file_ext)) {
            image.srcImgThumb = helpers.setImageThumb(file, pathFile);
            image.srcImgHd = helpers.setImageHd(file, pathFile);
          } else {
            image.srcImgThumb = "";
            image.srcImgHd = "";
          }
        }

        // Cette route ne renvoie maintenant que les modèles liés au projet
        // pour renvoyer plus largement tous les modèles de tous les projets, utiliser la route getMetadataModelGeneral
        const data = await archeogrid_client.metadata.getMetadataModelProject.query({
          branch,
          language,
          folder_id,
        });

        const same_type_data = data.filter((m) => m.metadata_type === type);
        const modellist = same_type_data.map((m) => {
          return { name: m.name, label: m.label };
        });
        const modeldesc = same_type_data.map((m) => m.description);

        if (!item_id) {
          // on indexe tout un répertoire
          const values = { id: 0 };
          const folderSimple = await req.fetchApi(`/api/folderSimple/${folder_id}, ${branch}`);

          res.render("edit", {
            ...base_params,
            idItem: item_id,
            item,
            values,
            val: [],
            thesval: [],
            nomenval: [],
            multival: [],
            pactolsval: [],
            folder: folderSimple,
            model,
            modelist: modellist,
            modeldesc,
            data: data,
          });

          return;
        }

        const val = allmetadata[model] ?? [];
        // Traiter le résultat pour récupérer des valeurs simples comme avant pour les métadonnées de type unique
        for (const v of val) {     
          if (v.status ===  'actor' || v.status ===  'datation' || v.status ===  'location'){
            // Check if there is values
            if(v.value.length === 0) continue;
            // Check if the values are not nullish (null, undefined or empty)
            if(v.value.every((meta_val: string | null | undefined) => !meta_val)) {
              v.value = [];
              continue;
            }
            // Try to parse the string values into json
            try {
              v.value = JSON.parse(v.value);
            } catch (e) {
              console.log(e);
              v.value = v.value;
            }
            v.value = Array.isArray(v.value) ? v.value : [v.value];
          }else if (v.isunique) v.value = v.value[0];
        }
        // TODO : traiter les valeurs multiples à afficher sur les metadonnées de type non unique
        const folder = allmetadata.name;
        const thesaurusVal = allmetadata.thesaurus;
        const nomenVal = allmetadata.nomenclature;
        const multiVal = allmetadata.multi;
        const pactolsVal = allmetadata.pactols;

        console.log("metadatas =>", allmetadata[model]);

        res.render("edit", {
          ...base_params,
          idItem: item_id,
          item,
          values: allmetadata,
          val,
          thesval: thesaurusVal,
          nomenval: nomenVal,
          multival: multiVal,
          pactolsval: pactolsVal,
          folder,
          model,
          modelist: modellist,
          modeldesc,
          data,
          image
        });
      },
    ),
  )
  .post(
    isLoggedIn,
    asyncHandler(
      async (
        req: Request<{
          root: Branch;
          model: string;
          type: string;
          idItem: string;
          idFolder: string;
        }>,
        res,
      ) => {
        const type = req.params.type;

        if (type !== "file" && type !== "folder" && type !== "object" && type !== "unico") {
          throw new Error(`Invalid type! (${type})`);
        }

        const branch = req.params.root;
        const item_id = Number.parseInt(req.params.idItem);
        const folder_id = Number.parseInt(req.params.idFolder);
        const language = res.locals.lang ?? "fr";
        const model = req.params.model === "0" ? "DublinCore" : req.params.model;
        const user = res.locals.user;

        if (branch === branchConfig) {
          if (req.params.type === "folder") res.locals.myroot = "/projectsL";
          else res.locals.myroot = "/projects";
        } else {
          res.locals.myroot = branch;
        }

        // supprimer les retours chariots qui s'enregistrent inopinément quand on referme le questionnaire
        for (const b in req.body) {
          if (req.body[b]) {
            if (typeof req.body[b] === "object") {
              for (const ob in req.body[b]) {
                req.body[b][ob] = req.body[b][ob].trim();
              }
            } else {
              req.body[b] = req.body[b].trim();
            }
          }
        }
        let overall = [];
        let datapost = req.body;

        type DataItem = Requests["metadata"]["updateValues"]["data"][number];

        const advanced_model_datapost = Object.entries(datapost).filter(([key]) => key.match(/^[0-1]#(datation|location|actor)_(\d+)_(\d+)$/));
        let advanced_form_data: DataItem[] = [];
        if (advanced_model_datapost.length > 0) {
          const adv_old_datapost = advanced_model_datapost.filter(([key,value]) => key.includes("old"));
          const adv_new_datapost = advanced_model_datapost.filter(([key,value]) => !key.includes("old"));
          // Vérifier les valeurs déjà présentes et les comparer avec les nouvelles correspondantes
          // Si une ancienne valeur n'a pas de nouvelle valeur, la supprimer
          // Si il y une nouvelle valeur mais elle est différente, supprimer l'ancienne (la nouvelle sera ajoutée aprés)
          // Sinon si les deux valeurs sont identiques, on retire la nouvelle valeur du tableau (elle ne sera pas ajoutée après)
          for(const [key, value] of adv_old_datapost){
            const oldItem = value as string[];
            const newItem = adv_new_datapost.find(([newKey, newValue]) => newKey === key.replace("_old", ""));
            if(newItem){
              const newItem_value: string[] = newItem[1] as string[];
              if(oldItem.every((v,i) => v === newItem_value[i])){
                adv_new_datapost.splice(adv_new_datapost.indexOf(newItem), 1);
                continue; // même valeur
              }
            }
            const [_, key_split] = key.split("#");
            const [type, metadata_id] = key_split.split("_");
            switch(type){
              case "actor":
                if(oldItem[1] === "" && oldItem[2] === "") continue;
                advanced_form_data.push({
                  type: "actor",
                  metadata_id: Number(metadata_id),
                  actor_type: oldItem[0],
                  actor_literal: oldItem[1],
                  actor_identifier: oldItem[2],
                  delete: true
                } satisfies DataItem);
                break;
              case "datation":
                if(oldItem[0] === "" && oldItem[1] === "" && oldItem[2] === "" && oldItem[3] === "") continue;
                advanced_form_data.push({
                  type: "datation",
                  metadata_id: Number(metadata_id),
                  datation_date_min: oldItem[0],
                  datation_date_max: oldItem[1],
                  datation_date_literal: oldItem[2],
                  datation_id_periodo: oldItem[3],
                  delete: true
                } satisfies DataItem);
                break;
              case "location":
                if(oldItem[0] === "" && oldItem[1] === "" && oldItem[2] === "") continue;
                advanced_form_data.push({
                  type: "location",
                  metadata_id: Number(metadata_id),
                  location_name: oldItem[0],
                  location_uri_geonames: oldItem[1],
                  location_longlat: oldItem[2],
                  delete: true
                } satisfies DataItem);
                break;
            }
          }

          // Parcour des nouvelles valeurs
          // Si la valeur est vide, on l'ignore
          for(const [key, value] of adv_new_datapost){
            const newItem_value = value as string[];
            const [is_unique, key_split] = key.split("#");
            const [type, metadata_id] = key_split.split("_");
            switch(type){
              case "actor":
                if(newItem_value[1] === "") continue; // Pas de nom 
                advanced_form_data.push({
                  type: "actor",
                  metadata_id: Number(metadata_id),
                  actor_type: newItem_value[0].trim(),
                  actor_literal: newItem_value[1].trim(),
                  actor_identifier: newItem_value[2].trim(),
                  delete: false,
                  is_unique: Boolean(is_unique)
                } satisfies DataItem)
                break;
              case "datation":
                if(newItem_value[0] === "") continue; // Pas de date_min
                advanced_form_data.push({
                  type: "datation",
                  metadata_id: Number(metadata_id),
                  datation_date_min: newItem_value[0].trim(),
                  datation_date_max: newItem_value[1].trim(),
                  datation_date_literal: newItem_value[2].trim(),
                  datation_id_periodo: newItem_value[3].trim(),
                  delete: false,
                  is_unique: Boolean(is_unique)
                } satisfies DataItem);
                break;
              case "location":
                if(newItem_value[0] === "" && newItem_value[1] === "" && newItem_value[2] === "") continue; // Tous les champs sont vides
                advanced_form_data.push({
                  type: "location",
                  metadata_id: Number(metadata_id),
                  location_name: newItem_value[0].trim(),
                  location_uri_geonames: newItem_value[1].trim(),
                  location_longlat: newItem_value[2].trim(),
                  delete: false,
                  is_unique: Boolean(is_unique)
                } satisfies DataItem)
                break;
            }
          }

        datapost = Object.entries(datapost)
          .filter(([key]) => !key.match(/^[0-1]#(datation|location|actor)_(\d+)_(\d+)$/))
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
        }

        // traiter le cas indexation multiple : idItem = 0
        // on lance une boucle sur tous les item du folder !
        if(!item_id){ 
          let keysToIgnore = Object.entries(datapost).filter(([key, values]) => {
            if(!key.endsWith("_old")){
              if(Array.isArray(values)){
                return values.every((value) => value.trim() === "");
              }else{
                return (values as string).trim() === ""; 
              }
            }else{
              return false;
            }
          }).map(([key, values]) => key);

          // Retirer les champs qui sont vide pour qu'ils soit ignorer (et pas supprimés)
          datapost = Object.entries(datapost)
            .filter(([key]) => !keysToIgnore.includes(key))
            .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
        }

        // On conserve l'ancienne valeur dans les _old pour les comparer aux nouvelles, ensuite, on les supprime
        // Si les nouvelles valeurs sont égales aux vieilles on les  enlève aussi des mises à jour à faire
        // ATTENTION : pour les champs select de thesaurus : on "fabrique des champ avec _t (et _t_old)
        // MAIS il ne faut pas tenir compte des champs d'origine du select qu'on ne peut comparer
        // c'est le cas des champs multi...
        for (const item in datapost) {
          // on supprime les checkbox  de repr image
          if (item.includes("checkbox_repr_image")) continue;
          if (item.includes("old")) {
            for (const newItem in datapost) {
              if (item.replace("_old", "") === newItem) {
                if (datapost[newItem] === datapost[item]) {
                  if (newItem.includes("_t_id")) {
                    for (const another in datapost) {
                      if (newItem.replace("_t_id", "") === another) {
                        datapost[another] = undefined;
                      }
                    }
                  }
                  datapost[newItem] = undefined;
                }
              }
            }
            datapost[item] = undefined;
          } else if (item.includes("_t")) {
            for (const other in datapost) {
              if (item.replace("_t", "") === other) {
                // Pour les métadonnées de type choix Ouvert choico , on se sert des variables _t pour comparer
                // on supprime l'id metadta inutile
                datapost[other] = undefined;
              } else if (item.replace("_t_id", "") === other) {
                // Cas des item avec select multi (il faut supprimer le select simple qui ne contient pas toutes les valeurs )
                // BUG : il faut le garder !
                if (other.includes("multi--")) {
                  // on le garde ! permet d'indexer depuis les pages d'indexation!
                } else if (other.includes("pactols--")) {
                  // idem
                } else {
                  datapost[other] = undefined;
                }
              }
            }
          } else if (item.includes("function_overall")) {
            overall = datapost[item];
            datapost[item] = undefined;
          }
        }

        const mainfolder = await archeogrid_client.folders.mainFolder.query({
          branch,
          language,
          folder_id,
        });

        if (!mainfolder) throw new Error("Main folder not found!");

        if (item_id) {
          if (req.params.type === "folder") {
            // Ajout pour les projets à lier avec des projets overall
            await req.fetchApi(`/api/projectWithOverall/${branch},${item_id}`, undefined, "DELETE");
            await req.fetchApi(`/api/projectWithOverall/${branch},${item_id}`, { overall }, "PATCH");
          }

          if (req.params.model === "project") {
            // traitement particulier pour repre image
            // TODO récupérer l'id de l'image du projet si elle a été mise en base de données
            if (datapost.checkbox_repr_image) {
              if (!datapost.checkbox_repr_image_old) {
                await req.fetchApi(
                  `/api/representativeImage/${branch},${mainfolder.mainfolder}`,
                  { idImage: "1" },
                  "PATCH",
                );
              }
            } else if (datapost.checkbox_repr_image_old) {
              // il y avait une repr_image avant mais plus maintenant
              await req.fetchApi(
                `/api/representativeImage/${branch},${mainfolder.mainfolder}`,
                { idImage: "0" },
                "PATCH",
              );
            }
          }
          datapost.checkbox_repr_image = undefined;
          datapost.checkbox_repr_image_old = undefined;

          try {
            let form_data: DataItem[] = Object.entries<string | string[] | undefined>(datapost)
              .map(([key, value]) => {
                let should_delete = false;
                if (key.endsWith("_old_disabled")) {
                  return undefined;
                }

                if (key.endsWith("_disabled") || (value === "" && datapost[`${key}_old`] === undefined)) {
                  should_delete = true;
                } else if (
                  key.endsWith("_old") ||
                  !value ||
                  value === datapost[`${key}_old`] ||
                  key.endsWith("function_overall") ||
                  key.startsWith("checkbox_repr_image")
                ) {
                  return undefined;
                }

                const key_split = key.split("#");
                const is_unique = Boolean(Number.parseInt(key_split[0]));

                const metadata_info = key_split[1].endsWith("_disabled")
                  ? key_split[1].substring(0, key_split[1].indexOf("_disabled"))
                  : key_split[1];

                const id = Number.parseInt(metadata_info);

                if (id) {
                  if (should_delete) {
                    return {
                      type: "id",
                      delete: true,
                      metadata_id: id,
                      geo: false,
                    } satisfies DataItem;
                  }
                  return {
                    type: "id",
                    is_unique,
                    delete: false,
                    metadata_id: id,
                    geo: false,
                    value: Array.isArray(value)
                      ? (value as string[]).filter(Boolean)
                      : [value as string].filter(Boolean),
                  } satisfies DataItem;
                }

                if (metadata_info.startsWith("geonames")) {
                  const metadata_id = Number.parseInt(metadata_info.split("--")[0]);
                  if (!metadata_id) throw new Error("Could not parse geonames metadata id");
                  return {
                    type: "id",
                    is_unique,
                    delete: should_delete,
                    metadata_id,
                    geo: true,
                    value: Array.isArray(value)
                      ? (value as string[]).flatMap((s) => s.split(",")).filter(Boolean)
                      : (value as string).split(",").filter(Boolean),
                  } satisfies DataItem;
                }

                if (metadata_info.startsWith("latlng")) {
                  const metadata_id = Number.parseInt(metadata_info.split("--")[1]);
                  if (should_delete) {
                    return {
                      type: "latlng",
                      delete: true,
                      metadata_id,
                    } satisfies DataItem;
                  }
                  if (typeof value !== "string")
                    throw new Error("Bad value type for latlng, expected string[], got string");
                  if (!metadata_id) throw new Error("Could not parse latlng metadata id");
                  if (value.split(",").length !== 2) throw new Error("Could not parse latlnt metadata value");
                  return {
                    type: "latlng",
                    is_unique,
                    delete: false,
                    metadata_id,
                    value,
                  } satisfies DataItem;
                }

                if (metadata_info.startsWith("opentheso")) {
                  return {
                    type: "opentheso",
                    is_unique,
                    delete: should_delete,
                  } satisfies DataItem;
                }

                if (metadata_info.startsWith("nomen")) {
                  const [_, thesaurus] = metadata_info.split("--");
                  if (should_delete) {
                    return {
                      type: "nomen",
                      delete: true,
                      thesaurus,
                      id_metadata: parseInt(key_split[2]),
                    } satisfies DataItem;
                  }
                  if (typeof value !== "string") {
                    throw new Error("Bad value for nomen, expected string, got string[]");
                  }
                  const [thesaurus_id, id_thes] = value.split("_");
                  return {
                    type: "nomen",
                    is_unique,
                    delete: false,
                    thesaurus,
                    thesaurus_id: Number.parseInt(thesaurus_id),
                    id_thes: Number.parseInt(id_thes),
                    id_metadata: parseInt(key_split[2]),
                  } satisfies DataItem;
                }

                if (metadata_info.startsWith("multi")) {
                  const [_, thesaurus] = metadata_info.split("--");

                  let real_value: string[];

                  if (should_delete || !value) {
                    return {
                      type: "multi",
                      delete: true,
                      thesaurus,
                      values: [],
                      id_metadata: parseInt(key_split[2]),
                    } satisfies DataItem;
                  }
                  if (typeof value === "string") {
                    real_value = [value];
                  } else {
                    real_value = value;
                  }
                  const values = real_value.filter(Boolean).map((s) => {
                    const split = s.split("_");
                    const thesaurus_id = Number.parseInt(split[0]);
                    const id_thes = Number.parseInt(split[1]);
                    const thesaurus_path = split[2];
                    if (!thesaurus_id) throw new Error("Could not parse thesaurus_id in multi values");
                    if (!id_thes) throw new Error("Could not parse id_thes in multi values");
                    return { thesaurus_id, id_thes, thesaurus_path };
                  });
                  return {
                    type: "multi",
                    is_unique,
                    delete: false,
                    thesaurus,
                    values,
                    id_metadata: parseInt(key_split[2]),
                  } satisfies DataItem;
                }

                if (metadata_info.startsWith("theso")) {
                  const [_, thesaurus] = metadata_info.split("--");
                  if (should_delete) {
                    return {
                      type: "thesaurus",
                      delete: true,
                      thesaurus,
                      id_metadata: parseInt(key_split[2]),
                    } satisfies DataItem;
                  }
                  if (typeof value !== "string") {
                    throw new Error("Bad value for theso, expected string, got string[]");
                  }
                  const [thesaurus_id, id_thes] = value.split("_");
                  return {
                    type: "thesaurus",
                    is_unique,
                    delete: false,
                    thesaurus,
                    thesaurus_id: Number.parseInt(thesaurus_id),
                    id_thes: Number.parseInt(id_thes),
                    id_metadata: parseInt(key_split[2]),
                  } satisfies DataItem;
                }

                throw new Error(`Unrecognized metadata schema: ${key}`);
              })
              .filter(defined);

            form_data.push(...advanced_form_data)

            await archeogrid_client.metadata.updateValues.mutate({
              branch,
              language,
              item_id,
              item_type: type,
              user_id: user.id,
              data: form_data,
            });

            // TODO : on ne met le message d'update que s'il y en a eu -ATTENTION, il reste un plan clasement
            req.flash("ok", "metadata updated");
          } catch (error) {
            console.warn(datapost);
            console.error(error);
            res.sendStatus(400);
            throw error;
          }

          if (branch !== branchConfig) {
            res.redirect(`/edit,${branch},${model},${type},${item_id},${folder_id}`);
            return;
          }

          if (req.params.model === "project") {
            res.redirect(`/projectv/${mainfolder.mainfolder}`);
            //res.redirect(`/edit,${branch},${model},${type},${item_id},${folder_id}`);
            return;
          }

          if (req.params.model === "overall_project") {
            //res.redirect(`/edit,${branch},${model},${type},${item_id},${folder_id}`);
            res.redirect(`/op/${req.params.idFolder}`);
            return;
          }

          //res.redirect(`/project/${mainfolder.mainfolder}?folder=${req.params.idFolder}`);
          res.redirect(`/edit,${branch},${model},${type},${item_id},${folder_id}`);
          return;
        }

        if (Object.keys(datapost).length === 0 && datapost.constructor === Object) {
          //res.redirect(`/project/${mainfolder.mainfolder}?folder=${req.params.idFolder}`);
          return;
        }

        datapost.userId = user.id;
        // traitement par lot: soit on écrase les valeurs existantes : lot = 0 (erase)
        // soit on garde les valeurs existantes et on les complètes : lot = 1 (keep)
        const item = await req.fetchApi(`/api/explore/${folder_id},${branch}`, {
          userId: user.id.toString(),
        });

        let shouldOverwrite = datapost.lot === "0" ? true : false;

        for (let f = 0; f < item.length; f++) {

          // Récupérer les valeurs des métadonnées de l'item en cours
          const allmetadata = await req.fetchApi(`/api/allmetadata/${item[f].id},${item[f].item_type},${branch},${language}`);

          let update_datapost = structuredClone(datapost);

          if(allmetadata[0][model]){
            for(const m of allmetadata[0][model]) {
              if(m.value && m.value.some(Boolean) && !shouldOverwrite) {
                delete update_datapost[`${m.isunique}#${m.id_metadata}`];
              }
            }
          }

          const [_, updateMetadataCode] = await req.fetchApiCode(
            `/api/metadatavalue/${item[f].id},${type},${model},${branch},${language}`,
            update_datapost,
            "PATCH",
          );

          if (updateMetadataCode !== 200 && updateMetadataCode !== 201) {
            req.flash("error", "Unable to update metadata");
            req.myroot(res.locals.myroot);
            return;
          }
        }

        if (branch === branchConfig) {
          //res.redirect(`/edit,${branch},${model},${type},0,${folder_id}`);
          res.redirect(`/projectv/${mainfolder.mainfolder}?folder=${req.params.idFolder}`);
          return;
        }
        //res.redirect("/");
      },
    ),
  )

  edit
    .route("/actorAutoCompletion/:name,:branch,:actor_type")
    .get(
      asyncHandler(
        async (
          req: Request<{
            name: string;
            branch: Branch;
            actor_type: "person" | "organization";
          }>,
          res,
        ) => {
          const result = await archeogrid_client.actor.searchActorsByName.query({name: decodeURIComponent(req.params.name), branch: req.params.branch, actor_type: req.params.actor_type});
          res.setHeader("Content-Type", "application/json"); 
          res.send(JSON.stringify(result));
        },
      ), 
  );

  edit
    .route("/isURLValid")
    .get(
      asyncHandler(
        async (
          req: Request<{ url: string }>,
          res,
        ) => {
          if(!req.query.url) res.sendStatus(400);
          const result = await helpers.isURLValid(req.query.url as string);
          res.setHeader("Content-Type", "application/json"); 
          res.send(JSON.stringify(result));
        },
      ),
  );

export default edit;
