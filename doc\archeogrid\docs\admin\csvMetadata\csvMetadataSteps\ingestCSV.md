---
sidebar_position: 3
---

# Ingest the CSV

This features allows you to ingest the data of the CSV file into the table and/or the metadata model. It displayed the overview table with every 'csv-metadata' being created or already existing in the database. by clicking in a row you have access to three possibilities:
- Create or Update the table
- Create or Update the metadata model
- Ingest the data of the CSV into the table and/or the metadata model

You can decide to ingest the data only in the table or only in the model by clicking on '...' of the ingest button.

![](ingestCSV-EN.png)

For the model ingestion, only basic status are fully supported such as *boolean*, *char*, *choice*, *choico*, *date*, *int*, *list*, *map* or *text*. The other types such as *actor*, *datation*, *doc*, *inventory*, *link*, *location*, *nomenclature*, *thesaurus*, *multi*, *pactols*, *periodo*, are not fully supported which mean that it would be stored as text. Futher implementation of the other types will be added in the future.

After the ingest, a success message will be displayed and the table for table and model content will be displayed (depending if the ingest was in the table and/or in the model). You can check if the data ingested in the table and/or the model is correct.

![](ingestCSV-CT-EN.png)
![](ingestCSV-CM-EN.png)

The final result can directly be seen othe the viewer pages of the item when displaying the metadata. In our example  with 'Claveau' object from N-Dame project here is the result : 

![](finalGoal-EN.png)