<%- include('utils/title-content', { home: "home" }) %>
<script src="/js/searchable-dropdown.js"></script>
<script src="/js/vitrine-search.js"></script>
<script src="/js/archeogrid.js"></script>
<script src="/js/lib/imagesloaded.pkgd.min.js"></script>
<script src="/js/lib/masonry.pkgd.min.js"></script>
<script src="/js/folders-tree-vitrine.js"></script>
<link rel="stylesheet" href="/css/folders-tree-vitrine.css">
<link rel="stylesheet" href="/css/mobile-vitrine.css">
<script src="/js/mobile-vitrine.js"></script>

<style>
/* Disconnected search bar and button styling */
.input-group-disconnected {
    display: flex;
    align-items: center;
    width: 100%;
}

.input-group-disconnected .searchable-select,
.input-group-disconnected .custom-select-container .custom-select-input {
    border-radius: 0.375rem !important;
    flex: 1;
}

.input-group-disconnected .disconnected-search-btn {
    margin-left: 8px;
    border-radius: 0.375rem !important;
    flex-shrink: 0;
}

/* Ensure the custom searchable dropdown input maintains proper styling */
.input-group-disconnected .custom-select-container {
    flex: 1;
    display: flex;
}
</style>

<div class="d-flex justify-content-between" style="height: 79vh;" id="GROS">

    <div id="menuGauche" class="hide-on-mobile d-flex flex-column justify-content-between col-3"
         style="max-height: none; overflow: visible; width: calc(25% + 20px);">
        <div id="left-top" class="d-flex flex-column gap-2">
            <div class="form-inline d-flex gap-1 align-items-center">
                <div class="input-group-disconnected">
                    <select id="searchSelect" class="searchable-select form-control">
                        <option value="search" data-type="search"><%=__('asearch')%>...</option>
                        <% for (let i = 0; i < tree.length; i++) { %>
                            <option value="<%= tree[i].id %>" data-type="folder" data-depth="<%= tree[i].depth %>" data-path="<%= tree[i].global_rank %>">
                                <%= tree[i].name %><% if (tree[i].nb_items > 0) { %> [<%= tree[i].nb_items %>]<% } %><% if (tree[i].nb_tot_items > 0 && tree[i].get_children !== null) { %> (<%= tree[i].nb_tot_items %>)<% } %>
                            </option>
                        <% } %>
                    </select>
                    <button class="btn btn-secondary disconnected-search-btn" type="button" id="search-button" onclick="goToAdvancedSearch()" title="<%=__('advancedSearch')%>"><i
                           class="fas fa-search-plus"></i></button>
                </div>
            </div>
            <% if (accessProject) { %>
                <div id="project-folders-container" class="hide-on-mobile">
                    <%- include('search/foldersTreeVitrine.ejs', {tree: tree, user: user, selectedFolder: isfolder, isfolder: isfolder, lng: lng, accessProject: accessProject, comment: typeof comment !== 'undefined' ? comment : {length: 0}, branch: branch, projectId: projectId, thesinfo: typeof thesinfo !== 'undefined' ? thesinfo : '', thesSimpleinfo: typeof thesSimpleinfo !== 'undefined' ? thesSimpleinfo : '', unicosNb: typeof unicosNb !== 'undefined' ? unicosNb : 0, project: typeof project !== 'undefined' ? project : {}, __: __ }) %>
                </div>
            <% } else { %>
                <p>Projet privé</p>
            <% } %>
        </div> <%# close left-top %>

        <div id="left-bot" style="overflow: auto;">
            <%# QUID du USER qui a des accès en ecriture et qui veut se faire des sélection ? %>

            <div id="tagprojet">


            </div>

        </div>
    </div> <%# close menuGauche %>

    <div class="col-12 col-md-9" id="menuCentre" style="display: none;">
        <% if (locals.flash && locals.flash.ok) { %>
        <div class="m-2 alert alert-success text-capitalize">
            <%= flash.ok %>
        </div>
        <% } %>

        <% if (typeof writeRights !== 'undefined' && writeRights && typeof branch !== 'undefined' && typeof projectId !== 'undefined') { %>
            <a type="button" href="/edit,<%= branch %>,<%= projectModel %>,folder,<%= projectId %>,<%= projectId %>"
               class="btn-vitrine-secondary m-2"
               id="edit-button"
               title="<%= __('edit') %>">
                <i class="fas fa-edit"></i> <%= __('edit') %>
            </a>
        <% } %>

        <div id="tabloid" class="container-fluid">
            <% if (data && data.metadata && data.metadata.length > 0) { %>
                <% if (data['metadata'][0]['rank'] == 1) { %>
                <h3><%= data['metadata'][0]['value'] %></h3>
                <% } else { %>
                <h3><%= data['name'] %></h3>
                <% } %>
                <% if (data['representativePictureId'] !== 0) { %>
                <div class="container-fluid" style="text-align: center">
                    <img src="/assets/<%= data['url'] %><%= data['folder_name'] %>/<%= data['folder_name'] %>.jpg"
                         class="img-thumbnail" alt="<%= data['name'] %>">
                </div>
                <% } %>

                <%# --- Start of refined project information --- %>
                <%# projectMapData is now prepared in the route (projects.ts) and passed directly %>

                <% if (typeof dataModel !== 'undefined' && typeof projectModel !== 'undefined' && typeof data !== 'undefined') { %>
                    <% dataModel.forEach(function(model) { %>
                        <% if (model['name'] === projectModel) { %>
                            <div class="mt-3"> <%# Direct container, removed card styling %>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h4 class="mb-0"><%= model['description'] %></h4>

                                </div>

                                <% if (data.metadata && data.metadata.length > 0) { %>
                                    <table class="table table-striped table-sm tableVitrine">
                                        <tbody>
                                            <% data.metadata.forEach(function(meta) { %>
                                                <% if (meta.status === 'map') { return; } %>
                                                <% if (meta.value && meta.value.toString().trim() !== '' && meta.query == 'y') { %>
                                                    <% if (meta.label == 'Latitude' || meta.label == 'Longitude') { return; } %>
                                                    <% if (meta.rank == 1 && meta.value == (data.metadata[0] && data.metadata[0].value) ) { return; } %>

                                                    <% if (meta.status === 'link') { %>
                                                        <tr>
                                                            <td><strong><%= meta.label %></strong></td>
                                                            <td>
                                                                <% let link_values = Array.isArray(meta.value) ? meta.value : [meta.value]; %>
                                                                <% link_values.forEach(function(val, index) { %>
                                                                    <a href="<%= val %>" target="_blank"><%= val %></a><%= (index < link_values.length - 1) ? '<br>' : '' %>
                                                                <% }); %>
                                                            </td>
                                                        </tr>
                                                    <% } else if (meta.status === 'doc' || meta.status === 'text') { %>
                                                        <tr>
                                                            <td><strong><%= meta.label %></strong></td>
                                                            <td>
                                                                <% if (meta.isunique) { %><%- meta.value %><% } else { %>
                                                                    <% let text_values = Array.isArray(meta.value) ? meta.value : [meta.value]; %>
                                                                    <% text_values.forEach(function(val, index) { %><%- val %><%= (index < text_values.length - 1) ? '<br>' : '' %><% }); %>
                                                                <% } %>
                                                            </td>
                                                        </tr>
                                                    <% } else { %>
                                                        <tr>
                                                            <td><strong><%= meta.label %></strong></td>
                                                            <td>
                                                                <% if (meta.isunique) { %><%= meta.value %><% } else { %>
                                                                    <% let regular_values = Array.isArray(meta.value) ? meta.value : [meta.value]; %>
                                                                    <% regular_values.forEach(function(val, index) { %><%= val %><%= (index < regular_values.length - 1) ? '<br>' : '' %><% }); %>
                                                                <% } %>
                                                            </td>
                                                        </tr>
                                                    <% } %>
                                                <% } %>
                                            <% }); %>
                                            <% if (typeof nomenclature !== 'undefined' && nomenclature.length > 0) { %>
                                                <% nomenclature.forEach(function(item) { %>
                                                    <tr>
                                                        <td><strong><%= item.label %></strong></td>
                                                        <td><%= item.value %></td>
                                                    </tr>
                                                <% }); %>
                                            <% } %>
                                        </tbody>
                                    </table>
                                <% } %>
                            </div>
                        <% } %>
                    <% }); %>
                <% } %>

                <% if (projectMapData) { %>
                    <div id="projectMapDisplayArea" class="map-container" style="width: 100%; height: 400px; border-radius: 8px; position: relative; overflow: hidden; margin-top: 1rem; min-height: 400px; visibility: visible;">
                        <div id="loading_projectMapDisplayArea" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: rgba(255,255,255,0.8); z-index: 10;">
                            <div><%= __('loadingMapData') || 'Loading map data...' %></div>
                        </div>
                    </div>
                <% } %>
                <%# --- End of refined project information --- %>
            <% } else { %>
                <h3><%= projectName || 'Project' %></h3>
            <% } %>
        </div>
    </div>

    <%- include('explore/exploreVitrine') %>

</div> <%# GROS se ferme%>
</div>
</div>
</div>

<script src="/js/selectionVitrine.js"></script>
<script>

(function() {
    // Make projectId available globally
    window.projectId = "<%- projectId %>";
    window.currentBranch = "<%- branch %>";
    window.currentModel = "<%- model %>";
    window.currentModelType = "<%- model_type %>";

    // Context menu messages
    window.msg_addVF = '<%- lng === "fr" ? "Ajouter un dossier virtuel dans" : "Add virtual folder in" %>';
    window.msg_download = '<%- lng === "fr" ? "Télécharger" : "Download" %>';
    window.msg_collections = '<%- lng === "fr" ? "Collections" : "Collections" %>';
    window.msg_noVirtualFolders = '<%- lng === "fr" ? "Aucune collection" : "No collections" %>';

    // Function to initialize map with geolocation
    window.initMapGeoloc = function(name, lat, lng) {
        let mymap = L.map(name, { scrollWheelZoom: false }).setView([lat, lng], 4);
        L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>' +
                ' contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
            maxZoom: 18,
        }).addTo(mymap);
        let marker = L.marker([lat, lng]).addTo(mymap);
    };

    // Initialize the VitrineSearch module
    VitrineSearch.init({
        projectId: window.projectId,
        branch: window.currentBranch,
        model: window.currentModel,
        modelType: window.currentModelType,
        searchSelectId: 'searchSelect',
        folderTreeSelector: '.folders-tree',
        exploreResultsSelector: '#explore-results',
        exploreDivSelector: '#explore-div',
        menuCentreSelector: '#menuCentre',
        searchFunction: 'exploreSearch',
        debounceDelay: 350,
        minSearchLength: 2,
        advancedSearchPath: '/search/',
        keepMenuCentreVisible: false
    });

    // Initialize on window load
    window.addEventListener('load', function() {
        const isSelectionView = window.location.href.includes('exploreSelection') ||
                              window.location.href.includes('selection');

        <% if (isfolder !== '') { %>
            if (!isSelectionView) {
                exploreFolderVitrine("<%- isfolder %>");
                $('#<%- isfolder %>').parents('li').children('input').prop("checked", true);
            }
        <% } else { %>
            $('#menuCentre').show();
            $('#explore-div').hide();
        <% } %>

        if (document.getElementById('save-selection')) {
            document.getElementById('save-selection').addEventListener('click', function() {
                registerSelection('<%- user.id %>', '<%- projectId %>', '<%- lng %>');
            });
        }

        setTimeout(function() {
            window.dispatchEvent(new Event('resize'));
        }, 1000);

        <% if (typeof projectMapData !== 'undefined' && projectMapData && projectMapData.lat && projectMapData.lng) { %>
        const clientSideProjectMapData = <%- JSON.stringify(projectMapData) %>;

        (function() {
            if (typeof clientSideProjectMapData === 'undefined' || !clientSideProjectMapData.lat || !clientSideProjectMapData.lng) {
                console.error('[ProjectMapInit] clientSideProjectMapData is not correctly defined or missing lat/lng. Halting map init.');
                return;
            }
            const currentLat = parseFloat(clientSideProjectMapData.lat);
            const currentLng = parseFloat(clientSideProjectMapData.lng);
            const displayContainerId = 'projectMapDisplayArea';
            const loadingIndicatorId = 'loading_projectMapDisplayArea';

            function setupLeafletMap() {
                const mapDisplayContainer = document.getElementById(displayContainerId);
                const loadingIndicator = document.getElementById(loadingIndicatorId);

                if (loadingIndicator) loadingIndicator.style.display = 'none';
                if (!mapDisplayContainer) {
                    console.error("[projectVitrine.ejs] Map display container not found for Leaflet fallback:", displayContainerId);
                    return;
                }
                mapDisplayContainer.innerHTML = '';

                let leafletMap;
                let mapMarker;
                const lat = currentLat;
                const lng = currentLng;
                const zoomLevel = 13;

                try {
                    if (typeof L === 'undefined' || typeof L.map !== 'function') {
                        console.error("[projectVitrine.ejs] Leaflet library (L) is not loaded. Cannot initialize Leaflet map.");
                        if(mapDisplayContainer) mapDisplayContainer.innerHTML = "<p><%- __('errorLoadingMapLibrary') || 'Error loading mapping library.' %></p>";
                        return;
                    }
                    leafletMap = L.map(displayContainerId, {
                        scrollWheelZoom: true,
                        fadeAnimation: true,
                        zoomAnimation: true,
                        markerZoomAnimation: true,
                        preferCanvas: false,
                        renderer: L.canvas()
                    }).setView([lat, lng], zoomLevel);

                    // Primary tile layer with better loading options
                    const tileLayer = L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
                        maxZoom: 19,
                        keepBuffer: 10,
                        updateWhenIdle: false,
                        updateWhenZooming: true,
                        crossOrigin: true,
                        detectRetina: false,
                        reuseTiles: true,
                        bounds: [[-90, -180], [90, 180]]
                    });

                    // Fallback tile layer for better reliability
                    const fallbackTileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19,
                        keepBuffer: 10,
                        updateWhenIdle: false,
                        updateWhenZooming: true,
                        crossOrigin: true,
                        detectRetina: false,
                        reuseTiles: true
                    });

                    // Add error handling for tile loading
                    tileLayer.on('tileerror', function(e) {
                        console.warn('[Map] Tile loading error, attempting fallback:', e);
                        if (!leafletMap.hasLayer(fallbackTileLayer)) {
                            leafletMap.addLayer(fallbackTileLayer);
                        }
                    });

                    tileLayer.addTo(leafletMap);

                    mapMarker = L.marker([lat, lng]).addTo(leafletMap);
                    mapMarker.bindPopup(`<b>${clientSideProjectMapData.name || 'Location'}</b><br>Lat: ${lat.toFixed(5)}, Lng: ${lng.toFixed(5)}`);

                    // Manual refresh function for tiles
                    window.refreshMapTiles = function() {
                        if (leafletMap && tileLayer) {
                            console.log('[Map] Manually refreshing tiles...');
                            leafletMap.invalidateSize(true);
                            tileLayer.redraw();
                            leafletMap.eachLayer(function(layer) {
                                if (layer._url) {
                                    layer.redraw();
                                }
                            });
                        }
                    };

                    // Improved resize handling
                    let resizeTimeout;
                    const handleResize = function() {
                        clearTimeout(resizeTimeout);
                        resizeTimeout = setTimeout(function() {
                            if (leafletMap) {
                                leafletMap.invalidateSize(true);
                                // Force tile refresh after resize
                                setTimeout(function() {
                                    if (leafletMap && tileLayer) {
                                        tileLayer.redraw();
                                    }
                                }, 50);
                            }
                        }, 250);
                    };

                    window.addEventListener('resize', handleResize);

                    // Handle visibility changes (tab switching, etc.)
                    document.addEventListener('visibilitychange', function() {
                        if (!document.hidden && leafletMap) {
                            setTimeout(function() {
                                leafletMap.invalidateSize(true);
                                if (tileLayer) tileLayer.redraw();
                            }, 100);
                        }
                    });

                    // Initial size validation with multiple attempts
                    const validateMapSize = function(attempts = 0) {
                        if (attempts < 5) {
                            setTimeout(function() {
                                if (leafletMap) {
                                    leafletMap.invalidateSize(true);
                                    tileLayer.redraw();
                                }
                                validateMapSize(attempts + 1);
                            }, 100 * (attempts + 1));
                        }
                    };
                    validateMapSize();

                    // Force a final refresh after everything is loaded
                    setTimeout(function() {
                        if (leafletMap && tileLayer) {
                            leafletMap.invalidateSize(true);
                            tileLayer.redraw();
                        }
                    }, 1500);

                } catch (e) {
                    console.error("[projectVitrine.ejs] Error initializing Leaflet map:", e);
                    if(mapDisplayContainer) mapDisplayContainer.innerHTML = "<p><%- __('errorLoadingMap') || 'Error loading map.' %></p>";
                }
            }

            // Start the Leaflet map setup process
            if (document.readyState === 'complete' || (document.readyState !== 'loading' && !document.documentElement.doScroll)) {
                const loadingIndicator = document.getElementById(loadingIndicatorId);
                if (loadingIndicator) loadingIndicator.innerHTML = '<div>' + ('<%- __("loadingMap") || "Loading map..." %>') + '</div>';
                setupLeafletMap();
            } else {
                document.addEventListener('DOMContentLoaded', function() {
                    const loadingIndicator = document.getElementById(loadingIndicatorId);
                    if (loadingIndicator) loadingIndicator.innerHTML = '<div>' + ('<%- __("loadingMap") || "Loading map..." %>') + '</div>';
                    setupLeafletMap();
                });
            }
        })();
        <% } %>
    });

    if (typeof window.monmenu !== 'function') {
        console.error('monmenu function is not available from archeogrid.js!');

        window.monmenu = function(element, isVirtual, isDownloadable, idProject, nameProject, userStatus, userWrite, quality, lng) {
            console.log('Temporary monmenu called before archeogrid.js loaded');
            return false;
        };
    }
})();
</script>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="project-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive"
         aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
    // Initialize toast for notifications
    document.addEventListener('DOMContentLoaded', function() {
        const toastElement = document.getElementById("project-toast");
        if (toastElement && typeof bootstrap !== 'undefined') {
            bootstrap.Toast.getOrCreateInstance(toastElement);
        }
    });
</script>