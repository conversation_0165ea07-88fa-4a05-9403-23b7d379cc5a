<%- include('utils/title-content', { home: "home" }) %>

<!-- Hidden fields for JavaScript -->
<input type="hidden" id="user-id" value="<%= user ? user.id : '' %>">
<input type="hidden" id="project-id" value="<%= typeof projectId !== 'undefined' ? projectId : '' %>">
<input type="hidden" id="language" value="<%= lng || 'en' %>">

<script src="/js/searchable-dropdown.js"></script>
<script src="/js/archeogrid.js"></script>
<script src="/js/lib/imagesloaded.pkgd.min.js"></script>
<script src="/js/lib/masonry.pkgd.min.js"></script>
<script src="/js/mobile-vitrine.js"></script>
<script src="/js/selection-dropdown.js"></script>
<script src="/js/lazy-loading.js"></script>
<script src="/js/size-sliders.js"></script>
<script src="/js/folders-tree-vitrine.js"></script>
<script src="/js/thesaurus.js"></script>

<link rel="stylesheet" href="/css/mobile-vitrine.css">
<link rel="stylesheet" href="/css/selection-dropdown.css">
<link rel="stylesheet" href="/css/size-sliders.css">
<link rel="stylesheet" href="/css/folders-tree-vitrine.css">
<link rel="stylesheet" href="/css/thesaurus-vitrine.css">

<div class="d-flex justify-content-between" style="height: 79vh;" id="GROS">

    <div id="menuGauche" class="hide-on-mobile d-flex flex-column justify-content-between col-3"
         style="max-height: none; overflow: visible; width: calc(25% + 20px);">
        
        <div id="left-top" class="d-flex flex-column gap-2">
            <!-- Navigation Links -->
            <div class="container">
                <a href="/thesaurusV/<%= root %>,<%= projectId %>" class="btn btn-outline-primary btn-sm mb-2">
                    <%=__('allThesauri')%>
                </a>
                
                <a href="/thesaurusPactolsV/<%= root %>,<%= projectId %>" class="btn btn-outline-primary btn-sm mb-2">
                    <%=__('browsePACTOLS')%>
                </a>
            </div>

            <!-- PACTOLS Tree -->
            <div class="container d-flex flex-column" style="flex: 1; min-height: 0;">
                <div class="row flex-shrink-0">
                    <div class="col-12">
                        <img src="../assets/images/pactolsbl.png" class="img-fluid mb-2" />
                        <h2><%= typeof thesaurus !== 'undefined' && thesaurus.name ? thesaurus.name : 'PACTOLS' %></h2>
                    </div>
                </div>
                
                <div id="pactols-tree-container" style="flex: 1; overflow-y: auto; overflow-x: hidden; min-height: 0;">
                    <%
                    // Transform PACTOLS tree data for foldersTreeVitrineFoldersOnly component
                    const transformedPactolsTree = tree.map(item => ({
                        id: item.id,
                        name: item.name || item.short_name || `Item ${item.id}`,
                        folder_name: item.name || item.short_name || `Item ${item.id}`,
                        nb_images: item.nb_item || 0,
                        nb_objects: 0,
                        nb_unicos: 0,
                        nb_tot_images: item.nb_tot_item || item.nb_item || 0,
                        nb_tot_objects: 0,
                        nb_tot_unicos: 0,
                        nb_tot_items: item.nb_tot_item || item.nb_item || 0,
                        virtual: 0,
                        id_parent: item.id_parent,
                        depth: item.depth,
                        global_rank: item.global_rank || 0
                    }));
                    %>
                    <%- include('search/foldersTreeVitrineFoldersOnly', {
                        tree: transformedPactolsTree,
                        user: user,
                        selectedFolder: null,
                        isfolder: null,
                        lng: lng,
                        accessProject: true,
                        comment: {length: 0},
                        projectId: projectId,
                        project: project,
                        branch: root,
                        thesinfo: 'no',
                        thesSimpleinfo: 'no',
                        unicosNb: null
                    }) %>
                </div>
            </div>
        </div>

        <div id="left-bot">
            <div class="row">
                <div class="col-12 d-flex align-items-center justify-content-left">
                    <button id="back-to-thesaurus-btn" onclick="hideThesaurusTree()">
                        <i class="fas fa-arrow-left"></i> <%=__('back')%>
                    </button>

                    <h4 class="mb-2" style="margin-left: 10px;"><%=__('browseThesaurus')%></h4>
                </div>
            </div>
            
            <div id="dynamic-thesaurus-tree" style="min-height: 50px;">
            </div>
        </div>
    </div>

    <div class="col-12 col-md-9" id="menuCentre" style="display: none;">
        <% if (locals.flash && locals.flash.ok) { %>
        <div class="m-2 alert alert-success text-capitalize">
            <%= flash.ok %>
        </div>
        <% } %>

        <%- include('explore/exploreVitrine') %>
    </div>

</div>

<!-- Scripts -->
<script src="/js/explore.js"></script>
<script src="/js/selectionVitrine.js"></script>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="thesaurus-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toastElement = document.getElementById("thesaurus-toast");
        if (toastElement && typeof bootstrap !== 'undefined') {
            bootstrap.Toast.getOrCreateInstance(toastElement);
        }

        // Initialize toast variables for selection functionality
        setTimeout(function() {
            const toastElement = document.getElementById("thesaurus-toast");
            if (toastElement) {
                window.toastContent = toastElement.querySelector(".toast-body");
                window.projectToast = bootstrap.Toast.getOrCreateInstance(toastElement);
            }
        }, 100);

        // Set up PACTOLS folder click handlers
        const root = '<%= root %>';
        const projectId = '<%= projectId %>';
        
        // Add click handlers for PACTOLS folders
        $(document).on('click', '#pactols-tree-container .folder-name, #pactols-tree-container .folder-number, #pactols-tree-container .folder-name-container', function(e) {
            const $li = $(this).closest('li');
            const folderId = $li.attr('folderId');
            if (!folderId) return;

            const folderNumber = $(this).closest('.folder-name-container').find('.folder-number');
            const numberText = folderNumber ? folderNumber.text() : '';
            const hasItems = numberText.includes('[') || numberText.includes('(');

            if (hasItems) {
                // Show the menu centre and trigger exploration
                const menuCentre = document.getElementById('menuCentre');
                if (menuCentre) {
                    menuCentre.style.display = 'block';
                }
                
                loadPactolsOneItems(root, projectId, folderId);
            }

            e.preventDefault();
            e.stopPropagation();
        });
    });

    function showThesaurusTree() {
        const leftTop = document.getElementById('left-top');
        const leftBot = document.getElementById('left-bot');
        const backButton = document.getElementById('back-to-thesaurus-btn');
        const dynamicTree = document.getElementById('dynamic-thesaurus-tree');
        
        if (leftTop) leftTop.classList.add('hidden');
        if (leftBot) leftBot.classList.add('expanded');
        if (backButton) backButton.classList.add('show');
        if (dynamicTree) dynamicTree.classList.add('expanded');
    }

    function hideThesaurusTree() {
        const leftTop = document.getElementById('left-top');
        const leftBot = document.getElementById('left-bot');
        const backButton = document.getElementById('back-to-thesaurus-btn');
        const dynamicTree = document.getElementById('dynamic-thesaurus-tree');
        const menuCentre = document.getElementById('menuCentre');
        
        if (leftTop) leftTop.classList.remove('hidden');
        if (leftBot) leftBot.classList.remove('expanded');
        if (backButton) backButton.classList.remove('show');
        if (dynamicTree) {
            dynamicTree.classList.remove('expanded');
            dynamicTree.innerHTML = '';
        }
        if (menuCentre) menuCentre.style.display = 'none';
    }

    window.loadPactolsOneItems = function(root, projectId, thesaurusId) {
        const exploreDiv = document.getElementById('explore-div');
        if (exploreDiv) {
            exploreDiv.style.display = 'flex';
            
            setTimeout(() => {
                if (typeof window.reinitializeSelectionDropdown === 'function') {
                    window.reinitializeSelectionDropdown();
                }
            }, 300);
        }

        // Set up the explorePage object for proper view switching support
        if (typeof window.explorePage === 'undefined') window.explorePage = {};
        
        window.explorePage.thesaurus = 'sujet';
        window.explorePage.thesId = thesaurusId;
        window.explorePage.id = projectId;
        window.explorePage.thesPath = thesaurusId;
        
        // Set up URL function for PACTOLS exploration with dynamic display reference
        window.explorePage.url = function(newPage) {
            const currentDisplay = (typeof display !== 'undefined') ? display : 'grid';
            return `/exploreThesaurusVitrinePactolsPage/sujet,${thesaurusId},${newPage}?display=${currentDisplay}&thes_path=${thesaurusId}`;
        };

        // Use the URL function to get the correct URL for page 1
        const exploreUrl = window.explorePage.url(1);

        $.ajax({
            url: exploreUrl,
            method: 'GET',
            success: function(data) {
                if (!data || data.trim() === "") {
                    console.warn('[DEBUG] Received empty HTML data for PACTOLS items');
                    $('#explore-results').html('<div class="alert alert-warning">No content received from server.</div>');
                } else {
                    $('#explore-results').html(data);
                    
                    // Initialize lazy loading for the new content
                    if (typeof initLazyLoading === 'function') {
                        initLazyLoading();
                    }
                    
                    // Handle the newly visible container
                    const exploreResults = document.getElementById('explore-results');
                    if (exploreResults && typeof handleNewlyVisibleContainer === 'function') {
                        handleNewlyVisibleContainer(exploreResults);
                    }
                    
                    // Trigger masonry re-layout if the function exists
                    setTimeout(function() {
                        if (typeof window.masonryGrids !== 'undefined' && window.masonryGrids.length > 0) {
                            window.masonryGrids.forEach(function(masonry) {
                                if (masonry && typeof masonry.reloadItems === 'function') {
                                    masonry.reloadItems();
                                    masonry.layout();
                                }
                            });
                        }
                        
                        // Also try to trigger imagesLoaded if available
                        if (typeof imagesLoaded === 'function') {
                            imagesLoaded(exploreResults, function() {
                                // Force another lazy loading refresh
                                if (typeof refreshLazyLoadingForVisibleImages === 'function') {
                                    refreshLazyLoadingForVisibleImages();
                                }
                            });
                        }
                    }, 100);
                }
                
                $.ajax({
                    url: `/exploreThesaurusVitrinePactolsNb/sujet,${thesaurusId}`,
                    method: 'GET',
                    success: function(countData) {
                        const total = countData.total || 0;
                        const totalCount = countData.totalCount || total;
                        const restrictedCount = totalCount - total;
                        
                        let resultText = `${total} items found`;
                        if (restrictedCount > 0) {
                            resultText += ` (no perms for ${restrictedCount} files)`;
                        }
                        
                        $('#nb-results').text(resultText);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading item count:', error);
                    }
                });
                
                // Set current page to 1 since we're starting fresh
                if (typeof window.currentPage !== 'undefined') {
                    window.currentPage = 1;
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading PACTOLS thesaurus items:', error);
                $('#explore-results').html('<div class="alert alert-danger">Error loading thesaurus items: ' + error + '</div>');
            }
        });
    };
</script>

<!-- Server-side variables -->
<script>
    window.SERVER_DATA = {
        USER_READ: <%- JSON.stringify((user && user.read) ? user.read : []) %>,
        USER_STATUS: '<%= (user && user.user_status) ? user.user_status : "guest" %>',
        LNG: '<%= lng || "en" %>',
        USER_WRITE: <%- JSON.stringify((user && user.write) ? user.write : []) %>,
        PROJECT_ID: '<%= typeof projectId !== "undefined" ? projectId : "" %>'
    };
</script>