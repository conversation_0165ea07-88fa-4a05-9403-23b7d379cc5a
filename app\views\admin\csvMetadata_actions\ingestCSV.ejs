<style>
    #csv-metadata-info {
        margin-top: 2rem;
        color: #888;
    }

    #csv-metadata-info.hide {
        display: none;
    }

    #btn-actions-container {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .select-csv-infos {
        margin-top: 1rem;
        display: none;
    } 

    .select-csv-infos.show {
        display: block;
    }

    #select-csv-table-container, #select-csv-model-container {
        margin-top: 1rem;
        overflow: auto;
        max-height: 40vh;
    }

    #select-csv-table thead th, #select-csv-model thead th {
        position: sticky;
        background-color: var(--primary-color);
        top: 0px;
        z-index: 10;
        padding-right: 1rem;
        padding: 0.5rem 0.75rem 0.5rem 0.375rem;
        white-space: nowrap;
    }

    #select-csv-table thead th:first-child, #select-csv-model thead th:first-child {
        border-radius: 8px 0px 0px 0px;
    }

    #select-csv-table thead th:last-child, #select-csv-model thead th:last-child {
        border-radius: 0px 8px 0px 0px;
    }

    #btn-ingestCSV-container {
        display: flex;
        cursor: pointer;
    }

    #btn-ingestCSV-container.disabled {
        opacity: 0.65;
        pointer-events: none;
    }

    #btn-ingestCSV-subcontainer {
        height: 2.375rem;
        padding: 0 0.75rem;
        background-color: var(--action-color);
        border: solid 1px var(--action-color);
        color: white;
        border-radius: 0.375rem 0px 0px 0.375rem;
        cursor: pointer;
        border-right: solid 1px var(--highlight-color);
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
    }  
    
    #btn-ingestCSV-subcontainer:hover {
        background-color: var(--action-color);
        border-color: var(--action-color);
    }

    #btn-ingestCSV {
        background-color: transparent;
        border: none;
        color: white;
    }

    #btn-ingestCSV-loading {
        height: auto;
    }

    #btn-ingestCSV-options-container {
        display: flex;
        gap: 1.2rem;
        height: 2.375rem;
        padding: 0 0.75rem;
        border-radius: 0px 0.375rem 0.375rem 0px;
        background-color: var(--action-color);
        border: solid 1px var(--action-color);
        color: white;
        border-left: solid 1px var(--highlight-color);
        transition: all 0.15s ease-in-out;
        overflow: hidden;
        width: 36px;
    }

    #btn-ingestCSV-options-container:hover {
        background-color: var(--action-color);
        border-color: var(--action-color);
    }

    #btn-ingestCSV-options-container.expand {
        width: 100%;
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
    }

    #btn-ingestCSV-options {
        display: inline-block;
        transform: translateY(2px);
        user-select: none;
    }

    #btn-ingestCSV-options-input-container.hide {
        display: none;
    }

    #btn-ingestCSV-options-input-container {
        display: flex;
        gap: 1rem;
    }

    #btn-ingestCSV-options-input-container div {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 0.375rem;
    }

    #btn-ingestCSV-options-input-container div label {
        white-space: nowrap;
    }

    .danger-container, .success-container {
        margin: 1rem 0;
        display: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    p.tableDescription {
        max-height: 3rem;
        overflow: hidden;
        transition: max-height 0.5s ease, opacity 0.5s ease 0.5s;
    }

    p.tableDescription.hide {
        max-height: 0;
        opacity: 0;
    }

    #select-csv-table-title {
        margin-top: 2.5rem;
    }
</style>

<div class="container">
    <h4><%=__('admin.content.csvMetadata.ingest.IngestCSVTitle')%></h4>
    <h6><%=__('admin.content.csvMetadata.ingest.IngestCSVDescription')%></h6>
</div>

<div id="ingestCSV-form" class="container">
    
    <%- include('./csvMetadataTable', {csv_metadata: csv_metadata, branches: branches, mode: 'extended'}) %>

    <i id="csv-metadata-info"><%= __('admin.content.csvMetadata.ingest.InfoSelectRow') %></i>
    
    <div id="btn-actions-container">
        <form action="/admin/csvMetadata" method="get">    
            <input id="btn-menuCSV" class="btn btn-secondary" type="submit" value="<%= __('admin.content.csvMetadata.Menu') %>">
        </form>

        <form action="/admin/csvMetadata" method="post" id="form-createTable">
            <input type="hidden" name="step" value="1">
            <input type="hidden" name="action" value="createTable">
            <input type="hidden" name="csv_metadata_id">
            <input id="btn-createTable" class="btn btn-primary" type="submit" value="<%= __('admin.content.csvMetadata.CreateTable') %>" disabled>
        </form>

        <form action="/admin/csvMetadata" method="post" id="form-createModel">
            <input type="hidden" name="step" value="1">
            <input type="hidden" name="action" value="createModel">
            <input type="hidden" name="csv_metadata_id">
            <input id="btn-createModel" class="btn btn-primary" type="submit" value="<%= __('admin.content.csvMetadata.CreateModel') %>" disabled> 
        </form>

        <form action="/admin/csvMetadata" method="post" id="form-addKeywordsFromMetadata">
            <input type="hidden" name="step" value="0">
            <input type="hidden" name="action" value="addKeywordsFromMetadata">
            <input type="hidden" name="csv_metadata_id">
            <input id="btn-addKeywordsFromMetadata" class="btn btn-primary" type="submit" value="<%= __('admin.content.csvMetadata.extractKeywords') %>" disabled>
        </form>

        <form id="form-ingestCSV" name="form-ingestCSV">   
            <input type="hidden" name="step" value="1" />
            <input type="hidden" name="action" value="ingestCSV" />
            <input id="input-csv-metadata-id" type="hidden" name="csv-metadata-id" /> 
            <div id="btn-ingestCSV-container" class="disabled">
                <div id="btn-ingestCSV-subcontainer">
                    <input id="btn-ingestCSV" type="submit" value="<%= __('Ingest') %>" /> 
                    <i id="btn-ingestCSV-loading" class="fa fa-spinner fa-spin d-none" aria-hidden="true"></i>
                </div>
                <div id="btn-ingestCSV-options-container">
                    <span id="btn-ingestCSV-options">...</span>
                    <div id="btn-ingestCSV-options-input-container">
                        <div>
                            <label for="btn-ingestCSV-table"><%= __('Table') %></label>
                            <input id="btn-ingestCSV-table" type="checkbox" name="updateTable" checked />
                        </div>
                        <div>
                            <label for="btn-ingestCSV-model"><%= __('Model') %></label>
                            <input id="btn-ingestCSV-model" type="checkbox" name="updateModel" checked />
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <h5 id="select-csv-table-title" class="select-csv-infos"><%= __('admin.content.csvMetadata.TableContent') %> <i id="select-csv-table-moreInfo" class="fa fa-info-circle" aria-hidden="true" onclick="this.parentElement.nextElementSibling.classList.toggle('hide')"></i></h5>
    <p class="tableDescription hide"><%= __('admin.content.csvMetadata.TableContentDescription') %></p>
    <div id="select-csv-table-container">
        <table id="select-csv-table" class="table table-sm table-striped">
            <thead class="table-dark"></thead>
            <tbody></tbody>
        </table>    
    </div>

    <br>

    <h5 id="select-csv-model-title" class="select-csv-infos"><%= __('admin.content.csvMetadata.ModelContent') %> <i id="select-csv-table-moreInfo" class="fa fa-info-circle" aria-hidden="true" onclick="this.parentElement.nextElementSibling.classList.toggle('hide')"></i></h5>
    <p class="tableDescription hide"><%= __('admin.content.csvMetadata.ModelContentDescription') %></p>
    <div id="select-csv-model-container">
        <table id="select-csv-model" class="table table-sm table-striped">
            <thead class="table-dark"></thead>
            <tbody></tbody>
        </table>    
    </div>
</div>

<script>
    const csv_metadata = JSON.parse('<%- JSON.stringify(csv_metadata) %>');

    const selected_csv_metadata_id = Number.parseInt('<%- selected_csv_metadata_id %>');

    document.addEventListener("DOMContentLoaded", () => {
        const csvMetadataRow = document.getElementsByClassName("csv-metadata-row");
        const csvMetadataInfo = document.getElementById("csv-metadata-info");
        const actionButtonsContainer = document.getElementById("btn-actions-container");

        const formCreateTable = document.getElementById("form-createTable");
        const btnCreateTable = document.getElementById("btn-createTable");
        const csvMetadataIdCreateTable = formCreateTable.querySelector("input[name='csv_metadata_id']"); 

        const formCreateModel = document.getElementById("form-createModel");
        const btnCreateModel = document.getElementById("btn-createModel");
        const csvMetadataIdCreateModel = formCreateModel.querySelector("input[name='csv_metadata_id']");

        const buttonIngestCSVContainer = document.getElementById("btn-ingestCSV-container");
        const inputCSVMetadataId = document.getElementById("input-csv-metadata-id");

        const btnIngestOptionContainer = document.getElementById("btn-ingestCSV-options-container");
        const btnIngestOption = document.getElementById("btn-ingestCSV-options");
        const formIngestCSV = document.getElementById("form-ingestCSV");
        const btnIngestCSV = document.getElementById("btn-ingestCSV");
        const btnIngestLoadingIcon = document.getElementById("btn-ingestCSV-loading");

        const formAddKeywordsFromMetadata = document.getElementById("form-addKeywordsFromMetadata");
        const btnExtractKeywords = document.getElementById("btn-addKeywordsFromMetadata");

        for(const row of csvMetadataRow){
            row.addEventListener("click", () => {
                const selectedRow = document.getElementsByClassName("selected");
                if(selectedRow.length > 0){
                    selectedRow[0].classList.remove("selected");
                }
                row.classList.add("selected");

                btnCreateModel.disabled = false;
                btnCreateTable.disabled = false;
                
                const CSVMetadataId = row.id.split('-')[3];
                const CSVMetadata = csv_metadata.find((csv) => csv.id == CSVMetadataId);    

                csvMetadataIdCreateTable.value = CSVMetadataId;
                if(CSVMetadata.table_name){
                    btnCreateTable.value = "<%= __('admin.content.csvMetadata.UpdateTable') %>";
                    if(CSVMetadata.last_ingestion_table){
                        displaySelectCSVTable(CSVMetadataId);
                    }else{
                        clearSelectCSVTable();
                    }
                }else{
                    btnCreateTable.value = "<%= __('admin.content.csvMetadata.CreateTable') %>";
                    clearSelectCSVTable();
                }
                
                csvMetadataIdCreateModel.value = CSVMetadataId;
                if(CSVMetadata.metadata_model_name){
                    btnCreateModel.value = "<%= __('admin.content.csvMetadata.UpdateModel') %>";
                    if(CSVMetadata.last_ingestion_model){
                        displaySelectCSVModel(CSVMetadataId);
                    }else{
                        clearSelectCSVModel();
                    }
                }else{
                    btnCreateModel.value = "<%= __('admin.content.csvMetadata.CreateModel') %>";
                }

                if(CSVMetadata.table_name && CSVMetadata.metadata_model_name){
                    buttonIngestCSVContainer.classList.remove("disabled");
                    inputCSVMetadataId.value = CSVMetadataId;
                    csvMetadataInfo.innerHTML = "<%= __('admin.content.csvMetadata.ingest.InfoSelectRowIngestAvailable') %>";
                }else{
                    buttonIngestCSVContainer.classList.add("disabled");
                    btnIngestCSV.disabled = true;
                    if(!CSVMetadata.table_name){
                        csvMetadataInfo.innerHTML = "<%= __('admin.content.csvMetadata.ingest.InfoSelectRowCreateTable') %>";
                    }
                    if(!CSVMetadata.metadata_model_name){
                        csvMetadataInfo.innerHTML = "<%= __('admin.content.csvMetadata.ingest.InfoSelectRowCreateModel') %>";
                    }
                }

                // There is items with this csv metadata, meaning possibly fields with keywords
                if(CSVMetadata.last_ingestion_model){
                    btnExtractKeywords.disabled = false;
                    formAddKeywordsFromMetadata.querySelector("input[name='csv_metadata_id']").value = CSVMetadataId;
                }else{
                    btnExtractKeywords.disabled = true;
                }
            })

            if(row.id == `csv-metadata-row-${selected_csv_metadata_id}`){
                row.click();
            }
        }
    
        btnIngestOption.addEventListener("click", () => {
            btnIngestOptionContainer.classList.toggle("expand");
        });

        formIngestCSV.addEventListener("submit", async (e) => {
            e.preventDefault();

            deleteErrors();
            deleteSuccess();

            btnIngestCSV.disabled = true;
            btnIngestLoadingIcon.classList.remove("d-none");

            try{
                const body = new URLSearchParams(new FormData(formIngestCSV));

                const updateTable = body.get('updateTable') === 'on';
                const updateModel = body.get('updateModel') === 'on';
                
                const result = await fetch(`/admin/csvMetadata`, {
                    method: "POST",
                    body
                }).then(res => res.json());

                if(updateTable){
                    result.updateTable.message = `UPDATE TABLE : ${result.updateTable.message}`
                    if(result.updateTable.status){
                        createSuccess(result.updateTable.message);
                        displaySelectCSVTable(result.updateTable.csv_metadata_id);   
                    }else{
                        createError(result.updateTable.message);
                    }
                }

                if(updateModel){
                    result.updateModel.message = `UPDATE MODEL : ${result.updateModel.message}`
                    if(result.updateModel.status){
                        createSuccess(result.updateModel.message);
                        displaySelectCSVModel(result.updateModel.csv_metadata_id);   
                    }else{
                        createError(result.updateModel.message);
                    }
                }
            }catch(e){
                console.log(e);
            }
            
            btnIngestCSV.disabled = false;
            btnIngestLoadingIcon.classList.add("d-none");
        })
    })

    const formContainer = document.getElementById("ingestCSV-form"); 

    function createError(message){
        const container = document.createElement("div");
        container.classList.add("alert", "alert-danger", "danger-container");
        
        const span = document.createElement("span");
        span.classList.add("danger-container-message");
        span.innerText = message;

        const i = document.createElement("i");
        i.classList.add("fas", "fa-times");
        i.addEventListener("click", () => {
            container.remove();
        });

        container.appendChild(span);
        container.appendChild(i);
        
        formContainer.parentElement.insertBefore(container, formContainer);
    }

    function deleteErrors(){
        const errors = document.getElementsByClassName("alert alert-danger");
        while(errors.length > 0){
            errors[0].remove();
        }
    }

    function createSuccess(message){
        const container = document.createElement("div");
        container.classList.add("alert", "alert-success", "success-container");
        
        const span = document.createElement("span");
        span.classList.add("success-container-message");
        span.innerText = message;

        const i = document.createElement("i");
        i.classList.add("fas", "fa-times");
        i.addEventListener("click", () => {
            container.remove();
        });
        
        container.appendChild(span);
        container.appendChild(i);
        
        formContainer.parentElement.insertBefore(container, formContainer);
    }

    function deleteSuccess(){
        const success = document.getElementsByClassName("alert alert-success");
        while(success.length > 0){
            success[0].remove();
        }
    }

    const MAX_ROW = 20;
    async function displaySelectCSVTable(csv_metadata_id){
        const selectCSVTable = document.getElementById("select-csv-table");
        const selectCSVTableTitle = document.getElementById("select-csv-table-title");
        const selectCSVTableHead = selectCSVTable.querySelector("thead");
        const selectCSVTableBody = selectCSVTable.querySelector("tbody");

        const raw_csv_table = await fetch(`/admin/csvMetadata/${csv_metadata_id}/csvTable`).then(res => res.json());

        if(!raw_csv_table || raw_csv_table.length == 0){
            selectCSVTableTitle.classList.remove("show");
            selectCSVTableHead.innerHTML = "";
            selectCSVTableBody.innerHTML = "";
            return;
        }

        selectCSVTableTitle.classList.add("show");

        const column_names = Object.keys(raw_csv_table[0]).map((key) => key.charAt(0).toUpperCase() + key.slice(1));

        selectCSVTableHead.innerHTML = "";
        for(const column_name of column_names){
            const th = document.createElement("th");
            th.innerHTML = column_name;
            selectCSVTableHead.appendChild(th);
        }   

        selectCSVTableBody.innerHTML = "";
        for(const row of raw_csv_table){
            const tr = document.createElement("tr");
            for(const value of Object.values(row)){
                const td = document.createElement("td");
                td.innerHTML = value ?? '---';
                tr.appendChild(td);
            }
            selectCSVTableBody.appendChild(tr);
        }       
    }

    async function displaySelectCSVModel(csv_metadata_id){
        const selectCSVModel = document.getElementById("select-csv-model");
        const selectCSVModelTitle = document.getElementById("select-csv-model-title");
        const selectCSVModelHead = selectCSVModel.querySelector("thead");
        const selectCSVModelBody = selectCSVModel.querySelector("tbody");

        const raw_csv_model = await fetch(`/admin/csvMetadata/${csv_metadata_id}/csvModel`).then(res => res.json());

        const column_names = raw_csv_model.reduce((acc, row) => {
            let col_row = row.metadata.map((metadata) => metadata.code)

            if(acc.length == 0){
                acc.push(...col_row);
            }else{
                acc.push(...col_row.filter((col) => !acc.includes(col)))
            }
            return acc;
        }, [])

        if(!raw_csv_model || raw_csv_model.length == 0){
            return;
        }

        selectCSVModelTitle.classList.add("show");

        const format_csv_model = raw_csv_model.map((row) => row.metadata);

        const format_column_names = column_names.map((col) => col.charAt(0).toUpperCase() + col.slice(1)).map((col) => col.replace("_", " "));
            
        selectCSVModelHead.innerHTML = "";
        for(const column_name of format_column_names){
            const th = document.createElement("th");
            th.innerHTML = column_name;
            selectCSVModelHead.appendChild(th);
        }

        selectCSVModelBody.innerHTML = "";
        for(const row of format_csv_model){
            let values = column_names.map((row_col) => {
                let m = row.find((metadata) => metadata.code === row_col);
                if(!m) return "---";
                switch(m.status){
                    case 'thesaurus':
                    case 'pactols':
                    case 'multi':
                        return m.value.map((thes) => thes.name).join(', ');
                    default:
                        return m.value;
                }
            });
            const tr = document.createElement("tr");
            for(const value of values){
                let format_value = value;
                const td = document.createElement("td");
                td.innerHTML = value;
                tr.appendChild(td);
            }
            selectCSVModelBody.appendChild(tr);
        }
    }

    function clearSelectCSVTable(){
        const selectCSVTable = document.getElementById("select-csv-table");
        const selectCSVTableTitle = document.getElementById("select-csv-table-title");
        const selectCSVTableHead = selectCSVTable.querySelector("thead");
        const selectCSVTableBody = selectCSVTable.querySelector("tbody");

        selectCSVTableTitle.classList.remove("show");
        selectCSVTableHead.innerHTML = "";
        selectCSVTableBody.innerHTML = "";
    }

    function clearSelectCSVModel(){
        const selectCSVModel = document.getElementById("select-csv-model");
        const selectCSVModelTitle = document.getElementById("select-csv-model-title");
        const selectCSVModelHead = selectCSVModel.querySelector("thead");
        const selectCSVModelBody = selectCSVModel.querySelector("tbody");

        selectCSVModelTitle.classList.remove("show");
        selectCSVModelHead.innerHTML = "";
        selectCSVModelBody.innerHTML = "";
    }
</script>