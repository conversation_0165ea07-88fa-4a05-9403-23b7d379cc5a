<div class="container">
    <h3><%=__('admin.metadata.editModel') %></h3>
    <% if (!pft3d_models.length && !conservatoire3d_models.length && !corpus_models.length) { %>
    <h4><%= __('scribe.noUserModel') %></h4>
    <% } else { %>
    <form class="form-horizontal" action="/admin/editMetadataModel" method="post">
        <div class="d-flex align-items-center mb-3 gap-2">
            <label for="branch" class="form-label m-0"
                   style="min-width: 140px;"><%= __('admin.content.BranchSelection') %></label>
            <select class="form-select" id="branch" aria-describedby="branch" name="branch">
                <% for (const branch of branches) { %>
                <% if (locals[branch.branchename + "_models"].length) { %>
                <option value="<%= branch.branchename %>" <% if(selected_branch === branch.branchename) { %>selected<% } %>><%= branch.description %></option>
                <% } %>
                <% } %>
            </select>
        </div>
        <div id="model-selection-container" class="d-flex align-items-center mb-3 gap-2">
            <label class="form-label m-0"
                   style="min-width: 140px;"><%= __('admin.content.metadata.selectModel') %></label>
            <select class="form-select" id="selection-pft3d" name="pft3d_model">
                <% for (const model of pft3d_models) { %>
                <option value="<%= model.id %>"><%= model.name %> (<%= model.metadata_type %>)</option>
                <% } %>
            </select>
            <select class="form-select" id="selection-cnd3d" name="cnd3d_model">
                <% for (const model of conservatoire3d_models) { %>
                <option value="<%= model.id %>"><%= model.name %> (<%= model.metadata_type %>)</option>
                <% } %>
            </select>
            <select class="form-select" id="selection-corpus" name="corpus_model">
                <% for (const model of corpus_models) { %>
                <option value="<%= model.id %>"><%= model.name %> (<%= model.metadata_type %>)</option>
                <% } %>
            </select>
        </div>
        <button type="submit" class="btn btn-primary"><%= __('submit') %></button>
    </form>
    <% } %>
</div>

<script>
    const branch = document.getElementById('branch');
    const pft3d = document.getElementById('selection-pft3d');
    const conservatoire3d = document.getElementById('selection-cnd3d');
    const corpus = document.getElementById('selection-corpus');
    const branches = {pft3d, conservatoire3d, corpus};

    const hide = (el) => {
        el.classList.add('d-none');
    }

    const show = (el) => {
        el.classList.remove('d-none');
    }

    const update = () => {
        const { value } = branch;

        for(const b in branches) {
            if(value === b) {
                show(branches[b]);
            }else{
                hide(branches[b]);
            }
        }
    }

    branch.addEventListener('change', update);

    update();
</script>