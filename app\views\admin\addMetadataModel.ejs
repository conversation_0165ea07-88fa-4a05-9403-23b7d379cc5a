<div class="container">
    <h3><%=__('admin.metadata.addModel') %></h3>
    <% if (requireParamsSelection) { %>
    <form class="form-horizontal" id="addgroup" action="/admin/addMetadataModel" method="get">
        <div class="d-flex align-items-center gap-2">
            <label for="branch" class="form-label m-0" style="min-width: 200px;"><%= __('admin.content.BranchSelection') %></label>
            <select class="form-select" id="branch" aria-describedby="branch" name="branch">
                <% for (const branch of branches) { %>
                    <option value="<%= branch.branchename %>"><%= branch.description %></option>
                <% } %>
            </select>
        </div>
        <div class="d-flex align-items-center justify-content-center my-3 gap-2">
            <label for="type" class="form-label m-0" style="min-width: 200px;"><%= __('admin.content.metadata.addModel.form.type') %></label>
            <select class="form-select" id="type" aria-describedby="type" name="type">
                <option value="file" class="text-capitalize"><%= __('file') %></option>
                <option value="folder" class="text-capitalize"><%= __('folder') %></option>
                <option value="object" class="text-capitalize"><%= __('object') %></option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary"><%= __('submit') %></button>
    </form>
    <% } else { %>
        <div>
            <form action="/admin/addMetadataModel" method="post">
                <input type="hidden" name="branch" id="branch-input">
                <input type="hidden" name="type" id="type-input">
                <div class="mb-3">
                    <label for="name" class="form-label"><%= __('admin.content.metadata.addModel.form.name') %></label>
                    <input type="text" class="form-control" id="name" aria-describedby="name" name="name">
                </div>
                <div class="mb-3">
                    <label for="desc" class="form-label"><%= __('admin.content.metadata.addModel.form.desc') %></label>
                    <input type="text" class="form-control" id="desc" aria-describedby="desc" name="desc">
                </div>
                <input type="hidden" name="visible" value="1">
                <% if (user.user_status === "admin") { %>
                <div class="mb-3">
                    <label for="projects" class="form-label"><%= __('admin.content.metadata.addModel.form.projects') %></label>
                    <select class="form-select" id="projects" aria-describedby="projects" name="projects">
                        <option value="all"><%= __('admin.content.metadata.addModel.form.all') %></option>
                        <option value="select"><%= __('admin.content.metadata.addModel.form.select') %></option>
                    </select>
                </div>
                <% } else { %>
                    <label for="projects" class="form-label"><%= __('admin.content.metadata.addModel.form.projects') %></label>
                    <input type="hidden" id="projects" value="select" name="projects">
                <% } %>
                <div id="projects-selection-container" class="d-none mb-3">
                    <select class="form-select" id="selection" aria-describedby="selection" name="selection" multiple style="height: 240px;">
                        <% for (const project of selectableProjects) { %>
                            <option value="<%= project.id %>"><%= project.name %></option>
                        <% } %>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary"><%= __('submit') %></button>
            </form>
        </div>
    <% } %>
</div>

<script>
    const query = new URLSearchParams(location.search);
    const branchInput = document.getElementById('branch-input');
    branchInput.value = query.get('branch');
    const typeInput = document.getElementById('type-input');
    typeInput.value = query.get('type');

    const projects = document.getElementById('projects');
    const selection = document.getElementById('projects-selection-container');

    const change = (value) => {
        if (value === "select") {
            selection.classList.remove('d-none');
        } else {
            selection.classList.add('d-none');
        }
    }

    projects.addEventListener('change', (e) => {
        change(e.target.value);
    });

    change(projects.value);
</script>