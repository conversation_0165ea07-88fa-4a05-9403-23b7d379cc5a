:root {
  /* BASE COLORS */
  --dark-color: #121421;
  --primary-color: #242942;
  --primary-color-rgb: 36, 41, 67;
  --secondary-color: #124b68;
  --action-color: #177eed;
  --highlight-color: #c0eaff;
  --light-color: #f6faff;
  --item-color: #F8F8F8;

  /* LINK STYLE */
  --link-color: rgba(255, 255, 255, 0.5);
  --link-hover-color: rgba(255, 255, 255, 0.75);
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a.active {
  color: var(--action-color);
}

a:hover {
  color: var(--action-color);
}

.nav3dicons:hover {
  background-color: var(--secondary-color) !important;
}

.navbar-inverse {
  background: var(--primary-color) !important;
}

.nav-link {
  color: var(--link-color);
}.nav-link:hover {
  color: var(--link-hover-color);
}

#brand strong {
  background-color: white;
  color: var(--primary-color);
  border: 0;
  display: inline-block;
  font-size: 0.8em;
  height: inherit;
  line-height: 1.65em;
  padding: 0 0.125em 0 0.375em;
}

/* contanier map + item */
.map {
  height: 80vh;
  background-color: var(--primary-color);
}

.marker.is-active {
  z-index: 300;
  background-color: var(--action-color);
  color: inherit;
  box-shadow: none;
  text-align: inherit;
}

.marker:hover {
  z-index: 300;
  color: inherit;
  box-shadow: none;
  text-align: inherit;
}

.marker.is-active::after {
  border-top-color: var(--action-color);
}

.folder-selected,
.folder-selected-hover {
  border-radius: 5px;
}

.folder-hover {
  border-radius: 5px;
}

/* bouton ancre visuel bleu ArcheoGRID */
#btn-archeogrid-info {
  background-color: var(--secondary-color);
  border-color: var(--primary-color);
  color: white;
}

/* bouton changer de page sur explore */
.page-item.active .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

/* bouton changer de page sur explore */
.page-item.active:hover .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

/* Custom pagination styles for pft3d theme */
/* Removed .custom-page-item.active .custom-page-link, .custom-page-link, .custom-page-link:hover, .custom-page-info */
/* Removed .custom-page-link i.fas, .custom-page-item.active .custom-page-link i.fas, .nav-arrow.active i.fas */

.vitrine-details h4,
.vitrine-details h5,
.vitrine-details strong {
  color: var(--primary-color);
}

strong {
  color: var(--primary-color);
}

.check_item.checked.see-select {
  background-color: var(--highlight-color) !important;
  box-shadow: 0 0 5px var(--secondary-color);
}

.list-vitrine-actions a {
  color: var(--primary-color);
}

.list-vitrine-actions a:hover {
  color: var(--primary-color);
}

.grid-vitrine-item.checked,
.grid-vitrine-item.see-select.checked {
  background-color: var(--highlight-color) !important;
  box-shadow: 0 0 5px var(--secondary-color) !important;
}

.grid-vitrine-item.checked .grid-vitrine-body,
.grid-vitrine-item.see-select.checked .grid-vitrine-body {
  background-color: var(--highlight-color) !important;
}

.grid-vitrine-item.checked .grid-vitrine-img-link,
.grid-vitrine-item.see-select.checked .grid-vitrine-img-link {
  background-color: var(--highlight-color) !important;
}

.check_item.checked .see-select {
  box-shadow: inset 0px 0px 5px var(--primary-color);
}

.see-select {
  transition: all 200ms cubic-bezier(0.19, 1, 0.22, 1);
}

.check_item.checked.see-select,
.check_item.checked {
  background-color: var(--highlight-color) !important;
  box-shadow: 0 0 5px var(--secondary-color);
}

.check_item.checked a,
.check_item.checked .card-body {
  background-color: inherit;
}

.check_item.checked .grid-vitrine-body {
  background-color: var(--highlight-color) !important;
}

.check_item.checked .grid-vitrine-img-link {
  background-color: var(--highlight-color) !important;
}

.check_item.checked .list-vitrine-content {
  background-color: var(--highlight-color) !important;
}

.check_item.checked .list-vitrine-image {
  background-color: var(--highlight-color) !important;
}

/* Rectangle selection style */
#drag-selection-rectangle {
  position: fixed;
  border: 1px solid var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
  pointer-events: none;
  display: none;
  z-index: 1000;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.mx-1 {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-secondary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}


/* Removed hover effect for btn-secondary */
.btn-secondary:disabled {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.option {
  background-color: var(--primary-color) !important;
  color: white;
}

/* Direct Bootstrap override - similar to pagination */
.custom-select option:-moz-focusring,
.form-select option:-moz-focusring,
#nb-results-page option:-moz-focusring,
select option:-moz-focusring {
  color: white !important;
  background-color: var(--primary-color) !important;
}

/* Remove blue focus effects */
.btn:focus,
.btn-primary:focus,
.btn-secondary:focus,
.btn-hand:focus,
.form-control:focus,
.custom-select-input:focus,
select:focus,
a:focus,
.dropdown-toggle:focus,
.page-link:focus {
  box-shadow: none !important;
  outline: none !important;
}

/* Remove blue focus ring from Bootstrap components */
.form-control:focus,
.custom-select-input:focus {
  border-color: inherit !important;
}

*::selection {
  background-color: rgba(var(--primary-color-rgb), 0.3) !important;
  color: inherit !important;
}

*::-moz-selection {
  background-color: rgba(var(--primary-color-rgb), 0.3) !important;
  color: inherit !important;
}

/* .card-link, .list-link {
  pointer-events: auto !important;
  -webkit-user-drag: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.list-link .check_item.checked {
  background-color: var(--highlight-color) !important;
} */

.dropdown-menu > li > a,
.dropdown-menu > .dropdown-item {
    color: black !important;
    text-decoration: none;
    border-radius: 0.25rem;
}

/* Ensure no text decoration on hover for all dropdown items */
.dropdown-menu > li > a:hover,
.dropdown-menu > .dropdown-item:hover {
    text-decoration: none !important;
}

/* Additional Bootstrap overrides */
.dropdown-menu .active > a {
  background-color: var(--primary-color) !important;
  color: white !important;
  background-image: none !important;
}

.dropdown-menu .active > a:focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  background-image: none !important;
}

.dropdown-menu a:focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  background-image: none !important;
}

/* Target Bootstrap dropdown menu */
.dropdown-menu {
  background-color: #fff !important;
}

.form-range {
  --slider-thumb-color: var(--primary-color) !important;
  --slider-thumb-hover-color: var(--primary-color) !important;
}

/* .list-link .check_item.checked,
.list-link .check_item.checked .list-vitrine-content,
.list-link .check_item.checked .list-vitrine-image {
  background-color: var(--highlight-color) !important;
} */

.selection-dropdown .form-check-input:indeterminate {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.selection-dropdown .form-check-input:hover {
  border-color: var(--primary-color) !important;
}

.dropdown-toggle-icon .fas {
  color: var(--primary-color) !important;
}

.selection-dropdown .form-check-input:checked {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.dropdown-menu > li > a:focus,
.dropdown-menu > .dropdown-item:focus {
  background-color: inherit !important;
  color: inherit !important;
}

.custom-select-option.keyboard-selected {
  background-color: var(--highlight-color);
  border-left: 3px solid var(--primary-color);
  padding-left: calc(0.75rem - 3px);
}

.custom-select-option.selected {
  background-color: var(--highlight-color);
  font-weight: 500;
}

.btn-outline-secondary {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

button.btn-multi-tag, .btn-multi-tag {
  background-color: white !important;
  border-color: var(--primary-color) !important;
}

/* Vitrine button styles - PFT3D theme */
.btn-vitrine-primary {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

.btn-vitrine-secondary {
  background-color: white !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* Hover effects for vitrine buttons - PFT3D theme */
.btn-vitrine-primary:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
  text-decoration: none !important;
}

.btn-vitrine-secondary:hover {
  background-color: white !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  box-shadow: none !important;
  text-decoration: none !important;
}

/* Apply underline only to the text span - PFT3D theme */
.btn-vitrine-primary:hover .btn-text {
  text-decoration: underline !important;
}

/* Remove active/click effect for buttons - PFT3D theme */
.btn-vitrine-primary:active,
.btn-vitrine-primary:focus {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
  box-shadow: none !important;
  transform: none !important;
  text-decoration: none !important;
  outline: none !important;
}

.btn-vitrine-secondary:active,
.btn-vitrine-secondary:focus {
  background-color: white !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  box-shadow: none !important;
  transform: none !important;
  text-decoration: none !important;
  outline: none !important;
}

/* No underline on active state - PFT3D theme */
.btn-vitrine-primary:active .btn-text,
.btn-vitrine-secondary:active .btn-text,
.btn-vitrine-primary:focus .btn-text,
.btn-vitrine-secondary:focus .btn-text {
  text-decoration: none !important;
}

.btn-hand.btn-multi-tag {
  background-color: white !important;
  border-color: var(--primary-color) !important;
}

/* Grid/List toggle button active state */
.btn-active {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

.btn-active i {
  color: white !important;
}

#menuGauche .form-inline .btn-secondary .fa-search {
  background-color: transparent !important;
  color: var(--primary-color) !important;
}

/* Theme-specific styling for the chevron icon */
.nb-per-page-container::after {
  color: var(--primary-color);
}

/* Project header for pft3d theme */
.project-header {
  color: var(--primary-color);
}

/* Project name for pft3d theme - this will be applied to the entire header link */
.project-name {
  color: var(--primary-color) !important;
}
.project-name:hover {
  color: var(--action-color) !important;
}

/* Folders header for pft3d theme */
.folders-header {
  color: var(--primary-color);
}

/* Tag styling for pft3d theme */
.tag-pft3d {
  background-color: white !important;
  border-color: var(--primary-color) !important;
  border-radius: 4px;
  padding: 3px 8px;
  margin: 2px;
  display: inline-flex;
  align-items: center;
  font-size: 0.85rem;
}

.btn-toggle-section {
  color: var(--primary-color) !important;
}

.column-resizer:hover,
.column-resizer.active {
    background-color: var(--primary-color) !important;
}

.btn-toggle-section:hover {
  color: var(--secondary-color) !important;
}

.model3d-section h4 {
  color: var(--primary-color) !important;
}

.primary-icon-color {
  color: white !important;
}

.secondary-icon-color {
    color: var(--primary-color) !important;
}

.create-vf-btn {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

.list-icon-btn:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.list-icon-btn {
    color: var(--primary-color);
}

.list-icon-btn:hover {
    color: var(--secondary-color);
}

.fakelink {
  color: var(--secondary-color) !important;
}

.column-resizer:hover,
.column-resizer.active {
  transition: all 0.1s ease;
    color: var(--primary-color);
}

#list-btn.btn-hand {
  background-color: var(--item-color) !important;
  border: none !important;
}

#grid-btn.btn-hand {
  background-color: var(--item-color) !important;
  border: none !important;
}

.fas.fa-list-ul.fa-lg {
  color: var(--primary-color);
}

.fas.fa-th-large.fa-lg {
  color: var(--primary-color);
}

.custom-range::-webkit-slider-thumb {
  background: var(--primary-color);
}

.custom-range::-moz-range-thumb {
  background: var(--primary-color);
}

.custom-range::-ms-thumb {
  background: var(--primary-color);
}

.custom-range::-webkit-slider-thumb:active {
  background-color: var(--primary-color);
}

.custom-range:focus::-webkit-slider-thumb, 
.custom-range:focus::-moz-range-thumb,
.custom-range:focus::-ms-thumb {
  box-shadow: var(--primary-color);
}