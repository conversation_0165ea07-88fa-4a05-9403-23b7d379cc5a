<script>
    function initMapGeoloc(name, lat, lng) {
        try {
            let mymap = L.map(name, { scrollWheelZoom: false }).setView([lat, lng], 4);
            <PERSON>.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>' +
                    ' contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
                maxZoom: 18,
            }).addTo(mymap);
            let marker = L.marker([lat, lng]).addTo(mymap);
        } catch (error) {
            console.error("Error initializing map:", error);
        }
    }
    setTimeout(function () {
        window.dispatchEvent(new Event('resize'));
    }, 1000);
</script>
<% if (context === 'p') { %>
<%- include('utils/title-content', { home: "home" }) %>
<hr style="margin-top: 0rem;">
<%} %>
<div class="d-flex justify-content-between" style="height: 79vh;">
    <% if (context === 'p') { %>
    <div id="menuGauche" class="hide-on-mobile d-flex flex-column justify-content-between col-3"
         style="max-height: none; overflow: visible;">
        <div id="left-top" class="d-flex flex-column gap-2">
            <div class="form-inline d-flex gap-1">
                <input type="text" class="form-control" name="mySearch" id="mySearch"
                       onkeydown="if (event.keyCode == 13){ searchURL(<%= projectId%>) }">
                <button class="btn btn-secondary" type="submit" onclick="searchURL(<%= projectId%>)"><i
                       class="fas fa-search"></i></button>
            </div>
            <% if ((user.id) && ((user.user_status === 'admin') ||(user.user_status === 'scribe') || (user.user_status === 'user'))) { %>
            <div class="dropdown">
                <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" id="dropdownMenuButton"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <%=__('myselection')%>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li id="save-selection">
                        <a type="button" class="dropdown-item">
                            <%=__('selectionSave')%>
                        </a>
                    </li>
                    <li>
                        <a type="button" class="dropdown-item" href="#" onclick="exploreSelection(<%= projectId %>)">
                            <%=__('selectionOpen')%>
                        </a>
                    </li>
                    <li>
                        <a type="button" class="dropdown-item" href="#"
                           onclick="removeSelection(<%= user.id %>, <%= projectId %>, '<%= lng%>')">
                            <%=__('selectionDelete')%>
                        </a>
                    </li>
                </ul>
            </div>
            <%  } %>

            <% if (accessProject) { %>
            <% if ((user.user_status !== 'guest') && (comment.length > 0)) { %>
            <a href="javascript:"
               onclick="getComments('<%= branch %>',<%= projectId %>, '<%=user.id%>','<%= lng %>', <%= comment.length %>);"><i
                   class="far fa-comments"></i> <%=__('note')%><% if (comment.length > 1) {%>s<% } %></a>
            <% } %>
                <% let varThesinfo = 0 %>
            <% if ((thesinfo === 'yes') || (thesSimpleinfo === 'yes') ) { varThesinfo = 1 }  %>
            <% if (varThesinfo === 1 ) { %>
            <a href="/thesaurusv/<%= branch %>,<%= projectId %>"><i class="fas fa-book"></i>
                <%=__('thesaurusOnly').charAt(0).toUpperCase() + __('thesaurusOnly').slice(1)%></a>
            <% } else if (varThesinfo === 0) { if (branch !== 'corpus') { %><a href="/thesaurusPactols/<%= branch %>,<%= projectId %>"><i class="fas fa-book"></i>
                <%=__('thesaurusOnly').charAt(0).toUpperCase() + __('thesaurusOnly').slice(1)%> Pactols</a><% } } %>
            <% if (unicosNb) { %>
            <a href="javascript:" onclick="exploreUnicos(<%= projectId %>);">
                <i class="fa fa-file-image" aria-hidden="true"></i> Unicos (<%= unicosNb %>)
            </a><% } %>

            <ul id="main-ul">
                <% if (tree.length >= 1) { %>
                <% let prevDepth = 0 %>
                <% let depth = 0 %>
                <% let nextDepth = 0 %>

                <li>
                    <input type="checkbox" id="<%= tree[0].id%>" />
                    <i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>
                    <% if (tree[0].nb_items > 0) { %>
                        <a href="javascript:"
                        <% if (tree[0].nb_items > 0) { %>
                           onclick="exploreFolder(<%= tree[0].id %>);"
                                <% } %>
                        <% if (tree[0].get_children !== null || tree[0].nb_items > 0 || tree[0].nb_tot_items > 0) { %>
                           style="text-decoration: none;font-weight: bolder;"
                                <% } %>>
                            <label class="fakelink" id="toptop"
                                   <% if (tree[0].nb_tot_items > 0 || tree[0].nb_items > 0) { %>for="<%=tree[0].id%>" <%}%> style="margin-bottom: 0px;">
                                <% let foldername = tree[0].name %>
                                <% if (tree[0].nb_items > 0) { foldername += ' ['+tree[0].nb_items+']' } %>
                                <% if (tree[0].nb_tot_items > 0 && tree[0].get_children !== null) { foldername += ' ('+tree[0].nb_tot_items+')' } %>
                                <% if (user.read.indexOf(tree[0]['id']) !== -1   || user.user_status === 'admin' )  {%>
                                <span id="context_<%= tree[0].id%>"
                                      oncontextmenu="return monmenu(this, <%= tree[0].virtual %>, <%= tree[0].downloadable %>, '<%= tree[0].id %>', '<%= tree[0].name %>', '<%= user.user_status %>', <%= user.write.indexOf(tree[0].id) %>, 'slave', '<%= lng %>')"
                                      <% if (tree[0]['virtual'] === 1) { %>style="color: rgb(20,91,222); font-style: italic;"
                                        <% } %>><%= foldername %></span><% } %>
                            </label> <a type="button" href="/project/<%= projectId %>" style="text-decoration: none">
                                &nbsp;<i class="fas fa-info-circle"></i></a>
                        </a>
                    <% } else { %>
                    <label id="toptop" class="fakelink" for="<%= tree[0].id%>" style="margin-bottom: 0px">
                        <% if (user.read.indexOf(tree[0]['id']) !== -1   || user.user_status === 'admin' )  {%>
                        <span id="context_<%= tree[0].id%>"
                              oncontextmenu="return monmenu(this, <%= tree[0].virtual %>, 0, '<%= tree[0].id %>', '<%= tree[0].name %>', '<%= user.user_status %>', <%= user.write.indexOf(tree[0]['id']) %>, 'master', '<%= lng %>')"><%= tree[0].name%></span>
                        <% } %>
                        <a type="button" href="/project/<%= projectId %>" style="text-decoration: none">
                            &nbsp;<i class="fas fa-info-circle"></i>
                        </a>
                    </label>
                    <% } %>



                    <ul>

                        <% if (tree.length >= 3) { %>

                        <% prevDepth = tree[0].depth %>
                        <% depth = tree[1].depth %>
                        <% nextDepth = tree[2].depth %>

                        <% for (let i = 1; i < tree.length - 1; i++) { %>
                        <% if ( user.read.indexOf(tree[i]['id']) !== -1 || user.user_status === 'admin' ) { %>
                        <% prevDepth = depth %>
                        <% depth = tree[i].depth %>
                        <% nextDepth = tree[i + 1].depth %>

                        <% for (let j = 0; j < (prevDepth - depth); ++j) { %>
                    </ul>
                </li>
                <% } %>

                <li>
                    <input type="checkbox" id="<%=tree[i].id%>">
                    <% if (tree[i].get_children !== null) { %>
                    <i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;
                    <% } else { %>
                    <i class="far fa-folder"></i><i class="far fa-folder-open"></i>&nbsp;
                    <% } %>

                    <a href="javascript:"
                       <% if (tree[i].nb_items > 0) { %>
                       onclick="exploreFolder(<%= tree[i].id %>);"
                       <% } %>
                       <% if (tree[i].get_children !== null || tree[i].nb_items > 0 || tree[i].nb_tot_items > 0) { %>
                       style="text-decoration: none;font-weight: bolder;"
                       <% } %>>
                        <label class="fakelink"
                               <% if (tree[i].get_children !== null || tree[i].nb_tot_items > 0) { %>for="<%=tree[i].id%>" <%}%> style="margin-bottom: 0px;">
                                            <% let foldername = tree[i].name %>
                               <% if (tree[i].nb_items > 0) { foldername += ' ['+tree[i].nb_items+']' } %>
                               <% if (tree[i].nb_tot_items > 0 && tree[i].get_children !== null) { foldername += ' ('+tree[i].nb_tot_items+')' } %>
                               <span id="context_<%= tree[i].id%>"
                               oncontextmenu="return monmenu(this, <%= tree[i].virtual %>, <%= tree[i].downloadable %>, '<%= tree[i].id %>', '<%= tree[i].name %>', '<%= user.user_status %>', <%= user.write.indexOf(tree[i].id) %>, 'slave', '<%= lng %>')"
                               <% if (tree[i]['virtual'] === 1) { %>style="color: rgb(20,91,222);font-style: italic;"
                               <% } %>><%= foldername %></span>
                        </label>
                    </a>

                    <% if (nextDepth > depth) { let ouverture = 0 ;%>
                    <%# avant d'ouvrir le niveau on s'assure que le niveau suivant (et tous les folders du niveau suivant ! est bien autorisé pour le user%>
                      <% for (let t = i+1; t < tree.length ; t++) { if (tree[t].global_rank.indexOf(tree[i].global_rank+'.') !== -1 ) { %>
                        <% if ( user.read.indexOf(tree[t]['id']) !== -1 || user.user_status === 'admin' ) { %>
                        <%# on peut ouvrir %><% ouverture = 1 %>
                        <% } %>
                    <% } } %>
                    <% if (ouverture) { %>
                    <ul>
                    <% } %>
                        <% } %>
                        <% if (nextDepth == depth) { %>
                </li>
                <% } %>
                <% } %>
                <% } %>
                <% } %>

                <% prevDepth = depth %>
                <% depth = tree[tree.length - 1].depth %>
                <% for (let j = 0; j < (prevDepth - depth); ++j) { %>
            </ul>
            </li>
            <% } %>

            <% if (tree.length >= 2) { %>
            <% if ( user.read.indexOf(tree[tree.length - 1]['id']) !== -1 || user.user_status === 'admin' ) { %>
            <li>
                <input type="checkbox" id="<%=tree[tree.length - 1].id%>">
                <% if (tree[tree.length - 1].get_children !== null) { %>
                <i class="fas fa-folder"></i><i class="fas fa-folder-open"></i>&nbsp;
                <% } else { %>
                <i class="far fa-folder"></i><i class="far fa-folder-open"></i>&nbsp;
                <% } %>

                <a href="javascript:"
                   <% if (tree[tree.length - 1].nb_items > 0) { %>
                   onclick="exploreFolder(<%= tree[tree.length - 1].id %>);"
                   <% } %>
                   <% if (tree[tree.length - 1].get_children !== null || tree[tree.length - 1].nb_items > 0 || tree[tree.length - 1].nb_tot_items > 0) { %>
                   style="text-decoration: none;font-weight: bolder;"
                   <% } %>>
                    <label class="fakelink"
                           <% if (tree[tree.length - 1].nb_tot_items > 0 || tree[tree.length - 1].nb_items > 0) { %>for="<%=tree[tree.length - 1].id%>" <%}%> style="margin-bottom: 0px;">
                                            <% let foldername = tree[tree.length - 1].name %>
                           <% if (tree[tree.length - 1].nb_items > 0) { foldername += ' ['+tree[tree.length - 1].nb_items+']' } %>
                           <% if (tree[tree.length - 1].nb_tot_items > 0 && tree[tree.length - 1].get_children !== null) { foldername += ' ('+tree[tree.length - 1].nb_tot_items+')' } %>
                           <span id="context_<%= tree[tree.length - 1].id%>"
                           oncontextmenu="return monmenu(this, <%= tree[tree.length - 1].virtual %>, <%= tree[tree.length - 1].downloadable %>, '<%= tree[tree.length - 1].id %>', '<%= tree[tree.length - 1].name %>', '<%= user.user_status %>', <%= user.write.indexOf(tree[tree.length - 1].id) %>, 'slave', '<%= lng %>')"
                           <% if (tree[tree.length - 1]['virtual'] === 1) { %>style="color: rgb(20,91,222); font-style: italic;"
                           <% } %>><%= foldername %></span>
                    </label>
                </a>
            </li>
            <% } %>
            <% } %>
            </ul> <%# close first folder ul %>
            </li> <%# close first folder li %>
            <% } %>
            </ul> <%# close main-ul %>
            <% } else { %>
            <p>Projet privé</p>
            <% } %>
        </div> <%# close left-top %>

        <div id="left-bot" style="overflow: auto;">
            <%# QUID du USER qui a des accès en ecriture et qui veut se faire des sélection ? %>

            <div id="tagprojet">

                <% if (user.user_status === 'admin') { %>
                <% if (doi.id) { %>
                <% if (doi.doi) { %> <% } else { %>
                <br><a href="#" type="button" class="btn btn-sm btn-light"
                   onclick="validate_doi('folder', <%= projectId%>)"
                   style="border:2px solid #242943;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;">VALIDATE
                    DOI</a>
                <%  } } else  { %>
                <br><a href="#" type="button" class="btn btn-sm btn-light"
                   onclick="generate_doi('folder', <%= projectId%>)"
                   style="border:2px solid #242943;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;">GENERATE
                    DOI</a>
                <% } %>
                <% } %>
            </div>

        </div>
    </div> <%# close menuGauche %>
    <% } %>


    <div class="col-12 <% if (context === 'p') { %>col-md-9<% } %>" id="menuCentre" style="display: none;">
        <% if (locals.flash && locals.flash.ok) { %>
        <div class="m-2 alert alert-success text-capitalize">
            <%= flash.ok %>
        </div>
        <% } %>

        <div id="tabloid" class="container-fluid">
            <% if (data.metadata.length > 0) { %>
            <% if (data['metadata'][0]['rank'] == 1) { %>
            <h3><%= data['metadata'][0]['value'] %><% if (doi.doi) { %> <a href="/<%= branch.toUpperCase() %>/<%= url %>"><img
                         src="/assets/images/indexDOI.jpg" style="width: 1em;"></a><% } %></h3>
            <% } else { %>
            <h3>
                <%= data['name'] %>
                <% if (doi.doi) { %>
                <a href="https://doi.org/<%= prefixDOI %>/<%= branch.toUpperCase() %>/<%= url %>"><img src="/assets/images/indexDOI.jpg"
                         style="width: 1em;"></a>
                <% } %>
            </h3>
            <% } %>
            <% if (data['representativePictureId'] !== 0) { %>
            <div class="container-fluid" style="text-align: center"><% if ((data['status'] === 'public') &&  (branch === 'corpus')) { %>
                <div class="superpose">
                    <a href="/projectV/<%= data['folderId'] %>" title="<%=__('browse')%>"><% } %>
                <img src="/assets/<%= data['url'] %><%= data['folder_name'] %>/<%= data['folder_name'] %>.jpg"
                     class="img-thumbnail" alt="<%= data['name'] %>">
                <% if ((data['status'] === 'public') &&  (branch === 'corpus')) { %>
                <div class="text_superpose" style="font-color: white;"><strong><%=__('browse')%> <%=__('corpus') %></strong></div>
                </a></div><% } %>
            </div>
            <% } %>
            <% if (overall.length !== 0) {%>
            <h4>
                <% if (overall.length === 1) {%>
                <%=__('overallproject')%>
                <%=__('concerned')%>
                <% } else { %>
                <%=__('overallprojects')%> <%=__('concerned_plur')%>
                <% } %>
            </h4>
            <% for (let ov = 0; ov < overall.length; ov++) { %>
            <a class="btn btn-sm" id="btn-archeogrid-info" href="/op/<%= overall[ov]['id']%>">
                <%= overall[ov]['name']%>
            </a>
            <% } } %>
            <br />
            <br /><% if (branch === 'pft3d') {%>
                <p style="text-align: center"><strong><%=__('codeproject') %></strong> <%= data['name'] %></p><% } %>
            <% for (let ind = 0; ind < dataModel.length; ind++) { %>
            <% if (dataModel[ind]['name'] === projectModel  ) { %>
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">
                        <% if (writeRights) { %>
                        <a type="button" href="/edit,<%= branch %>,<%= projectModel %>,folder,<%= projectId %>,<%= projectId %>"
                           class="float-end"
                           title="Indexer">
                            <i class="fas fa-edit"></i>
                        </a>
                        <% } %>
                        <%= dataModel[ind]['description'] %>
                    </h4>
                </div>
                <table class="table table-striped tableVitrine">
                    <tbody>
                        <% for (i = 0; i < data['metadata'].length; i++) { %>
                        <% if ((data['metadata'][i]['value'] != '') && (data['metadata'][i]['query'] == 'y')) { %>
                        <% if (!!data['metadata'][i]['value']) { %>
                        <% if ((data['metadata'][i]['label'] == 'Latitude') || (data['metadata'][i]['label'] == 'Longitude')) { %>
                        <% }%>
                        <% if (data['metadata'][i]['status'] == 'link') { %>
                        <% let link_values = Array.isArray(data['metadata'][i]['value']) ? data['metadata'][i]['value'] : [data['metadata'][i]['value']] %>
                        <tr>
                            <td>
                                <strong><%= data['metadata'][i]['label'] %></strong>
                            </td>
                            <td>
                                <% for(let val of link_values) { %>
                                    <a href="<%= val %>" target="_blank">
                                        <%= val %>
                                    </a>
                                    <br>
                                <% } %>
                            </td>
                        </tr>
                        <% } else if (data['metadata'][i]['status'] == 'map') { %>
                        <% let name = 'mapDemo'+data['metadata'][i]['id_metadata']
                                                                let lat = data['metadata'][i]['value'].split(",")[0]
                                                                let lng = data['metadata'][i]['value'].split(",")[1] %>
                        <tr>
                            <td><strong><%= data['metadata'][i]['label'] %></strong></td>
                            <td>
                                <div id="mapDemo<%= data['metadata'][i]['id_metadata'] %>" style="height:300px;">
                                </div>

                            </td>
                        </tr>
                        <% } else if ((data['metadata'][i]['status'] == 'doc') || (data['metadata'][i]['status'] == 'text')) { %>
                        <tr>
                            <td>
                                <strong><%= data['metadata'][i]['label'] %></strong>
                            </td>
                            <td><% if (data['metadata'][i]['isunique']) { %>
                                <%- data['metadata'][i]['value'] %>
                                <% } else { %>
                                <% for (let v = 0; v < data['metadata'][i]['value'].length; v++) { %>
                                <%- data['metadata'][i]['value'][v] %><br>
                                <%}%>

                                                        <% } %></td>
                        </tr>
                        <% } else { %><%# ne pas afficher la métadonnées "Nom usuel qui est affichée au départ %>
                        <% if (data['metadata'][i]['rank'] !== 1) { %>
                        <tr>
                            <td>
                                <strong><%= data['metadata'][i]['label'] %></strong>
                            </td>
                            <td>
                                <% if (data['metadata'][i]['isunique']) { %>
                                <%= data['metadata'][i]['value'] %>
                                <% } else { %>
                                <% for (let v = 0; v < data['metadata'][i]['value'].length; v++) { %>
                                <%= data['metadata'][i]['value'][v] %><br>
                                <%}%>

                                                        <% } %></td>
                        </tr>
                        <% }%>
                        <% }%>
                        <% }%>
                        <% }%>
                        <% } %>
                        <% if (nomenclature.length > 0) { for (let th = 0; th < nomenclature.length; th++) { %>
                        <tr>
                            <td>
                                <strong><%= nomenclature[th]['label'] %></strong>
                            </td>
                            <td>
                                <%= nomenclature[th]['value'] %></td>
                            <% }  } %>
                    </tbody>
                </table>
            </div>
            <% } %>
            <% } %>


            <% } else { %>
            <h3><%= data['name'] %></h3>
            <% if (data['representativePictureId'] !== 0) { %>
            <div class="container-fluid" style="text-align: center">
                <img src="/assets/<%= data['url'] %><%= data['folder_name'] %>/<%= data['folder_name'] %>.jpg"
                     class="img-thumbnail" alt="<%= data['name'] %>">
            </div>
            <% } %>
            <br><% if (branch === 'pft3d') {%>
                    <p style="text-align: center"><strong><%=__('codeproject') %></strong> <%= data['name'] %></p><% } %>
            <% for (let ind = 0; ind < dataModel.length; ind++) { %>
            <% if (dataModel[ind]['name'] == projectModel) { %>
            <% if ((nomenclature.length > 0) || (writeRights) )  { %>
            <div class="card">
                <h4><% if (writeRights) { %>
                    <a href="/edit,<%= branch %>,<%= projectModel %>,folder,<%= projectId %>,<%= projectId %>" class="float-end" title="Indexer">
                        <i class="fas fa-edit"></i>
                    </a>
                    <% } %>&nbsp;<%= dataModel[ind]['description'] %>
                </h4>
                <% if (nomenclature.length > 0) { %>
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <th style="width: 30%"></th>
                        <th style="width: 70%"></th>
                    </tr>
                    </thead>
                    <tbody>
                    <% for (let th = 0; th < nomenclature.length; th++) { %>
                    <tr>
                        <td style="text-align: right">
                            <strong><%= nomenclature[th]['label'] %></strong>
                        </td>
                        <td>
                            <%= nomenclature[th]['value'] %></td>
                        <% }   %>
                    </tbody>
                </table><% } %>
            </div><% } %>
            <% } %>
            <% } } %>

            <br>
            <% if (tagthes.length > 0 ) { let display = 0; let name = 'vide'; %>
            <% for (const thesaurus_value of thesaurusName.map(name => tagthes.find(el => el.thesaurus_name === name)).filter(Boolean)) { %>
            <% if (name !== thesaurus_value.name) { %>
            <% if ((name !== 'vide') && (display === 1)) { %>
        </div>
        <% } %>
        <div id="thes_<%= thesaurusName.indexOf(thesaurus_value.thesaurus_name) %>">
            <%= thesaurus_value.thesaurus_name %> :
            <% display = 1; %>
            <% } %>
            <button class="btn btn-sm btn-hand">
                <a href="/conceptName/<%= thesaurus_value.name %>"><%= thesaurus_value.name %></a>
                <% if ((user.id) && (writeRights)) { %>
                <span title="<%=__('delete')%>" onclick="delete_thesmulti_item('<%= branch %>', '<%= projectId%>', 'folder', '<%= thesaurus_value.thes_path %>', '<%= thesaurus_value.thesaurus %>')">
                    &nbsp;<i class="far fa-trash-alt"></i>
                </span>
                <% } %>
            </button>
            <% name = thesaurus_value.thesaurus_name; %>
            <% } %>
        </div>
        <% } %>
    </div>
</div>
<ul class="nav scroll-top">
    <li><a href="#toptop" title="<%=__('backtotop') %>">
            <i class="fas fa-chevron-up fa-2x">
            </i>
        </a>
    </li>
</ul>
<%- include('explore/explore') %>
</div>
</div>


</div> <!-- Main ouvert dans le header -->
<div class="loader"></div>

<div class="modal" tabindex="-1" role="dialog" id="infoRecherche">
    <div class="modal-dialog" role="document" style="height: auto!important;">
        <div class="modal-content" style="height: auto!important;">
            <div class="modal-header">
                <h5 class="modal-title">Recherche</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="height: auto!important;">
                <p>Pour faire une recherche ajouter + ou - devant chaque element de recherche
                    selon que l'on souhaite voir figurer cet élément (+) ou non (-)<br>
                    Exemple:<br>
                    <strong>+phare +Alexandrie -voiture -Egypte</strong><br>
                    Résultat :<br> tous les items dont les informations contiennent phare et Alexandrie<br>
                    mais pas voiture ni Egypte.<br>
                    <br>
                    Le + est facultatif<br>
                    <strong>+phare +Alexandrie -voiture -Egypte</strong><br>
                    est équivalent à <br>
                    <strong>phare Alexandrie -voiture -Egypte</strong><br>
                    Le moteur de recherche effectue ses recherches dans toutes les métadonnées qui ont été renseignées,
                    quelles que soient leur type.<br><br>
                    Les recherches sur des expressions (contenant des espaces) seront opérationnelles dans un futur
                    proche...

                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><%=__('close')%></button>
            </div>
        </div>
    </div>
</div>


<div class="toast-container bottom-0 end-0 p-3">
    <div id="project-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive"
         aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
try {
    <% for (let i = 1; i < data['metadata'].length; i++) { %>
        <% if (data['metadata'][i]['status'] == 'map') { %>
            <% let name = 'mapDemo' + data['metadata'][i]['id_metadata']; let lat = data['metadata'][i]['value'].split(",")[0]; let lng = data['metadata'][i]['value'].split(",")[1]; %>
            initMapGeoloc('<%= name %>', '<%= lat %>', '<%= lng %>');
        <% } %>
    <% } %>

    <% if (isfolder !== '') { %>
        window.onload = function() {
            exploreFolder(<%= isfolder %>);
            $('#<%= isfolder %>').parents('li').children('input').prop("checked", true); // déroule l'arborescence de répertoires juqu'au répertoire ouvert
        };
    <% } else { %>
        $('#menuCentre').show();
    <% } %>

    function submitSelection() {
        registerSelection(<%= user.id %>, <%= projectId %>, '<%= lng %>');
    }

    if (document.getElementById('save-selection')) {
        document.getElementById('save-selection').addEventListener('click', submitSelection);
    }
} catch (error) {
    console.error("Error in script execution:", error);
    $('#menuCentre').show(); // Ensure content is visible even if there's an error
}
</script>