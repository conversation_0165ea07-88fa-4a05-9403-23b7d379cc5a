<style>
    #table-csv-metadata-container {
        width: 100%; 
        overflow-x: auto;
        margin-bottom: 1rem;
    }

    #table-csv-metadata thead th {
        white-space: nowrap;
        background-color: var(--primary-color);
    }

    #table-csv-metadata thead th:first-child {
        border-radius: 8px 0 0 0;
    }

    #table-csv-metadata thead th:last-child {
        border-radius: 0 8px 0 0;
    }

    #table-csv-metadata {
        margin-top: 1rem;
    }

    #table-csv-metadata tr:hover td{
        cursor: pointer;
        background-color: #f0f0f0;
    }

    #table-csv-metadata tr.selected td{
        background-color: #f0f0f0;
    }

    #actions-csv-metadata-container {
        display: flex;
        gap: 1.2rem;
    }
</style>

<% if(mode === 'extended') { %>
    <div id="actions-csv-metadata-container">
        <div>
            <label for="branchSelector"><%=  __('Branch') %></label>
            <select id="branchSelector" class="form-select">
                <option value="all"><%=__('AllFP')%></option>
                <% for(const branch of branches){ %>
                    <option value="<%= branch.branchename %>"><%= branch.branchename %></option>
                <% } %>
            </select>
        </div>

        <div>
            <label for="search-csv-metadata"><%= __('admin.content.csvMetadata.searchCsvMetadataLabel') %></label>
            <input class="form-control" type="text" id="search-csv-metadata">
        </div>
    </div>
<% } %>

<div id="table-csv-metadata-container">
    <table id="table-csv-metadata" class="table">
        <thead class="table-dark">
            <th scope="col">#</th>
            <th scope="col"><%= __('Branch') %></th> 
            <th scope="col"><%= __('project') %></th> 
            <th scope="col"><%= __('file') %> CSV</th> 
            <% if(mode === 'extended') { %>
                <th scope="col"><%= __('admin.content.csvMetadata.ModelName') %></th> 
                <th scope="col"><%= __('admin.content.csvMetadata.TableName') %></th> 
                <th scope="col"><%= __('admin.content.csvMetadata.LastTableUpdate') %></th> 
                <th scope="col"><%= __('admin.content.csvMetadata.LastModelUpdate') %></th> 
            <% } %>
        </thead>
        <tbody>
            <% for(const row of csv_metadata) { %>
                <tr class="csv-metadata-row" id="csv-metadata-row-<%= row.id %>">
                    <td><%= row.id %></td>
                    <td><%= row.branch %></td>
                    <td><%= row.project_name %></td>
                    <td><%= row.csv_file_name %></td>
                    <% if(mode === 'extended') { %>
                        <td><%= row.metadata_model_name ?? '---' %></td>
                        <td><%= row.table_name ?? '---' %></td>
                        <td>
                            <% if(row.last_ingestion_table){ %>
                                <% const f_date = new Date(row.last_ingestion_table).toLocaleDateString() %>
                                <% if(f_date === new Date().toLocaleDateString()){ %>
                                    <%= __('Today_at') + ' ' + new Date(row.last_ingestion_table).toLocaleTimeString().slice(0, 5) %>
                                <% }else{ %>
                                    <%= f_date %>
                                <% } %>
                            <% }else{ %>
                                ---
                            <% } %>
                        </td>
                        <td>
                            <% if(row.last_ingestion_model){  %>
                                <% const f_date = new Date(row.last_ingestion_model).toLocaleDateString() %>
                                <% if(f_date === new Date().toLocaleDateString()){ %>
                                    <%= __('Today_at') + ' ' + new Date(row.last_ingestion_model).toLocaleTimeString().slice(0, 5) %>
                                <% }else{ %>
                                    <%= f_date %>
                                <% } %>
                            <% }else{ %>
                                ---
                            <% } %>
                        </td>
                    <% } %>
                </tr>
            <% } %>
        </tbody>
    </table>
</div>

<% if(mode === 'extended') { %>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const branchSelector = document.getElementById('branchSelector');
            const searchInput = document.getElementById('search-csv-metadata');
            const table = document.getElementById('table-csv-metadata');

            // Fiter by branch
            branchSelector.addEventListener('change', () => {
                const branch = branchSelector.value;
                for(let i = 1; i < table.rows.length; i++){
                    const row = table.rows[i];
                    if(branch === 'all' || row.cells[1].textContent === branch){
                        row.hidden = false;
                    }else{
                        row.hidden = true;
                    }
                }
            })
            
            // Filter by project name OR csv file name
            searchInput.addEventListener('input', () => {
                const value = searchInput.value;
                for(let i=1; i < table.rows.length; i++){
                    if(table.rows[i].cells[2].textContent.toLowerCase().includes(value.toLowerCase()) || table.rows[i].cells[3].textContent.toLowerCase().includes(value.toLowerCase())){
                        table.rows[i].hidden = false;
                    }else{
                        table.rows[i].hidden = true;
                    }
                }
            })
        })
    </script>
<% } %>