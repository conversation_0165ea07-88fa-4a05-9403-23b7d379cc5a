<%- include('unicoModal') %>

<!-- Visionneusevitrine CSS -->
<link rel="stylesheet" href="/css/visionneusevitrine.css">
<link rel="stylesheet" href="/css/visionneusevitrine-responsive.css">

<%- include('utils/title-content', { project: mainFolder, projectId: mainFolder.mainfolder, home: "home" }) %>

<%
function hasDisplayableMetadata(metadata, model) {
    if (!metadata || typeof metadata !== 'object') {
        return false;
    }
    for (const key of Object.keys(metadata)) {
        if (metadata[key] !== null && (model.includes(key) || ['nomenclature', 'thesaurus'].includes(key))) {
            const values = metadata[key];
            if (Array.isArray(values) && values.length > 0) {
                for(const item of values) {
                    if (key === 'nomenclature' || key === 'thesaurus') {
                        if (item && typeof item === 'object' && Object.keys(item).length > 0) {
                            for(const prop in item){
                                if(Object.prototype.hasOwnProperty.call(item, prop) && item[prop] && item[prop].value){
                                    return true;
                                }
                            }
                        }
                    } else if (item && item.displayable !== 0) {
                        if (Array.isArray(item.value) && item.value.some(v => v && String(v).trim() !== '')) return true;
                        if (!Array.isArray(item.value) && item.value && String(item.value).trim() !== '') return true;
                    }
                }
            }
        }
    }
    return false;
}

// Helper function to check if a metadata item should be rendered as tags
function shouldRenderAsTag(metaItem) {
    // Check if the metadata item has status 'thesaurus'
    return metaItem && metaItem.status === 'thesaurus';
}
%>
<div class="containerV">
    <div class="containerRow">
        <link rel="stylesheet" href="/css/mobile-vitrine.css">
        <script>
            (function() {
                if (window.innerWidth > 768) { // Condition for desktop
                    let savedLeftWidth = localStorage.getItem('vitrine-leftcol-width');
                    let savedRightWidth = localStorage.getItem('vitrine-details-width');
                    if (savedLeftWidth && savedRightWidth) {
                        savedLeftWidth = savedLeftWidth.toString().replace('%', '');
                        savedRightWidth = savedRightWidth.toString().replace('%', '');
                        const style = document.createElement('style');
                        style.textContent = `
                            .vitrine-leftcol { width: ${savedLeftWidth}% ; }
                            .vitrine-details { width: ${savedRightWidth}% ; }
                        `;
                        document.head.appendChild(style);
                    }
                }
            })();
        </script>
        <div class="vitrine-leftcol">
            <div class="vitrine-text-center">
                <div class="d-flex align-items-start">
                    <div class="superpose position-relative flex-grow-1">
                        <div class="vitrine-image-container" id="imageContainer">
                            <% if (locals.itemType === 'object') { %>
                                <% if (idFile !== '0') { %>
                                    <div class="object-image-container">
                                        <img id="objectImage" class="mx-auto d-block img-fluid visionneuseImg"
                                        src="/hhdd/<%= image['fid'] %>_<%= image['id']%>"
                                        alt="<%= image['filename'] %>">
                                    </div>

                                    <script>
                                        document.addEventListener('DOMContentLoaded', function() {
                                            var objectImage = document.getElementById('objectImage');
                                            if (objectImage) { // Check if the element exists
                                                var viewerUrl = '/viewer/<%= image.fid %>_<%= image.id %>?format=hhdd';

                                                objectImage.addEventListener('click', function(event) {
                                                    if (window.innerWidth <= 768 && typeof global_showFullscreen === 'function') { // Mobile behavior
                                                        event.preventDefault();
                                                        global_showFullscreen(objectImage);
                                                    } else { // Desktop behavior
                                                        window.open(viewerUrl, '_blank');
                                                        event.preventDefault();
                                                    }
                                                });
                                            }
                                        });
                                    </script>
                                <% } else { %>
                                    <% if (dataObject['id_nakala']) { %>
                                        <img class="mx-auto img-fluid" src="<%= dataObject['id_nakala'] %>" alt="<%= dataObject['name']%>">
                                    <% } else { %>
                                        <img class="mx-auto default-object-image" src="/assets/images/default_repre_image_object.png" alt="<%= image['filename']%>">
                                        <p><%=__('no2')%> <%=__('visualization')%> <%=__('for')%> <%=__('object2')%></p>
                                    <% } %>
                                <% } %>
                            <% } else { %>
                                <% if (image['file_ext'] === 'url') { %>
                                    <a href="<%= image['urlTarget']%>" target="_blank">
                                        <img class="mx-auto img-fluid" id="visionneuseImg"
                                            src="/media/image?fid=<%= folderId %>&id=<%= image['id'] %>&format=<% if (context == 'v' && image['parent_unico']==0) { %><%= image['srcImgSmall']%>&type=small&root=<%= root %>"
                                            <% } else { %><%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"<% } %>
                                            alt="<%= image['filename']%>" >
                                    </a>
                                <% } else if (image['file_ext'] === '3d') { %>
                                    <% if (HDrights) { %><a href="/viewer3d,<%= image['srcImg3d'] %>,<%= folderId %>,<%= root%>"
                                    title="The 3D viewer" target="_blank"><% } %>
                                        <img class="card-img mx-auto img-fluid" id="visionneuseImg"
                                            src="/media/image?fid=<%= folderId %>&id=<%= image['id']%>&format=<%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"
                                            alt="<%= image['filename'] %>">
                                        <% if (HDrights) { %></a><% } %>
                                <% } else { %>
                                    <img id="mainImage" class="mx-auto d-block img-fluid visionneuseImg"
                                    src="/hhdd/<%= folderId %>_<%= image['id']%>"
                                    alt="<%= image['filename'] %>">
                                    <script>
                                        document.addEventListener('DOMContentLoaded', function() {
                                            var mainImage = document.getElementById('mainImage');
                                            if (mainImage) { // Check if the element exists
                                                var viewerUrl = '/viewer/<%= folderId %>_<%= image["id"] %>?format=hhdd';

                                                mainImage.addEventListener('click', function(event) {
                                                    if (window.innerWidth <= 768 && typeof global_showFullscreen === 'function') { // Mobile behavior
                                                        event.preventDefault();
                                                        global_showFullscreen(mainImage);
                                                    } else { // Desktop behavior
                                                        window.open(viewerUrl, '_blank');
                                                        event.preventDefault();
                                                    }
                                                });
                                            }
                                        });
                                    </script>
                                <% } %>
                            <% } %>
                        </div>
                        <div class="vitrine-image-container d-none" id="modelContainer">
                            <% if (image['file_ext'] === '3d') { %>
                                <iframe id="model3dFrame" class="mx-auto d-block img-fluid visionneuseImg"
                                    data-src="/viewer3d,<%= image['srcImg3d'] %>,<%= folderId %>,<%= root%>"
                                    style="height: 400px; width: 100%;"
                                    title="3D Model Viewer">
                                </iframe>
                            <% } else if (locals.itemType === 'object' && ((typeof viewer !== 'undefined' && viewer && viewer.length > 0) || (typeof online !== 'undefined' && online && online.length > 0))) { %>
                                <% if (typeof viewer !== 'undefined' && viewer && viewer.length > 0) { %>
                                    <iframe id="model3dFrame" class="mx-auto d-block img-fluid visionneuseImg"
                                        data-src="<%= viewer[0]['value'] %>"
                                        style="height: 400px; width: 100%;"
                                        title="3D Model Viewer">
                                    </iframe>
                                <% } else if (typeof online !== 'undefined' && online && online.length > 0) { %>
                                    <iframe id="model3dFrame" class="mx-auto d-block img-fluid visionneuseImg"
                                        data-src="/viewer3d,<%= online[0]['hash'] %>,<%= image['fid'] %>,<%= root %>"
                                        style="height: 400px; width: 100%;"
                                        title="3D Model Viewer">
                                    </iframe>
                                <% } %>
                            <% } %>
                        </div>
                    </div>
                    <% if ((image['file_ext'] === '3d') ||
                          (locals.itemType === 'object' &&
                           ((typeof viewer !== 'undefined' && viewer && viewer.length > 0) ||
                            (typeof online !== 'undefined' && online && online.length > 0)))) { %>
                        <div class="ms-3 mt-2">
                            <button id="toggleModelBtn" class="btn btn-vitrine-secondary mx-1  border-1">
                                3D viewer
                            </button>
                        </div>
                    <% } %>
                </div>
                <% if (license) { %><p style="margin-top:3vh;font-size: smaller"><%- license['html'] %></p><% } %>
            </div>


            <div class="d-flex justify-content-center mt-3 mb-3">

                <% if (locals.itemType === 'object') { %>
                    <% if (Wrights) { %>
                        <a href="/keyword,<%= root %>,object,<%= idObj %>,<%= folderId %>" class="btn btn-secondary mx-1 btn-vitrine-secondary">
                            <i class="fas fa-tag secondary-icon-color"></i> <span class="btn-text"><%=__('addKeyword.title')%></span>
                        </a>
                    <% } %>

                    <% if (user.id !== 0) { %>
                        <% if (mainFolder['id_metadata_model']) { %>
                            <a href="/comment,<%= root%>,<%= folderId %>,<%= idObj %>,object" type="button" class="btn btn-secondary mx-1 btn-vitrine-secondary"
                            title="<%= mainFolder['description'] %>"><i class="fas fa-comments secondary-icon-color"></i><span class="btn-text"><%=__('comment')%> /
                                <%= mainFolder['label']%></span></a>
                        <% } else { %>
                            <a href="/comment,<%= root%>,<%= folderId %>,<%= idObj %>,object" type="button" class="btn btn-secondary mx-1 btn-vitrine-secondary"
                            title="<%=__('note')%>"><i class="fas fa-comments secondary-icon-color"></i> <span class="btn-text"><%=__('comment')%></span></a>
                        <% } %>
                    <% } %>

                    <% if (Wrights) { %>
                        <a href="/edit,<%= root %>,<%= modelObject %>,object,<%= idObj %>,<%= folderId %>" class="btn btn-secondary mx-1 btn-vitrine-secondary"
                        title="Indexer"><i class="fas fa-edit secondary-icon-color"></i> <span class="btn-text"><%=__('enrichData')%></span></a>
                    <% } %>
                <% } else { %>
                    <% if ((typeof downloadable !== 'undefined' && (downloadable === 'true' || downloadable === true)) || (user && user.user_status === 'admin')|| (user && user.user_status === 'scribe')) { %>
                        <a type="button" href="/download/<%= image['fid'] %>_<%= image['id'] %>" title="<%= __('download') %>"
                        class="btn btn-secondary mx-1 btn-vitrine-primary"><span class="btn-text"><%=__('download')%></span> <i class="fas fa-download primary-icon-color"></i>
                        </a>
                    <% } %>

                    <% if (rights) { %>
                        <a type="button" href="/keyword,<%= root %>,file,<%= idFile %>,<%= folderId %>" class="btn btn-secondary mx-1 btn-vitrine-secondary">
                            <i class="fas fa-tag secondary-icon-color"></i> <span class="btn-text"><%=__('addKeyword.title')%></span>
                        </a>
                    <% } %>
                    <% if (user.id) { %>
                        <% if (mainFolder['id_metadata_model']) { %>
                            <a type="button" href="/comment,<%= root%>,<%= folderId %>,<%= image['id'] %>,file"
                            class="btn btn-secondary mx-1 btn-vitrine-secondary"
                            title="<%= mainFolder['description'] %>">
                                <i class="fas fa-comments secondary-icon-color"></i> <span class="btn-text"><%=__('comment')%> / <%= mainFolder['label']%></span>
                            </a>
                        <% } else { %>
                            <a type="button" href="/comment,<%= root%>,<%= folderId %>,<%= image['id'] %>,file"
                            class="btn btn-secondary mx-1 btn-vitrine-secondary"
                            title="<%=__('comment')%>">
                                <i class="fas fa-comments secondary-icon-color"></i> <span class="btn-text"><%=__('comment')%></span>
                            </a>
                        <% } %>
                    <% } %>
                    <% if (rights) { %>
                        <a type="button" href="/edit,<%= root %>,<%= modelFile %>,file,<%= idFile %>,<%= folderId %>"
                        class="btn btn-secondary mx-1 btn-vitrine-secondary"
                        title="<%=__('enrichData')%>"><i class="fas fa-edit secondary-icon-color"></i> <span class="btn-text"><%=__('enrichData')%></span>
                        </a>
                    <% } %>
                <% } %>
            </div>
            <% if (typeof items !== 'undefined' && items && items.length > 0) { %>
                <% if (typeof nbObjects !== 'undefined' && nbObjects > 0) { %>
                    <% let previewObjectIds = []; %>
                    <div class="mt-4" id="associated-objects-container">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><strong>
                                <% if (nbObjects > 1) { %><%=__('associatedObjectplur')%><% } else { %><%=__('associatedObject')%><% } %>
                                : <%= nbObjects %>
                            </strong></h5>
                            <% const remainingObjects = nbObjects - Math.min(7, nbObjects); %>
                            <% if (remainingObjects > 0) { %>
                            <button class="btn btn-toggle-section" type="button" data-bs-toggle="collapse" data-bs-target=".objects-collapse" aria-expanded="false" aria-controls="objects-collapse">
                                <i class="fas fa-chevron-down"></i>
                                <span class="count-indicator">+ <%= remainingObjects %></span>
                            </button>
                            <% } %>
                        </div>
                        <div class="d-flex flex-wrap gap-2 mt-2 preview-row">
                            <%
                            let objectCount = 0;
                            for (let i = 0; i < items.length; i++) {
                                if (items[i]['type'] === 'object') {
                                    objectCount++;
                                    previewObjectIds.push(items[i]['id']);
                                    const isCollapsed = objectCount > 7 ? 'collapse' : '';
                                    const collapseClass = objectCount > 7 ? 'objects-collapse' : '';
                            %>
                                <a href="/visionneuseObj,<%= items[i]['itemId'] %>,<%= items[i]['id'] %>-<%= items[i]['itemFolder'] %>,<%= root %>-v,0" class="card text-center lazy-load-card <%= isCollapsed %> <%= collapseClass %>" title="<%= items[i]['name'] %>">
                                    <% if (items[i]['id'] && items[i]['id'] !== '0') { %>
                                        <div class="lazy-image-container">
                                            <img class="card-img mx-auto d-block lazy-image"
                                                data-src="/thumb/<%= items[i]['itemFolder'] %>_<%= items[i]['id'] %>"
                                                src="/assets/images/placeholder.png"
                                                alt="<%= items[i]['name'] %>">
                                        </div>
                                    <% } else { %>
                                        <div class="lazy-image-container">
                                            <img class="card-img mx-auto d-block default-object-image"
                                                src="/assets/images/default_repre_image_object.png"
                                                alt="<%= items[i]['name'] %>">
                                        </div>
                                    <% } %>
                                    <div class="card-body p-1">
                                        <div class="card-text vitrine-text">
                                            <% if (items[i]['name']) { %>
                                                <div class="text-center">
                                                    <% if (items[i]['name'].length > 17) { %>
                                                        <small><%= items[i]['name'].substring(0, 17) %>...</small>
                                                    <% } else { %>
                                                        <small><%= items[i]['name'] %></small>
                                                    <% } %>
                                                </div>
                                            <% } else { %>
                                                <small></small>
                                            <% } %>
                                        </div>
                                    </div>
                                </a>
                            <% } } %>
                        </div>
                    </div>
                <% } %>
                <% if (typeof nbFiles !== 'undefined' && nbFiles > 0) { %>
                    <% let previewFileIds = []; %>
                    <div class="mt-4" id="associated-files-container">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><strong>
                                <% if (nbFiles > 1) { %><%=__('associatedFileplur')%><% } else { %><%=__('associatedFile')%><% } %>
                                : <%= nbFiles %>
                            </strong></h5>
                            <% const remainingFiles = nbFiles - Math.min(7, nbFiles); %>
                            <% if (remainingFiles > 0) { %>
                            <button class="btn btn-toggle-section" type="button" data-bs-toggle="collapse" data-bs-target=".files-collapse" aria-expanded="false" aria-controls="files-collapse">
                                <i class="fas fa-chevron-down"></i>
                                <span class="count-indicator">+ <%= remainingFiles %></span>
                            </button>
                            <% } %>
                        </div>
                        <div class="d-flex flex-wrap gap-2 mt-2 preview-row">
                            <%
                            let fileCount = 0;
                            for (let i = 0; i < items.length; i++) {
                                if (items[i]['type'] === 'file') {
                                    fileCount++;
                                    previewFileIds.push(items[i]['id']);
                                    const isCollapsed = fileCount > 7 ? 'collapse' : '';
                                    const collapseClass = fileCount > 7 ? 'files-collapse' : '';
                            %>
                                <a href="/visionneuse,<%= items[i]['itemId'] %>-<%= items[i]['itemFolder'] %>,<%= root %>-v,0" class="card text-center lazy-load-card lazy-card <%= isCollapsed %> <%= collapseClass %>" title="<%= items[i]['name'] %>">
                                    <div class="lazy-image-container">
                                        <img class="card-img mx-auto d-block lazy-image"
                                            data-src="/thumb/<%= items[i]['itemFolder'] %>_<%= items[i]['id'] %>"
                                            src="/assets/images/placeholder.png"
                                            alt="<%= items[i]['name'] %>">
                                    </div>
                                    <div class="card-body p-1">
                                        <div class="card-text vitrine-text">
                                            <% if (items[i]['name']) { %>
                                                <div class="text-center">
                                                    <% if (items[i]['name'].length > 17) { %>
                                                        <small><%= items[i]['name'].substring(0, 17) %>...</small>
                                                    <% } else { %>
                                                        <small><%= items[i]['name'] %></small>
                                                    <% } %>
                                                </div>
                                            <% } else { %>
                                                <small></small>
                                            <% } %>
                                        </div>
                                    </div>
                                </a>
                            <% } } %>
                        </div>
                    </div>
                <% } %>
                <link rel="stylesheet" href="/css/lazy-loading.css">
                <script src="/js/lazy-loading.js"></script>
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Initialize lazy loading on page load
                    if (typeof initLazyLoading === 'function') {
                        initLazyLoading();
                    }

                    // Handle toggle button icon changes
                    document.querySelectorAll('.btn-toggle-section').forEach(function(button) {
                        button.addEventListener('click', function() {
                            const icon = this.querySelector('i');
                            if (icon.classList.contains('fa-chevron-down')) {
                                icon.classList.remove('fa-chevron-down');
                                icon.classList.add('fa-chevron-up');
                            } else {
                                icon.classList.remove('fa-chevron-up');
                                icon.classList.add('fa-chevron-down');
                            }
                        });
                    });

                    // Handle text changes for collapse sections
                    document.querySelectorAll('#files-collapse, #objects-collapse').forEach(function(collapseEl) {
                        const targetId = collapseEl.id;
                        const moreBtn = document.querySelector(`button[data-bs-target="#${targetId}"]`);

                        collapseEl.addEventListener('shown.bs.collapse', function() {
                            if (moreBtn) {
                                if (moreBtn.querySelector('.more-text')) {
                                    moreBtn.querySelector('.more-text').classList.add('d-none');
                                }
                                if (moreBtn.querySelector('.less-text')) {
                                    moreBtn.querySelector('.less-text').classList.remove('d-none');
                                }
                            }
                            // Lazy loading is handled by the global event listener in lazy-loading.js
                        });

                        collapseEl.addEventListener('hidden.bs.collapse', function() {
                            if (moreBtn) {
                                if (moreBtn.querySelector('.more-text')) {
                                    moreBtn.querySelector('.more-text').classList.remove('d-none');
                                }
                                if (moreBtn.querySelector('.less-text')) {
                                    moreBtn.querySelector('.less-text').classList.add('d-none');
                                }
                            }
                        });
                    });
                });
                </script>
            <% } %>

            <% if (nbObjects > 1 && typeof objects !== 'undefined' && objects) { %>
                <div class="mt-4" id="virtual-objects-container">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5><strong><%=__('associatedObject')%> : <%= objects.length %></strong></h5>
                    </div>
                    <div class="d-flex flex-wrap gap-2 mt-2">
                        <% for (let i = 0; i < objects.length; i++) { %>
                            <% if (objects[i]['name']) { %>
                                <div class="d-flex flex-column">
                                    <a href="/visionneuseObj,<%= objects[i]['itemId'] %>,<%- objects[i]['id'] ? objects[i]['id'] : 0 %>-<%- objects[i]['idfolder'] ? objects[i]['idfolder'] : objects[i]['itemFolder'] %>,<%= root %>-v,0" class="card lazy-load-card lazy-card" title="<%= objects[i]['name'] %>" target="_blank">
                                        <% if (objects[i]['id']) {%>
                                        <div class="lazy-image-container">
                                            <img class="card-img mx-auto d-block lazy-image"
                                                data-src="/thumb/<%= objects[i]['itemFolder'] %>_<%= objects[i]['id'] %>"
                                                src="/assets/images/placeholder.png"
                                                alt="<%= objects[i]['filename']%>">
                                        </div>
                                        <% } else { %>
                                        <div class="lazy-image-container">
                                            <img class="card-img mx-auto d-block default-object-image"
                                                src="/assets/images/default_repre_image_object.png"
                                                alt="<%= image['filename']%>">
                                        </div>
                                        <% } %>
                                        <div class="card-body p-1">
                                            <div class="card-text vitrine-text">
                                                <% if (objects[i]['name']) { %>
                                                <div class="text-center">
                                                    <% if (objects[i]['name'].length > 17) {%><small><%= objects[i]['name'].substring(0, 17)%>...</small>
                                                    <% } else { %><small><%= objects[i]['name']%></small><% } %>
                                                </div>
                                                <% } else { %><small></small><% } %>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            <% } %>
                        <% } %>
                    </div>
                </div>
            <% } else if (nbObjects > 0 && typeof objects !== 'undefined' && objects) { let i = 0 %>
                <div class="mt-4" id="virtual-object-container">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5><strong><%=__('associatedObject')%></strong></h5>
                    </div>
                    <div class="d-flex flex-wrap gap-2 mt-2">
                        <div class="d-flex flex-column">
                            <a href="/visionneuseObj,<%- objects[i]['itemId'] %>,<%- objects[i]['id'] ? objects[i]['id'] : 0 %>-<%- objects[i]['idfolder'] ? objects[i]['idfolder'] : objects[i]['itemFolder'] %>,<%= root %>-v,0" class="card lazy-load-card lazy-card" title="<%- objects[i]['name'] %>" target="_blank">
                                <% if (objects[i]['id']) {%>
                                <div class="lazy-image-container">
                                    <img class="card-img mx-auto d-block lazy-image" src="/thumb/<%- objects[i]['itemFolder'] %>_<%- objects[i]['id'] %>"
                                         alt="<%- objects[i]['filename']%>">
                                </div>
                                <% } else { %>
                                <div class="lazy-image-container">
                                    <img class="card-img mx-auto d-block default-object-image" src="/assets/images/default_repre_image_object.png" alt="<%= image['filename']%>">
                                </div>
                                <% } %>
                                <div class="card-body p-1">
                                    <div class="card-text vitrine-text">
                                        <% if (objects[i]['name']) { %>
                                        <div class="text-center">
                                            <% if (objects[i]['name'].length > 17) {%><small><%- objects[i]['name'].substring(0, 17)%>...</small>
                                            <% } else { %><small><%- objects[i]['name']%></small><% } %>
                                        </div>
                                        <% } else { %><small></small><% } %>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            <% } %>
        </div>
        <div id="column-resizer" class="column-resizer"></div>
                <div class="vitrine-details">
                    <% if (locals.itemType === 'object') { %>
                        <%
                        // Check for visible image tags for objects
                        let hasVisibleImageTagsObject = false;
                        if (image['tag'] && image['tag'].length > 0) {
                            for (var i = 0; i < image['tag'].length; i++) {
                                if (image['tag'][i] && image['tag'][i].trim()) {
                                    hasVisibleImageTagsObject = true;
                                    break;
                                }
                            }
                        }
                        console.log('hasVisibleImageTagsObject:', hasVisibleImageTagsObject);
                        if (hasVisibleImageTagsObject) { %>
                            <h5><i class="fa fa-tag secondary-icon-color"></i> <%= __('keywords')%> :</h5>
                            <% for (var i = 0; i < image['tag'].length; i++) { %>
                                <% if (image['tag'][i] && image['tag'][i].trim()) { %>
                                    <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %> m-1"><%= image['tag'][i] %></span>
                                <% } %>
                            <% } %>
                        <% } %>
                    <% } else { %>
                        <%
                        // Check for visible image tags for files
                        let hasVisibleImageTagsFile = false;
                        if (image['tag'] && image['tag'].length > 0) {
                            for (var i = 0; i < image['tag'].length; i++) {
                                if (image['tag'][i] && image['tag'][i].trim()) {
                                    hasVisibleImageTagsFile = true;
                                    break;
                                }
                            }
                        }
                        console.log('hasVisibleImageTagsFile:', hasVisibleImageTagsFile);
                        if (hasVisibleImageTagsFile) { %>
                            <h5><i class="fa fa-tag secondary-icon-color"></i> <%= __('keywords')%> :</h5>
                            <% for (var i = 0; i < image['tag'].length; i++) { %>
                                <% if (image['tag'][i] && image['tag'][i].trim()) { %>
                                    <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %> m-1"><%= image['tag'][i] %></span>
                                <% } %>
                            <% } %>
                        <% } %>
                    <% } %>
            <%
            if (typeof metadataDisplayCount === 'undefined') {
                var metadataDisplayCount = 0;
            }
            %>
            <% for (let key in metadata) { %>
                <% for (let i in modelComplet) { if (modelComplet[i]['name'] === key) { %>
                <% if (Object.prototype.hasOwnProperty.call(metadata, key)) { %>

                <%
                const shouldDisplay = (metadata[key] !== null) && ((model.indexOf(key) != -1) || (key === 'nomenclature') ||  (key === 'thesaurus'));
                
                if ( shouldDisplay ) { %>
                <% if (metadataDisplayCount > 0) { %>
                    <hr>
                <% }
                metadataDisplayCount++;
                %>
                <h4><%= modelComplet[i]['label'] %></h4>
                <% if ((key === 'nomenclature') || (key === 'thesaurus')) {%>
                <div class="mb-4">
                    <% for (let dat in metadata[key][0]) {%>
                    <% if ({}.hasOwnProperty.call(metadata[key][0], dat)) { %>
                    <h5><strong><%= metadata[key][0][dat]['label'] %></strong></h5>
                    <% if (shouldRenderAsTag(metadata[key][0][dat])) { %>
                        <p>
                            <% let tagValues = Array.isArray(metadata[key][0][dat]['value']) ? metadata[key][0][dat]['value'] : [metadata[key][0][dat]['value']]; %>
                            <% for (let tagVal of tagValues) { %>
                                <% if (tagVal && (typeof tagVal === 'object' ? tagVal.name : String(tagVal)).trim() !== '') { %>
                                    <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %> m-1" title="<%= metadata[key][0][dat]['label'] %>">
                                        <% if (typeof tagVal === 'object' && tagVal.short_name) { %>
                                            <a href="/concept/<%= tagVal.short_name %>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= tagVal['id_thes']%>&thesaurus=<%= tagVal['thesaurus']%>"><%= tagVal.name %></a>
                                            <% if (typeof rights !== 'undefined' && rights) { %>
                                                &nbsp;
                                                <span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                                                onclick="delete_thes_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= tagVal.thesaurus_path %>', '<%= tagVal.thesaurus %>')">
                                                <i class="far fa-trash-alt"></i>
                                                </span>
                                            <% } %>
                                        <% } else { %>
                                            <%= typeof tagVal === 'object' ? tagVal.name || String(tagVal) : String(tagVal).trim() %>
                                        <% } %>
                                    </span>
                                <% } %>
                            <% } %>
                        </p>
                    <% } else { %>
                        <p><%- (metadata[key][0][dat]['value']) %></p>
                    <% } %>
                    <% } %>
                    <% }%>
                </div>
                <% } else { %>
                <div class="mb-4">
                    <% for (meta_key in metadata[key]) { %>
                    <% if ({}.hasOwnProperty.call(metadata[key], meta_key)) { %>
                    <% if (metadata[key][meta_key]['displayable'] !== 0) { %>
                    <% const metaItem = metadata[key][meta_key]; %>
                    <% if (metaItem['status'] === 'link') {
                        let link_values = Array.isArray(metaItem['value']) ? metaItem['value'] : [metaItem['value']]; %>
                        <h5><strong><%= metaItem['label'] %></strong></h5>
                        <p>
                            <% for(let val of link_values) { %>
                                <a href="<%= val %>" target="_blank">
                                    <%- ( val ) %>
                                </a>
                                <br>
                            <% } %>
                        </p>
                    <% } else if (metaItem['status'] === 'thesaurus') { %>
                        <h5><strong><%= metaItem['label'] %></strong></h5>
                        <p>
                        <% let multi = 0; let d = metadata[key][meta_key]['value'].length %>
                        <% for (let t = 0; t < thes.length; t++) { %>
                            <% for (let theskey in metadata) { %>
                                <% if (metadata[theskey]) { %>
                                <%for (let i = 0; i < metadata[theskey].length; i++) {  %>
                                <% for ( let metakey in metadata[theskey][i]['thesmaisoninfo'] ) {%>
                                        <% if (((metakey === 'list') && metadata[theskey][i]['thesmaisoninfo']['query']=== 'y')
                                                && (thes[t]['list'] === metadata[theskey][i]['thesmaisoninfo'][metakey])
                                                && (thes[t]['code'] === metadata[theskey][i]['thesmaisoninfo']['qualifier'])
                                                && (metadata[theskey][i]['thesmaisoninfo']['id_metadata']=== metadata[key][meta_key]['id_metadata'])
                                        )  { multi++; %>
                                            <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %> m-1" title="<%= metaItem['label'] %>">
                                            <a href="/concept/<%= metadata[theskey][i]['thesmaisoninfo']['value']%>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= metadata[theskey][i]['thesmaisoninfo']['id_thes_thesaurus']%>&thesaurus=<%= metadata[theskey][i]['thesmaisoninfo']['thesaurus']%>">
                                                <%= metadata[theskey][i]['thesmaisoninfo']['value']%></a>
                                            <% if (typeof rights !== 'undefined' && rights) { %>
                                                &nbsp;
                                            <span class="btn-hand" title="<%=__('delete')%>"
                                            <% if (metadata[theskey][i]['thesmaisoninfo']['qualifier'] !== '') { %>
                                                onclick="delete_thes_item_qual('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= metadata[theskey][i]['thesmaisoninfo']['id_thes_thesaurus']%>', '<%= metadata[theskey][i]['thesmaisoninfo']['list']%>', '<%= metadata[theskey][i]['thesmaisoninfo']['qualifier']%>')">
                                                <% } else { %>
                                                    onclick="delete_thes_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= metadata[theskey][i]['thesmaisoninfo']['thes_path']%>', '<%= metadata[theskey][i]['thesmaisoninfo']['list']%>')">
                                                <% } %>
                                                <i class="far fa-trash-alt"></i>
                                            </span>
                                        <% } %>
                                    </span>
                                <% if (d > 1) {%>,<%} %>
                                <% d = d - 1 %>
                                <% } %>
                        <% } } } } } %></p>
                    <% } else if  (metaItem['status'] === 'multi') { %>
                    <h5><strong><%= metaItem['label'] %></strong></h5>
                    <p>
                        <% let multi = 0; let d = metaItem['value'].length %>
                        <% for (let t = 0; t < thes.length; t++) { %>
                            <% for (let multikey in metadata) { %>
                                <% if (metadata[multikey]) { %>
                                <%for (let i = 0; i < metadata[multikey].length; i++) {  %>
                                    <% for ( let metakey in metadata[multikey][i]['multiinfo'] ) {%>
                                        <% if (((metakey === 'list') && metadata[multikey][i]['multiinfo']['query']=== 'y')
                                                && (thes[t]['list'] === metadata[multikey][i]['multiinfo'][metakey])
                                                && (thes[t]['code'] === metadata[multikey][i]['multiinfo']['qualifier'])
                                                && (metadata[multikey][i]['multiinfo']['id_metadata']=== metadata[key][meta_key]['id_metadata'])
                                        )  { multi++; %>
                                        <button class="btn btn-sm btn-hand btn-multi-tag">
                                            <a href="/concept/<%= metadata[multikey][i]['multiinfo']['value']%>?rootF=<%= mainFolder['mainfolder']%>&type=multi&idThes=<%= metadata[multikey][i]['multiinfo']['id_thes_thesaurus']%>&thesaurus=<%= metadata[multikey][i]['multiinfo']['thesaurus']%>"
                                                class="btn-multi-tag">
                                                <%= metadata[multikey][i]['multiinfo']['value']%></a>
                                            <% if (typeof rights !== 'undefined' && rights) { %>
                                            &nbsp;<span class="btn-hand" title="<%=__('delete')%>"
                                            <% if (metadata[multikey][i]['multiinfo']['qualifier'] !== '') { %>
                                                  onclick="delete_thesmulti_item_qual('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= metadata[multikey][i]['multiinfo']['thes_path']%>', '<%= metadata[multikey][i]['multiinfo']['list']%>', '<%= metadata[multikey][i]['multiinfo']['qualifier']%>')">
                                                <% } else { %>
                                                    onclick="delete_thesmulti_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= metadata[multikey][i]['multiinfo']['thes_path']%>', '<%= metadata[multikey][i]['multiinfo']['list']%>')">
                                                <% } %>
                                                <i class="far fa-trash-alt secondary-icon-color"></i>
                                            </span><% } %>
                                        </button> <% if (d > 1) {%>,<%} %>
                                        <% d = d - 1 %>
                                        <% } %>
                        <% } } } } } %>
                    </p>
                    <% } else if (metaItem['status'] === 'API_StreetView') { %>
                        <h5><strong><%= metaItem['label'] %></strong></h5>
                        <%
                        let coordValueSv = metaItem['value'];
                        let latSv = null;
                        let lngSv = null;
                        try {
                            if (Array.isArray(coordValueSv) && coordValueSv.length > 0) {
                                coordValueSv = coordValueSv[0];
                            }
                            if (typeof coordValueSv !== 'string') {
                                coordValueSv = String(coordValueSv);
                            }
                            coordValueSv = coordValueSv.replace(/^"|"$/g, '');
                            if (coordValueSv.includes(',')) {
                                const partsSv = coordValueSv.split(',');
                                if (partsSv.length >= 2) {
                                    latSv = partsSv[0].trim();
                                    lngSv = partsSv[1].trim();
                                    if (isNaN(parseFloat(latSv)) || isNaN(parseFloat(lngSv))) {
                                        latSv = null;
                                        lngSv = null;
                                    }
                                }
                            }
                        } catch (e) {
                            console.error("Error parsing Street View coordinates:", e, "Raw value:", metaItem['value']);
                            latSv = null;
                            lngSv = null;
                        }
                        %>
                        <% if (latSv !== null && lngSv !== null) { %>
                            <% const svDisplayId = "streetViewDisplay_" + String(metaItem['id_metadata']).replace(/[^a-zA-Z0-9]/g, '_'); %>
                            <% const svCallbackName = "initStreetViewDirectCallback_" + String(metaItem['id_metadata']).replace(/[^a-zA-Z0-9]/g, '_'); %>
                            <div id="<%= svDisplayId %>" class="map-container" style="width: 100%; height: 400px; border-radius: 8px; position: relative; overflow: hidden;">
                                <div id="loading_<%= svDisplayId %>" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: rgba(255,255,255,0.8); z-index: 10;">
                                    <div><%= __('loadingStreetView') || 'Loading Street View...' %></div>
                                </div>
                            </div>
                            <script>
                                (function() {
                                    const currentLat = parseFloat('<%= latSv %>');
                                    const currentLng = parseFloat('<%= lngSv %>');
                                    const displayContainerId = '<%= svDisplayId %>';
                                    const loadingIndicatorId = 'loading_<%= svDisplayId %>';
                                    const GOOGLE_MAPS_API_KEY = '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';

                                    window['<%= svCallbackName %>'] = function() {
                                        initializeGoogleStreetViewOnly();
                                    };

                                    function loadGoogleMapsApiScriptForStreetViewOnly() {
                                        if (typeof window.google === 'object' && typeof window.google.maps === 'object' && typeof window.google.maps.StreetViewService === 'function') {
                                            initializeGoogleStreetViewOnly();
                                        } else {
                                            const scriptTag = document.createElement('script');
                                            scriptTag.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&loading=async&callback=<%= svCallbackName %>&libraries=streetView&v=weekly`;
                                            scriptTag.async = true;
                                            scriptTag.defer = true;
                                            scriptTag.onerror = function() {
                                                console.error("Google Maps API script could not be loaded for Street View.");
                                                const mapDisplayContainer = document.getElementById(displayContainerId);
                                                const loadingIndicator = document.getElementById(loadingIndicatorId);
                                                if (loadingIndicator) loadingIndicator.style.display = 'none';
                                                if (mapDisplayContainer) mapDisplayContainer.innerHTML = "<p>" + ('<%= __("errorLoadingMapApi") || "Error loading mapping service." %>') + "</p>";
                                            };
                                            document.head.appendChild(scriptTag);
                                        }
                                    }

                                    function initializeGoogleStreetViewOnly() {
                                        const loadingIndicator = document.getElementById(loadingIndicatorId);
                                        const mapDisplayContainer = document.getElementById(displayContainerId);

                                        if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.StreetViewService === 'undefined') {
                                            console.error("Google Maps StreetViewService not available after API load attempt.");
                                            if (loadingIndicator) loadingIndicator.style.display = 'none';
                                            if (mapDisplayContainer) mapDisplayContainer.innerHTML = "<p>" + ('<%= __("errorLoadingMapService") || "Mapping service components not available." %>') + "</p>";
                                            return;
                                        }
                                        
                                        if (loadingIndicator) loadingIndicator.innerHTML = '<div>' + ('<%= __("checkingStreetView") || "Checking for Street View..." %>') + '</div>';

                                        const streetViewService = new google.maps.StreetViewService();
                                        const searchLocation = { lat: currentLat, lng: currentLng };

                                        streetViewService.getPanorama({ location: searchLocation, radius: 200, source: google.maps.StreetViewSource.OUTDOOR }, function(data, status) {
                                            if (status === google.maps.StreetViewStatus.OK) {
                                                if (loadingIndicator) loadingIndicator.style.display = 'none';
                                                if (mapDisplayContainer) mapDisplayContainer.innerHTML = ''; 

                                                new google.maps.StreetViewPanorama(mapDisplayContainer, {
                                                    position: data.location.latLng,
                                                    pov: { heading: 0, pitch: 0 },
                                                    zoom: 0,
                                                    visible: true,
                                                    addressControl: false,
                                                    linksControl: true,
                                                    panControl: true,
                                                    enableCloseButton: false
                                                });
                                            } else {
                                                console.warn('Street View not available for this location (Status: ' + status + ').');
                                                if (loadingIndicator) loadingIndicator.style.display = 'none';
                                                if (mapDisplayContainer) mapDisplayContainer.innerHTML = "<p>" + ('<%= __("streetViewNotAvailable") || "Street View is not available for this location." %>') + "</p>";
                                            }
                                        });
                                    }
                                    
                                    if (document.readyState === 'complete' || (document.readyState !== 'loading' && !document.documentElement.doScroll)) {
                                        loadGoogleMapsApiScriptForStreetViewOnly();
                                    } else {
                                        document.addEventListener('DOMContentLoaded', loadGoogleMapsApiScriptForStreetViewOnly);
                                    }
                                })();
                            </script>
                        <% } else { %>
                            <p><%- metaItem['value'] %></p>
                            <p><%= __('invalidCoordinates') || 'Invalid coordinates for Street View.' %></p>
                        <% } %>
                    <% } else if (metaItem['status'] === 'map') { %>
                        <h5><strong><%= metaItem['label'] %></strong></h5>
                        <% const mapDisplayId = "mapDisplayArea_" + String(metaItem['id_metadata']).replace(/[^a-zA-Z0-9]/g, '_'); %>
                        <% const gMapsCallbackName = "initStreetViewLeafletCallback_" + String(metaItem['id_metadata']).replace(/[^a-zA-Z0-9]/g, '_'); %>
                        <%
                        let coordValue = metaItem['value'];
                        let lat = null;
                        let lng = null;
                        try {
                            if (Array.isArray(coordValue) && coordValue.length > 0) {
                                coordValue = coordValue[0];
                            }
                            if (typeof coordValue !== 'string') {
                                coordValue = String(coordValue);
                            }
                            coordValue = coordValue.replace(/^"|"$/g, '');
                            if (coordValue.includes(',')) {
                                const parts = coordValue.split(',');
                                if (parts.length >= 2) {
                                    lat = parts[0].trim();
                                    lng = parts[1].trim();
                                    if (isNaN(parseFloat(lat)) || isNaN(parseFloat(lng))) {
                                        lat = null;
                                        lng = null;
                                    }
                                }
                            }
                        } catch (e) {
                            console.error("Error parsing coordinates:", e, "Raw value:", coordValue);
                            lat = null;
                            lng = null;
                        }
                        %>
                        <% if (lat !== null && lng !== null) { %>
                            <div id="<%= mapDisplayId %>" class="map-container" style="width: 100%; height: 400px; border-radius: 8px; position: relative; overflow: hidden;">
                                <div id="loading_<%= mapDisplayId %>" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: rgba(255,255,255,0.8); z-index: 10;">
                                    <div><%= __('loadingMapData') || 'Loading map data...' %></div>
                                </div>
                            </div>

                            <script>
                                (function() {
                                    const currentLat = parseFloat('<%= lat %>');
                                    const currentLng = parseFloat('<%= lng %>');
                                    const displayContainerId = '<%= mapDisplayId %>';
                                    const loadingIndicatorId = 'loading_<%= mapDisplayId %>';
                                    const GOOGLE_MAPS_API_KEY = 'AIzaSyDP6EmTgPHx6845lJz1t7n4pD__tQcDIuE';

                                    window['<%= gMapsCallbackName %>'] = function() {
                                        initializeTopLevelMapLogic();
                                    };

                                    function loadGoogleMapsApiScript() {
                                        if (typeof window.google === 'object' && typeof window.google.maps === 'object') {
                                            initializeTopLevelMapLogic();
                                        } else {
                                            const scriptTag = document.createElement('script');
                                            scriptTag.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&loading=async&callback=<%= gMapsCallbackName %>&libraries=streetView&v=weekly`;
                                            scriptTag.async = true;
                                            scriptTag.defer = true;
                                            scriptTag.onerror = function() {
                                                console.error("Google Maps API script could not be loaded. Falling back to Leaflet.");
                                                setupLeafletMapFallback('Failed to load Google Maps API.');
                                            };
                                            document.head.appendChild(scriptTag);
                                        }
                                    }

                                    function initializeTopLevelMapLogic() {
                                        const loadingIndicator = document.getElementById(loadingIndicatorId);
                                        if (loadingIndicator) loadingIndicator.innerHTML = '<div>' + '<%= __("checkingStreetView") || "Checking for Street View..." %>' + '</div>';

                                        const streetViewService = new google.maps.StreetViewService();
                                        const searchLocation = { lat: currentLat, lng: currentLng };

                                        streetViewService.getPanorama({ location: searchLocation, radius: 200, source: google.maps.StreetViewSource.OUTDOOR }, function(data, status) {
                                            if (status === google.maps.StreetViewStatus.OK) {
                                                if (loadingIndicator) loadingIndicator.style.display = 'none';
                                                const mapDisplayContainer = document.getElementById(displayContainerId);
                                                if(mapDisplayContainer) mapDisplayContainer.innerHTML = '';

                                                new google.maps.StreetViewPanorama(mapDisplayContainer, {
                                                    position: data.location.latLng,
                                                    pov: { heading: 0, pitch: 0 },
                                                    zoom: 0,
                                                    visible: true,
                                                    addressControl: false,
                                                    linksControl: true,
                                                    panControl: true,
                                                    enableCloseButton: false
                                                });
                                            } else {
                                                console.warn('Street View not available for this location (Status: ' + status + '). Falling back to Leaflet.');
                                                setupLeafletMapFallback();
                                            }
                                        });
                                    }

                                    function setupLeafletMapFallback() {
                                        const mapDisplayContainer = document.getElementById(displayContainerId);
                                        const loadingIndicator = document.getElementById(loadingIndicatorId);
                                        
                                        if (loadingIndicator) loadingIndicator.style.display = 'none';
                                        if (!mapDisplayContainer) {
                                            console.error("Map display container not found for Leaflet fallback:", displayContainerId);
                                            return;
                                        }
                                        mapDisplayContainer.innerHTML = '';

                                        let leafletMap;
                                        let mapMarker;
                                        const lat = currentLat;
                                        const lng = currentLng;
                                        const zoomLevel = 5;

                                        try {
                                            leafletMap = L.map(displayContainerId, {
                                                scrollWheelZoom: false,
                                                fadeAnimation: true,
                                                zoomAnimation: true,
                                                markerZoomAnimation: true
                                            }).setView([lat, lng], zoomLevel);

                                            L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
                                                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
                                                maxZoom: 19,
                                                keepBuffer: 4
                                            }).addTo(leafletMap);

                                            mapMarker = L.marker([lat, lng]).addTo(leafletMap);
                                            mapMarker.bindPopup(`${lat.toFixed(5)}, ${lng.toFixed(5)}`);

                                            let updateInterval;
                                            let isUpdating = false;

                                            function startContinuousUpdates() {
                                                if (isUpdating) return;
                                                isUpdating = true;
                                                updateMapInternal();
                                                updateInterval = setInterval(function() {
                                                    updateMapInternal();
                                                }, 16);
                                            }

                                            function stopContinuousUpdates() {
                                                if (!isUpdating) return;
                                                clearInterval(updateInterval);
                                                isUpdating = false;
                                            }

                                            function updateMapInternal() {
                                                if (!leafletMap || !mapMarker) return;
                                                const currentCenter = leafletMap.getCenter();
                                                const currentZoom = leafletMap.getZoom();
                                                leafletMap.invalidateSize(true);
                                                leafletMap.setView(currentCenter, currentZoom, { animate: false });
                                            }

                                            window.addEventListener('resize', function() {
                                                updateMapInternal();
                                            });

                                            const columnResizer = document.getElementById('column-resizer');
                                            if (columnResizer) {
                                                columnResizer.addEventListener('mousedown', function() {
                                                    startContinuousUpdates();
                                                });
                                                document.addEventListener('mouseup', function() {
                                                    stopContinuousUpdates();
                                                    updateMapInternal();
                                                });
                                                columnResizer.addEventListener('touchstart', function(e) {
                                                    // e.preventDefault(); // May be needed depending on desired scroll behavior
                                                    startContinuousUpdates();
                                                });
                                                document.addEventListener('touchend', function() {
                                                    stopContinuousUpdates();
                                                    updateMapInternal();
                                                });
                                            }
                                            setTimeout(function() { updateMapInternal(); }, 100);

                                        } catch (e) {
                                            console.error("Error initializing Leaflet map:", e);
                                            if(mapDisplayContainer) mapDisplayContainer.innerHTML = "<%= __('errorLoadingMap') || 'Error loading map.' %>";
                                        }
                                    }

                                    document.addEventListener('DOMContentLoaded', function() {
                                        if (typeof L === 'object' && typeof L.map === 'function') {
                                            loadGoogleMapsApiScript();
                                        } else {
                                            console.error('Leaflet library (L) not found. Cannot initialize any map.');
                                            const mapDisplayContainer = document.getElementById(displayContainerId);
                                            if (mapDisplayContainer) mapDisplayContainer.innerHTML = 'Error: Required mapping library not found.';
                                            const loadingIndicator = document.getElementById(loadingIndicatorId);
                                            if (loadingIndicator) loadingIndicator.style.display = 'none';
                                        }
                                    });
                                })();
                            </script>
                        <% } else { %>
                            <p>
                                Raw coordinate value: <%- typeof coordValue === 'string' ? coordValue : JSON.stringify(coordValue) %>
                                <br>
                                <%= __('invalidCoordinates') || 'Invalid coordinates format provided.' %>
                            </p>
                        <% } %>
                    <% } else if( metaItem['status'] === 'actor' || metaItem['status'] === 'datation' || metaItem['status'] === 'location' ) { %>
                        <h5><strong><%= metaItem.label %></strong></h5>
                        <div class="advanced_model_display">
                            <%- include('displayMetadataSwitch.ejs', {metadata: metaItem}) %>
                        </div>
                    <% } else if (metaItem['status'] === 'text') { %>
                        <%# pour rendre les retour chariot style white-space: pre-line mais d coup il faut attenuer le premier espace d blic en réduisant la marge du champ titre a dessus%>
                        <h5 style="margin-bottom:-1rem;"><strong><%= metaItem.label %></strong></h5>
                        <% for (let val in  metaItem['value']) { %>
                        <p style="white-space:pre-line;">
                            <%- (metaItem['value'][val]) %>
                        </p>
                        <% } %>
                    <% } else { %>
                        <h5><strong><%= metaItem['label'] %></strong></h5>
                        <% if (shouldRenderAsTag(metaItem)) { %>
                            <p>
                                <% if (metaItem['isunique']) { %>
                                    <% let singleValue = Array.isArray(metaItem['value']) && metaItem['value'].length > 0 ? metaItem['value'][0] : metaItem['value']; %>
                                    <% if (singleValue && String(singleValue).trim() !== '') { %>
                                        <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %> m-1" title="<%= metaItem['label'] %>">
                                            <%
                                            // Use the actual thesaurus name from metaItem.list with proper fallbacks
                                            let thesaurusName = '';
                                            
                                            // Try different sources for the thesaurus name
                                            if (metaItem['list']) {
                                                thesaurusName = metaItem['list'];
                                            } else if (metaItem['code']) {
                                                // If list is not available, we might need to map from code to list
                                                // For license metadata, the list should be "license"
                                                if (metaItem['code'] === 'dblcore_file_license') {
                                                    thesaurusName = 'license';
                                                } else {
                                                    thesaurusName = metaItem['code'];
                                                }
                                            } else if (typeof thes !== 'undefined' && thes.length > 0) {
                                                // Last resort: find from thes array
                                                for (let t = 0; t < thes.length; t++) {
                                                    if (thes[t]['id_metadata'] === metaItem['id_metadata'] && thes[t]['status'] === 'thesaurus') {
                                                        thesaurusName = thes[t]['list'];
                                                        break;
                                                    }
                                                }
                                            }
                                            %>
                                            <a href="/concept/<%= encodeURIComponent(String(singleValue).trim()) %>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= metaItem['id_metadata']%>&thesaurus=<%= thesaurusName %>"><%= String(singleValue).trim() %></a>
                                            <% if (typeof rights !== 'undefined' && rights) { %>
                                            &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                                                onclick="delete_thes_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= metaItem.thes_path %>','<%= metaItem.thesaurus %>')">
                                                <i class="far fa-trash-alt"></i>
                                            </span><% } %>
                                        </span>
                                    <% } %>
                                <% } else { %>
                                    <% for (let val in metaItem['value']) { %>
                                        <% if (metaItem['value'][val] && String(metaItem['value'][val]).trim() !== '') { %>
                                            <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %> m-1" title="<%= metaItem['label'] %>">
                                                <%
                                                // Use the actual thesaurus name from metaItem.list with proper fallbacks
                                                let thesaurusName = '';
                                                
                                                // Try different sources for the thesaurus name
                                                if (metaItem['list']) {
                                                    thesaurusName = metaItem['list'];
                                                } else if (metaItem['code']) {
                                                    // If list is not available, map from code to list
                                                    if (metaItem['code'] === 'dblcore_file_license') {
                                                        thesaurusName = 'license';
                                                    } else {
                                                        thesaurusName = metaItem['code'];
                                                    }
                                                } else if (typeof thes !== 'undefined' && thes.length > 0) {
                                                    // Last resort: find from thes array
                                                    for (let t = 0; t < thes.length; t++) {
                                                        if (thes[t]['id_metadata'] === metaItem['id_metadata'] && thes[t]['status'] === 'thesaurus') {
                                                            thesaurusName = thes[t]['list'];
                                                            break;
                                                        }
                                                    }
                                                }
                                                %>
                                                <a href="/concept/<%= encodeURIComponent(String(metaItem['value'][val]).trim()) %>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= metaItem['id_metadata']%>&thesaurus=<%= thesaurusName %>"><%= String(metaItem['value'][val]).trim() %></a>
                                                <% if (typeof rights !== 'undefined' && rights) { %>
                                                &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                                                    onclick="delete_thes_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= metaItem.thes_path %>','<%= metaItem.thesaurus %>')">
                                                    <i class="far fa-trash-alt"></i>
                                                </span><% } %>
                                            </span>
                                        <% } %>
                                    <% } %>
                                <% } %>
                            </p>
                        <% } else { %>
                            <p>
                                <% if (metaItem['isunique'] ) { %><%- (Array.isArray(metaItem['value']) && metaItem['value'].length > 0 ? metaItem['value'][0] : metaItem['value']) %><% } else { %>
                                <% for (let val  in  metaItem['value']) { %>
                                <%- (metaItem['value'][val]) %><br><% } } %>
                            </p>
                        <% } %>
                    <% } %>
                    <% } %>
                    <% } %>
                    <% } %>
                </div>
                <% } %>
                <% } %>
                <% } %>
                <% } } } %>
                <% let multi = 0; %>
                <% for (let t = 0; t < thes.length; t++)   { %>
                <% for (let key in metadata) { %>
                <% if (metadata[key]) { %>
                <% if (key === 'pactols') { let display = ''; let d = 0; %>
                <% for (let i = 0; i < metadata[key].length; i++) { %>
                <% for (let p in metadata[key][i]['thespactolsinfo']) { %>
                <% if (p === 'list') {%>
                <% if (thes[t]['list'] === metadata[key][i]['thespactolsinfo'][p]) { %>
                <% display = thes[t]['label'] %><% d++ %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <% if (display !== '') { %>
                <br><%= display %> :
                <%for (let i = 0; i < metadata[key].length; i++) { %>
                <% for ( let metakey in metadata[key][i]['thespactolsinfo'] ) { %>
                <% if ((metakey === 'list') && (thes[t]['list'] === metadata[key][i]['thespactolsinfo'][metakey]))  {%>
                <button class="btn btn-sm btn-hand">
                    <a href="<%= metadata[key][i]['thespactolsinfo']['identifier'] %>">
                        <%= metadata[key][i]['thespactolsinfo']['value']%></a>
                </button> <% if (d > 1) {%>,<%} %>
                <% d = d - 1 %>
                <% } %>
                <% } %>
                <% } %>
                <% } else if (key === 'multi') { } else { let display = ''; let d = 0; %>
                <% let surkey = key + 'info' %>
                <% for (let i = 0; i < metadata[key].length; i++) { %>
                <% for (let p in metadata[key][i][surkey]) { %>
                <% if (p === 'list') {%>
                <% if (thes[t]['list'] === metadata[key][i][surkey][p]) { %>
                <% display = thes[t]['label'] %><% d++ %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <% if (display !== '') { %>
                <br><%= display %> :
                <%for (let i = 0; i < metadata[key].length; i++) { %>
                <% for ( let metakey in metadata[key][i][surkey] ) { %>
                <% if ((metakey === 'list') && (thes[t]['list'] === metadata[key][i][surkey][metakey]))  {%>
                <button class="btn btn-sm btn-hand">
                    <a href="#">
                        <%= metadata[key][i][surkey]['value']%></a>
                </button> <% if (d > 1) {%>,<%} %>
                <% d = d - 1 %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
                <%# tag de thesaurus SANS MODELE De METADONNEES RATTACHEES donc pour le moment pas possibilité de qualifier %>
                <%# deja des tag multi présents dans des modèles de métadonnées ?  multi ? s'il y en a alors on ne les affiche pas ecore... %>
                <% if (((tagThesmulti.length > 0 ) && (multi === 0 )) || ( tagPactols.length > 0 ) || ( tagMaison.length > 0 ) ) {
                let affTab = [] ; // on crée un tableau des tags déjà affichés dans les metadata
                let hasVisibleTags = false; // Track if there are actually visible tags to display
                

                
                if (metadata.multi && metadata.multi.length ) {
                    for (let tt = 0; tt < tagThesmulti.length; tt++) {
                        for ( let m =0; m < metadata['multi'].length; m++ ) {
                            if (tagThesmulti[tt]['thesaurus'] === metadata['multi'][m]['multiinfo']['thesaurus'] && tagThesmulti[tt]['name'] === metadata['multi'][m]['multiinfo']['value']) {
                                // C'est le même on ne l'affiche pas
                                tagThesmulti[tt]['affichage'] = 0;
                                affTab.push(tagThesmulti[tt]) ;
                            }
                        }
                    }
                }
                if (affTab.length < tagThesmulti.length ) {
                    // Créer un Set des IDs de affTab (sur le id_thesaurus)
                    const idsB = new Set(affTab.map(item => item.id));
                    
                    // Check if there are visible thesmulti tags
                    if (tagThesmulti.length > 0) {
                        tagThesmulti.forEach(element => {
                            if (!idsB.has(element.id) && element.name && element.name.trim()) {
                                hasVisibleTags = true;
                            }
                        });
                    }
                }
                
                // Check if there are visible PactolsGeo tags
                if (tagPactolsGeo.length > 0) {
                    for (let i = 0; i < tagPactolsGeo.length; i++) {
                        if (tagPactolsGeo[i].name && tagPactolsGeo[i].name.trim()) {
                            hasVisibleTags = true;
                            break;
                        }
                    }
                }
                
                // Check if there are visible Maison tags
                if (tagMaison.length > 0) {
                    for (var i = 0; i < tagMaison.length; i++) {
                        if (tagMaison[i].name && tagMaison[i].name.trim()) {
                            hasVisibleTags = true;
                            break;
                        }
                    }
                }
                
                // Only show separator if there are visible tags
                if (hasVisibleTags) { %>
                    <% if (hasDisplayableMetadata(metadata, model) || doi.doi) { %>
                    <hr>
                    <% } %>
                    <i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%> : <br>
                <% }
                if (affTab.length < tagThesmulti.length ) {
                    if (tagThesmulti.length > 0 ) {
                        const idsB = new Set(affTab.map(item => item.id));
                        for (let i = 0; i < tagThesmulti.length; i++) {
                            const element = tagThesmulti[i];
                            if (!idsB.has(element.id) && element.name && element.name.trim()) { %><%# si l'id n'est pas présent dans le tableau des tags déjà affichés (affTab) mappé avec les id (idsB) alors seulement on affiche %>
                            <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %>">
                                <a href="/concept/<%= element.name %>?rootF=<%= mainFolder['mainfolder']%>&type=multi&idThes=<%= element['id_thes']%>&thesaurus=<%= element['thesaurus']%>"><%= element.name %></a><% if (typeof rights !== 'undefined' && rights) { %>
                                &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                                      onclick="delete_thesmulti_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= element.thes_path %>','<%= element.thesaurus %>')">
                                    <i class="far fa-trash-alt"></i>
                                </span><% } %>
                            </span>
                            <%      }
                        }
                    }
                }
                
                // Only show PACTOLS section if there are visible PACTOLS tags
                let hasVisiblePactolsTags = false;
                if (tagPactols.length > 0) {
                    for (var i = 0; i < tagPactols.length; i++) {
                        if (tagPactols[i].name && tagPactols[i].name.trim()) {
                            hasVisiblePactolsTags = true;
                            break;
                        }
                    }
                }
                if (hasVisiblePactolsTags) { %>
                    <% if (hasDisplayableMetadata(metadata, model) || comments.length > 0 || doi.doi || unicos.length > 0) { %>
                    <hr>
                    <% } %>
                    <br>
                    <i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%> (PACTOLS) : <br>
                    <% for (var i = 0; i < tagPactols.length; i++ ) { %>
                    <% if (tagPactols[i].name && tagPactols[i].name.trim()) { %>
                    <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %>">
                        <a href="/concept/<%= tagPactols[i].name %>?rootF=<%= mainFolder['mainfolder']%>&type=pactols&idThes=<%= tagPactols[i]['id_thes']%>&thesaurus=<%= tagPactols[i]['thesaurus']%>"><%= tagPactols[i].name %></a><% if (typeof rights !== 'undefined' && rights) { %>
                        &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                              onclick="delete_thespactols_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= tagPactols[i].id %>', '<%= tagPactols[i].id_thes %>', '<%= tagPactols[i].thesaurus %>')">
                            <i class="far fa-trash-alt"></i>
                        </span><% } %>
                    </span>
                    <% } %>
                    <% } %>
                <% }
                
                // Only show PactolsGeo tags if there are visible ones
                let hasVisiblePactolsGeoTags = false;
                if (tagPactolsGeo.length > 0) {
                    for (let i = 0; i < tagPactolsGeo.length; i++) {
                        if (tagPactolsGeo[i].name && tagPactolsGeo[i].name.trim()) {
                            hasVisiblePactolsGeoTags = true;
                            break;
                        }
                    }
                }
                if (hasVisiblePactolsGeoTags) { %>
                    <% if (hasDisplayableMetadata(metadata, model) || comments.length > 0 || doi.doi || unicos.length > 0) { %>
                    <hr>
                    <% } %>
                    <br>
                    <i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%> (PACTOLS GEO) : <br>
                    <% for (let i = 0; i < tagPactolsGeo.length; i++ ) { %>
                    <% if (tagPactolsGeo[i].name && tagPactolsGeo[i].name.trim()) { %>
                    <div>
                        <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %>">
                            <a href="/concept/<%= tagPactolsGeo[i].name %>?rootF=<%= mainFolder['mainfolder']%>&type=pactolsgeo&idThes=<%= tagPactolsGeo[i]['id']%>"><%= tagPactolsGeo[i].name %></a><% if (typeof rights !== 'undefined' && rights) { %>
                            &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                                  onclick="delete_thespactolsgeo_item('<%= root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%= tagPactolsGeo[i].id %>')">
                                <i class="far fa-trash-alt"></i>
                            </span><% } %>
                        </span>
                    </div>
                    <% } %>
                    <% } %>
                <% }
                
                // Only show Maison tags if there are visible ones
                // Filter out thesaurus data that should be handled by proper metadata sections
                let filteredTagMaison = [];
                if (tagMaison.length > 0) {
                    // Create a Set of thesaurus names that are already being displayed in metadata sections
                    let displayedThesaurusNames = new Set();
                    
                    // Check for thesaurus metadata that's already displayed
                    if (metadata) {
                        for (let key in metadata) {
                            if (metadata[key] && typeof metadata[key] === 'object') {
                                // Check if this is a direct thesaurus key
                                if (key === 'thesaurus') {
                                    if (metadata[key][0]) {
                                        for (let dat in metadata[key][0]) {
                                            if (metadata[key][0][dat] && metadata[key][0][dat].value) {
                                                let values = Array.isArray(metadata[key][0][dat].value) ? metadata[key][0][dat].value : [metadata[key][0][dat].value];
                                                values.forEach(val => {
                                                    if (val && typeof val === 'object' && val.short_name) {
                                                        displayedThesaurusNames.add(val.short_name);
                                                    } else if (val && typeof val === 'string') {
                                                        displayedThesaurusNames.add(val);
                                                    }
                                                });
                                            }
                                        }
                                    }
                                } else {
                                    // Check for metadata items with thesaurus status
                                    for (let meta_key in metadata[key]) {
                                        if (metadata[key][meta_key] && metadata[key][meta_key].status === 'thesaurus') {
                                            let values = Array.isArray(metadata[key][meta_key].value) ? metadata[key][meta_key].value : [metadata[key][meta_key].value];
                                            values.forEach(val => {
                                                if (val && String(val).trim() !== '') {
                                                    displayedThesaurusNames.add(String(val).trim());
                                                }
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // Filter tagMaison to exclude items already displayed in metadata sections
                    filteredTagMaison = tagMaison.filter(item =>
                        item.name && item.name.trim() && !displayedThesaurusNames.has(item.short_name || item.name)
                    );
                }
                
                let hasVisibleMaisonTags = filteredTagMaison.length > 0;
                
                if (hasVisibleMaisonTags) { %>
                    <% if (hasDisplayableMetadata(metadata, model) || comments.length > 0 || doi.doi || unicos.length > 0) { %>
                    <hr>
                    <% } %>
                    <br>
                    <i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%> (MAISON) : <br>
                    <% for (var i = 0; i < filteredTagMaison.length; i++ ) { %>
                    <% if (filteredTagMaison[i].name && filteredTagMaison[i].name.trim()) { %>
                    <%
                    const thesPath = filteredTagMaison[i]['thesaurus_path'] || '';
                    const thesName = filteredTagMaison[i]['thesaurus'] || '';
                    %>
                    <div>
                        <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %>">
                            <a href="/concept/<%= filteredTagMaison[i].short_name %>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= filteredTagMaison[i]['id_thes']%>&thesaurus=<%= filteredTagMaison[i]['thesaurus']%>"><%= filteredTagMaison[i].name %></a><% if (typeof rights !== 'undefined' && rights) { %>
                            &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                                  onclick="delete_thes_item('<%- root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%- thesPath %>', '<%- thesName %>')">
                                <i class="far fa-trash-alt"></i>
                            </span><% } %>
                        </span>
                    </div>
                    <% } %>
                    <% } %>
                <% }
                } %>
                <%# tag non alignés %>
                <%
                // Check if there are visible free tags
                let hasVisibleFreeTags = false;
                if (tags.length > 0) {
                    for (let t = 0; t < tags.length; t++) {
                        if (tags[t].name && tags[t].name.trim()) {
                            hasVisibleFreeTags = true;
                            break;
                        }
                    }
                }
                if (hasVisibleFreeTags) { %>
                    <% if (hasDisplayableMetadata(metadata, model) || comments.length > 0 || doi.doi || unicos.length > 0) { %>
                    <hr>
                    <% } %>
                    <br>
                    <i class="fas fa-tags"></i> &nbsp;<%= __('freetags')%> (<%=__('no3') %> <%=__('alignedPlural')%>) :<p>
                    <% for (let t = 0; t < tags.length; t++ ) { %>
                        <% if (tags[t].name && tags[t].name.trim()) { %>
                        <% const tagId = tags[t]['id'] || ''; %>
                        <span class="tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %>"><%= tags[t].name %><% if (typeof rights !== 'undefined' && rights) { %>
                        &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>"
                              onclick="delete_tag_item('<%- root %>', '<%= itemType === 'object' ? idObj : idFile %>', '<%= itemType %>', '<%- tagId %>')"> <i
                                    class="far fa-trash-alt"></i>
                </span><% } %></span>
                        <% } %>
                    <% } %></p>
                <% } %>
                <% if (metadata && Object.keys(metadata).length > 0) { %>
                <% if (metadata['iptc'] ) { %>
                <% if (image['iptc_gm']) { %>
                <% if (Object.prototype.hasOwnProperty.call(image['iptc_gm'], 'Keyword[2,25]')) { %>
                <h5><strong><%= __('keywords') %></strong></h5>
                <p><%= image['iptc_gm']['Keyword[2,25]'] %></p>
                <!-- 4 closing braces here -->
                <% } %>
                <% } %>
                <% } %>
                <% } %>

                <% if (comments.length > 0) { %>
                <% if (hasDisplayableMetadata(metadata, model) || doi.doi || tagThesmulti.length > 0) { %>
                <hr>
                <% } %>
                <div class="mt-4">
                    <h4><%= comments.length %> <%=__('note')%><% if (comments.length > 1) {%>s<% }%></h4>
                    <% for (let i = 0; i <  comments.length; i++) { %>
                    <div class="comment-block">
                        <p><strong><%=__('content')%>:</strong> <%- comments[i]['content'] %></p>
                        <p><strong><%=__('author')%>:</strong> <%= comments[i]['signature']%></p>
                        <% if (comments[i]['author'].length > 0 ) {%>
                            <p><strong><%=__('inNameOf')%>:</strong> <%= comments[i]['author']%></p>
                        <% } %>
                        <p><strong>Date:</strong> <%= comments[i]['date']%></p>
                        <% if (comments[i]['other_date'].length > 0 ) {%>
                            <p><strong><%=__('dateExplicite')%>:</strong> <%- comments[i]['other_date'] %></p>
                        <% } %>
                    </div>
                    <% if (i < comments.length - 1) { %><hr><% } %>
                    <% }%>
                </div>
                <% } %>
                <% if (unicos.length > 0) { %>
                <% if (hasDisplayableMetadata(metadata, model) || comments.length > 0 || doi.doi || tagThesmulti.length > 0) { %>
                <hr>
                <% } %>
                <div>
                    <div id="alerts" class="d-flex justify-content-center">
                        <div class="alert alert-success mb-2 alert-success d-none" role="alert">
                            <%=__('unicoDeleteSuccess')%></div>
                        <div class="alert alert-danger mb-2 alert-notconnected d-none" role="alert">
                            <%=__('disconnected')%></div>
                        <div class="alert alert-danger mb-2 alert-forbidden d-none" role="alert">
                            <%=__('forbidden')%></div>
                        <div class="alert alert-danger mb-2 alert-notfound d-none" role="alert"><%=__('notFound')%>
                        </div>
                        <div class="alert alert-danger mb-2 alert-error d-none" role="alert"><%=__('error')%></div>
                    </div>
                    <h4>Unicos</h4>
                    <table class="table" id="unicos-table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col"><%= __('name') %></th>
                                <th scope="col"><%= __('annotation') %></th>
                                <th scope="col">Thesaurus</th>
                                <th scope="col"><%= __('date') %></th>
                                <th scope="col"><%= __('link') %></th>
                                <th scope="col"><%= __('actions') %></th>
                            </tr>
                        </thead>
                        <tbody>
                            <% for (let u in unicos) { %>
                            <% const unico = unicos[u] %>
                            <% const date = new Date(unico.creation_date) %>
                            <% const url = `/crop/${folderId}_${unico.id_file}/${unico.x},${unico.y},${unico.width},${unico.height}` %>
                            <tr id-unico="<%= unico.id %>">
                                <th scope="row"><%= parseInt(u) + 1 %></th>
                                <td><%= unico.name %></td>
                                <td><%= unico.annotation %></td>
                                <td>
                                    <ul class="unico-tag-list">
                                        <% for (let thes in unico.thesaurus) { %>
                                        <% const thesaurus =  unico.thesaurus[thes] %>
                                        <li>
                                            <span class="thesaurus-name"><%= thesaurus.name ?? (thesaurus.thesaurus.charAt(0).toUpperCase() + thesaurus.thesaurus.slice(1)) %> :</span>
                                            <div class="d-flex gap-2">
                                                <% for (let t in thesaurus.tags) { %>
                                                    <% const tag = thesaurus.tags[t] %>
                                                    <% if (tag.name && tag.name.trim()) { %>
                                                    <%
                                                    const tagIdParts = tag.id ? tag.id.split('_') : ['', ''];
                                                    const tagId0 = tagIdParts[0] || '';
                                                    const tagId1 = tagIdParts[1] || '';
                                                    const thesaurusName = thesaurus.thesaurus || '';
                                                    %>
                                                    <span class="tag-value tag <%= root === 'corpus' ? 'tag-corpus' : 'tag-pft3d' %> p-1" thesaurus="<%= thesaurus.thesaurus %>" status="<%= thesaurus.status %>" id="<%= tag.id %>">
                                                        <a href="/concept/<%= tag.name %>?rootF=<%= mainFolder['mainfolder']%>&type=<%= thesaurus.status %>&idThes=<%= tag.id.split('_')[1] %>&thesaurus=<%= thesaurus.thesaurus%>">
                                                            <%= tag.name %>
                                                        </a>
                                                        <% if (typeof rights !== 'undefined' && rights) { %>
                                                            <% if (thesaurus.status === 'pactols') { %>
                                                            &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>" onclick="delete_thespactols_item('<%- root %>', '<%- unico.id %>', 'unico', '<%- tagId0 %>', '<%- tagId1 %>', '<%- thesaurusName %>')">
                                                                <i class="far fa-trash-alt"></i>
                                                            </span>
                                                            <% } else if (thesaurus.status === 'geopactols') { %>
                                                                &nbsp;<span class="btn-hand secondary-icon-color" title="<%=__('delete')%>" onclick="delete_thespactolsgeo_item('<%- root %>', '<%- unico.id %>', 'unico', '<%- tagId0 %>')">
                                                                    <i class="far fa-trash-alt"></i>
                                                                </span>
                                                            <% } %>
                                                        <% } %>
                                                    </span>
                                                    <% } %>
                                                <% } %>
                                            </div>
                                        </li>
                                        <% } %>
                                    </ul>
                                </td>
                                <td>
                                    <time datetime="<%= unico.creation_date %>"><%= date.getDate()+'/'+(date.getMonth()+1)+'/'+date.getFullYear()+' '+date.getHours()+':'+date.getMinutes() %></time>
                                </td>
                                <td>
                                    <a href="<%-url%>" target="_blank" class="unico-link">
                                        <% if (unico.type === 'rect') { %>
                                        <img src="<%-url%>" class="unico-image">
                                        <% } else if (unico.type === 'poly') { %>
                                        <% let points = unico.polygon.split(' ') %>
                                        <% for (let i in points) { let e = points[i].split(','); e[0] = parseInt(e[0]) - unico.x; e[1] = parseInt(e[1]) - unico.y; points[i] = e.join(',') } %>
                                        <% points = points.join(' ') %>
                                        <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                                             class="unico-svg"
                                             viewBox="0 0 <%- unico.width %> <%- unico.height %>" id="unico-svg-<%- unico.id %>">
                                            <mask id="svgmask-<%- unico.id %>">
                                                <polygon fill="#ffffff" points="<%- points %>" />
                                            </mask>
                                            <image xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="<%- url %>"
                                                   width="<%- unico.width %>" height="<%- unico.height %>"
                                                   mask="url(#svgmask-<%- unico.id %>)" />
                                        </svg>
                                        <% } %>
                                    </a>
                                </td>
                                <td>
                                    <div class="actions-container">
                                    <% if ((user.id === unico.id_user) || (user.user_status === 'admin')) { %>
                                    <% const unicoId = unico.id || 0; %>
                                    <button type="button" class="btn btn-vitrine-secondary mr-1" unico-id="<%=unico.id%>" onclick="editUnico(<%- unicoId %>)">
                                        <i class="fas fa-edit secondary-icon-color"></i>
                                    </button>
                                    <button type="button" class="btn btn-vitrine-secondary erase-search" unico-id="<%=unico.id%>">
                                        <i class="fas fa-trash-alt secondary-icon-color"></i>
                                    </button>
                                    <% } %>
                                    </div>
                                </td>
                            </tr>
                            <% } %>
                        </tbody>
                    </table>

                    <script>
                        $('#unicos-table .erase-search').on('click', function () {
                            let element = $(this)

                            $('.alert').hide()
                            $.ajax({
                                url: '/deleteUnico/' + element.attr('unico-id')
                            }).done(function () {
                                $('.alert-success').show().delay(2000).fadeOut(1000)
                                element.parent().parent().remove()
                                $('th[scope="row"]').each(function (index) { // change les indices de la colomne de gauche
                                    $(this).text(index + 1)
                                })
                            }).fail(function (jqXHR, textStatus, errorThrown) {
                                console.log('erreur', jqXHR.status)
                                if (jqXHR.status === 401) {
                                    $('.alert-notconnected').show().delay(2000).fadeOut(1000)
                                } else if (jqXHR.status === 403) {
                                    $('.alert-forbidden').show().delay(2000).fadeOut(1000)
                                } else if (jqXHR.status === 404) {
                                    $('.alert-notfound').show().delay(2000).fadeOut(1000)
                                } else {
                                    $('.alert-error').show().delay(2000).fadeOut(1000)
                                }
                            })
                        })
                    </script>
                </div>
                <% } %>
                <% if (doi.doi) { %>
                <% if (hasDisplayableMetadata(metadata, model) || comments.length > 0 || doi.doi || unicos.length > 0) { %>
                <hr>
                <% } %>
                    <div class="mt-4">
                        <h5><strong><%=__('citation').charAt(0).toUpperCase() + __('citation').slice(1)%></strong></h5>
                        <p><%= doi_info['cit'] %>
                        (<%= doi_info['date_integration'].split('-')[0] %>). <%= doi_info['title']%>.<i>Archeovision</i>.
                        <a href="<%= doi_info['doi'] %>"><img src="/assets/images/indexDOI.jpg"
                                 class="doi-image">&nbsp;<%= doi_info['doi'] %></a>
                        </p>
                    </div>
                    <% } %>
                    <% if(hasDisplayableMetadata(metadata, model) || comments.length > 0 || doi.doi || unicos.length > 0) { %>
                    <hr>
                    <% } %>
                    <div class="mt-3">
                        <% if (locals.itemType === 'object') { %>
                            <p><i class="fas fa-cube"></i> <%= __('objectName')%>: <%= dataObject['name'] %></p>
                            <p><%= __('enterDate')%> <%=__('of2')%> <%=__('object2')%> <%=__('in') %> ArcheoGRID :
                            <%= dataObject['date_integration'] %></p>
                        <% } else { %>
                            <p><i class="fas fa-file"></i> <%= __('filename')%>: <%= image['filename'] %></p>
                            <p><%= __('enterDate')%> <%=__('of2')%> <%=__('file').toLowerCase()%> <%=__('in') %> ArcheoGRID:
                            <%= image['date_integration'] %></p>
                            <% if (size !== "") { %><p><%= __('filesize')%>: <%= size %></p><% } %>
                        <% } %>
                    </div>
                    <% if (metadata && Object.keys(metadata).length > 0) { %>
                        <% if (metadata['unico'] ) { %>
                        <div class="mt-3">
                            <h5>
                                <a href="/getSmallImage/<%= metadata['parent_unico'] %>/<%= root %>"
                                onclick="window.open(this.href, 'photo', 'status=no, resizable=yes, scrollbars=yes');return false;">
                                    Parent unico
                                </a>
                            </h5>
                        </div>
                        <% } %>
                    <% } %>
        </div>
    </div>
</div>

<div class="loader"></div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const columnResizer = document.getElementById('column-resizer');
        const leftCol = document.querySelector('.vitrine-leftcol');
        const rightCol = document.querySelector('.vitrine-details');
        let isResizing = false;
        let startX, startLeftWidth, startRightWidth;

        const saveColumnWidths = () => {
            const leftWidth = leftCol.style.width.replace('%', '');
            const rightWidth = rightCol.style.width.replace('%', '');
            localStorage.setItem('vitrine-leftcol-width', leftWidth);
            localStorage.setItem('vitrine-details-width', rightWidth);
        };

        const loadColumnWidths = () => {
            if (window.innerWidth > 768) {
                leftCol.style.width = '';
                rightCol.style.width = '';

                let savedLeftWidth = localStorage.getItem('vitrine-leftcol-width');
                let savedRightWidth = localStorage.getItem('vitrine-details-width');
                if (savedLeftWidth && savedRightWidth) {
                    savedLeftWidth = savedLeftWidth.toString().replace('%', '');
                    savedRightWidth = savedRightWidth.toString().replace('%', '');
                    leftCol.style.cssText = `width: ${savedLeftWidth}% ;`;
                    rightCol.style.cssText = `width: ${savedRightWidth}% ;`;
                } else {
                    leftCol.style.cssText = 'width: 60% ;';
                    rightCol.style.cssText = 'width: 40% ;';
                }
            } else {
                leftCol.style.cssText = '';
                rightCol.style.cssText = '';
            }
        };

        loadColumnWidths();

        window.addEventListener('load', () => {
            setTimeout(() => {
                leftCol.style.transition = 'width 0.1s ease';
                rightCol.style.transition = 'width 0.1s ease';
            }, 100);
        });
        columnResizer.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;
            startLeftWidth = leftCol.offsetWidth;
            startRightWidth = rightCol.offsetWidth;
            columnResizer.classList.add('active');
            document.body.style.userSelect = 'none';
            document.body.style.cursor = 'col-resize';
        });
        document.addEventListener('mousemove', (e) => {
            if (!isResizing || window.innerWidth <= 768) return;
            const containerWidth = leftCol.parentElement.offsetWidth;
            const dx = e.clientX - startX;
            const newLeftWidth = ((startLeftWidth + dx) / containerWidth * 100);
            const newRightWidth = ((startRightWidth - dx) / containerWidth * 100);
            if (newLeftWidth >= 20 && newRightWidth >= 20) {
                leftCol.style.cssText = `width: ${newLeftWidth}% ;`;
                rightCol.style.cssText = `width: ${newRightWidth}% ;`;
            }
        });
        document.addEventListener('mouseup', () => {
            if (isResizing) {
                isResizing = false;
                columnResizer.classList.remove('active');
                document.body.style.userSelect = '';
                document.body.style.cursor = '';
                if (window.innerWidth > 768) {
                    saveColumnWidths();
                }
            }
        });
        columnResizer.addEventListener('touchstart', (e) => {
            if (e.touches.length === 1) {
                isResizing = true;
                startX = e.touches[0].clientX;
                startLeftWidth = leftCol.offsetWidth;
                startRightWidth = rightCol.offsetWidth;
                columnResizer.classList.add('active');
            }
        });
        document.addEventListener('touchmove', (e) => {
            if (!isResizing || e.touches.length !== 1 || window.innerWidth <= 768) return;
            const containerWidth = leftCol.parentElement.offsetWidth;
            const dx = e.touches[0].clientX - startX;
            const newLeftWidth = ((startLeftWidth + dx) / containerWidth * 100);
            const newRightWidth = ((startRightWidth - dx) / containerWidth * 100);
            if (newLeftWidth >= 20 && newRightWidth >= 20) {
                leftCol.style.cssText = `width: ${newLeftWidth}% ;`;
                rightCol.style.cssText = `width: ${newRightWidth}% ;`;
            }
        });
        document.addEventListener('touchend', () => {
            if (isResizing) {
                isResizing = false;
                columnResizer.classList.remove('active');
                if (window.innerWidth > 768) {
                    saveColumnWidths();
                }
            }
        });
        const objectsCollapseBtn = document.querySelector('[data-bs-target=".objects-collapse"]');
        if (objectsCollapseBtn) {
            const objectsCollapseIcon = objectsCollapseBtn.querySelector('i');
            const expandableObjects = document.querySelectorAll('.objects-collapse');
            
            if (expandableObjects.length > 0) {
                // Listen to the first item for collapse events
                expandableObjects[0].addEventListener('show.bs.collapse', function() {
                    objectsCollapseIcon.style.transform = 'rotate(0deg)';
                });
                expandableObjects[0].addEventListener('hide.bs.collapse', function() {
                    objectsCollapseIcon.style.transform = 'rotate(0deg)';
                });
            }
        }

        const filesCollapseBtn = document.querySelector('[data-bs-target=".files-collapse"]');
        if (filesCollapseBtn) {
            const filesCollapseIcon = filesCollapseBtn.querySelector('i');
            const expandableFiles = document.querySelectorAll('.files-collapse');

            if (expandableFiles.length > 0) {
                // Listen to the first item for collapse events
                expandableFiles[0].addEventListener('show.bs.collapse', function() {
                    filesCollapseIcon.style.transform = 'rotate(0deg)';
                });
                expandableFiles[0].addEventListener('hide.bs.collapse', function() {
                    filesCollapseIcon.style.transform = 'rotate(0deg)';
                });
            }
        }

        const toggleModelBtn = document.getElementById('toggleModelBtn');
        const model3dFrame = document.getElementById('model3dFrame');
        const imageContainer = document.getElementById('imageContainer');
        const modelContainer = document.getElementById('modelContainer');

        if (toggleModelBtn && model3dFrame) {
            toggleModelBtn.addEventListener('click', function() {
                const viewerUrl = model3dFrame.getAttribute('data-src');
                window.open(viewerUrl, '_blank');
            });
        }
        function checkMobileLayout() {
            const isMobile = window.innerWidth <= 768;
        }
        checkMobileLayout();
        window.addEventListener('resize', checkMobileLayout);
        window.addEventListener('resize', loadColumnWidths);
    });
</script>

<script>
    let global_fullscreen_currentOriginalElement = null;
    let global_fullscreen_touchstartX = 0;
    let global_fullscreen_touchstartY = 0;
    let global_fullscreen_isSwiping = false;
    let global_fullscreen_currentX = 0;
    let global_fullscreen_currentY = 0;

    function global_showFullscreen(imgElement) {
        global_fullscreen_currentOriginalElement = imgElement;
        let overlay = document.getElementById('fullscreen-image-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'fullscreen-image-overlay';
            document.body.appendChild(overlay);

            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    global_closeFullscreen();
                }
            });
        }

        let hhddSrc = imgElement.src;
        const currentSrc = imgElement.src.toString();

        try {
            const currentUrl = new URL(currentSrc, window.location.origin);
            const currentPathname = currentUrl.pathname;

            if (currentPathname.startsWith('/thumb/')) {
                hhddSrc = currentPathname.replace('/thumb/', '/hhdd/');
            } else if (currentPathname.startsWith('/media/image')) {
                const fid = currentUrl.searchParams.get('fid');
                const id = currentUrl.searchParams.get('id');
                if (fid && id) {
                    hhddSrc = `/hhdd/${fid}_${id}`;
                }
            }
        } catch (e) {
            console.error("Error processing image URL for hhdd version:", e, "Current src:", currentSrc);
        }

        const clonedImage = imgElement.cloneNode(true);
        clonedImage.id = 'fullscreened-image-content';
        clonedImage.style.cssText = '';
        clonedImage.src = hhddSrc;

        while (overlay.firstChild) {
            overlay.removeChild(overlay.firstChild);
        }
        overlay.appendChild(clonedImage);
        
        overlay.classList.remove('active');
        void overlay.offsetHeight;
        overlay.classList.add('active');

        document.body.style.overflow = 'hidden';

        overlay.addEventListener('touchstart', global_handleTouchStart, { passive: false });
        overlay.addEventListener('touchmove', global_handleTouchMove, { passive: false });
        overlay.addEventListener('touchend', global_handleTouchEnd, { passive: false });
    }

    function global_closeFullscreen() {
        const overlay = document.getElementById('fullscreen-image-overlay');
        if (overlay && overlay.classList.contains('active')) {
            overlay.classList.remove('active');
            document.body.style.overflow = '';

            global_fullscreen_isSwiping = false;
            global_fullscreen_currentX = 0;
            global_fullscreen_currentY = 0;

            setTimeout(() => {
                const currentOverlay = document.getElementById('fullscreen-image-overlay');
                if (currentOverlay && !currentOverlay.classList.contains('active') && currentOverlay.firstChild && currentOverlay.firstChild.id === 'fullscreened-image-content') {
                    currentOverlay.removeChild(currentOverlay.firstChild);
                }
            }, 300);

            overlay.removeEventListener('touchstart', global_handleTouchStart);
            overlay.removeEventListener('touchmove', global_handleTouchMove);
            overlay.removeEventListener('touchend', global_handleTouchEnd);
            global_fullscreen_currentOriginalElement = null;
        }
    }

    function global_handleTouchStart(event) {
        if (event.touches.length === 1) {
            global_fullscreen_touchstartX = event.touches[0].clientX;
            global_fullscreen_touchstartY = event.touches[0].clientY;
            global_fullscreen_isSwiping = true;
            const img = document.getElementById('fullscreened-image-content');
            const overlay = document.getElementById('fullscreen-image-overlay');
            if (img) {
                img.style.transition = 'none';
            }
            if (overlay) {
                overlay.style.transition = 'none';
            }
        }
    }

    function global_handleTouchMove(event) {
        if (!global_fullscreen_isSwiping || event.touches.length !== 1) return;
        event.preventDefault();

        const touchX = event.touches[0].clientX;
        const touchY = event.touches[0].clientY;

        global_fullscreen_currentX = touchX - global_fullscreen_touchstartX;
        global_fullscreen_currentY = touchY - global_fullscreen_touchstartY;

        const img = document.getElementById('fullscreened-image-content');
        const overlay = document.getElementById('fullscreen-image-overlay');

        if (img) {
            img.style.transform = `translate(${global_fullscreen_currentX}px, ${global_fullscreen_currentY}px) scale(1)`;
        }

        if (overlay) {
            const swipeDistance = Math.sqrt(global_fullscreen_currentX**2 + global_fullscreen_currentY**2);
            const maxSwipeDistance = window.innerHeight / 2;
            let opacity = 1 - Math.min(swipeDistance / maxSwipeDistance, 1) * 0.7;
            overlay.style.backgroundColor = `rgba(0, 0, 0, ${0.9 * opacity})`;
        }
    }

    function global_handleTouchEnd(event) {
        if (!global_fullscreen_isSwiping) return;
        global_fullscreen_isSwiping = false;

        const img = document.getElementById('fullscreened-image-content');
        const overlay = document.getElementById('fullscreen-image-overlay');
        const swipeThreshold = 150;
        const absDeltaX = Math.abs(global_fullscreen_currentX);
        const absDeltaY = Math.abs(global_fullscreen_currentY);

        if (absDeltaX > swipeThreshold || absDeltaY > swipeThreshold) {
            if (img) {
                const endX = global_fullscreen_currentX * 3;
                const endY = global_fullscreen_currentY * 3;
                img.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
                img.style.transform = `translate(${endX}px, ${endY}px) scale(0.8)`;
                img.style.opacity = '0';
            }
            global_closeFullscreen();
        } else {
            if (img) {
                img.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)';
                img.style.transform = 'translate(0px, 0px) scale(1)';
            }
            if (overlay) {
                overlay.style.transition = 'background-color 0.3s ease-in-out';
                overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
            }
        }
    }

    function global_handleSwipeGesture(touchendX, touchendY) {
        const swipeThreshold = 50;
        const deltaX = touchendX - global_fullscreen_touchstartX;
        const deltaY = touchendY - global_fullscreen_touchstartY;

        if (Math.abs(deltaX) > swipeThreshold || Math.abs(deltaY) > swipeThreshold) {
            global_closeFullscreen();
        }
    }
</script>

<script src="/assets/js/nested-scroll-fix.js"></script>
