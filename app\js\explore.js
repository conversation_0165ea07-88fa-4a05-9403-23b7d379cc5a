// Default values
if (typeof display === 'undefined') {
  var display = "grid";
}
if (typeof nbRes === 'undefined') {
  var nbRes = 0;
}

if (typeof explorePage === 'undefined') {
  var explorePage = {
    url: null,
    id: 0,
    search: {},
    thesaurus: "",
    thesId: 0,
    thesPath: "",
  };
}

// Function to save display preferences to localStorage
function saveDisplayPreferences() {
  try {
    const preferences = {
      display: display,
    };
    localStorage.setItem("exploreDisplayPreferences", JSON.stringify(preferences));
  } catch (e) {
    console.error("Failed to save display preferences:", e);
  }
}

// Function to load display preferences from localStorage
function loadDisplayPreferences() {
  try {
    const savedPreferences = localStorage.getItem("exploreDisplayPreferences");
    if (savedPreferences) {
      const preferences = JSON.parse(savedPreferences);
      display = preferences.display || display;
      return true;
    }
  } catch (e) {
    console.error("Failed to load display preferences:", e);
  }
  return false;
}

// Helper function to safely access i18n properties
function getI18nValue(key, defaultValue) {
  if (typeof i18n !== 'undefined' && i18n) {
    if(typeof i18n[key] !== 'undefined'){
      return i18n[key];
    }else{
      console.warn(`[explore.js] i18n key "${key}" not found, using default: "${defaultValue}"`);
    }
  }else{
    console.warn(`[explore.js] i18n not found, using default: "${defaultValue}"`);
  }
  return defaultValue;
}

$(document).ready(function() {
  const preferencesLoaded = loadDisplayPreferences();

  if (!preferencesLoaded) {
    display = "grid";
  }

  if (display === "grid") {
    $("#grid-btn").removeClass("btn-hand").addClass("btn-active");
    $("#list-btn").removeClass("btn-active").addClass("btn-hand");
  } else {
    $("#list-btn").removeClass("btn-hand").addClass("btn-active");
    $("#grid-btn").removeClass("btn-active").addClass("btn-hand");
  }

  // Directly call the function to update slider visibility
  if (typeof updateSliderVisibility === 'function') {
    updateSliderVisibility();
  }
});

function exploreUnicosUrl(newPage) {
  return `/exploreUnicosPage/${explorePage.id},${newPage}?display=${display}&limit=0`;
}

function exploreFolderUrl(newPage) {
  return `/exploreFolderPage/${explorePage.id},${newPage}?display=${display}&limit=0`;
}

function exploreFolderVitrineUrl(newPage) {
  return `/exploreFolderVitrinePage/${explorePage.id},${newPage}?display=${display}&limit=0`;
}

function exploreSelectionUrl(newPage) {
  return `/exploreSelectionPage/${explorePage.id},${newPage}?display=${display}&limit=0`;
}

function exploreSelectionVitrineUrl(newPage) {
  return `/exploreSelectionVitrinePage/${explorePage.id},${newPage}?display=${display}&limit=0`;
}

function exploreSearchUrl(newPage) {
  return `/exploreSearchPage/${explorePage.id},${newPage}?display=${display}&search=${explorePage.search}&limit=0`;
}

function exploreThesaurusUrl(newPage) {
  return `/exploreThesaurusPage/${explorePage.thesaurus},${explorePage.thesId},${newPage}?display=${display}&limit=0&thes_path=${explorePage.thesPath}&projectId=${explorePage.id}&type=${explorePage.type}`;
}

function exploreThesaurusPactolsUrl(newPage) {
  return `/exploreThesaurusPactolsPage/${explorePage.thesaurus},${explorePage.thesId},${newPage}?display=${display}&limit=0&thes_path=${explorePage.thesPath}&projectId=${explorePage.id}`;
}

function exploreThesaurusPactolsGeoUrl(newPage) {
  return `/exploreThesaurusPactolsGeoPage/${explorePage.thesId},${newPage}?display=${display}&limit=0&projectId=${explorePage.id}`;
}

function checkAllFromStorage() {
  const storage = localStorage.getItem("selection") || "[]";
  const parsedStorage = JSON.parse(storage);
  const countElement = document.getElementById("nb-selected");

  if (!Array.isArray(parsedStorage) || !countElement) {
    return;
  }

  let foundElements = 0;
  let checkedElements = 0;

  for (const id of parsedStorage) {
    const _index = id.lastIndexOf("_");
    const item_id = `item-${id.charAt(0)}-${id.substring(1, _index)}_${id.substring(_index + 1)}`;

    const htmlElement = document.getElementById(item_id);
    if (htmlElement) {
      foundElements++;
      htmlElement.classList.add("checked");

      const checkbox = htmlElement.querySelector(".form-check-input");
      if (checkbox && !checkbox.checked) {
        checkbox.checked = true;
        checkedElements++;
      }
    }
  }

  const uniqueCount = new Set(parsedStorage).size;
  countElement.innerText = uniqueCount.toString();
}

function exploreNb(url) {
  totalPage = 0;
  currentPage = 1;
  $.ajax({
    url: url,
    method: "GET",
    dataType: "json",
    success: function (data) {
      explorePage.title = data.name;
      $("#explore-title").html(explorePage.title);
      if (data.nbRes && data.nbRes === -1) {
        // cas explore folder random sur un projet
        $("#explore-results").html(
          '<div class="alert alert-info" role="alert">' + getI18nValue('exploreRandomNoResult', 'No results found for random exploration.') + "</div>",
        );
        $("#pagination-explore").empty();
        $('#nb-results').text('');
      } else if (data.nbRes === 0 || data.nbAccess === 0) {
        $("#explore-results").html(
          '<div class="alert alert-info" role="alert">' + getI18nValue('noResult', 'No result found.') + "</div>",
        );
        $("#pagination-explore").empty();
        const zeroResultsText = getI18nValue('noResult', 'No results found');
        $('#nb-results').text(zeroResultsText);
      } else {
        explorePage.nbRes = data.nbAccess || data.nbRes;
        
        const resultsText = `${explorePage.nbRes} ${getI18nValue('nbResultsText', 'results found')}`;
        $('#nb-results').text(resultsText);

        exploreLoadPage(1);
      }
    },
    error: function (xhr, status, error) {
      console.error('[explore.js] exploreNb AJAX error:', { status: status, error: error, xhr: xhr });
      $("#explore-results").html(
        '<div class="alert alert-danger" role="alert">' +
          getI18nValue('errorOccured', 'An error occurred') +
          ": " +
          error +
          "</div>",
      );
      $("#pagination-explore").empty();
    },
  });
}

function exploreUnicos(id) {
  if (!id || ($("#explore-div").is(":visible") && explorePage.id === id && explorePage.url === exploreUnicosUrl)) {
    return;
  }

  cleanupPreviousContent();

  history.replaceState({}, "", window.location.origin + window.location.pathname);

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").removeAttr('style').removeClass('d-none hidden').css("display", "flex");
  $("#menuCentre").removeAttr('style').removeClass('d-none hidden').hide();
  $("#tabloid").removeAttr('style').removeClass('d-none hidden').hide();

  explorePage = {
    url: exploreUnicosUrl,
    id: id,
  };

  exploreNb(`/exploreUnicosNb/${id}`);
}

function exploreFolder(id) {
  if (!id || (explorePage.id === id && explorePage.url === exploreFolderUrl)) {
    return;
  }

  cleanupPreviousContent();

  if ($(`#${explorePage.id}`).next().is(".far")) {
    $(`#${explorePage.id}`).prop("checked", false);
  }

  history.replaceState({}, "", window.location.origin + window.location.pathname + "?folder=" + id);

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").css("display", "flex");
  $("#menuCentre").hide();

  explorePage = {
    url: exploreFolderUrl,
    id: id,
  };

  exploreNb(`/exploreFolderNb/${id}`);
}

function exploreFolderVitrine(id) {
  if (!id) {
    const exploreResults = $("#explore-results");
    const hasSpinner = exploreResults.find(".spinner-border").length > 0;
    const hasAlerts = exploreResults.find(".alert").length > 0;
    const hasContentElements = exploreResults.find(".card, .result-item, .vitrine-item, .explore-item, [data-id]").length > 0;
    
    const hasResults = hasSpinner || hasContentElements || (hasAlerts && exploreResults.find(".alert-info").length > 0);
    
    if (!hasResults) {
      $("#explore-div").hide();
      $("#menuCentre").show();
    }

    const isSelectionView = window.location.href.includes('exploreSelection') ||
                           window.location.href.includes('selection') ||
                           window.location.pathname.includes('/selectionV/');
    if (!isSelectionView) {
      history.replaceState({}, "", window.location.origin + window.location.pathname);
    }

    return;
  }

  if (explorePage.id === id && explorePage.url === exploreFolderVitrineUrl) {
    return;
  }

  cleanupPreviousContent();

  if ($(`#${explorePage.id}`).next().is(".far")) {
    $(`#${explorePage.id}`).prop("checked", false);
  }

  const isSelectionView = window.location.href.includes('exploreSelection') ||
                         window.location.href.includes('selection') ||
                         window.location.pathname.includes('/selectionV/');

  if (!isSelectionView) {
    history.replaceState({}, "", window.location.origin + window.location.pathname + "?folder=" + id);
  }

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").css("display", "flex");
  $("#menuCentre").hide();

  explorePage = {
    url: exploreFolderVitrineUrl,
    id: id,
  };

  exploreNb(`/exploreFolderNb/${id}`);
}
function exploreSelection(id) {
  if (!id) {
    return;
  }

  cleanupPreviousContent();

  if (!window.location.pathname.includes('/selectionV/')) {
    history.replaceState({}, "", window.location.origin + window.location.pathname);
  }

  // Check if we're in any vitrine context (thesaurusV, selectionV, projectV, etc.)
  const isVitrineContext = window.location.pathname.includes('/selectionV/') ||
                          window.location.pathname.includes('/thesaurusV/') ||
                          window.location.pathname.includes('Vitrine');

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").css("display", "flex");
  $("#menuCentre").hide();

  explorePage = {
    url: isVitrineContext ? exploreSelectionVitrineUrl : exploreSelectionUrl,
    id: id,
  };

  exploreNb(`/exploreSelectionNb/${id}`);
}

function exploreThesaurus(projectId, thesaurus, idThes, path, type) {
  cleanupPreviousContent();

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").css("display", "flex");
  $("#menuCentre").hide();

  explorePage = {
    url: exploreThesaurusUrl,
    id: projectId,
    thesaurus: thesaurus,
    thesId: idThes,
    thesPath: path,
    type: type,
  };

  exploreNb(`/exploreThesaurusNB/${thesaurus},${idThes}?thes_path=${path}&projectId=${projectId}&type=${type}`);
}

function exploreThesaurusPactols(projectId, thesaurus, idThes, path) {
  cleanupPreviousContent();

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").css("display", "flex");
  $("#menuCentre").hide();

  explorePage = {
    url: exploreThesaurusPactolsUrl,
    id: projectId,
    thesaurus: thesaurus,
    thesId: idThes,
    thesPath: path,
  };

  exploreNb(`/exploreThesaurusPactolsNB/${thesaurus},${idThes}?thes_path=${path}&projectId=${projectId}`);
}

function exploreThesaurusPactolsGeo(projectId, idThes) {
  cleanupPreviousContent();

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").css("display", "flex");
  $("#menuCentre").hide();

  explorePage = {
    url: exploreThesaurusPactolsGeoUrl,
    id: projectId,
    thesId: idThes,
  };

  exploreNb(`/exploreThesaurusPactolsGeoNB/${idThes}?projectId=${projectId}`);
}

function exploreSearch(id, searchParameters) {
  cleanupPreviousContent();

  $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
  $("#explore-div").css("display", "flex");
  $("#menuCentre").hide();

  explorePage = {
    url: exploreSearchUrl,
    id: id,
    search: searchParameters,
  };

  exploreNb(`/exploreSearchNb/${explorePage.id}?search=${explorePage.search}`);
}

function exploreLoadPage(page) {
  currentPage = page;
  const url = explorePage.url(page);

  $("#explore-results").html(
    '<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>',
  );

  $.ajax({
    url: url,
    method: "GET",
    dataType: "html",
    success: function (data) {
      if (!data || data.trim() === "") {
        console.warn('[explore.js] exploreLoadPage: Received empty HTML data.');
        $("#explore-results").html(
          '<div class="alert alert-warning" role="alert">' + getI18nValue('emptyResponse', 'Received an empty response from the server.') + '</div>',
        );
      } else {
        $("#explore-results").html(data);
        if (typeof initLazyLoading === 'function') {
          initLazyLoading();
        }
      }
      if (typeof updatePagination === 'function') {
        updatePagination();
      }
    },
    error: function (xhr, status, error) {
      console.error('[explore.js] exploreLoadPage AJAX error:', { status: status, error: error, xhr: xhr });
      $("#explore-results").html(
        '<div class="alert alert-danger" role="alert">' +
          getI18nValue('errorOccured', 'An error occurred') +
          ": " +
          error +
          "</div>",
      );
      if (typeof updatePagination === 'function') {
        updatePagination();
      }
    },
  });
}

function cleanupPreviousContent() {
  $(".modal").remove();
  $("#explore-results").find("*").off();
  $("#explore-results").empty();
  if (window.CollectGarbage) {
    window.CollectGarbage();
  }
}

function switchDisplay(newDisplay) {
  if (newDisplay === display) {
    return;
  }

  display = newDisplay;

  if (display === "grid") {
    $("#grid-btn").removeClass("btn-hand").addClass("btn-active");
    $("#list-btn").removeClass("btn-active").addClass("btn-hand");
  } else {
    $("#list-btn").removeClass("btn-hand").addClass("btn-active");
    $("#grid-btn").removeClass("btn-active").addClass("btn-hand");
  }

  saveDisplayPreferences();

  // Directly call the function to update slider visibility
  if (typeof updateSliderVisibility === 'function') {
    updateSliderVisibility();
  }

  // Preserve the current page instead of always going to page 1
  exploreLoadPage(currentPage || 1);
}


async function duplicateItem(branch, type, id){
    $.ajax({
      url: `/duplicateItem,${branch},${type},${id}`,
      type: "GET"
    }).then(
      result => {
        console.log(result);
        loadPage(page);
      },
      error => {
        console.error(error);
      }
    );
}
