<%# Simple tree-like folder view for vitrine pages - Folders Section Only %>
<%
// Helper function to check if a folder is selected
function isFolderSelected(folderId) {
  return folderId == selectedFolder;
}

// Helper function to calculate the immediate file count for a folder
function calculateImmediateFolderCount(folder, isVirtual) {
  if (isVirtual) {
    const nbImages = folder.nb_images || 0;
    const nbObjects = folder.nb_objects || 0;
    const nbUnicos = folder.nb_unicos || 0;
    return nbImages + nbObjects + nbUnicos;
  } else {
    return folder.nb_images || 0;
  }
}

// Helper function to calculate the total file count for a folder (including subtree)
function calculateTotalFolderCount(folder, isVirtual) {
  if (isVirtual) {
    if (typeof folder.nb_tot_items !== 'undefined' && folder.nb_tot_items !== null) {
      return folder.nb_tot_items;
    }
    else if (
      (typeof folder.nb_tot_images !== 'undefined' && folder.nb_tot_images !== null) ||
      (typeof folder.nb_tot_objects !== 'undefined' && folder.nb_tot_objects !== null) ||
      (typeof folder.nb_tot_unicos !== 'undefined' && folder.nb_tot_unicos !== null)
    ) {
      const nbTotImages = folder.nb_tot_images || 0;
      const nbTotObjects = folder.nb_tot_objects || 0;
      const nbTotUnicos = folder.nb_tot_unicos || 0;
      return nbTotImages + nbTotObjects + nbTotUnicos;
    }
    else {
      const nbImages = folder.nb_images || 0;
      const nbObjects = folder.nb_objects || 0;
      const nbUnicos = folder.nb_unicos || 0;
      return nbImages + nbObjects + nbUnicos;
    }
  } else {
    if (typeof folder.nb_tot_images !== 'undefined' && folder.nb_tot_images !== null) {
      return folder.nb_tot_images;
    }
    else {
      return folder.nb_images || 0;
    }
  }
}

function renderFolder(folder, level) {
  const hasAccess = user.read.indexOf(folder.id) !== -1 || user.user_status === 'admin';
  if (!hasAccess) return;

  const hasChildren = folder.children && folder.children.length > 0;
  const isSelected = isFolderSelected(folder.id);
  const isVirtual = folder.isVirtual || folder.virtual === 1 || folder.folder_name === null;
  %>
  <li class="<%- isSelected ? 'folder-selected' : '' %> <%- isVirtual ? 'virtual' : '' %>"
      folderId="<%- folder.id %>"
      rank="<%- folder.global_rank || '' %>"
      data-level="<%- level %>">
    <div class="d-flex align-items-center">
      <% if (hasChildren) { %>
        <span class="folder-parent"></span>
      <% } else { %>
        <span class="folder-spacer"></span>
      <% } %>
      <div class="folder-name-container">
        <span class="folder-name" title="<%- folder.name %>" data-full-name="<%- folder.name %>">
          <i class="fas fa-folder"></i> <%- folder.name %>
        </span>
        <span class="folder-number">
          <%
          const immediateCount = calculateImmediateFolderCount(folder, isVirtual);
          const totalCount = calculateTotalFolderCount(folder, isVirtual);
          %>
          <% if (immediateCount > 0) { %>
            [<%- immediateCount %>]
            <% if (totalCount !== immediateCount && totalCount > 0) { %>(<%- totalCount %>)<% } %>
          <% } else if (totalCount > 0) { %>
            (<%- totalCount %>)
          <% } %>
        </span>
      </div>
    </div>

    <% if (hasChildren) { %>
      <ul class="folder-children" style="display: none;">
        <% folder.children.forEach(child => {
          renderFolder(child, level + 1);
        }); %>
      </ul>
    <% } %>
  </li>
  <%
}

function renderFolders(folders, ulId) {
  %>
  <ul id="<%= ulId %>" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
    <%
    folders.forEach(folder => {
      const hasAccess = user.read.indexOf(folder.id) !== -1 || user.user_status === 'admin';
      if (hasAccess) {
        renderFolder(folder, 0);
      }
    });
    %>
  </ul>
  <%
}
%>

<div id="thesaurus-folders-container" class="thesaurus-folders-container">
  <% if (tree && tree.length > 0) { %>
    <%
    const processedFolders = new Set(); // Track folders we've already processed
    const normalRootFolders = [];
    const folderMap = {};

    // First pass: create folder objects and exclude virtual folders (we only want normal folders)
    tree.forEach(folder => {
      // Only include non-virtual folders for thesaurus display
      const isVirtual = folder.virtual === 1 || folder.folder_name === null;
      if (!isVirtual) {
        folderMap[folder.id] = {
          ...folder,
          children: [],
          isVirtual: false,
          virtual: 0
        };
      }
    });

    // Second pass: build parent-child relationships for normal folders only
    tree.forEach(folder => {
      if (processedFolders.has(folder.id)) return;
      const isVirtual = folder.virtual === 1 || folder.folder_name === null;
      if (isVirtual) return; // Skip virtual folders

      processedFolders.add(folder.id);

      const folderObj = folderMap[folder.id];
      if (!folderObj) return;

      if (!folder.id_parent) {
        normalRootFolders.push(folderObj);
      } else if (folderMap[folder.id_parent]) {
        folderMap[folder.id_parent].children.push(folderObj);
      } else {
        normalRootFolders.push(folderObj);
      }
    });
    %>

    <div class="thesaurus-section">
      <div class="thesaurus-tree-container">
        <%
        if (normalRootFolders.length > 0) {
          const allChildFolders = [];
          normalRootFolders.forEach(rootFolder => {
            if (rootFolder.children && rootFolder.children.length > 0) {
              allChildFolders.push(...rootFolder.children);
            }
          });

          if (allChildFolders.length > 0) {
            %>
            <ul id="thesaurus-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
              <%
              allChildFolders.forEach(folder => {
                const hasAccess = user.read.indexOf(folder.id) !== -1 || user.user_status === 'admin';
                if (hasAccess) {
                  renderFolder(folder, 0);
                }
              });
              %>
            </ul>
            <%
          } else {
            renderFolders(normalRootFolders, "thesaurus-folders-tree");
          }
        } else { %>
          <ul id="thesaurus-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
            <li><span>No thesaurus items available</span></li>
          </ul>
        <% } %>
      </div>
    </div>
  <% } else { %>
    <div class="thesaurus-section">
      <div class="thesaurus-tree-container">
        <ul id="thesaurus-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
          <li><span>No thesaurus items available</span></li>
        </ul>
      </div>
    </div>
  <% } %>
</div>

<%# Add data attributes to the folders container for JavaScript access %>
<script>
  $(document).ready(function() {
    $('#thesaurus-folders-container').attr({
      'data-user-status': '<%- user.user_status %>',
      'data-lng': '<%- lng %>',
      'data-user-write': '<%- JSON.stringify(user.write) %>'
    });

    // Initialize basic tree functionality for thesaurus mode
    if (typeof window.initFoldersTreeVitrine === 'function') {
      window.initFoldersTreeVitrine({
        projectId: '<%- typeof projectId !== "undefined" ? projectId : "" %>',
        lng: '<%- lng %>',
        selectedFolder: typeof selectedFolder !== 'undefined' ? selectedFolder : null,
        thesaurusMode: true
      });
    } else {
      console.error('[DEBUG] initFoldersTreeVitrine function not found!');
    }

    // Add click debugging
    $(document).on('click', '.folders-tree .folder-name', function(e) {
      const $li = $(this).closest('li');
      const folderId = $li.attr('folderId');
    });

    // Ensure all folder-children start collapsed (except initially expanded ones)
    $('#thesaurus-folders-tree .folder-children:not(.active)').removeClass('active').hide().css('display', 'none');
    $('#thesaurus-folders-tree .folder-parent:not(.folder-parent-down)').removeClass('folder-parent-down');
    $('.folder-children:not(.active)').removeClass('active').hide();
    $('.folder-parent:not(.folder-parent-down)').removeClass('folder-parent-down');
  });
</script>