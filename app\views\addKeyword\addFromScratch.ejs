<% for( let k = 0; k < thes.length; k++) { %>
    <form id="passport" action="" method="post" class="container px-4 mb-2">
        <div class="d-flex">
            <label for="<%= thes[k]['name']%>" class="form-label col-4">
                <% if (thes[k]['status'] === 'pactols') { %>Thesaurus <%= thes[k]['name']%> (<a
                    href="https://pactols.frantiq.fr/opentheso/" class="btn-outline-primary"
                    target="_blank">PACTOLS</a>)
                <% } else if (thes[k]['status'] === 'geopactols') { %>Thesaurus <%=__('places') %> (<a
                    href="https://pactols.frantiq.fr/opentheso/" class="btn-outline-primary"
                    target="_blank">PACTOLS</a>)
                <% } else if (thes[k]['status'] === 'thesaurus') { %>
                <%# C'est un thesaurus simple donc c'est une nomenclature ???%>
                Nomenclature <%= thes[k]['name']%><% } else { %><%= thes[k]['name']%><% } %>
                <% if (thes[k]['description'] != '') { %>
                <a href="#" data-bs-toggle="modal" title="<%= thes[k]['description'] %>" data-bs-target="#infoThes<%= thes[k]['thesaurus']%>">
                    <i class="fas fa-info-circle"></i>
                </a>
                <% } %>
            </label>
            <div class="input-group">
                <input type="hidden" value="<%= virtual %>" name="virtual" id="virtual" />
                <% if (thes[k]['status'] === 'multi') {%>
                <input class="form-control" placeholder="<%=__('beginEntertext') %>..." type="text"
                        name="0#openthesomulti_<%= thes[k]['thesaurus'] %>_value"
                        id="openthesomulti_<%= thes[k]['thesaurus'] %>_value">
                <input class="form-control" type="hidden" name="0#openthesomulti_<%= thes[k]['thesaurus'] %>_id"
                        id="openthesomulti_<%= thes[k]['thesaurus'] %>_id">
                <input class="btn btn-secondary btn-hand" value="<%=__('save') %>"
                        onclick="addkey('<%= thes[k]['status'] %>','<%= thes[k]['thesaurus'] %>','openthesomulti_<%= thes[k]['thesaurus'] %>_value');" />
                <% } else if (thes[k]['status'] === 'thesaurus') { %>
                <input class="form-control" placeholder="<%=__('beginEntertext') %>..." type="text"
                        name="0#openthesothesaurus_<%= thes[k]['thesaurus'] %>_value"
                        id="openthesothesaurus_<%= thes[k]['thesaurus'] %>_value">
                <input class="form-control" type="hidden" name="0#openthesothesaurus_<%= thes[k]['thesaurus'] %>_id"
                        id="openthesothesaurus_<%= thes[k]['thesaurus'] %>_id">
                <input class="btn btn-secondary btn-hand" value="<%=__('save') %>"
                        onclick="addkey('<%= thes[k]['status'] %>','<%= thes[k]['thesaurus'] %>','openthesothesaurus_<%= thes[k]['thesaurus'] %>_value');" />
                <% } else if (thes[k]['status'] === 'pactols') { %>
                <input class="form-control" placeholder="<%=__('beginEntertext') %>..." type="text"
                        name="0#opentheso_<%= thes[k]['thesaurus'] %>_value"
                        id="opentheso_<%= thes[k]['thesaurus'] %>_value">
                <input class="form-control" type="hidden" name="0#opentheso_<%= thes[k]['thesaurus'] %>_id"
                        id="opentheso_<%= thes[k]['thesaurus'] %>_id">
                <input class="btn btn-secondary btn-hand" value="<%=__('save') %>"
                        onclick="addkey('<%= thes[k]['status'] %>','<%= thes[k]['thesaurus'] %>','opentheso_<%= thes[k]['thesaurus'] %>_value');" />
                <% } else if (thes[k]['status'] === 'geopactols') { %>
                <input class="form-control" placeholder="<%=__('beginEntertext') %>..." type="text"
                        name="0#openthesogeopactols_<%= thes[k]['thesaurus'] %>_value"
                        id="openthesogeopactols_<%= thes[k]['thesaurus'] %>_value">
                <input class="form-control" type="hidden"
                        name="0#openthesogeopactols_<%= thes[k]['thesaurus'] %>_id"
                        id="openthesogeopactols_<%= thes[k]['thesaurus'] %>_id">
                <input class="btn btn-secondary btn-hand" value="<%=__('save') %>"
                        onclick="addkey('<%= thes[k]['status'] %>','<%= thes[k]['thesaurus'] %>','openthesogeopactols_<%= thes[k]['thesaurus'] %>_value');" />
                <% } %>
            </div>

        </div>
    </form>
<%  } %>