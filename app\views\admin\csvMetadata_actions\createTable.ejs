<style>
    #createTable-form {
        margin-top: 2rem;
    }

    .charVarContainer {
        display: none;
        margin-left: 1.2rem;
    }

    .charVarContainer.selected {
        display: block;
    }

    .csvColumn {
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
        height: 10vh;
        border: solid 1px #f0f0f0;
        border-radius: 5px;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }

    .csvColumn h5 {
        padding-left: 1.2rem;
        width: 15%;
    }

    .csvColumn .typeContainer {
        margin-left: 1.2rem;
    }

    .typeContainer label {
        margin-bottom: 0.2rem;
    }

    #auto-complete-projects {
        margin-bottom: 0.6rem;
    }

    #csvFileSelector {
        display: flex;
        flex-wrap: wrap;
    }

    #csvFileSelector div {
        padding: 0.5rem;
        border: 1px solid #f0f0f0;
        margin: 0.2rem;
        border-radius: 5px;
    }

    #csvFileSelector div.selected {
        background-color: #f0f0f0;
    }

    #csvFileSelector div:hover {
        background-color: #f0f0f0;
    }

    #csvFileSelector div input {
        margin-left: 0.5rem;
        float: none !important;
    }

    input[type="submit"] {
        margin-top: 1rem;
    }

    #btn-actions-container {
        display: flex;
        gap: 1rem;
    }

    .input-container {
        margin-bottom: 1.2rem;
    }

    #btn-ingestCSV, #btn-createModel, #btn-menuCSV {
        margin: 0px;
    }
    
    .col-md-7 {
        margin-bottom: 1rem;
    }
</style>
<% if(step < 3) { %>
    <div id="title-container" class="container">
        <h4><%=__('admin.content.csvMetadata.table.CreateTableTitle')%></h4>
        <h6><%=__('admin.content.csvMetadata.table.CreateTableDescription')%></h6>
    </div>
<% }else if(status){ %>
    <div id="title-container" class="container">
        <h4><%=__('admin.content.csvMetadata.table.CreateTableEndTitle')%></h4>
        <h6><%=__('admin.content.csvMetadata.table.CreateTableEndDescription')%></h6>
    </div>
<% } %>

<div id="createTable-form" class="container">
    <% switch(step) { 
        case 1: %>
        <form method="post" class="step1 row g-3">
            <input type="hidden" name="step" value="1">
            <input type="hidden" name="action" value="createTable">
            <div class="col-md-7">
                <label for="branchSelector" class="form-label"><%= __('admin.content.BranchSelection') %></label>
                <select name="requestedBranch" id="branchSelector" size="3" class="form-select" required>
                    <% for(const branch of branches) { %>
                        <option value="<%= branch.branchename %>" <% if( branch.branchename === 'pft3d' ) { %> selected <% } %> ><%= branch.description %></option>
                    <% } %>    
                </select>

                
                <label for="projectSelector" class="form-label"><%= __('admin.content.ProjectSelection') %></label>
                <input type="text" class="form-control" id="auto-complete-projects" placeholder="<%= __('admin.content.SearchProject') %>" >
                <select name="project" size="15" class="form-select" id="projectSelector" required>
                    <% for(const project of listprojectByBranch[0].listproject) { %>
                        <option value="<%= project %>"><%= project %></option>
                    <% } %>  
                </select>
            </div>
            <div class="col-12">
                <input class="btn btn-primary" type="submit" value="<%=__('Next')%>">
            </div>
        </form>
        <% break;
        case 2: %>
        <% if(csvFiles.length > 0) { %>
            <form method="post" id="csvFileSelectorForm" class="needs-validation" novalidate>
                <input type="hidden" name="step" value="2">
                <input type="hidden" name="action" value="createTable">
                <input type="hidden" name="projectFolder" value="<%= projectFolder %>">
                <input type="hidden" name="requestedBranch" value="<%= requestedBranch %>">

                <div id="csvFileSelectorContainer" class="col-md-7 input-container">
                    <h5><%= __('admin.content.csvMetadata.ListOfCSV') %> <%= projectFolder %></h5> 
                    <fieldset id="csvFileSelector" class="form-group" required>
                        <% for(const [index, file] of csvFiles.entries()) { %>
                            <div class="form-check">
                                <label for="<%= file.name %>"><%= file.name %></label>
                                <input class="form-check-input" <% if( index === 0 ) { %> required <% } %> type="radio" name="csvFile" value="<%= file.name %>" id="<%= file.name %>" <% if( file.name === default_values.csv_file_name ) { %> checked <% } %>>
                            </div>
                        <% } %>
                    </fieldset>   
                </div>

                <div class="col-md-7 input-container">
                    <label for="delimiter" class="form-label"><%= __('Delimiter') %></label>
                    <br>
                    <i style="color: grey;"><%= __('admin.content.csvMetadata.UseTab') %></i>
                    <input type="checkbox" class="form-check-input" id="delimiterIsTab" name="delimiterIsTab" <% if( default_values.delimiterIsTab ) { %> checked <% } %>>
                    <input type="text" class="form-control" id="delimiter" name="delimiter" value="<%= default_values.csv_delimiter?.trim() ?? '#' %>" required <% if( default_values.delimiterIsTab ) { %> disabled <% } %>> 
                    <div class="invalid-feedback d-none" id="delimiterError">
                        <%= __('admin.content.csvMetadata.DelimiterError') %>
                    </div>
                </div>

                <div class="col-md-7 input-container">
                    <label for="tableName" class="form-label"><%= __('admin.content.csvMetadata.TableName') %></label>
                    <input type="text" class="form-control" id="tableName" name="tableName" value="<%= default_values.table_name  ?? '' %>" required>
                </div>

                <div class="input-container col-md-7" style="margin-bottom: 0.6rem;">
                    <label for="tableItemType"><%= __('admin.content.csvMetadata.TableItemType') %></label>
                    <select class="form-control" name="tableItemType" id="tableItemType">
                        <option value="file" <% if( default_values.tableItemType === 'file' ) { %> selected <% } %> ><%=__('File')%></option>
                        <option value="folder" <% if( default_values.tableItemType === 'folder' ) { %> selected <% } %> ><%=__('Folder')%></option>
                        <option value="object" <% if( default_values.tableItemType === 'object' ) { %> selected <% } %> ><%=__('Object')%></option>
                    </select>
                </div>

                <input class="btn btn-primary" type="submit" value="<%=__('Next')%>">
            </form>
        <% } else { %>
            <div class="alert alert-warning"><%= __('admin.content.csvMetadata.NoCSV') %> <%= __('for') %>:  <%= projectFolder %></div>
        <% } %>
        <% break;
        case 3: %>
            <% if(status){ %> 
                <div id="btn-actions-container">
                    <form action="/admin/csvMetadata" method="post">
                        <input type="hidden" name="step" value="1">
                        <input type="hidden" name="csv_metadata_id" value="<%= csv_metadata_id %>">    
                        <input type="hidden" name="action" value="createModel">
                        <input id="btn-createModel" class="btn btn-primary" type="submit" value="<%= __('admin.content.csvMetadata.CreateModel') %>">
                    </form>
                    <form action="/admin/csvMetadata" method="post">
                        <input type="hidden" name="step" value="0">
                        <input type="hidden" name="action" value="ingestCSV">
                        <input type="hidden" name="csv_metadata_id" value="<%= csv_metadata_id %>">    
                        <input id="btn-ingestCSV" class="btn btn-primary" type="submit" value="<%= __('admin.content.csvMetadata.IngestCSV') %>">
                    </form>
                    <form action="/admin/csvMetadata" method="get">    
                        <input id="btn-menuCSV" class="btn btn-secondary" type="submit" value="<%= __('admin.content.csvMetadata.Menu') %>">
                    </form>
                </div>
            <% } else { %>
                <div class="alert alert-danger"><%= bad %></div>
            <% } %>
        <% break;
    } %>
</div>

<% if(step === 1){ %>
    <script>
        const listprojectByBranch = JSON.parse('<%- JSON.stringify(listprojectByBranch) %>');

        document.addEventListener("DOMContentLoaded", () => {
            const branchSelector = document.getElementById("branchSelector");
            const projectSelector = document.getElementById("projectSelector");

            branchSelector.addEventListener("change", (e) => {
                const projects = listprojectByBranch.find(p => p.branch === branchSelector.value)?.listproject;

                projectSelector.innerHTML = '';
                for(const project of projects){
                    const option = document.createElement("option");
                    option.value = project;
                    option.text = project;
                    projectSelector.appendChild(option);
                }
            })

            const autoCompleteProjects = document.getElementById("auto-complete-projects");
            
            autoCompleteProjects.addEventListener('input', () => {
                const value = autoCompleteProjects.value;

                console.log(value);

                for(const option of projectSelector.options){
                    if(option.text.toLowerCase().includes(value.toLowerCase())){
                        option.hidden = false;
                    }else{
                        option.hidden = true;
                    }
                }
            });
        })
    </script>
<% } %>

<% if(step === 2){ %>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            const csvFileSelector = document.getElementById("csvFileSelector");
            csvFileSelector.addEventListener("change", (e) => {
                const selectedElement = csvFileSelector.querySelectorAll("input[type=radio]:checked");
                const selected = [...selectedElement].map(el => el.value);
                console.log(selected);
            });

            const tableName = document.getElementById("tableName");

            const inputRadio = document.querySelectorAll("input[type=radio]");
            for(const input of inputRadio){
                input.addEventListener("change", (e) => {
                    if(input.checked){
                        input.parentElement.classList.add("selected");

                        const tableNameValue = input.value.split(".")[0];
                        tableName.value = `<%= requestedBranch %>_${tableNameValue}`;
                    }

                    inputRadio.forEach(el => {
                        if(el !== input){
                            el.parentElement.classList.remove("selected");
                        }
                    })
                })
            }

            const delimiterError = document.getElementById("delimiterError");
            const inputDelimiter = document.getElementById("delimiter");
            const checkboxDelimiterTab = document.getElementById("delimiterIsTab");
            const form = document.getElementById("csvFileSelectorForm");
            form.addEventListener("submit", async (e) => {
                e.preventDefault();

                const dataForm = new FormData(form);

                let delimiterValid = true;
                if(dataForm.get('delimiter') === '' && !checkboxDelimiterTab.checked){
                    delimiterError.classList.remove("d-none");
                    inputDelimiter.classList.add("is-invalid");
                    checkboxDelimiterTab.classList.add("is-invalid");
                    delimiterValid = false;
                }else{
                    delimiterError.classList.add("d-none");
                    inputDelimiter.classList.remove("is-invalid");
                    checkboxDelimiterTab.classList.remove("is-invalid");
                }

                form.classList.add("was-validated");

                if(!delimiterValid || !form.checkValidity()){
                    return;
                }

                const tableName = dataForm.get('tableName');
                const url = new URL(`/admin/csvMetadata/tableExists,${tableName}`, window.location.origin);

                const result_exists = await fetch(url).then(res => res.json());

                if(result_exists.exists){
                    if(confirm(`<%= __('admin.content.csvMetadata.table.ConfirmUpdate') %> ${tableName}? <%= __('admin.content.csvMetadata.table.AllDataWillBeLost') %>.`)){
                        form.submit();
                    }
                    return;
                }else{
                    form.submit();
                }
            });

            var prevDel = '#';
            checkboxDelimiterTab.addEventListener("change", (e) => {
                if(checkboxDelimiterTab.checked){
                    inputDelimiter.disabled = true;
                    prevDel = inputDelimiter.value;
                    inputDelimiter.value = '';
                    inputDelimiter.classList.remove("is-invalid");
                }else{
                    inputDelimiter.disabled = false;
                    inputDelimiter.value = prevDel;
                }
            });
        })    
    </script>
<% } %>
