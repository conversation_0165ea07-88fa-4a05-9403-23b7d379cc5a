<style>
    #menuCentreAdmin {
        overflow-y: scroll;
    }

    #title-container {
        margin: 2rem auto;
    }

    .csvMetadata-feature-card{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 8px;
    }

    .csvMetadata-feature-card i {
        font-size: 2.2rem;
        margin-right: 1rem;
    }

    .csvMetadata-feature-card p {
        font-size: large;
    }
    
    .csvMetadata-feature-card#quickIngest{
        border: 2px solid rgba(var(--primary-color-rgb), 0.2);
        padding: 1.5rem 2.5rem;
    }

    .csvMetadata-feature-card#quickIngest p {
        flex: 1;
    }

    #quickIngest-container {
        display: grid;
        grid-template-columns: 2.5fr 1fr;
        gap: 0.5rem;
    }

    #quickIngest input[type=submit] {
        background-color: var(--primary-color);
        border: 2px solid var(--primary-color);
        padding: 0.5rem 1.5rem;
        font-size: 1.2rem;
    }

    #quickIngest input[type=submit]:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    #otherFeatures-intro {
        color: gray;
        margin: 1rem 0;
        padding-left: 0.5rem;
    }

    #otherFeatures-intro p {
        font-size: small;
    }

    #otherFeatures-container {
        display: grid;
        grid-template-columns: 1fr 1.5fr 1.5fr;
        gap: 0.5rem;
        max-height: 0px;
        overflow: hidden;
        opacity: 0;
        transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out 0.5s;
        margin-top: 1rem;
    }

    #otherFeatures-container.show {
        max-height: 200vh;
        opacity: 1;
    }

    .csvMetadata-feature-mini-card {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 8px;
        padding: 0.75rem 1.25rem;
        border: 3px solid lightgray;
    }

    .card-step {
        background-color: var(--light-color);
        color: var(--primary-color);
        padding: 0.2rem 0.5rem;
        border-radius: 5px;
        font-weight: bold;
        margin-bottom: 0.75rem;
    }

    .csvMetadata-feature-mini-card i {
        font-size: 1.5rem;
        margin-right: 0.75rem;
    }

    .csvMetadata-feature-mini-card p {
        color: gray;
        flex-grow: 1;
    }

    .csvMetadata-feature-mini-card form {
        display: flex;
        width: 100%;
        justify-content: center;
    }

    .csvMetadata-feature-mini-card input[type=submit] {
        background-color: gray;
        border-color: gray;
        padding: 0.2rem 0.6rem;
    }

    #table-csv-metadata-bigContainer {
        margin-top: 1.5rem;
    }

    #table-csv-metadata-container tr:hover td{
        cursor: initial !important;
        background-color: white !important;
    }

    #moreOptionButton {
        margin: 0.5rem auto;
        background-color: darkgrey;
        border-color: darkgrey;
    }

    #moreOptionButton:hover {
        background-color: gray;
        border: 1px solid gray;
        color: white;
    }

    .btn-moreinfo {
        padding: 0.5rem 1rem;
        background-color: var(--primary-color);
        border: 2px solid var(--primary-color);
    }

    .btn-moreinfo:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .btn-moreinfo i {
        font-size: 0.75rem;
        transform: rotate(90deg);
    }
</style>

<% if(bad){ %>
    <div class="alert alert-danger">
        <%= bad %>
    </div>
<% } %>

<div id="title-container" class="container">
    <h4><%=__('admin.content.csvMetadata.MetadataCSVTitle')%></h4>
    <h6><%=__('admin.content.csvMetadata.MetadataCSVDescription')%></h6>
</div>

<div id="csvMetadata" class="container">
    <div id="quickIngest-container">
        <div class="csvMetadata-feature-card" id="quickIngest">
            <div class="card-header">
                <h3><i class="fas fa-bolt"></i><%=__('admin.content.csvMetadata.quick.QuickIngestTitle')%></h3>
            </div>
            <p><%=__('admin.content.csvMetadata.quick.QuickIngestDescription')%></p>
            <form method="post" id="csvMetadata-quickIngest">
                <input type="hidden" name="action" value="quickIngest" />
                <input type="hidden" name="step" value="0">
                <input class='btn btn-secondary' type="submit" value="<%= __('admin.content.csvMetadata.StartQuickIngest') %>" />
            </form>
        </div>
        <div id="otherFeatures-intro">
            <h4><%= __('admin.content.csvMetadata.quick.OtherFeaturesTitle') %></h4>
            <p><%= __('admin.content.csvMetadata.quick.OtherFeaturesDescription') %></p>
            <div id="moreOptionButton" class="btn btn-primary" onclick="toggleOtherFeatures()"><%= __('admin.content.csvMetadata.moreOptionButton') %></div>
        </div>
    </div>
    <div id="otherFeatures-container">
        <div class="csvMetadata-feature-mini-card" id="createTable">
            <div class="card-step">
                <%= __('Step') %> 1 
            </div>
            <div class="card-header">
                <h5><i class="fas fa-database"></i><%=__('admin.content.csvMetadata.table.CreateTableTitle')%></h5>
            </div>
            <p><%=__('admin.content.csvMetadata.table.CreateTableDescription')%></p>
            <form method="post" id="csvMetadata-createTable">
                <input type="hidden" name="action" value="createTable" />
                <input type="hidden" name="step" value="0">
                <input class='btn btn-secondary' type="submit" value="<%= __('admin.content.csvMetadata.CreateTable') %>" />
            </form>
        </div>
        <div class="csvMetadata-feature-mini-card" id="createModel">
            <div class="card-step">
                <%= __('Step') %> 2 
            </div>
            <div class="card-header">
                <h5><i class="fas fa-plus"></i></i><%=__('admin.content.csvMetadata.model.CreateModelTitle')%></h5>
            </div>
            <p><%=__('admin.content.csvMetadata.model.CreateModelDescription')%></p>
            <form method="post" id="csvMetadata-createModel">
                <input type="hidden" name="action" value="createModel" />
                <input type="hidden" name="step" value="0">
                <input class='btn btn-secondary' type="submit" value="<%= __('admin.content.csvMetadata.CreateModel') %>" />
            </form>
        </div>
        <div class="csvMetadata-feature-mini-card" id="ingestCSV">
            <div class="card-step">
                <%= __('Step') %> 3 
            </div>
            <div class="card-header">
                <h5><i class="far fa-arrow-alt-circle-down"></i></i><%=__('admin.content.csvMetadata.ingest.IngestCSVTitle')%></h5>
            </div>
            <p><%=__('admin.content.csvMetadata.ingest.IngestCSVDescription')%></p>
            <form method="post" id="csvMetadata-ingestCSV">
                <input type="hidden" name="action" value="ingestCSV" />
                <input type="hidden" name="step" value="0">
                <input class='btn btn-secondary' type="submit" value="<%=__('admin.content.csvMetadata.IngestCSV')%>" />
            </form>
        </div>
    </div>

    <div id="table-csv-metadata-bigContainer">
        <h5><%= __('admin.content.csvMetadata.csvMetadataTableOverview') %></h5>
    
        <%- include('./csvMetadata_actions/csvMetadataTable', {csv_metadata: csv_metadata, branches: branches, mode: 'light'}) %>
    </div>
</div>    

<script>
    function toggleOtherFeatures() {
        const otherFeatures = document.getElementById('otherFeatures-container');
        otherFeatures.classList.toggle('show');
    }

    document.addEventListener('DOMContentLoaded', () => {
        const tableHeader = document.querySelector('#table-csv-metadata thead tr');
        const moreInfoButtonHeader = document.createElement('th');
        moreInfoButtonHeader.innerHTML = `<%= __('admin.content.csvMetadata.moreInfo') %>`;
        tableHeader.appendChild(moreInfoButtonHeader);

        const tableRows = Array.from(document.querySelectorAll('#table-csv-metadata tr')).slice(1);
        for(const row of tableRows){
            const moreInfoButton = document.createElement('td');
            moreInfoButton.style.textAlign = 'center';
            moreInfoButton.innerHTML = `
                <form method="post" action="/admin/csvMetadata">
                    <input type="hidden" name="action" value="ingestCSV">
                    <input type="hidden" name="step" value="0">
                    <input type="hidden" name="csv_metadata_id" value="${row.id.split('-').pop()}">
                    <div class="btn btn-primary btn-sm btn-moreinfo" onclick="this.parentNode.submit()">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </form>`;
            row.appendChild(moreInfoButton);
        }
    });
</script>
