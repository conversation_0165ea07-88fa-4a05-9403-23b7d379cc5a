import type { IDatabase } from "pg-promise";
import { z } from "zod";
import { t } from "~/trpcContext";
import type { Branch } from "~/types/api";
import { branch, id } from "~/types/schemas";

type Element = { start: string; where: string; type: string; id: string };

const postSearchOutput = z
  .object({
    id_item: id,
    item_type: z.string(),
    id_folder: id,
    name: z.string(),
    path: z.string().nullable(),
    extension: z.string().nullable(),
    idfile: id.nullish(),
    idfolder: id.nullish(),
    x: z.number(),
    y: z.number(),
    width: z.number(),
    height: z.number(),
    type: z.string(),
    polygon: z.string().nullable(),
  })
  .array();

// Helper functions for quoted/exact search
function isQuoted(term: string) {
  // Accept both literal and escaped quotes
  return typeof term === "string" && term.length > 1 &&
    ((term.startsWith('"') && term.endsWith('"')) ||
     (term.startsWith('\\"') && term.endsWith('\\"')));
}
function stripQuotes(term: string) {
  if (term.startsWith('\\"') && term.endsWith('\\"')) {
    return term.substring(2, term.length - 2);
  }
  return term.substring(1, term.length - 1);
}

function buildSearchPattern(term: string) {
  if (isQuoted(term)) {
    // Use (^|\W)term($|\W) for robust whole-word match
    const exact = stripQuotes(term).replace(/'/g, "''");
    return `(^|\\W)${exact}($|\\W)`;
  } else {
    // Default: substring match
    return String(term).replace(/'/g, "''");
  }
}

/**
 * Crée un string à partir d'une liste de strings et d'un séparateur inséré entre chaque élément
 * @param values Liste de strings
 * @param sep Séparateur
 * @returns
 */
function separateValues(values: string[], sep: string) {
  let res = "";
  let resfin = "";
  for (const [i, v] of values.entries()) {
    if (i > 0) res += sep;
    res += buildSearchPattern(v);
  }
  // si la chaine de caractère contient un single quote ' cela ne peut fonctionner avec un  ~* '....'
  // cela peut fonctionner avec un ~* '.......'
  resfin = res.replace("'", ".");
  return resfin;
}

/**
 * Crée le texte d'une requête de recherche faisant des jointures entre différentes requêtes
 * @param values Liste de requêtes
 * @param sep Séparateur (UNION, UNION ALL, INTERSECT, EXCEPT)
 * @returns
 */
function separateQueries(values: string[], sep: string) {
  let res = "";
  for (const [i, v] of values.entries()) {
    if (i > 0) res += `${sep} `;
    res += `( ${v}) `;
  }
  return res;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus multi ou sur un thesaurus multi en particulier si précisé
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param tags Liste de valeurs à rechercher
 * @param thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns
 */
function createThesaurusMultiQuery(branch: Branch, item: Element, tags: string[], thesaurus = "") {
  let join = `INNER JOIN ${branch}_thesaurus_multi_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}' `;
  if (thesaurus === "") {
    // champs multiples
    join +=
      `INNER JOIN ${branch}_thesaurus_multi t ON t.id = ti.id_thesaurus AND ` +
      `t.thesaurus_path <@ (SELECT ARRAY_AGG(thesaurus_path) FROM ${branch}_thesaurus_multi WHERE name ~* '${separateValues(
        tags,
        "|",
      )}') ` +
      ` AND t.thesaurus IN (SELECT thesaurus FROM  ${branch}_thesaurus_multi WHERE name ~* '${separateValues(
        tags,
        "|",
      )}') `;
  } else {
    // recherche thesaurus
    join +=
      `INNER JOIN ${branch}_thesaurus_multi t ON t.id_thes = ti.id_thes_thesaurus ` +
      `AND t.thesaurus = '${thesaurus}' AND t.thesaurus = ti.thesaurus AND t.thesaurus_path @ '${separateValues(
        tags,
        "|",
      )}' `;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus pactols ou sur un thesaurus pactols en particulier si précisé
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param tags Liste de valeurs à rechercher
 * @param thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns
 */
function createThesaurusPactolsQuery(branch: Branch, item: Element, tags: string[], thesaurus = "") {
  let join = `INNER JOIN ${branch}_thesaurus_pactols_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}' `;
  if (thesaurus === "") {
    // champs multiples
    join +=
      `INNER JOIN ${branch}_thesaurus_pactols t ON t.id = ti.id_thesaurus AND ` +
      `t.thesaurus_path <@ (SELECT ARRAY_AGG(thesaurus_path) FROM ${branch}_thesaurus_pactols WHERE name ~* '${separateValues(
        tags,
        "|",
      )}') ` +
      ` AND t.thesaurus IN (SELECT thesaurus FROM  ${branch}_thesaurus_pactols WHERE name ~* '${separateValues(
        tags,
        "|",
      )}') `;
  } else {
    // recherche thesaurus
    // TODO : ne pas jointer sur l'id absolu du thesaurus mais sur l'id interne d'un thesaurus ?
    join +=
      `INNER JOIN ${branch}_thesaurus_pactols t ON t.id_thes = ti.id_thes_thesaurus ` +
      `AND t.thesaurus_path @ '${separateValues(tags, "|")}' `;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus geo pactols ou sur un thesaurus pactols en particulier si précisé
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param tags Liste de valeurs à rechercher
 * @param thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns
 */
function createThesaurusPactolsGeoQuery(branch: Branch, item: Element, tags: string[], thesaurus = "") {
  let join = `INNER JOIN ${branch}_thesaurus_pactols_geo_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}' `;
  if (thesaurus === "") {
    // champs multiples
    join += `INNER JOIN ${branch}_thesaurus_pactols_geo t ON t.id = ti.id_thesaurus AND t.name ~* '${separateValues(
      tags,
      "|",
    )}' `;
  } else {
    // recherche thesaurus
    // TODO : ne pas jointer sur l'id absolu du thesaurus mais sur l'id interne d'un thesaurus ?
    join +=
      `INNER JOIN ${branch}_thesaurus_pactols_geo t ON t.id = ti.id_thesaurus ` +
      `AND t.id::text ~* '${separateValues(tags, "|")}' `;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus periodo ou sur un thesaurus periodo en particulier si précisé
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param tags Liste de valeurs à rechercher
 * @param thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns
 */
function createThesaurusPeriodoQuery(branch: Branch, item: Element, tags: string[], thesaurus = "") {
  let join = `INNER JOIN ${branch}_thesaurus_periodo_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}' `;
  if (thesaurus === "") {
    // champs multiples
    join += `INNER JOIN ${branch}_thesaurus_periodo t ON t.id = ti.id_periodo AND t.label ~* '${separateValues(
      tags,
      "|",
    )}' `;
  } else {
    // recherche thesaurus
    join += `INNER JOIN ${branch}_thesaurus_periodo t ON t.id = ti.id_periodo AND t.id_periodo ~* '${separateValues(
      tags,
      "|",
    )}' `;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des tags libres
 * @param branch Le nom de la branche (pft3d ou conservatoire3d ou corpus)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item?
 * @param tags Liste de valeurs à rechercher
 * @param thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns
 */
function createThesaurusTagQuery(branch: Branch, item: Element, tags: string[], thesaurus = "") {
  let join = `INNER JOIN ${branch}_item_tag ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}' `;
  if (thesaurus === "") {
    // champs multiples
    join += `INNER JOIN ${branch}_tag t ON t.id = ti.id_tag AND t.name ~* '${separateValues(
        tags,
        "|",
    )}' `;
  } else {
    // recherche thesaurus
    join += `INNER JOIN ${branch}_tag t ON t.id = ti.id_tag AND t.name ~* '${separateValues(
        tags,
        "|",
    )}' `;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur le nom des items
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param names Liste des noms à rechercher
 * @returns
 */
function createNameQuery(item: Element, names: string[]) {
  let where = item.where !== "" ? `${item.where} AND ` : "WHERE ";
  const patterns = names.map(name => {
    if (isQuoted(name)) {
      return `${item.id}.name ~* '${buildSearchPattern(name)}'`;
    } else {
      return `${item.id}.name ~* '${buildSearchPattern(name)}'`;
    }
  });
  where += patterns.join(" OR ");
  return item.start + where;
}

/**
 * Crée le texte d'une requête de recherche sur champs multiples en faisant l'UNION de plusieurs requêtes
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param tags Liste de valeurs à rechercher
 * @returns
 */
function createMultipleFieldsQuery(branch: Branch, item: Element, tags: string[]) {
  const queries = [];
  queries.push(createPassportQuery(branch, item, tags));
  queries.push(createThesaurusQuery(branch, item, tags));
  queries.push(createThesaurusMultiQuery(branch, item, tags));
  queries.push(createThesaurusPeriodoQuery(branch, item, tags));
  queries.push(createThesaurusPactolsQuery(branch, item, tags));
  queries.push(createThesaurusTagQuery(branch, item, tags));
  queries.push(createNameQuery(item, tags));

  return separateQueries(queries, "UNION");
}

/**
 * Crée le texte d'une requête de recherche sur la globalité du passport
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param tags Liste de valeurs à rechercher
 * @returns
 */
function createPassportQuery(branch: Branch, item: Element, tags: string[]) {
  const join = `INNER JOIN ${branch}_passport p ON p.id_item = ${item.id}.id AND p.item_type = '${item.type}' AND p.value::text ~* '${separateValues(tags, "|")}' `;
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur la globalité des thesaurus ou sur un thesaurus en particulier si précisé
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param tags Liste de valeurs à rechercher
 * @param thesaurus (Optionnel) Contient le nom du thesaurus
 * @returns
 */
function createThesaurusQuery(branch: Branch, item: Element, tags: string[], thesaurus = "") {
  let join = `INNER JOIN ${branch}_thesaurus_item ti ON ti.id_item = ${item.id}.id AND ti.item_type = '${item.type}' `;
  if (thesaurus === "") {
    // champs multiples
    join +=
      `INNER JOIN ${branch}_thesaurus t ON t.id = ti.id_thesaurus ` +
      `AND t.thesaurus_path <@ (SELECT ARRAY_AGG(thesaurus_path) FROM ${branch}_thesaurus WHERE name ~* '${separateValues(tags, "|")}') ` +
      ` AND t.thesaurus IN (SELECT thesaurus FROM  ${branch}_thesaurus WHERE name ~* '${separateValues(tags, "|")}') `;
  } else {
    // recherche thesaurus
    join +=
      `INNER JOIN ${branch}_thesaurus t ON t.id_thes = ti.id_thes_thesaurus ` +
      `AND t.thesaurus = '${thesaurus}' AND t.thesaurus = ti.thesaurus AND t.thesaurus_path @ '${separateValues(tags, "|")}' `;
  }
  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur des métadonnées du passport données
 * @param branch Le nom de la branche (pft3d ou conservatoire3d)
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param passport Contient l'identifiant de la métadonnée et les valeurs à rechercher
 * @returns
 */
function createMetadataQuery(branch: Branch, item: Element, passport: Record<string, string[]>) {
  let join = "";
  for (const p in passport) {
    const tags = passport[p];
    const alias = `p_${p}`;

    join +=
      `INNER JOIN ${branch}_passport ${alias} ON ${alias}.id_item = ${item.id}.id` +
      ` AND ${alias}.item_type = '${item.type}'` +
      ` AND ${alias}.id_metadata = ${p} AND ${alias}.value::text ~* '${separateValues(tags, "|")}' `;
  }

  return item.start + join + item.where;
}

function createAdvancedMetadataQuery(branch: Branch, item: Element, parameters: any) {
  let join = "";
  
  for(const a in parameters.actors) {
    join += `
      INNER JOIN ${branch}_actor_item ai_${a} ON ai_${a}.id_item = ${item.id}.id
      AND ai_${a}.item_type = '${item.type}'
      AND ai_${a}.id_metadata = ${a}
      INNER JOIN ${branch}_actor a_${a} ON a_${a}.id = ai_${a}.id_actor 
        AND a_${a}.actor_type IN ('${parameters.actors[a].types.join("', '")}') 
        AND a_${a}.name ~* '${separateValues(parameters.actors[a].names, "|")}'
        AND a_${a}.identifier ~* '${separateValues(parameters.actors[a].identifiers, "|")}' 
    `;
  }

  ///// TODO victor : ajouter la recherche pour les datations
  // type => ('exact' ::DATE = ::DATE, 'before' ::DATE < ::DATE, 'after' ::DATE > ::DATE, 'between' ::DATE BETWEEN ::DATE AND ::DATE)
  // date_min -> AAAA-MM-JJ
  // date_max -> AAAA-MM-JJ

  for(const d in parameters.datations) {

    let caseJoin = "";
    switch(parameters.datations[d].type) {
      case "exact":
        caseJoin = `AND d_${d}.date_min_formated = '${parameters.datations[d].min}'::DATE `;
        break;
      case "before":
        caseJoin = `AND d_${d}.date_min_formated < '${parameters.datations[d].max}'::DATE `;
        break;
      case "after":
        caseJoin = `AND d_${d}.date_max_formated > '${parameters.datations[d].min}'::DATE `;
        break;
      case "between":
        caseJoin = `AND CASE WHEN d_${d}.date_min_formated = d_${d}.date_max_formated 
          THEN d_${d}.date_min_formated BETWEEN '${parameters.datations[d].min}'::DATE AND '${parameters.datations[d].max}'::DATE 
          ELSE (d_${d}.date_min_formated >= '${parameters.datations[d].min}'::DATE AND d_${d}.date_min_formated <= '${parameters.datations[d].max}'::DATE) OR (d_${d}.date_max_formated <= '${parameters.datations[d].max}'::DATE AND d_${d}.date_max_formated >= '${parameters.datations[d].min}'::DATE) END `;
        break;
    }

    if(parameters.datations[d].literal) {

      caseJoin += `AND d_${d}.date_literal ~* '${buildSearchPattern(parameters.datations[d].literal)}'`;
    }

    join += `
      INNER JOIN ${branch}_datation_item di_${d} ON di_${d}.id_item = ${item.id}.id
      AND di_${d}.item_type = '${item.type}'
      AND di_${d}.id_metadata = ${d}
      INNER JOIN ${branch}_datation d_${d} ON d_${d}.id = di_${d}.id_datation 
      ${caseJoin} `
  }

  return item.start + join + item.where;
}

/**
 * Crée le texte d'une requête de recherche sur les extensions des fichiers
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param extensions Liste des extensions à rechercher
 * @returns
 */
function createExtensionsQuery(item: Element, extensions: string[]) {
  let where = item.where !== "" ? `${item.where} AND ` : "WHERE ";
  if (separateValues(extensions, "|").includes("files")) {
    where += "fi.file_ext ~* '.' ";
  } else {
    where += `fi.file_ext ~* '${separateValues(extensions, "|")}' `;
  }
  return item.start + where;
}

/**
 * Crée le texte d'une requête de recherche sur les fichiers dont l'utilisateur a accès
 * @param item Contient les informations sur le type d'item
 * @param item.start Texte des débuts de requêtes
 * @param item.where Texte de la commande WHERE
 * @param item.type Le type d'item
 * @param item.id Alias de la table du type d'item
 * @param accessFolders Liste des répertoires accessibles
 * @returns
 */
function createAccessFoldersQuery(item: Element, accessFolders: string) {
  let where = item.where !== "" ? `${item.where} AND ` : "WHERE ";
  where += `fo.id IN ${accessFolders} `;
  return item.start + where;
}

/**
 * Crée le texte d'une requête de recherche entière
 * @param branch pft3d ou conservatoire3d
 * @param main_folder_id Id du projet
 * @param user_id Id de l'utilisateur
 * @param request JSON de recherche encodé au format URI, chaine de caractère dans l'url
 * @param selects Contient le texte des commandes SELECT pour chaque type d'items
 * @returns
 */
async function createQuery(
  database: IDatabase<unknown>,
  branch: Branch,
  main_folder_id: number,
  user_id: number,
  request: string,
  selects: Record<string, string>,
) {
  const parameters = JSON.parse(decodeURIComponent(request));
  let user_status: string;
  // USER STATUS
  try {
    const result = await database.oneOrNone(
      `SELECT COALESCE ((SELECT user_status FROM archeogrid_user WHERE id = ${user_id}), 'guest') AS status`,
    );
    user_status = result.status;
  } catch (e) {
    user_status = "guest";
  }
  // FOLDERS ACCESSIBLES
  let read = "";
  if (user_id) {
    read = `SELECT get_access_from_rootfolder_gen( '${branch}', ${main_folder_id}, $1 ) as fold `;
  } else {
    read = `SELECT array_agg(id) as fold FROM ${branch}_folder WHERE  status = 'public' `;
  }

  let accessFolders = "";
  if ((branch === "pft3d") || (branch === "corpus") ) {
    const userRead = await database.oneOrNone(read, user_id);
    if (userRead.fold.length > 0) {
      accessFolders = JSON.stringify(userRead.fold).replace("[", "(").replace("]", ")");
    }
  }

  // FOLDERS SELECTION
  let folders = "";
  if (parameters.folder) {
    folders = `fo.folder_path @ '${parameters.folder}'`; // répertoire et ses enfants
    // ALL FOLDERS
  } else {
    folders = `fo.folder_path <@ '${main_folder_id}'`;
  }

  // EXTENSIONS
  let searchFiles = false;
  let searchObjects = false;
  const searchFolders = false;
  let searchUnicos = false;
  let searchDeposits = false;
  const extensions: string[] = [];

  if (parameters.type) {
    if (parameters.type === "files" && ((branch === "pft3d") || (branch === "corpus")) ) {
      searchFiles = true;
    }
    if (parameters.type === "unicos" && ((branch === "pft3d") || (branch === "corpus")) ) {
      searchUnicos = true;
    } else if (parameters.type === "objects") {
      searchObjects = true;
    } else if (parameters.type === "deposits" && branch === "conservatoire3d") {
      searchDeposits = true;
    } else {
      searchFiles = true;
      extensions.push(parameters.type);
    }
  } else {
    if ((branch === "pft3d") || (branch === "corpus")) {
      searchFiles = searchObjects = searchUnicos = true;
    }
    if (branch === "conservatoire3d") {
      searchObjects = searchDeposits = true;
    }
  }

  const items: Record<string, Element> = {};
  if (searchFiles) {
    let startFile =
      `${selects.file}FROM ${branch}_file fi ` + `INNER JOIN ${branch}_folder fo ON fo.id = fi.id_folder `;
    if ((branch === "pft3d") || (branch === "corpus")) startFile += `AND ${folders} `;

    items.files = {
      start: startFile,
      where: "",
      type: "file",
      id: "fi",
    };
  }
  if (searchUnicos) {
    let startUnico =
      `${selects.unico}FROM ${branch}_unico u ` +
      `INNER JOIN ${branch}_file i ON i.id = u.id_file ` +
      `INNER JOIN ${branch}_folder fo ON fo.id = i.id_folder `;
    if ((branch === "pft3d") || (branch === "corpus")) startUnico += `AND ${folders} `;

    items.unicos = {
      start: startUnico,
      where: "",
      type: "unico",
      id: "u",
    };
  }
  if (searchObjects) {
    let startObject =
      `${selects.object}FROM ${branch}_object ob ` + `INNER JOIN ${branch}_folder fo ON fo.id = ob.id_folder `;
    if ((branch === "pft3d") || (branch === "corpus")) startObject += `AND ${folders} `;
    startObject += `LEFT OUTER JOIN ${branch}_file fi ON fi.id = ob.id_file_representative `;
    if (branch === "conservatoire3d") {
      // pour le conservatoire, pour les objets, on peut stocker dans la metadata 146 une url pour une image de visualisation
      startObject += `LEFT OUTER JOIN ${branch}_passport pass_nakala ON pass_nakala.id_item = ob.id AND pass_nakala.id_metadata = 146 `;
    }

    const where = branch === "conservatoire3d" && user_status !== "admin" ? "WHERE ob.doi IS NOT NULL " : "";

    items.objects = {
      start: startObject,
      where: where,
      type: "object",
      id: "ob",
    };
  }
  if (searchFolders) {
    let startFolder = `${selects.folder}FROM ${branch}_folder fo `;
    startFolder += `LEFT OUTER JOIN ${branch}_file fi ON fi.id = fo.id_representative_picture `;

    items.folders = {
      start: startFolder,
      where: "",
      type: "folder",
      id: "fo",
    };
  }
  if (searchDeposits) {
    let startDeposit = `${selects.deposit}FROM ${branch}_folder fo `;
    startDeposit +=
      `LEFT OUTER JOIN ${branch}_file fi ON fi.id = fo.id_representative_picture ` +
      `LEFT OUTER JOIN ${branch}_passport pass_nom ON pass_nom.id_item = fo.id AND pass_nom.id_metadata = 97 ` +
      `LEFT OUTER JOIN ${branch}_passport pass_nakala ON pass_nakala.id_item = fo.id AND pass_nakala.id_metadata = 142 `;

    let where = "WHERE fo.folder_passport = 'deposit' AND fo.visible = 'true'";
    where += user_status === "admin" ? " " : " AND fo.doi IS NOT NULL ";

    items.deposit = {
      start: startDeposit,
      where: where,
      type: "folder",
      id: "fo",
    };
  }

  /**
   * Crée le texte d'une requête de recherche sur un type d'item en particulier
   * @param item Contient les informations sur le type d'item
   * @param item.start Texte des débuts de requêtes
   * @param item.where Texte de la commande WHERE
   * @param item.type Le type d'item
   * @param item.id Alias de la table du type d'item
   * @returns
   */
  function createItemQuery(item: Element) {
    const queries = [];

    if (parameters.all) {
      const multipleFieldsQueries = [];
      for (const searchBar in parameters.all) {
        const tags = parameters.all[searchBar];
        multipleFieldsQueries.push(createMultipleFieldsQuery(branch, item, tags));
      }
      queries.push(separateQueries(multipleFieldsQueries, "INTERSECT"));
    }

    if (parameters.thesaurus) {
      const thesaurusQueries = [];
      for (const thesaurus in parameters.thesaurus) {
        const tags = parameters.thesaurus[thesaurus];
        thesaurusQueries.push(createThesaurusQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.multi) {
      const allThesaurusMultiQueries = [];
      for (const thesaurus in parameters.multi) {
        const searchBars = parameters.multi[thesaurus];

        const thesaurusQueries = [];
        for (const searchBar in searchBars) {
          const tags = searchBars[searchBar];
          thesaurusQueries.push(createThesaurusMultiQuery(branch, item, tags, thesaurus));
        }
        allThesaurusMultiQueries.push(separateQueries(thesaurusQueries, "INTERSECT"));
      }
      queries.push(separateQueries(allThesaurusMultiQueries, "INTERSECT"));
    }

    if (parameters.periodo) {
      const thesaurusQueries = [];
      for (const thesaurus in parameters.periodo) {
        const tags = parameters.periodo[thesaurus];
        thesaurusQueries.push(createThesaurusPeriodoQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.pactols) {
      const thesaurusQueries = [];
      for (const thesaurus in parameters.pactols) {
        const tags = parameters.pactols[thesaurus];
        thesaurusQueries.push(createThesaurusPactolsQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.geopactols) {
      const thesaurusQueries = [];
      for (const thesaurus in parameters.geopactols) {
        const tags = parameters.geopactols[thesaurus];
        thesaurusQueries.push(createThesaurusPactolsGeoQuery(branch, item, tags[0], thesaurus));
      }
      queries.push(separateQueries(thesaurusQueries, "INTERSECT"));
    }

    if (parameters.passport) {
      queries.push(createMetadataQuery(branch, item, parameters.passport));
    }

    if(parameters.advancedModel) {
      queries.push(createAdvancedMetadataQuery(branch, item, parameters.advancedModel));
    }

    if (extensions.length) {
      queries.push(createExtensionsQuery(item, extensions));
    }

    if (accessFolders.length > 0) {
      queries.push(createAccessFoldersQuery(item, accessFolders));
    }

    if (queries.length > 0) {
      return separateQueries(queries, "INTERSECT");
    }
    return item.start + item.where;
  }

  const queries = [];
  for (const key in items) {
    queries.push(createItemQuery(items[key]));
  }

  return separateQueries(queries, "UNION");
}

export default t.router({
  postSearch: t.procedure
    .input(
      z.object({
        branch,
        folder_id: id,
        user_id: z.number(),
        search: z.string(),
        page: z.number(),
        pagination: z.number(),
        limit: z.number(),
      }),
    )
    .output(postSearchOutput)
    .mutation(async ({ input, ctx }) => {
      try {
      const offset = (input.page - 1) * input.pagination;

      let selects: Record<string, string> = {};

      if ((input.branch === "pft3d")|| (input.branch === "corpus")) {
        selects = {
          file:
            "SELECT DISTINCT fi.id::integer AS id_item, 'file' AS item_type, fi.id_folder AS id_folder, fi.name AS name, " +
            "fi.path AS path, fi.file_ext AS extension, fi.id::integer AS idfile, fi.id_folder AS idfolder, " +
            "0 as x, 0 as y , 0 as width, 0 as height, '' as type, '' as polygon ",
          object:
            "SELECT DISTINCT ob.id::integer AS id_item, 'object' AS item_type, ob.id_folder AS id_folder, ob.name AS name, " +
            "fi.path AS path, fi.file_ext AS extension, ob.id_file_representative::integer AS idfile, COALESCE(ob.id_folder, fi.id_folder) AS idfolder, " +
            "0 as x, 0 as y , 0 as width, 0 as height, '' as type, '' as polygon ",
          unico:
            "SELECT DISTINCT u.id::integer AS id_item, 'unico' AS item_type, i.id_folder AS id_folder, u.name AS name, " +
            "i.path AS path, i.file_ext AS extension, u.id_file::integer AS idfile, i.id_folder AS idfolder, " +
            "u.x, u.y, u.width, u.height, u.type, u.polygon ",
        };
      }
      if (input.branch === "conservatoire3d") {
        //  récupérer l'affichage de la miniature nakala si elle existe pour les objets
        selects = {
          object:
            "SELECT DISTINCT ob.id AS id_item, 'object' AS item_type, ob.id_folder AS id_folder, ob.name AS name, " +
            "fi.path AS path, fi.file_ext AS extension, ob.id_file_representative AS idfile, COALESCE(ob.id_folder, fi.id_folder) AS idfolder, ob.doi AS doi, " +
            "CASE WHEN ob.id_nakala IS NOT NULL THEN ob.id_nakala  " +
            "WHEN ob.id_nakala IS NULL THEN pass_nakala.value[1] END AS nakala ",
          deposit:
            "SELECT DISTINCT fo.id AS id_item, 'deposit' AS item_type, fo.id_parent AS id_folder, COALESCE (pass_nom.value[1], fo.name) AS name, " +
            "fi.path AS path, fi.file_ext AS extension, fo.id_representative_picture AS idfile, fi.id_folder AS idfolder, fo.doi, pass_nakala.value[1] AS nakala ",
        };
      }

      let end = "ORDER BY item_type, id_item ";
      if (input.limit > 0) end += `OFFSET ${offset} LIMIT ${input.limit}`;

      // Première requete : construction de la requete
      const query =
        (await createQuery(ctx.database, input.branch, input.folder_id, input.user_id, input.search, selects)) + end;

      // Deuxieme requete : requete précédemment construite
      const search = await ctx.database.manyOrNone(query, input.folder_id);

      return postSearchOutput.parse(search);
      } catch (err) {
        console.error('POST SEARCH ERROR:', err);
        throw err;
      }
    }),
});
