---
sidebar_position: 1
---

# Create a table

The creation of a table is a first step to add your CSV data to the database. This step will create a new table in the database with the columns of the CSV file. This features is divided in two steps. If you come from the menu you will start from the project selection, but if you come from another features you will start from the CSV file selection with information already displayed.

### Select the project

This step allows you to choose the project where your CSV file is located. You can use the search bar to find a specific project.

![](projectSelector-CT-EN.png)

> **Note**: Only the project that you have access to are displayed.

### Select the CSV file

This step allows you to choose the CSV file that you want to use. This requires to define the delimiter used in the CSV file, the wanted name of the table and the type of items that the CSV describes. The default name for the table is the name of the branch followed by the name of the CSV file. 

![](csvFileSelector-CT-EN.png)

The name of the created table can not include spaces or special characters like ';', ':', '#' or ',', as well as the columns name. If any of these characters are found, they will be replaced by an underscore '_'.

> **Error**: An error will be displayed if the metadata folder doesn't exist and you'll be redirected to the menu. If there is no CSV file in the folder, a message will be displayed allowing you to go back to the project selection.

### Result

If the table have been created, a success message will be displayed. You can either go create the corresponding model, ingest the data of the CSV into the table or go back to the menu.

![](success-CT-EN.png)

> **Error**: An error will be displayed if the file doesn't exists, if there is no header in the file, if there is only one column in the file or if the table already exists.