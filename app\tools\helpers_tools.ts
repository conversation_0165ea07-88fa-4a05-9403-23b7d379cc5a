import fs from "node:fs";
import path from "node:path";
import store from "store";
import { acceptedPrevisuFormat, readAndParse3DFile } from "./globals";
import { archeogrid_client } from "./request";
import type { Branch } from "../../server/types/api";
import { Request, Response } from 'express';

interface GetCsvPathOptions {
  requestedBranch: string;
  projectFolder: string;
  csvFile: string;
}

export const getCsvPath = async ({
  requestedBranch,
  projectFolder,
  csvFile
}: GetCsvPathOptions): Promise<string> => {
  return new Promise(async (resolve, reject) => {
    // Get the site url from the folder ex: '../web-archeogrid/projets_data/'
    const site = await archeogrid_client.folders.getSite.query({
      branch: requestedBranch,
      folder_name: projectFolder
    });

    if (!site) {
      reject(`Site not found for folder ${projectFolder}`);
      return;
    }

    switch (requestedBranch) {
      case 'pft3d':
        resolve(path.join(__dirname, '..', site, projectFolder, 'metadata', csvFile));
        break;
      case 'conservatoire3d':
        const projetFolderInfos = await archeogrid_client.folders.getFolderProject.query({
          branch: 'conservatoire3d',
          folder_name: projectFolder
        });
        if (!projetFolderInfos) {
          reject(`Impossible de retrouver l'entité liée au dépôt ${projectFolder}.`);
          return;
        }
        const entityFolder = projetFolderInfos.entinty_folder_name;
        resolve(path.join(__dirname, '..', site, entityFolder, projectFolder, 'metadata', csvFile));
        break;
      case 'corpus':
        resolve(path.join(__dirname, '..', site, projectFolder, 'metadata', csvFile));
        break;
      default:
        reject(`Unknown branch type: ${requestedBranch}`);
        return;
    }
  });
};

export const getFolderMetadataPath = async ({
  requestedBranch,
  projectFolder,
}: {
  requestedBranch: Branch;
  projectFolder: string;
}): Promise<string> => {
  return new Promise(async (resolve, reject) => {
    // Get the site url from the folder ex: '../web-archeogrid/projets_data/'
    const site = await archeogrid_client.folders.getSite.query({
      branch: requestedBranch,
      folder_name: projectFolder
    });

    if (!site) {
      reject(`Site not found for folder ${projectFolder}`);
      return;
    }

    switch (requestedBranch) {
      case 'pft3d':
        console.log([__dirname, '..', site, projectFolder, 'metadata'])
        resolve(path.join(__dirname, '..', site, projectFolder, 'metadata'));
        break;
      case 'conservatoire3d':
        const projetFolderInfos = await archeogrid_client.folders.getFolderProject.query({
          branch: 'conservatoire3d',
          folder_name: projectFolder
        });
        if (!projetFolderInfos) {
          reject(`Impossible de retrouver l'entité liée au dépôt ${projectFolder}.`);
          return;
        }
        const entityFolder = projetFolderInfos.entinty_folder_name;
        resolve(path.join(__dirname, '..', site, entityFolder, projectFolder, 'metadata'));
        break;
      case 'corpus':
        resolve(path.join(__dirname, '..', site, projectFolder, 'metadata'));
        break;
      default:
        reject(`Unknown branch type: ${requestedBranch}`);
        return;
    }
  });
};



const md5 = require("md5");

export function getDateOfTheDay() {
  const d = new Date();
  let month = `${d.getMonth() + 1}`;
  let day = `${d.getDate()}`;
  const year = d.getFullYear();

  if (month.length < 2) month = `0${month}`;
  if (day.length < 2) day = `0${day}`;

  return [year, month, day].join("-");
}

export function setImageSmall(file: string, pathFile: string) {
  // TODO : problème si l'extension du small est .JPG ! On ne peut pas télécharger le small
  // TODO : car il n'existe pas => Comment garantir l'exactitude du nom du fichier small ? aller le lire sur disque ?
  const ldfile = `${file.substring(0, file.lastIndexOf("."))}.jpg`;
  const srcImgSmall = md5(pathFile.replace(file, `small/${ldfile}`));
  const monsmall = pathFile.replace(file, `small/${ldfile}`);
  store.set(`small_${srcImgSmall}`, monsmall);

  return srcImgSmall;
}

export function setImageThumb(file: string, pathFile: string) {
  let ldfile: string;
  let srcImgThumb: string;
  let monpath: string;
  if (file !== "object") {
    ldfile = `${file.substring(0, file.lastIndexOf("."))}.jpg`;
    srcImgThumb = md5(pathFile.replace(file, `small/${ldfile}`));
    monpath = pathFile.replace(file, `small/${ldfile}`);
  } else {
    // pour les objets, on ne travaille qu'avec le path de l'image repr
    ldfile = path.basename(pathFile);
    srcImgThumb = md5(pathFile.replace(ldfile, `small/${ldfile}`));
    monpath = pathFile.replace(ldfile, `small/${ldfile}`);
  }
  //srcImgThumb = md5(pathFile.replace(file, 'thumbnail/' + ldfile))
  //let monpath = pathFile.replace(file, 'thumbnail/' + ldfile)
  store.set(`thumbnail_${srcImgThumb}`, monpath);

  return srcImgThumb;
}

export function setImageHd(file: string, pathFile: string) {
  const srcImgHd = md5(pathFile);
  store.set(`hd_${srcImgHd}`, pathFile);

  return srcImgHd;
}

export function setImageSmallFake(file: string, pathFile: string) {
  const jpgFile = `${file.substring(0, file.lastIndexOf("."))}.jpg`;
  const small = pathFile.replace(file, `small/${jpgFile}`);
  const srcImgSmall = md5(small);
  if (fs.existsSync(small)) {
    store.set(`small_${srcImgSmall}`, small);
    return srcImgSmall;
  }
  const srcImgHd = md5(pathFile);
  store.set(`small_${srcImgHd}`, pathFile);
  return srcImgHd;
}

export function setImageThumbFake(file: string, pathFile: string) {
  const jpgFile = `${file.substring(0, file.lastIndexOf("."))}.jpg`;
  const small = pathFile.replace(file, `small/${jpgFile}`);
  const srcImgSmall = md5(small);
  if (fs.existsSync(small)) {
    store.set(`thumbnail_${srcImgSmall}`, small);
    return srcImgSmall;
  }
  const srcImgHd = md5(pathFile);
  store.set(`thumbnail_${srcImgHd}`, pathFile);
  return srcImgHd;
}

export function getFileName(pathFile: string) {
  let filename = "";
  filename = pathFile.substring(pathFile.lastIndexOf("/")).substring(1);
  return filename;
}

// https://coderrocketfuel.com/article/get-the-total-size-of-all-files-in-a-directory-using-node-js
export function getAllFiles(
  directory: string,
  files_paths: string[] = [],
  max_recursion = 0,
  depth = 0,
) {
  let working_files = files_paths;
  if (depth > max_recursion) return working_files;

  const files = fs.readdirSync(directory);

  for (const file of files) {
    if (fs.statSync(`${directory}/${file}`).isDirectory()) {
      //pour la récursitivé :
      working_files = getAllFiles(
        `${directory}/${file}`,
        working_files,
        max_recursion,
        depth + 1,
      );
    } else {
      working_files.push(path.join(directory, file));
    }
  }

  return working_files;
}

export function getTotalSize(directoryPath: string) {
  const arrayOfFiles = getAllFiles(directoryPath);

  let totalSize = 0;

  for (const filePath of arrayOfFiles) {
    totalSize += fs.statSync(filePath).size;
  }

  return convertBytes(totalSize);
}

export function convertBytes(bytes: number) {
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];

  if (bytes === 0) {
    return "n/a";
  }

  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  if (i === 0) {
    return `${bytes} ${sizes[i]}`;
  }

  return `${(bytes / 1024 ** i).toFixed(1)} ${sizes[i]}`;
}

export function walk(dir: string, done: (err: Error | null, data: string[]) => void) {
  const results: string[] = [];
  fs.readdir(dir, (err, list) => {
    if (err) {
      done(err, []);
      return;
    }
    let pending = list.length;
    if (!pending) {
      done(null, results);
      return;
    }
    for (let file of list) {
      file = path.resolve(dir, file);
      fs.stat(file, (_, stat) => {
        if (!stat.isDirectory()) {
          results.push(file);
          if (!--pending) done(null, results);
        }
      });
    }
  });
}

export function getStream(file: string) {
  return fs.readFileSync(file);
}

export function fileExists(file: string) {
  if (!fs.existsSync(file)) {
    console.warn("File not found");
    return false;
  }
  return true;
}

export function setBasePath(base: string) {
  let dbase = "";
  if (base === "E") {
    dbase = "web-archeogrid/extprojects_data/";
  } else if (base === "A") {
    dbase = "web-archeogrid/projets_data/";
  } else if (base === "C") {
    dbase = "web-archeogrid/corpus_data/";
  } else if (base === "CND3D") {
    dbase = "web-archeogrid/conservatoire_data/";
  }
  return dbase;
}

export function computeAccessItem(
  items: { id_folder: number; nb: string | number }[],
  access: number[],
  status: string,
) {
  const real_value = (value: string | number) =>
    typeof value === "string" ? Number.parseInt(value) : value;

  return items.reduce((sum, item) => {
    const { nb } = item;
    if (status === "admin") {
      return sum + real_value(nb);
    }

    if (access.includes(item.id_folder)) return sum + real_value(nb);

    return sum;
  }, 0);
}

export function computeAccessItemFile(
  items: { id_folder: number; nb_file: string | number }[],
  access: number[],
  status: string,
) {
  let cpt = 0;
  for (const item of items) {
    const { nb_file, id_folder } = item;
    if (status === "admin")
      if (typeof nb_file === "string") {
        cpt += Number.parseInt(nb_file);
      } else {
        cpt += nb_file;
      }
    else {
      for (const access_item of access) {
        if (id_folder === access_item) {
          if (typeof nb_file === "string") {
            cpt += Number.parseInt(nb_file);
          } else cpt += nb_file;
        }
      }
    }
  }
  return cpt;
}

export function computeAccessItemTypeFile(
  items: { id_folder: number; item_type: string; nb: string | number }[],
  access: number[],
  status: string,
) {
  let cpt = 0;
  for (const item of items) {
    const { item_type, id_folder, nb } = item;
    if (item_type === "file") {
      if (status === "admin")
        if (typeof nb === "string") {
          cpt += Number.parseInt(nb);
        } else {
          cpt += nb;
        }
      else {
        for (const access_item of access) {
          if (id_folder === access_item) {
            if (typeof nb === "string") {
              cpt += Number.parseInt(nb);
            } else cpt += nb;
          }
        }
      }
    }
  }

  return cpt;
}

export function computeAccessItemObj(
  items: { id_folder: number; nb_object: string | number }[],
  access: number[],
  status: string,
) {
  let cpt = 0;
  for (const item of items) {
    const { nb_object } = item;
    if (status === "admin")
      if (typeof nb_object === "string") {
        cpt += Number.parseInt(nb_object);
      } else {
        cpt += nb_object;
      }
    else {
      for (const access_item of access) {
        if (item.id_folder === access_item) {
          if (typeof nb_object === "string") {
            cpt += Number.parseInt(nb_object);
          } else cpt += nb_object;
        }
      }
    }
  }

  return cpt;
}

export function computeAccessItemUnico(
  items: { id_folder: number; nb_unico: string | number }[],
  access: number[],
  status: string,
) {
  let cpt = 0;
  for (const item of items) {
    const { nb_unico } = item;
    if (status === "admin")
      if (typeof nb_unico === "string") {
        cpt += Number.parseInt(nb_unico);
      } else {
        cpt += nb_unico;
      }
    else {
      for (const access_item of access) {
        if (item.id_folder === access_item) {
          if (typeof nb_unico === "string") {
            cpt += Number.parseInt(nb_unico);
          } else cpt += nb_unico;
        }
      }
    }
  }

  return cpt;
}

export function computeAccessItemTypeObject(
  items: { item_type: string; id_folder: number; nb: string | number }[],
  access: number[],
  status: string,
) {
  // pour le rendu du searchNB
  let cpt = 0;
  for (const item of items) {
    const { item_type, id_folder, nb } = item;
    if (item_type === "object") {
      if (status === "admin")
        if (typeof nb === "string") {
          cpt += Number.parseInt(nb);
        } else {
          cpt += nb;
        }
      else {
        for (const access_item of access) {
          if (id_folder === access_item) {
            if (typeof nb === "string") {
              cpt += Number.parseInt(nb);
            } else cpt += nb;
          }
        }
      }
    }
  }

  return cpt;
}

export function computeAccessItemTypeUnico(
  items: { item_type: string; id_folder: number; nb: string | number }[],
  access: number[],
  status: string,
) {
  // pour le rendu du searchNB
  let cpt = 0;
  for (const item of items) {
    const { nb } = item;
    if (item.item_type === "unico") {
      if (status === "admin")
        if (typeof nb === "string") {
          cpt += Number.parseInt(nb);
        } else {
          cpt += nb;
        }
      else {
        for (const access_item of access) {
          if (item.id_folder === access_item) {
            if (typeof nb === "string") {
              cpt += Number.parseInt(nb);
            } else cpt += nb;
          }
        }
      }
    }
  }

  return cpt;
}

export function getuniqueArray(array: unknown[]) {
  const uniqueArray: unknown[] = [];
  // Loop through array values
  for (const array_item of array) {
    if (!uniqueArray.includes(array_item)) {
      uniqueArray.push(array_item);
    }
  }
  return uniqueArray;
}

export function getuniqueObjectArray(array:unknown[]){
   // Create an empty object to keep track of unique keys
    let uniqueKeys = {};

    // Filter out duplicate objects based on a unique key
    return array.filter(obj => {
      // Generate a unique key for each object (here we use 'id' as the unique key)
      const key = obj.id;

      // If the key doesn't exist in the uniqueKeys object, add it and return true
      // If the key already exists, return false to filter out the duplicate object
      if (!uniqueKeys[key]) {
        uniqueKeys[key] = true;
        return true;
      }
      return false;
    });

}

export function privateRights(
  user_read: number[],
  user_write: number[],
  user_status: string,
  user_id: number,
  folder_id: number | string,
) {
  const working_folder_id =
    typeof folder_id === "string" ? Number.parseInt(folder_id) : folder_id;
  const resu = { HDrights: false, Rrights: false, Wrights: false };
  if (user_id) {
    // écriture
    if (
      (user_status === "user" && user_write.includes(working_folder_id)) ||
      (user_status === "scribe" && user_read.includes(working_folder_id)) ||
      user_status === "admin"
    )
      resu.Wrights = true;
    else resu.Wrights = false;
    // HD pour téléchargement
    resu.HDrights = user_status !== "guest";
    // lecture sans modification
    if (
      (user_status === "guest" && user_read.includes(working_folder_id)) ||
      (user_status === "user" && user_read.includes(working_folder_id)) ||
      (user_status === "scribe" && user_read.includes(working_folder_id)) ||
      user_status === "admin"
    )
      resu.Rrights = true;
    else resu.Rrights = false;
  }

  return resu;
}

export function publicRights(user_status: string) {
  return {
    Wrights: user_status === "scribe" || user_status === "admin",
    HDrights: true,
    Rrights: true,
  };
}

export function isObjectEmpty(
  value: Record<string, never> | Record<string, unknown>,
): value is Record<string, never> {
  return (
    Object.prototype.toString.call(value) === "[object Object]" &&
    JSON.stringify(value) === "{}"
  );
}

export async function getImageDataFromFile(branch: Branch, file_id: number) {
  if (!file_id) return null;

  const data = await archeogrid_client.files.prepareImage.query({ branch, file_id });
  const short_path = `${data.path.substring(0, data.path.lastIndexOf("/"))}/`;

  if (data.file_ext === "url") {
    const contenuUrl = fs.readFileSync(data.path, "utf-8");
    const url_file = JSON.parse(contenuUrl);

    return {
      ...data,
      urlTarget: url_file?.url as string,
      download: `${short_path}_html/${data.filename.replace("url", "html")}`,
      srcImgThumb: setImageThumb(data.filename, data.path),
      srcImgSmall: setImageSmall(data.filename, data.path),
      srcImgHd: setImageHd(
        data.filename.replace("url", "html"),
        `${short_path}_html/${data.filename.replace("url", "html")}`,
      ),
    };
  }

  if (data.file_ext === "3d") {
    const srcImg3d = md5(data.path);
    store.set(`image3d_${srcImg3d}`, data.path);

    const data3d = await readAndParse3DFile(data.path);
    if (!data3d) throw new Error("Reading 3d data failed!");

    if (data3d.type === "3DHOP" && data3d.data["3dhop"].thumbnail) {
      const thumb_name = short_path + data3d.data["3dhop"].thumbnail;
      const srcImgThumb = md5(thumb_name);
      const srcImgSmall = srcImgThumb;
      store.set(`thumbnail_${srcImgThumb}`, thumb_name);
      store.set(`small_${srcImgThumb}`, thumb_name);

      return { ...data, srcImgThumb, srcImgSmall, srcImg3d };
    }

    if (data3d.type === "Potree" && data3d.data.potree.thumbnail) {
      const thumb_name = short_path + data3d.data.potree.thumbnail;
      const srcImgThumb = md5(thumb_name);
      const srcImgSmall = srcImgThumb;
      store.set(`thumbnail_${srcImgThumb}`, thumb_name);
      store.set(`small_${srcImgThumb}`, thumb_name);

      return { ...data, srcImgThumb, srcImgSmall, srcImg3d };
    }
  }

  if (data.file_ext === "json") {
    return {
      ...data,
      srcImgThumb: "json",
      srcImgSmall: "json",
      srcImgHd: setImageHd(data.filename, data.path),
    };
  }

  if (data.file_ext === "zip") {
    return {
      ...data,
      srcImgThumb: "zip",
      srcImgSmall: "zip",
      srcImgHd: setImageHd(data.filename, data.path),
    };
  }

  if (data.file_ext === "png") {
    return {
      ...data,
      srcImgThumb: setImageThumbFake(data.filename, data.path),
      srcImgSmall: setImageSmallFake(data.filename, data.path),
      srcImgHd: setImageHd(data.filename, data.path),
    };
  }

  if (acceptedPrevisuFormat.includes(data.file_ext.toLowerCase())) {
    return {
      ...data,
      srcImgThumb: setImageThumb(data.filename, data.path),
      srcImgSmall: setImageSmall(data.filename, data.path),
      srcImgHd: setImageHd(data.filename, data.path),
    };
  }

  return { ...data, srcImgHd: setImageHd(data.filename, data.path) };
}

export const notNull = <TValue>(value: TValue | null): value is TValue => {
  return value !== undefined && value !== null;
};

export const isURLValid = async (url: string) => {
  const controller = new AbortController();
  try {
    const validUrl = new URL(url);
    const timeout = setTimeout(() => {
      controller.abort();
    }, 3500);
    const response = await fetch(validUrl, { method: "HEAD", signal: controller.signal });
    clearTimeout(timeout);
    // If the request was successful, or if we cannot reach the server
    return response.ok || response.status === 522;
  } catch (error) {
    // If we find that the url is not valid before timeout false
    // Else true
    return controller.signal.aborted;
  }
};

export const parseAdvancedModelItems = (metadata: any) => {
  for(const [key, value] of Object.entries(metadata)){
    if(Array.isArray(value) && value.length > 0){
      for(const m of value){
        if(m.status === "actor" || m.status === "datation" || m.status === "location"){
          try{
            m.value = JSON.parse(m.value);
          }catch(e){
            console.log(`ERROR PARSING ADVANCED METADATA ${m.label} =>`, e);
          }
        }
      }
    }
  }
};

export function sanitizeSQLName(name: string | undefined) {
  return name?.trim()
    .toLowerCase() // facultatif, si tu veux tout en minuscules
    .normalize('NFD').replace(/[\u0300-\u036f]/g, '') // remplace les accents
    .replace(/[^a-z0-9_]/gi, '_') // remplace tout sauf lettres, chiffres, underscore
    .replace(/^([^a-z_])/, '_$1'); // ajoute un _ devant si le nom commence par chiffre
};
