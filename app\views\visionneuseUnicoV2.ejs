<% const url = `/crop/${folderId}_${unico.id_file}/${unico.x},${unico.y},${unico.width},${unico.height}` %>
<%- include('unicoModal') %>

<% if ((context === 'p') || (context === 'd')) { %>
    <div class="row">
        <div class="col-2">
            <% if (mainFolder['mainfolder'] === '5535') { %>
                <img src="../assets/images/nd-archeogrid.png" style="height: 4em;margin-left:0em;margin-bottom: 0em;margin-top: 0em;" >
            <% } else { %>
                <img src="../assets/images/ArcheoGRID.png" style="height: 3em;" >
            <% } %>
        </div>
        <div class="col-8  text-center">

        </div>
        <div class="col-2" id="menuDroit">
        </div>
    </div>
<% } %>

<div class="container">
    <div class="text-center">
        <% if ( viewerFormat.indexOf(image['file_ext'].toLowerCase()) !== -1 ) { %>
            <a href="<%- url %>">
                <% if (unico.type === 'rect') { %>
                    <img class="<% if (context === 'm') {%>card-img <% } %>mx-auto d-block" src="<%= `/crop/${folderId}_${idFile}/${unico.x},${unico.y},${unico.width},${unico.height}` %>"<% if (context === 'd') {%>style="border: black 1px solid;"<% } %>>
                <% } else if (unico.type === 'poly') { %>
                    <% let points = unico.polygon.split(' ') %>
                    <% for (let i in points) { 
                        let e = points[i].split(','); 
                        e[0] = parseInt(e[0]) - unico.x; 
                        e[1] = parseInt(e[1]) - unico.y; 
                        points[i] = e.join(',') 
                    } %>
                    <% points = points.join(' ') %>
                    <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width="20vw" height="40vh" viewBox="0 0 <%- unico.width %> <%- unico.height %>" id="unico-svg-<%- unico.id %>" class="<% if (context === 'm') {%>card-img <% } %>mx-auto d-block">
                        <mask id="svgmask-<%- unico.id %>">
                            <polygon fill="#ffffff" points="<%- points %>"/>
                        </mask>
                        <image xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="<%= `/crop/${folderId}_${idFile}/${unico.x},${unico.y},${unico.width},${unico.height}` %>" width="<%- unico.width %>" height="<%- unico.height %>" mask="url(#svgmask-<%- unico.id %>)"/>
                    </svg>
                <% } %>
            </a>
        <% } else { %>
            <img class="<% if (context === 'm') {%>card-img <% } %>mx-auto d-block" src="<%= `/crop/${folderId}_${idFile}/${unico.x},${unico.y},${unico.width},${unico.height}` %>">
        <% } %>
            <br>
            <p style="text-align: center"><strong>Unico : </strong><%= unico['name'] %></p>
            <br>
    </div>
    <div class="row text-center">
        <div class="col-4"><%# on affiche l'image mère ? %>

            <a href="/visionneuse,<%= image['id'] %>-<%= folderId %>,<%= branch %>-p,0"><h3><%=__('sourceFile')%></h3>
                <img src="/thumb/<%= folderId %>_<%= idFile %>" class="img_respARCHEOGRID" style="width: 5em;"/></a>
        </div>
        <div class="col-4"><% if (user.id === 2000) { %><% if (mainFolder['id_metadata_model']) { %><%# TODO later %>
            <a href="/comment,<%= branch %>,<%= folderId %>,<%= unico['id'] %>,unico" type="button" class="btn btn-secondary" title="<%= mainFolder['description'] %>"><i class="far fa-comments"></i> <%=__('note')%> / <%= mainFolder['label']%></a><% } else { %>
            <a href="/comment,<%= branch %>,<%= folderId %>,<%= unico['id'] %>,unico" type="button" class="btn btn-secondary" title="<%=__('note')%>" ><i class="far fa-comments"></i></a><% } } %>
        </div>
        <div class="col-4" id="menuDroit">
        
        </div>
    </div>

    <div id="alerts" class="d-flex justify-content-center">
        <div class="alert alert-success mb-2 alert-success" role="alert" style="display: none;"><%=__('unicoDeleteSuccess')%></div>
        <div class="alert alert-danger mb-2 alert-notconnected" role="alert" style="display: none;"><%=__('disconnected')%></div>
        <div class="alert alert-danger mb-2 alert-forbidden" role="alert" style="display: none;"><%=__('forbidden')%></div>
        <div class="alert alert-danger mb-2 alert-notfound" role="alert" style="display: none;"><%=__('notFound')%></div>
        <div class="alert alert-danger mb-2 alert-error" role="alert" style="display: none;"><%=__('error')%></div>
    </div>

</div>

<br>

<% if (user.id) { %>
    <div class="d-flex text-center justify-content-between gap-2" style="flex-wrap: wrap; justify-content: center !important;">
        <div>
            <a type="button" href="/comment,<%= branch%>,<%= folderId %>,<%= unico.id %>,unico" class="btn btn-secondary" title="<%=__('comment')%>">
                <i class="far fa-comments"></i> <%=__('comment')%>
            </a>
        </div>
        <% if (rights) { %>  
            <div>
                <a type="button" href="/edit,<%= branch %>,<%= metadata_model %>,unico,<%= unico.id %>,<%= folderId %>"
                    class="btn btn-secondary"
                    title="<%=__('enrichData')%>"><i class="fas fa-edit"></i> <%=__('enrichData')%>
                </a>
            </div>
            <div>
                <a type="button" href="/keyword,<%= branch %>,unico,<%= unico.id %>,<%= folderId %>" class="btn btn-secondary">
                    <i class="fas fa-tag"></i> &nbsp;<%=__('addKeyword.title')%>
                </a>
            </div>
            <div>
                <a href="#" class="btn btn-secondary"
                title="Copy" onclick="copyMetadataItem('<%= branch %>', 'unico', '<%= unico.id %>')"><i class="far fa-copy"></i> <%=__('copyMetadata')%></a>
            </div>

            <div>
                <a href="#" class="btn btn-secondary"
                title="Paste" onclick="pasteMetadataItem('<%= branch %>', 'unico', '<%= unico.id %>', '<%= folderId %>', '<%= language %>')"><i class="fas fa-copy"></i> <%=__('pasteMetadata')%></a>
            </div>
        <% }%>
        <% if ((user.id === unico.id_user) || (user.user_status === 'admin')) { %>
            <% if (context === 'p') { %> <%# seulement si on est sur une page car les unicos sont mis à jour dans une modale et il y a un problème
             pour récupérer les mot-clés de thesaurus si on ouvre une modale dans un modale.. %>
                <button type="button" class="btn btn-secondary" unico-id="<%= unico.id %>" onclick="editUnico(<%= unico.id %>)">
                    <i class="fa-lg fas fa-edit"></i> <%=__('edit')%> <%=__('the3')%>unico
                </button>
            <% } %>
        <% } %>
    </div>
<% } %>

<hr>

<div style="display: none; visibility: hidden">
    <table class="table">
        <tbody>
            <tr id-unico="<%= unico.id %>">
                <th scope="row"><%= 1 %></th>
                <td><%= unico.name %></td>
                <td><%= unico.annotation %></td>
                <td>
                    <ul style="padding-left: 0%;">
                    <% for (const thesaurus of thesaurus_data) { %>
                        <li>
                            <div style="font-weight: bold;"><%= thesaurus.name %> :</div>
                            <% for (const tag of thesaurus.tags) { %>
                                <span class="pb-1 pr-1">
                                    <span class="tag p-1 my-1">
                                        <span class="tag-value pr-1" thesaurus="<%= thesaurus.thesaurus %>" status="<%= thesaurus.status %>" id="<%= tag.id %>">
                                            <%= tag.name %>
                                        </span>
                                    </span>
                                </span>
                            <% } %>
                        </li>
                    <% } %>
                    </ul>
                </td>
                <td></td>
                <td><a href="<%-url%>">
                    <img src="<%-url%>" style="max-width: 150px; max-height: 150px; object-fit: contain;">
                </a></td>
            </tr>
        </tbody>
    </table>
</div>

<div class="container">
    <h4><%= __('rawUnicoData') %></h4>
    <table class="table table-sm">
        <tbody>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= __('name') %></td>
                <td style="width: 70%"><%= unico.name %></td>
            </tr>
            <% if(unico.annotation){ %>
                <tr>
                    <td style="text-align: right;width: 30%"><strong><%= __('annotation') %></td>
                    <td style="width: 70%"><%= unico.annotation %></td>
                </tr>
            <% } %>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= __('unicoType') %></td>
                <td style="width: 70%"><%= unico.type %></td>
            </tr>
        </tbody>
    </table>

    <% if(metadata_models.length > 0) { %>
        <br>
        <% for(let model of metadata_models) { %>
            <h4><%= __('metadata') %> - <%= model.label %></h4>
            <table class="table table-sm">
                <tbody>
                    <% for (let metadata of model.metadata) { %>
                        <tr>
                            <td style="text-align: right;width: 30%"><strong><%= metadata.label %></td>
                            <td style="width: 70%" class="advanced_model_display">
                                <%- include('displayMetadataSwitch.ejs', {metadata: metadata}) %>
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        <% } %>
    <% } %>

<%# tag %>
<% if (image['tag'].length > 0 ) { %>
    <%= __('keywords')%> :
    <% for (var i = 0; i < image['tag'].length; i++ ) { %>
        <button class="btn"><%= image['tag'][i] %></button>
    <% } %>

<% } %>

<% if (tagThesmulti.length > 0  || tagPactols.length > 0 || tagMaison.length > 0) { %>
    <i class="fa fa-tag" aria-hidden="true"></i>
    <%=__('keywords')%>
<% } %>

<% if ((tagThesmulti.length > 0 )){ %>
    <% for (var i = 0; i < tagThesmulti.length; i++ ) { %>
        <a type="button" href="/concept/<%= tagThesmulti[i].name %>?rootF=<%= mainFolder['mainfolder']%>&type=multi&idThes=<%= tagThesmulti[i]['id_thes']%>&thesaurus=<%= tagThesmulti[i]['thesaurus']%>" class="btn btn-light btn-sm">
            <%= tagThesmulti[i].name %>
        </a>
        <% if (rights) { %>
            <span class="btn-hand" title="<%=__('delete')%>"
                  onclick="delete_thesmulti_item('<%= branch %>', '<%= unico.id %>', 'unico', '<%= tagThesmulti[i]['thes_path']%>', '<%= tagThesmulti[i]['thesaurus']%>')">
                <i class="far fa-trash-alt"></i>
            </span>
        <% } %>
    <% } %>
    <br>
<% } %>

<% if (comments.length > 0) { %>
    <hr>
    <h4><%= comments.length %> <%=__('note')%><% if (comments.length > 1) {%>s<% }%></h4>
        <% for (let [index, comment] of Object.entries(comments) ) { %>
            <dl class="row">
                <dt class="col-sm-3">
                    <strong><%=__('content')%></strong>
                </dt>
                <dd class="col-sm-9"><%- comment.content %></dd>
            </dl>
            <dl class="row">
                <dt class="col-sm-3">
                    <strong>
                        <em><%=__('author')%></em>
                    </strong>
                </dt>
                <dd class="col-sm-9"><%= comment.signature %></dd>
            </dl>
            <% if (comment.author.length > 0 ) {%>
                <dl class="row">
                    <dt class="col-sm-3">
                        <strong>
                            <em><%=__('inNameOf')%>:</em>
                        </strong>
                    </dt>
                    <dd class="col-sm-9"><%= comment.author %></dd>
                </dl>
            <% } %>
            <dl class="row">
                <dt class="col-sm-3">
                    <strong><%=__('date')%></strong>
                </dt>
                <dd class="col-sm-9"><%= comment.date %></dd>
            </dl>
            <% if (comment.other_date.length > 0 ) {%>
                <dl class="row">
                    <dt class="col-sm-3">
                        <strong><%=__('dateExplicite')%>: </strong>
                    </dt>
                    <dd class="col-sm-9"><%- (comment.other_date) %></dd>
                </dl>
            <% } %>
            <% if (index < comments.length - 1) {%><hr><% } %>
        <% }%>
<% } %>

</div>
</div>

<% if ((context === 'p') || (context === 'd') ) { %>
</div>
<div class="loader"></div>
<% } %>