function setupRedirection(options) {
    const {
        inputId,
        cancelBtnId,
        serverUrl,
        defaultUrl,
        excludedPrefixes
    } = options;

    document.addEventListener('DOMContentLoaded', function() {
        const inputElement = document.getElementById(inputId);
        if (!inputElement) {
            console.error(`Redirection setup failed: Input element with id "${inputId}" not found.`);
            return;
        }

        const isExcluded = (url) => {
            if (!url || typeof url !== 'string' || url.trim() === '') {
                return true;
            }

            let path;
            try {
                // Create a full URL to safely parse it
                const fullUrl = new URL(url, window.location.origin);
                if (fullUrl.origin !== window.location.origin) {
                    return true; // Exclude if from a different domain
                }
                path = fullUrl.pathname + fullUrl.search + fullUrl.hash;
            } catch (e) {
                // If it's not a full URL, treat it as a path
                path = url;
            }


            if (path.startsWith('/')) {
                path = path.substring(1);
            }

            return excludedPrefixes.some(prefix => path.startsWith(prefix));
        };

        const storageKey = 'lastValidRedirectUrl';
        let redirectUrl = '';

        // 1. Prioritize server-provided URL if it's not excluded
        if (!isExcluded(serverUrl)) {
            redirectUrl = serverUrl;
        }

        // 2. Fallback to document.referrer if it's a valid, same-origin, non-excluded URL
        if (!redirectUrl && document.referrer) {
            const referrerUrl = new URL(document.referrer);
            if (referrerUrl.origin === window.location.origin && !isExcluded(document.referrer)) {
                redirectUrl = referrerUrl.pathname + referrerUrl.search + referrerUrl.hash;
            }
        }

        // 3. If we found a valid URL, store it.
        if (redirectUrl) {
            sessionStorage.setItem(storageKey, redirectUrl);
        } else {
            // 4. If we didn't find a new valid URL (e.g., referrer was an excluded page),
            // try to get the last known good URL from storage.
            const storedUrl = sessionStorage.getItem(storageKey);
            if (storedUrl && !isExcluded(storedUrl)) {
                redirectUrl = storedUrl;
            }
        }

        // 5. Use default URL if no other valid URL was found
        if (!redirectUrl) {
            redirectUrl = defaultUrl;
        }

        // Ensure the final URL is a relative path starting from the root
        if (redirectUrl.startsWith('/')) {
            redirectUrl = redirectUrl.substring(1);
        }

        inputElement.value = redirectUrl;

        if (cancelBtnId) {
            const cancelBtn = document.getElementById(cancelBtnId);
            if (cancelBtn) {
                cancelBtn.href = '/' + redirectUrl;
            } else {
                console.error(`Redirection setup failed: Cancel button with id "${cancelBtnId}" not found.`);
            }
        }
    });
} 