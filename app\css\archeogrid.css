html {
  height: 100%;
}

/* Remove default focus outlines and blue highlight */
*:focus,
*:active,
button:focus,
button:active,
.btn:focus,
.btn:active,
.form-control:focus,
.custom-select-input:focus,
select:focus,
a:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Remove text selection highlight */
::selection {
  background-color: rgba(0, 0, 0, 0.1);
}

body {
  font-family: "Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif;
  /* background-color: #c8ecff; */
  /* background-color: #eaf8ff; */
  /* background: url("/assets/img/lil_fiber.png") repeat;*/
  opacity: 0.9;
  /* padding-top: 48px; */
  height: 100vh;
  position: relative;
  padding-bottom: 3vh; /* Height of the footer */
}

a:not([type="button"]):hover {
  text-decoration: underline;
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 3vh;
  text-align: center;
  background-color: white;
}

.nav3dicons:hover {
  z-index: 1030;
  background-color: #fff;
}

.nav-item {
  display: inline-block;
  text-align: center;
}

#navbarToggleExternalContent,
#brand {
  font-size: 0.8em;
  font-weight: 600;
  letter-spacing: 0.25em;
  line-height: 4em;
  text-decoration: none;
  text-transform: uppercase;
}

.navbar-toggler-left {
  left: 9rem;
}

.navbar-toggler-right {
  right: 2rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.5);
}

#brand {
  display: block;
  font-size: 0.8em;
  height: inherit;
  line-height: 1.65em;
  padding: 0 0.125em 0 0.375em;
}

#bars {
  width: 2.5em;
  margin-top: -0.5em;
  cursor: pointer;
}

#main {
  padding-top: 40px;
  padding-bottom: 3vh;
  /* gerer la hauteur pour fixer la carte leaflet */
  /* height: 5%;*/
  /* min-height: calc(100vh - 3vh - 40px); */
}

/***********************************/
/* geoLocation                     */
/***********************************/
#map {
  height: 75vh;
  width: 100%;
}

.card-img-top {
  width: 100%;
}

.card-img {
  width: 90%;
  height: 10vw;
  object-fit: scale-down;
}

#a-card-img-center {
  margin: auto;
}

/*************************************/
/* scroll top thesaurus, index pages */
/*************************************/
.scroll-top {
  position: fixed;
  bottom: 3vh;
  /* right:10%; */
  left: 20%;
  z-index: 100;
  font-size: 24px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.scroll-top-fiche {
  position: fixed;
  bottom: 3vh;
  right: 10%;
  z-index: 100;
  font-size: 24px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.explore-scroll-top {
  position: fixed;
  bottom: 3vh;
  right: 10%;
  z-index: 100;
  font-size: 24px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

/* Added to ensure menuGauche stays on top of menuCentre's content if it overflows */
#menuGauche {
  position: relative;
  z-index: 1; /* Adjust if other z-indexes conflict, higher means more on top */
}

#menuCentre {
  height: 79vh;
  max-height: 79vh;
  overflow-x: hidden; /* Prevent horizontal scrolling issues */
  overflow-y: visible; /* Let explore-div handle its own scrolling */
  display: flex;
  flex-direction: column;
  position: relative;
}

@media screen and (max-width: 950px) {
  #menuCentre {
    width: 100%;
  }

  #menuGauche {
    max-height: 70vh;
    overflow-y: auto;
  }
}

@media screen and (min-width: 950px) {
  #menuGauche {
    max-height: 70vh;
    width: 470px !important;
    overflow-y: auto;
  }
}

#menuGaucheAdmin {
  height: calc(100vh - 32px);
  min-width: 480px;
}

#menuGaucheAdmin.offcanvas-xl.show {
  height: 100vh;
}

#menuCentreAdmin {
  max-height: calc(100vh - 52px);
  overflow-y: auto;
}

@media screen and (max-width: 950px) {
  #menuGauche {
    max-height: 70vh;
    overflow-y: auto;
  }
}

@media screen and (min-width: 950px) {
  #menuGauche {
    max-height: 70vh;
    width: 470px !important;
    overflow-y: auto;
  }
}

#main-ul {
  padding-left: 1em !important;
}

#menuGauche li {
  font-size: medium;
}

/*************************************/
/* list tree menu gauche             */
/*************************************/
/* fonctionnel */
#menuGauche ul li input {
  display: none;
}
#menuGauche ul li input ~ ul {
  display: none;
}
#menuGauche ul li input:checked ~ ul {
  display: block;
}
#menuGauche ul li input ~ .fa-folder-open {
  display: none;
}
#menuGauche ul li input:checked ~ .fa-folder {
  display: none;
}
#menuGauche ul li input:checked ~ .fa-folder-open {
  display: inline;
}

/* habillage */
#menuGauche * li {
  display: block;
  padding-bottom: 0.2em;
}

#menuGauche * ul {
  padding-left: 10px;
}

/* Fix scrollbar width issue in menuGauche */
#menuGauche {
  scrollbar-gutter: stable;
}

#search-left-div.search-overflow {
  scrollbar-gutter: stable;
  overflow-y: auto;
  padding-right: 8px; /* Add some padding to prevent content from touching scrollbar */
}

/* Custom scrollbar styling for better appearance */
#menuGauche::-webkit-scrollbar,
#search-left-div.search-overflow::-webkit-scrollbar {
  width: 8px;
}

#menuGauche::-webkit-scrollbar-track,
#search-left-div.search-overflow::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

#menuGauche::-webkit-scrollbar-thumb,
#search-left-div.search-overflow::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

#menuGauche::-webkit-scrollbar-thumb:hover,
#search-left-div.search-overflow::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.mapPal {
  height: 70vh;
}

/* contanier map + item */

@media only screen and (min-width: 1100px) {
  .contenant {
    display: grid;
    grid-template-columns: 6fr 4fr;
  }

  .list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 15px;
    padding: 0 30px;
    max-height: calc(80vh - 3vh);
    overflow-y: auto;
  }

  .list3d {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 15px;
    padding: 0 30px;
    max-height: calc(80vh - 3vh);
    overflow-y: auto;
  }
  .map {
    position: sticky !important;
    left: 0;
    right: 0;
  }

  .listTabloid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-column-gap: 1em;
    grid-row-gap: 1em;
  }
  .list3dTabloid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 1em;
    grid-row-gap: 1em;
  }
}

@media only screen and (max-width: 1024px) {
  .contenant {
    display: grid;
    grid-template-columns: 6fr 4fr;
  }

  .list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 15px;
    padding: 0 30px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .map {
    position: sticky !important;
    left: 0;
    right: 0;
  }

  .listTabloid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 1em;
    grid-row-gap: 1em;
  }
}

/* on supprime toutes les vilaines mises en formes imposées par leaflet pour ne modifier que le marker au niveau du popup */
.marker .leaflet-popup-content-wrapper {
  background-color: transparent;
  color: inherit;
  box-shadow: none;
}

.marker.leaflet-popup {
  margin-bottom: 16px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  width: 25px !important;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background-color: #fff;
}

.marker.leaflet-popup::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -8px;
  /* le triangle */
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #fff;
}

.marker .leaflet-popup-content {
  text-align: inherit;
  padding-left: 6px;
  padding-right: 6px;
  color: inherit;
  margin: 0;
}

.marker .leaflet-popup-tip-container {
  display: none;
}

.marker.is-active,
.marker:hover {
  z-index: 300;
  background-color: #5069ff;
  color: inherit;
  box-shadow: none;
  text-align: inherit;
}

.marker.is-active::after,
.marker:hover::after {
  border-top-color: #5069ff;
}

.marker.is-expanded {
  background-color: #fff !important;
  color: inherit !important;
  z-index: 301;
  text-align: center;
  width: 350px !important;
}

/* Dropdown styling - similar to pagination */
.custom-select option {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.custom-select option:focus {
  background-color: var(--primary-color) !important;
  color: white !important;
}

#libelleProjet {
  font-size: small;
}

.fakelink {
  cursor: pointer;
}

.modal-dialog:not(.custom-modal),
.modal-content:not(.custom-modal) {
  height: 90vh;
}

.modal-body {
  position: relative;
  overflow-y: auto;
  max-height: 100vh;
  padding: 15px;
}

#modalinfo {
  height: auto !important;
}

#modalRootproj {
  height: 300px;
}

@media (min-width: 992px) {
  .modal-lg {
    min-width: 850px !important;
    max-width: 850px !important;
  }
}

@media (min-width: 950px) {
  .form-group.row > .col-form-label {
    text-align: right;
  }
}

.loader {
  display: none;
  position: fixed;
  z-index: 1029;
  top: 0;
  left: 0;
  height: calc(100% - 3vh);
  width: 100%;
  background: rgba(255, 255, 255, 0.8) url("/assets/images/wait.gif") 50% 50% no-repeat;
  background-size: 100px;
}

body.loading {
  overflow: hidden;
}

body.loading .loader {
  display: block;
}

.select_perm {
  min-height: 30vh;
  font-size: small;
  overflow-x: scroll !important;
}

.select_project {
  min-width: 30vw;
  max-width: 30em;
  overflow: auto;
}

.select_petit_perm {
  min-height: 20vh;
  font-size: small;
  min-width: 30vw;
  max-width: 30em;
  overflow-x: scroll !important;
}

input,
.select_perm,
.select_petit_perm,
.select_project {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/*************************************/
/* list sortable drag and drop       */
/*************************************/
.drop-placeholder {
  background-color: #242943;
  height: 3.5em;
  padding-top: 12px;
  padding-bottom: 12px;
  line-height: 1.2em;
}

.select_synch {
  height: 30vh;
}
.select_petitsynch {
  height: 10vh;
}

/* form entity admin */
.capitalize {
  text-transform: capitalize;
}

.textform {
  width: 100%;
}

/* form edit */
.latlngPerso {
  width: 100%;
}

/* Pour garder une fenêtre global à 100% et un contenu variable */
#tabloid {
  height: 75vh;
  overflow: auto;
}

#assignRights :required:invalid + label {
  color: red;
}
#assignRights :required:valid + label {
  color: green;
}

#search-button {
  border-color: #d9d9d9;
  background-color: white !important;
}

.blue {
  margin-left: 5%;
  color: darkblue;
}

.list-group-item {
  padding: 0.5rem 1.25rem;
}

.ui-autocomplete {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  float: left;
  display: none;
  min-width: 160px;
  padding: 4px 0;
  margin: 0 0 10px 25px;
  list-style: none;
  background-color: #ffffff;
  border-color: #ccc;
  border-color: rgba(0, 0, 0, 0.2);
  border-style: solid;
  border-width: 1px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
}

.ui-menu-item > a.ui-corner-all {
  display: block;
  padding: 3px 15px;
  clear: both;
  font-weight: normal;
  line-height: 18px;
  color: #555555;
  white-space: nowrap;
  text-decoration: none;
}

.ui-state-hover,
.ui-state-active {
  color: #ffffff;
  text-decoration: none;
  background-color: #0088cc;
  border-radius: 0px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  background-image: none;
}

.ui-helper-hidden-accessible {
  display: none;
}

.centre {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 5%;
}

/* ************ */
/* context menu */
/****************/
.ctxmenu {
  position: fixed;
  min-width: 128px;
  height: auto;
  padding: 8px;
  margin: 0;
  border: 1px solid #999;
  background: #f8f8f8;
  box-shadow: 2px 2px 2px #aaa;
  z-index: 11;
  overflow: visible;
}
.ctxline {
  display: block;
  margin: 0px;
  padding: 2px 2px 2px 8px;
  border: 1px solid #f8f8f8;
  border-radius: 3px;
  font-size: 13px;
  font-family: Arial, Helvetica, sans-serif;
  overflow: visible;
}
.ctxline:hover {
  border: 1px solid #bbb;
  background-color: #f0f0f0;
  background-image: -moz-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: -ms-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
  background-image: -webkit-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: -o-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: linear-gradient(top, #ffffff, #e6e6e6);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff',
    endColorstr='#e6e6e6', GradientType=0);
}

#content {
  position: static;
  margin-left: 218px;
  margin-top: 92px;
}

.img_respARCHEOGRID {
  max-width: 100%;
  height: auto;
}

/*********************
         DOI
 *********************/
#doi {
  font-size: small;
  line-height: 1em;
  height: 88vh;
  overflow: auto;
  padding-left: 15px;
}

.tab-article {
  margin-top: 20px;
  margin-bottom: 0;
  width: 100%;
}

.td_doi_col1 {
  font-weight: bold;
  width: 40%;
  text-align: left;
  vertical-align: top;
  padding: 5px;
}

.td_doi_col2 {
  width: 60%;
}

/*
* Search page
*/
#search-div {
  height: 85.5vh;
}

#search-left-div {
  padding-right: 0.5vw;
  display: flex !important;
  flex-direction: column !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

.hidden-search {
  display: none !important;
}

#explore-results {
  overflow-y: auto;
}

#explore-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  padding: 0em 0.5em 0.5em 0em;
}
@media (max-width: 950px) {
  #explore-grid {
    display: grid;
    grid-template-columns: 1fr;
    grid-column-gap: 1em;
    grid-row-gap: 1em;
    flex: auto;
  }
}

#left-top > #main-ul {
  padding-left: 0 !important;
  overflow: auto;
  max-height: 60vh;
}

#left-top {
  height: 100%;
}

/*
* Search bars
*/
.search-bar-list :first-child .input-group-addon {
  display: none;
}

.search-bar > *,
.group-borders > *,
.group-borders > .dropdown > button {
  border-radius: 0rem;
}

.search-bar > :first-child,
.group-borders > :first-child,
.group-borders > :first-child.dropdown > button {
  border-bottom-left-radius: 0.25rem !important;
  border-top-left-radius: 0.25rem !important;
}

.search-bar > :last-child,
.group-borders > :last-child {
  border-bottom-right-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

/* when addon is hidden */
.search-bar-list :first-child > :nth-child(2) {
  border-bottom-left-radius: 0.25rem !important;
  border-top-left-radius: 0.25rem !important;
}

.add-search-bar {
  color: rgb(0, 200, 0);
}

.remove-search-bar {
  color: rgb(200, 0, 0);
}

.input-actor-group, .input-datation-group {
  display: flex;
  gap: .5rem;
  flex-direction: column;
}

.input-actor-element {
  display: flex;
}

.input-actor-group, .input-datation-group {
  & div > label {
    width: 15%;
  }
}

.search-bar-actor-type input[type="checkbox"] {
  margin-right: 1rem;
}

.search-datation-interval-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.search-datation-interval-input-container {
  display: flex;
  gap: .5rem;
  flex-direction: column;
}

.search-datation-interval-input-container label {
  width: calc(15% + (1/2 * 15%)) !important;
}

.search-datation-preview_interval {
  display: flex;
  align-items: center;
  justify-content: left;

}

input.search-bar-datation-date_max.invalid, input.search-bar-datation-date_min.invalid {
  border-color: #242943;
}

/*
* Collapse icons
*/
/* Rotation */
.btn-collapse-search > .fa-chevron-right {
  transition: all 0.3s ease;
}

.btn-collapse-search:not(.collapsed) > .fa-chevron-right {
  transition: all 0.3s ease;
  transform: rotate(90deg);
}

/*
* Folders tree
*/
#folders-dropdown {
  flex: auto;
}

#folders-button {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
}

#folders-button,
#caret {
  position: absolute;
}

#folders-menu {
  max-width: 30vw;
}

.ui-autocomplete {
  max-height: 60vh;
  overflow: scroll;
}

.modal-open > .ui-autocomplete {
  max-height: 30vh;
  z-index: 1050;
}

/* Searchable Dropdown Styles */
.custom-select-container {
  position: relative;
  width: 100%;
}

.custom-select-input {
  width: 100%;
  padding: 0.375rem 0.75rem;
  border: 1px solid #d9d9d9;
  border-radius: 0;
  cursor: text;
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  appearance: none;
}

.custom-select-dropdown {
  position: absolute;
  z-index: 1050;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  margin-top: 2px;
}

.custom-select-option {
  padding: 0.375rem 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem;
}

.custom-select-option:last-child {
  border-bottom: none;
}

.custom-select-option:hover,
.custom-select-option.keyboard-selected {
  background-color: #f8f9fa;
  border-left: 3px solid #6c757d;
  padding-left: calc(0.75rem - 3px);
}

.custom-select-option.selected {
  background-color: #e9ecef;
  font-weight: 500;
}

.custom-select-option.no-results {
  color: #6c757d;
  font-style: italic;
  cursor: default;
}

ul,
#folders-tree {
  list-style-type: none;
}

#folders-tree {
  margin: 0;
  padding: 0;
  overflow: auto;
  max-height: 0;
}

#folders-tree span {
  user-select: none;
}

#folders-tree .folder-parent {
  cursor: pointer;
}

#folders-tree .folder-parent::before {
  content: "\25B6";
  display: inline-block;
  margin-right: 6px;
}

#folders-tree .folder-parent-down::before {
  transform: rotate(90deg);
}

/* Ensure vitrine trees use the modern arrow styling from folders-tree-vitrine.css */
#thesaurus-folders-tree .folder-parent {
  cursor: pointer;
  position: relative;
}

#thesaurus-folders-tree .folder-parent::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666;
  transition: transform 0.2s ease;
}

#thesaurus-folders-tree .folder-parent-down::before {
  transform: translate(-50%, -50%) rotate(-90deg);
}

/* Ensure thesaurus folder children are hidden by default */
#thesaurus-folders-tree .folder-children {
  display: none !important;
  padding-left: 20px;
  margin-top: 5px;
  visibility: hidden;
}

#thesaurus-folders-tree .folder-children.active {
  display: block !important;
  visibility: visible;
}

/* Override any potential Bootstrap or other CSS that might make children visible */
#thesaurus-folders-tree .folder-children:not(.active) {
  display: none !important;
  visibility: hidden !important;
}

.folder-children {
  display: none;
}

.folder-children.active {
  display: block;
}

/* Search */
.highlight-search {
  background-color: #fffd8f;
}

.folder-selected,
.folder-selected-hover {
  border-radius: 5px;
}

/* Only apply hover effect in folders-tree-vitrine.css */
.folder-hover {
  border-radius: 5px;
}

.tag {
  display: flex;
  align-items: flex-end;
  min-width: 1%;
  font: 400 14px Arial;
  border-style: solid;
  border-width: 1px;
  border-radius: 4px;
}

.tag:hover {
  cursor: pointer;
}

/*
* Results
*/
#display-menu > * {
  width: auto;
  height: min-content;
  margin-right: 0.5rem !important;
}

/*
* Profil
*/
#profil-tab .nav-link {
  color: black !important;
  text-decoration: underline !important;
}

#profil-tab .nav-link:hover {
  color: #014c8c !important;
  text-decoration: underline !important;
}

#searches-table td,
#searches-table th {
  text-align: center;
}

#alerts {
  position: fixed;
  top: 10px;
  left: 0px;
  width: 100%;
  max-height: calc(100vh - 3vh);
  overflow-y: auto;
}

.ui-menu-item-wrapper {
  cursor: pointer;
}

.verticalContainer {
  height: 70vh;
  position: relative;
}

.verticalCenter {
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* Pour garder une fenêtre global à 100% et un contenu variable lorsqu'il y a un entet / bandeau (cf 3DIcons) */
#tabloid_entete {
  height: 35vh;
  overflow: auto;
}

.superpose {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0 auto;
  padding: 0;
}
.image_superpose {
  display: inherit;
  position: absolute;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  opacity: 55%;
  width: 2.5rem;
}

/* Ensure scrollable containers don't overlap footer */
.scrollable-container {
  max-height: calc(100vh - 3vh) !important;
  overflow-y: auto;
}

.col-md-6 {
  display: flex;
  flex-direction: column;
}

.vitrine-text-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  margin-top: 0;
  padding-top: 0;
  width: 100%;
}

.vitrine-image-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #ffffff;
  padding: 0;
  margin-left: 0;
  margin-right: 0;
}

.vitrine-image-container img {
  width: auto;
  height: auto;
  object-fit: contain;
}

.visionneuseImg {
  max-height: calc(80vh - 3vh);
  width: auto;
  max-width: 100%;
  display: inline-block;
  object-fit: contain;
}
.visionneuseImg:hover {
  cursor: pointer;
}

.map-container {
  height: 500px !important;
  width: 100% !important;
  margin-bottom: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.text_superpose {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 1em 2em;
  border-radius: 3px;
}

.see-select {
  transition: all 300ms cubic-bezier(0.19, 1, 0.22, 1);
}

/* Main container styles */
#GROS {
  height: 79vh;
  overflow: hidden;
}

/* Selection header styles */
.selection-header {
  padding: 0;
  margin: 0;
}

/* Main content container */
.selection-content-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

/* Virtual folder container */
.virtual-folder-container {
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 1rem;
  height: 100%;
  border: 1px solid #dee2e6;
}

/* Disabled form elements */
.disabled-select,
select:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.65;
}

.disabled-button,
input[type="submit"]:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed !important;
  opacity: 0.65;
}

/* Enabled submit buttons should have pointer cursor */
input[type="submit"]:not(:disabled) {
  cursor: pointer;
}

/* Make sure tooltips are visible */
.tooltip {
  z-index: 1070;
  opacity: 1 !important;
}

.tooltip .tooltip-inner {
  background-color: #000;
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* Button wrapper for tooltips */
.button-wrapper {
  display: inline-block;
  position: relative;
  width: fit-content;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.button-wrapper.disabled {
  cursor: not-allowed;
}

.button-wrapper input[type="submit"],
.button-wrapper button {
  width: auto;
  padding: 0.25rem 0.5rem;
  margin: 0;
  box-sizing: content-box;
}

/* Selection buttons container */
#selection-buttons {
  width: 100%;
}

/* Explore results container */
.explore-results-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Selection count badge */
#selection-count {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
}

/* Display options */
#display-options {
  border-bottom: 1px solid #eee;
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
}

/* Pagination controls */
#pagination-controls {
  margin-top: 1.5rem;
}

/* Loading spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
  margin: 2rem auto;
}

/* Explore div styles */
#explore-div {
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  height: fit-content !important;
  max-height: calc(79vh - 20px) !important;
  position: relative !important;
  top: 0 !important;
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* Explore container styles */
#explore-container {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Additional vitrine-text styles */
.vitrine-text {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 2px !important;
  font-size: 14px !important;
}

.grid-vitrine-item-text {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 2px !important;
  font-size: 13px !important;
}

.list-vitrine-item-text {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 2px !important;
  font-size: 16px !important;
}

/* Additional grid-vitrine-img-link styles */
.grid-vitrine-img-link {
  text-decoration: none !important;
  display: block !important;
  -webkit-user-drag: none !important;
  user-select: none !important;
  -moz-user-select: none !important;
  -webkit-user-select: none !important;
  -ms-user-select: none !important;
}

/* Additional grid-vitrine-body styles */
.grid-vitrine-body {
  box-shadow: none !important;
  min-height: auto !important;
}

/* Explore results styles */
#explore-results {
  position: relative;
  overflow-x: hidden !important;
  width: 100%;
  box-sizing: border-box;
}

/*
* Vitrine Grid Styles
*/
.vitrine-grid {
  display: grid;
  gap: 8px !important;
  flex: auto;
  justify-content: center !important;
}

.grid-vitrine-item {
  margin: 0 !important;
  padding: 5px 5px 0 5px !important;
  transition: all 0.2s ease;
  border: none !important;
  border-radius: 4px !important;
  pointer-events: auto !important;
  height: 100% !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  cursor: pointer !important;
  background-color: #f8f9fa !important;
  overflow: hidden !important;
  position: relative !important;
}

.grid-vitrine-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25) !important;
}

.grid-vitrine-body {
  padding: 5px 2px !important;
  margin: 0 !important;
  border: none !important;
  background-color: #f8f9fa !important;
  border-radius: 0 0 4px 4px !important; /* Rounded corners only on bottom */
  cursor: pointer !important;
  position: relative;
  z-index: 1;
}

/* .vitrine-text {
  font-size: 0.7rem !important;
  line-height: 1 !important;
  display: block;
  max-height: 2rem !important;
  overflow: wrap !important;
  text-overflow: ellipsis !important;
  cursor: pointer !important;
} */

.grid-vitrine-img-link {
  margin: 0 !important;
  padding: 0 !important;
  display: block;
  width: 100%;
  height: auto;
  background-color: #f8f9fa !important;
  overflow: hidden;
  text-align: center;
  border-radius: 4px 4px 0 0 !important; /* Rounded corners only on top */
  cursor: pointer !important;
  position: relative;
  z-index: 1;
}

.vitrine-img {
  max-height: 100px !important;
  object-fit: contain;
  padding: 2px !important;
  cursor: pointer !important;
  margin-bottom: 0 !important;
  width: auto !important;
  max-width: 100% !important;

  -webkit-user-drag: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.grid-vitrine-footer {
  margin: 0 !important;
  padding: 0 !important;
}

/*
* Vitrine List Styles
*/
.explore-list {
  display: flex;
  flex-direction: column;
}

.list-link {
  margin-bottom: 0.7rem;
}

.list-vitrine-item {
  display: flex;
  border: none;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.2s ease;
  background-color: #fff;
  height: 120px;
  cursor: context-menu;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
  
.list-vitrine-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.list-vitrine-image {
  flex: 0 0 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.3rem;
  background-color: #f8f8f8;
  cursor: pointer;
  width: 120px;
  height: 120px;
  position: relative;
  overflow: hidden;
}

.list-img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  width: auto;
  height: auto;
  
  -webkit-user-drag: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
  
.list-vitrine-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  cursor: pointer;
  height: 100%;
  position: relative;
  }
  
.list-vitrine-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  }
  
.list-vitrine-actions {
  display: flex;
  gap: 0.7rem;
  align-items: center;
  }
  
.list-vitrine-actions a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  }

.grid-vitrine-item .see-select {
  height: 100%;
}

.update-item-selector, .grid-vitrine-item.see-select {
  cursor: context-menu;
}

.list-vitrine-image, .list-vitrine-content,
.grid-vitrine-img-link, .grid-vitrine-body {
  position: relative;
  z-index: 1;
}

.tableVitrine td{
  text-align: right;
  width: 30%;
}

.tableVitrine td + td {
  text-align: left;
  width: 70%;
  word-break: break-word;
}

#containerV {
  width: 100%;
  max-width: 1140px; /* Largeur maximale d'un container Bootstrap */
  margin: 0 auto;
}

.containerRow {
  display: flex;
  flex-wrap: nowrap;
}

@media (max-width: 768px) {
  .containerRow {
    flex-wrap: wrap;
  }
}

.dropdown-menu {
  padding : 0 !important;
  margin : 0 !important;
}

/* Global rule to ensure no text decoration on hover for dropdown items */
.dropdown-menu a,
.dropdown-menu .dropdown-item {
  text-decoration: none;
  border-radius: 0.25rem;
}

.dropdown-menu a:hover,
.dropdown-menu .dropdown-item:hover {
  text-decoration: none !important;
  background-color: #dee2e6 !important;
}

/* Searchable dropdown styles */

.searchable-select-container {
  position: relative;
  width: 100%;
}

.searchable-select {
  width: 100%;
  padding-right: 30px;
  cursor: pointer;
}

.searchable-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  display: none;
}

.searchable-select-dropdown.show {
  display: block;
}

.searchable-select-search {
  padding: 0.5rem;
  border-bottom: 1px solid #ced4da;
}

.searchable-select-search input {
  width: 100%;
  padding: 0.375rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.searchable-select-options {
  list-style: none;
  padding: 0;
  margin: 0;
}

.searchable-select-option {
  padding: 0.5rem;
  cursor: pointer;
}

.searchable-select-option:hover {
  background-color: #f8f9fa;
}

.searchable-select-option.selected {
  background-color: #e9ecef;
}

/* Project Search Dropdown Styles */
.project-search-container {
  position: relative;
  width: 100%;
}

.project-search-input {
  width: 100%;
  padding-right: 30px;
  cursor: text;
}

.project-search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1050;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.project-search-option {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 0.25rem;
}

.project-search-option:last-child {
  border-bottom: none;
}

.project-search-option i {
  margin-right: 0.5rem;
  width: 1rem;
  text-align: center;
}

.project-search-option:hover,
.project-search-option.selected {
  background-color: #f8f9fa;
}

.project-search-option.search-option i {
  color: #6c757d;
}

.project-search-option.folder-option i {
  color: #ffc107;
}

.project-search-option.no-results {
  color: #6c757d;
  font-style: italic;
  cursor: default;
}

.input-group.no-gap {
  display: flex;
  flex-wrap: nowrap;
  width: 100% !important;
}

.input-group.no-gap .form-control,
.input-group.no-gap .custom-select-container {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-right: 0;
  flex-grow: 1;
}

.input-group.no-gap .btn.no-gap-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: 0;
  border-left: 0;
}

.custom-select-container {
  width: 100%;
}

.input-group.no-gap .custom-select-container .custom-select-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}

.nb-per-page {
  min-width: 70px;
  height: 38px;
  border-radius: 5px;
  position: relative;
  cursor: pointer;
  appearance: none;
  background-image: none;
  padding-right: 25px;
  padding-left: 10px;
  border: 1px solid #dee2e6;
  background-color: #fff !important;
}

.nb-per-page + .dropdown-menu,
.dropdown-menu[aria-labelledby="nb-results-page"],
select#nb-results-page ~ .dropdown-menu {
  border-radius: 0.25rem !important;
  overflow: hidden !important;
  border: 1px solid #999999 !important;
  padding: 0 !important;
  margin: 0 !important;
  min-width: 70px;
}

.pagination-container {
  display: flex;
  align-items: center;
}

.pagination {
  height: 38px;
  display: flex;
  align-items: center;
}

.pagination .page-item .page-link {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 38px;
}

.nb-per-page-container {
  position: relative;
  display: inline-block;
  margin-right: 10px;
}

.nb-per-page-container::after {
  content: "\f078";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #666;
}

/* Grid/List toggle button styles */
.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #dee2e6;
}

.btn-secondary:hover {
  background-color: var(--dark-color);
  border-color: var(--dark-color);
}

.btn-secondary i {
  color: #666;
}

/* Styles for explore-grid layout fix */
#explore-results {
    position: relative;
    overflow-x: hidden !important;
    width: 100%;
    box-sizing: border-box;
}

#explore-grid {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    position: relative;
}

.metadata-copy-popup {
  z-index: 2000;
  position: fixed;
  background: #f8f8f8;
  border: 2px solid #183b6b;
  border-radius: 8px;
  padding: 18px 24px 14px 24px;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 15px;
  color: #222;
  min-width: 240px;
  text-align: left;
  opacity: 1;
  transition: opacity 0.3s;
  max-height: 90vh;
  padding: 1rem;
}

@keyframes fadeInMetadataPopup {
  from { opacity: 0; }
  to { opacity: 1; }
}
@keyframes fadeOutMetadataPopup {
  from { opacity: 1; }
  to { opacity: 0; }
}
.fade-in {
  animation: fadeInMetadataPopup 0.3s;
  opacity: 1;
}
.fade-out {
  animation: fadeOutMetadataPopup 0.3s;
  opacity: 0;
}

.metadata-copy-popup-title {
  font-weight: bold;
  font-size: 1.1em;
  margin-bottom: 6px;
}
.metadata-copy-popup-list {
  margin-bottom: 0;
  padding-left: 20px;
  text-align: left;
  overflow-y: auto;
  max-height: 80vh;
}
.metadata-copy-popup-list li {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}
.metadata-copy-popup-label {
  font-weight: 500;
  display: inline-block;
  width: calc(100% - 21px);
  overflow: hidden;
  text-overflow: ellipsis;
}
.metadata-copy-popup-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: transparent;
  border: none;
  font-size: 1.3em;
  cursor: pointer;
  color: #888;
}

#explore-grid > * {
    min-width: 0;
    box-sizing: border-box;
}

/* Styles for the vitrine list icon button */
.list-icon-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    padding: 0;
    text-decoration: none;
    border-radius: 4px;
    border-color: transparent;
    background-color: transparent;
}

.list-icon-btn {
    margin: 0;
}

#compact-slider-container{
  margin-right: calc(var(--bs-gutter-x) * .5) !important;
}

#grid-slider-container{
  margin-right: calc(var(--bs-gutter-x) * .5) !important;
}

div.mandatory-error-advanced-model{
  display: none;
}

div.mandatory-error-advanced-model.active{
  display: flex;
  color: #242943;
  font-weight: bold;
  margin: auto;
  margin-top: .5rem;
  align-items: baseline;
}

div.mandatory-error-advanced-model.active i{
  margin-right: .5rem;
}

div.mandatory-error-advanced-model.active p{
  margin-bottom: .5rem;
}

input.form-control.mandatory-error-advanced-model{
  border: #242943 solid 1px;
}

form#passport input[type="submit"]{
  color: white;
  background: transparent;
  border: none;
}

form#passport input[type="submit"] + i{
  display: none;
}

form#passport input[type="submit"].loading + i{
  display: inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
  }
}

/* Ensure SVG elements (unico items) are also properly contained */
.list-vitrine-image svg.list-img {
  max-height: 100%;
  max-width: 100%;
  width: auto;
  height: auto;
}

/* Additional safety for any image element within list-vitrine-image container */
.list-vitrine-image img,
.list-vitrine-image svg {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

.form-group.row:has(.actorRow)    div.metadata-content,
.form-group.row:has(.datationRow) div.metadata-content,
.form-group.row:has(.locationRow) div.metadata-content{
  padding-left: 0px;
  padding-right: 0px;
  margin-left: 25px;
  width: calc(58.3333333% - 25px);
}

.actorRow, .datationRow, .locationRow {
  display: flex;
  background-color: #f1f1f1;
  padding: .75rem .5rem;
  border-radius: 5px;
  width: 100%;
}

.actorRow_old, .datationRow_old, .locationRow_old{
  display: none;
}

.actorRow label, .datationRow label, .locationRow label {
  margin-left: .25rem;
}

.actorRow[required=true], .datationRow[required=true], .locationRow[required=true] {
  background-color: #dddddd;
}

.advanced_model_display ul{
  border-left: 2px solid #f1f1f1;
  padding-left: .3rem;
  margin-bottom: .50rem;
}

.advanced_model_display ul:last-child {
  margin-bottom: 0px;
}

.advanced_model_display p{
  margin-bottom: 0;
  padding-left: calc(0.3rem + 2px);
}

.advanced_model_display_value ul{
  border: none;
  padding-left: .3rem;
  margin-bottom: .50rem;
}

.btn.underline:hover{
  text-decoration: underline !important;
}