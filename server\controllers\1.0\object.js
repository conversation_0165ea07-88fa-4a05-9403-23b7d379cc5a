const db = require("../../helpers/db").default;
const responseHelper = require('../../helpers/response')
const { insertFileObject } = require('../../helpers/db_tools');

// ce raisonnement fonctionne tant qu'on a des object attaché à un fichier / file pour le visualiser
// si un object est un object 3D... il faudra modifier les tables pour référencer un fichier 3D et le visualiser autrement
// que par une image (on donne un path pour le visualiser ...)
// Create url endpoint for /object/:root (GET)
exports.getObject = async function(req, res) {

    // TODO : paser en parametre query ou body le id object pour pouvoir utiliser la meme route pour un put/create
    //let oId = parseInt(req.params.object_id)
    let oId = parseInt(req.query.object_id)
    let userId = parseInt(req.query.user_id)
    let root = req.params.root
    let lang = req.query.lang
    //let tableObject = root+'_object'
    //let tableFile = root+'_file'
    //let tableMetadataModel = root+'_metadata_model'

    try {
        // ON Ajoute les infos liés à l'objet sur sa representation 3D => il y a un fichier .3d avec un hash pour lancer le viewer3d
        const querydirect = await db.one('SELECT o.* , ' +
            root+'_get_hash3d( o.id, $1) as hash3d ' + // Retourne maintenant un tableau de tous les hash existant pour un seul objet !!
            // On ajoute les infos sur les droits du user sur ce modèle 3D
            //'CASE WHEN fi.hash IS NOT NULL THEN fi.hash ELSE \'\' END as hash,  ' +
            //'CASE WHEN fi.hash IS NOT NULL THEN fi.id_folder ELSE 0 END as hash_folder  ' +
            'FROM '+root+'_object o ' +
            //'LEFT OUTER JOIN '+root+'_file_object fio ON fio.id_object = o.id ' +
            //'LEFT OUTER JOIN '+root+'_file fi ON fi.id = fio.id_file AND fi.file_ext = \'3d\' ' +
            'WHERE o.id = $2 ', [userId,oId])

        //pour le conservatoire et pft3d (testé OK) et corpus idem pour récupérer les noms des tags de thesaurus (simple),on récupère
        // la colonne name au lier  de short_name car short_name contient parfois un code utilisé avec altag3D pour MainCategory entre autre
        // on prend la version 4 (name au lieu de short_name) de la fonction
        //const object_thes = (root === 'conservatoire3d') ? '_get_objectfull_thes4' : '_get_objectfull_thes3';
        const object_thes = '_get_objectfull_thes4'

        try {
            const query = await db.any('SELECT get_all_metadata_json(\''+lang+'\', $1,\''+root+'\', \'object\') as myquery ',
                [oId])
            try {
                let object = await db.one('SELECT $1:raw', [query[0].myquery, oId])

                try {
                    const querythes = await db.oneOrNone('SELECT '+root+ object_thes +'(\''+lang+'\', $1) as objectthes ',
                        [oId])
                    try {
                        // ATTENTION : no pas passer à la version supérieur (4 ou 5) qui fait l'egalite id_metadata = id_metadata et code = qualifier
                        // ce qui n'est pas vrai pour le conservatoire : on ne renseigne jamais pour le moment id_metadata quand
                        // taggue multi ou thesaurus et on peut avoir un qualifier different du code de metadata pour plus de souplesse
                        // TODO A REVOIR code et qualifier
                        const querymulti = await db.any('SELECT get_info_multi_gen3(\''+lang+'\', $1 , $2 , \'object\' ) as objectmulti ',
                            [oId, root])
                        if (querymulti) {
                            object['multi'] = Object.assign(querymulti)
                        } else {
                            object['multi'] = Object.assign([])
                        }

                        if (querythes) {
                            object['thesaurus'] = Object.assign(querythes)
                        } else {
                            object['thesaurus'] = Object.assign([])
                        }
                        object['direct'] = Object.assign(querydirect)
                        res.status(200).sendData(object)
                    }
                    catch (er) {
                        responseHelper.sendError(500, 'server_error in GET Object thesaurus multi', er, req, res)
                    }
                }
                catch (e) {
                    responseHelper.sendError(500, 'server_error in GET Object thesaurus', e, req, res)
                }
            }
            catch (e) {
                responseHelper.sendError(500, 'server_error in GET Object metadata', e, req, res)
            }
        }

        catch (e) {
            // error
            responseHelper.sendError(500, 'server_error in GET Object', e, req, res)
        }
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error in GET Object directs infos', e, req, res)
    }
}

// Create url endpoint for /object/:root (PUT)
// UPDATE 21 MAI 2021 : la table Object récupère une colonne id_folder pour avoir un ancrage de l'objet sur un folder
exports.createObject = async function(req, res) {

    const root = req.params.root
    const rootDir = req.body.rootDir.replace(/ /g, '')
    const idFolder = req.body.idFolder

    let version = 'V0'
    if (req.body.version) {
        version = req.body.version
    }

    let name = decodeURI(req.body.name)

    try {
        const object = await db.oneOrNone(
            'INSERT INTO ' + root + '_object (name, version, date_integration, root_dir, id_folder )' +
            'VALUES  ($1, $2, now(), $3, $4) RETURNING id',
            [name, version, rootDir, idFolder]
        )
        // console.log(object)
        try {
            const link = await db.none(
                'INSERT INTO '+root+'_folder_object(id_folder, id_object) VALUES ($1, $2)', [idFolder, object.id]
            )
            res.status(201).sendData(object)
        }
        catch (e) {
            responseHelper.sendError(500, 'server_error in create link folder_object ', e, req, res)
        }

    }
    catch (e) {
        responseHelper.sendError(500, 'server_error in create object ', e, req, res)

    }

}

exports.linkObject = function(req, res) {

    console.log(req.body)
    console.log(req.query)
    let reponse = 0
    // Pourquoi attendre 10sec ? pour être sûr que le file a bien été créé juste avant ?
    setTimeout(  () => {
        reponse = insertFileObject(req.body.file,req.body.objectId,req.params.root)
    },  10 * 1000);

    setTimeout(  () => {
        res.status(201).sendData([]  )
    },  11 * 1000);

}

// create url endpoint FOR /object/root DELETE
exports.deleteObject = async function(req, res) {

    let idObj = req.body.idItem
    let branch = req.params.root

    try {
        const del = await db.any(
            'SELECT '+branch+'_delete_object_link($1)', [idObj]
        )
        res.status(200).sendData([])
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error in delete object', e, req, res)
    }
}

/* avec enchainement de tasks : not working ...
db.task(t => {
    return t.one('SELECT name FROM '+ tableMetadataModel + ' WHERE metadata_type = \'object\' limit 1' )
        .then(model => {
            return  t.any('SELECT get_metadata_json(\'fr\', $1, $2, $3, \'file\')', [oId, model, root])
            //console.log(model)
        })
})
    .then(events => {
        console.log()
        //res.status(200).sendData(object)
    })
    .catch(error => {
        responseHelper.sendError(500, 'server_error', error, req, res)
    })
*/


// url endpoint for /itemFromObject/:branche,idObject
exports.getItemsFromObject = async function(req, res) {

    // On récupère le folder "principal" d'un object en remontant par le nom du folder qui est l'information root_dir
    // dans la table des objects
    let branche = req.params.branche
    let idObject = parseInt(req.params.idObject)

    let query = `SELECT o.id,` +
        `fi.name as name,` +
        `fi.id, 'file' as type,` +
        `fi.id_folder as item_folder,` +
        `fi.path,` +
        `fi.id as image_id,` +
        `fi.id_folder as image_folder,` +
        `fi.name as image_filename,` +
        `fi.hash as image_hash,` +
        `fi.file_ext as image_ext,` +
        `CASE WHEN get_viewer_info_gen('${branche}', fi.id_folder, fi.id, 'file') IS NOT NULL THEN get_viewer_info_gen('${branche}', fi.id_folder, fi.id, 'file') ELSE '' END  as viewerinfo` +
        `, CASE WHEN j.id IS NULL THEN 0 ELSE 1 END as json_file` +
        `, ffo.status ` +
        `FROM ${branche}_object AS o ` +
        `INNER JOIN ${branche}_file_object AS fo  ON fo.id_object = o.id ` +
        `INNER JOIN ${branche}_file AS fi  ON fi.id = fo.id_file ` +
        `LEFT OUTER JOIN ${branche}_json AS j  ON j.id_file = fo.id_file AND (status = 0 OR status = 2) ` +  // On récupère l'info pour les fichiers json s'ils ont été importé ou en erreur
        `LEFT OUTER JOIN ${branche}_folder AS ffo  ON ffo.id = fi.id_folder ` +  // On récupère l'info status du folder qui héberge le file pour les droits
        `WHERE o.id = $1 ` +
        `UNION ALL ` +
        `SELECT o.id,` +
        `oi.name as name,` +
        `oi.id, 'object' as type,` +
        `fo.id_folder as item_folder, ` +
        `CASE WHEN fi.id IS NOT NULL THEN fi.path ELSE '' END as path,` +
        `CASE WHEN fi.id IS NOT NULL THEN fi.id ELSE 0 END as image_id,` +
        `CASE WHEN fi.id IS NOT NULL THEN fi.id_folder ELSE 0 END as image_folder, ` +
        `CASE WHEN fi.id IS NOT NULL THEN fi.name ELSE '' END as image_filename ,` +
        `CASE WHEN fi.id IS NOT NULL THEN fi.hash ELSE '' END as image_hash ,` +
        `CASE WHEN fi.id IS NOT NULL THEN fi.file_ext ELSE '' END as image_ext ,` +
        `'' as viewerinfo` +
        `, 0 as json_file ` +
        `, ffo.status ` +
        `FROM ${branche}_object AS o ` +
        `INNER JOIN ${branche}_object_object AS oo ON oo.id_object_ref = o.id ` +
        `INNER JOIN ${branche}_object AS oi ON oi.id = oo.id_object_min ` +
        `INNER JOIN ${branche}_folder_object fo ON fo.id_object = oi.id ` +
        `LEFT OUTER JOIN ${branche}_file fi ON fi.id = oi.id_file_representative ` +   // pour récupérer les infos de l'objet relié
        `LEFT OUTER JOIN ${branche}_folder ffo ON ffo.id = fo.id_folder ` +   // pour récupérer les infos de status du folder de l'objet relié
        `WHERE o.id = $1 ORDER BY name`

    try {
        const itemList = await db.any( query, idObject)
        // success
        res.status(200).sendData(itemList)

    }
    catch(e){
        responseHelper.sendError(500, "error in get items from object ", e, req, res)
    }
}

// url endpoint for /folderFromObject/:root,idObject
exports.getFolderFromObject = async function(req, res) {

    // On récupère le folder "principal" d'un objet en remontant par le nom du folder qui est l'information root_dir
    // dans la table des objects
    let root = req.params.root
    let idObject = parseInt(req.params.idObject)

    try {
        const folder = await db.any(
            'SELECT f.id, count(*) FROM '+root+'_folder f ' +
            'INNER JOIN '+root+'_folder_object fo ON fo.id_folder = f.id WHERE folder_name = ( '+
            //'SELECT id FROM '+root+'_folder  WHERE folder_name = (' +
            ' SELECT root_dir FROM '+root+'_object WHERE id = $1) GROUP BY f.id', idObject)

        res.status(200).sendData(folder[0])

    }
    catch(e){
        responseHelper.sendError(500, "pas de folder pour l'object", e, req, res)
    }
}

// create url endpoint for /objectLinkFolder/:root,:idObject GET
exports.getLinkFolderFromObject = async function(req, res) {

    let idObject = req.params.idObject
    let root = req.params.root


    try {
        const folder = await db.any(
            'SELECT id_folder FROM '+root+'_folder_object  ' +
            'WHERE id_object = $1) ', idObject)

        res.status(200).sendData(folder[0])

    }
    catch(e){
        responseHelper.sendError(500, "pas de folder pour l'object en lien direct", e, req, res)
    }
}


// create url endpoint for /objectLinkFolder/:root,:idObject PUT
// UPDATE DU 21/05/2021 : prise en compte de la nouvelle colonne id_folder de la table _object
exports.createLinkFolderFromObject = async function(req, res) {

    let idF = req.body.idFolder
    let idObject = req.params.idObject
    let root = req.params.root

    try {
        // 1/ UPDATE dans la table object
        const majObject = await db.none('UPDATE  '+root+'_object SET id_folder = $1 ' +
            'WHERE id = $2  ', [ idF,  idObject])
        try {
            // 2/ on fait le lien avecla table de lien
            const folder = await db.none('INSERT INTO '+root+'_folder_object(id_folder, id_object) ' +
                'VALUES($1, $2) ', [ idF,  idObject])
            res.status(201).sendData(idF)
        }
        catch(e){
            responseHelper.sendError(500, "impossible insert into _folder_object", e, req, res)
        }
    }
    catch(e){
        responseHelper.sendError(500, "impossible update id_folder on  _object table", e, req, res)
    }



}

// create url endpoint for /objectLinkFolder/:root,:idObject DELETE
exports.deleteLinkFolderFromObject = async function(req, res) {

    let idF = req.body.idFolder
    let idObject = req.params.idObject
    let root = req.params.root
    console.log(idObject)
    console.log(idF)

    try {
        const folder = await db.none('DELETE FROM '+root+'_folder_object  ' +
            'WHERE id_object = $1 AND id_folder = $2 ', [idObject, idF])

        res.status(200).sendData([])
    }
    catch(e){
        responseHelper.sendError(500, "impossible delete from  _folder_object", e, req, res)
    }


}

// create url endpoint FOR /exploreObj/:root,:idFolder,:lng
// Maintenant, il y a un id_nakala dans la table object directement
exports.getObjectFromFolder = async function(req,res) {

    let root = req.params.root
    let lang = req.params.lang
    let depotName = []
    // to get nakala info when exists
    let nakala = 0,
        model = 'project';

/*    try {

        if (root === 'conservatoire3d') model = 'virtualObject'
        // Get id_metadata from nakala info and depot name (metadata rank 1)  info
        const startQ = await db.any('SELECT id , code , rank from ' + root + '_metadata WHERE id_metadata_model in ' +
            '(SELECT id from ' + root + '_metadata_model where name = $1) ', model)

        //console.log(startQ)
        for (let i = 0; i < startQ.length; i++) {
            // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
            if (startQ[i].code.substring(1) === 'akala') nakala = startQ[i].id
        }


 */
        try {
            const obj = await db.any(
                //'SELECT o.id as id_obj, o.root_dir as foldername, o.name , path, f.id, pnakala.value[1] as nakala   ' +
                'SELECT o.id as id_obj, o.root_dir as foldername, o.name , path, f.id, id_nakala as nakala   ' +
                ' FROM ' + root + '_object o ' +
                'LEFT OUTER JOIN ' + root + '_file f ON f.id = o.id_file_representative ' +
                //' LEFT OUTER JOIN ' + root + '_passport pnakala ON pnakala.id_item = o.id AND pnakala.item_type = \'object\' ' +
                //' AND pnakala.id_metadata =  $1 ' +
                'WHERE o.id_folder = $1 ORDER BY o.id',
                //[nakala, req.params.idFolder])
                [ req.params.idFolder])

            if (root === 'conservatoire3d') {
                try {
                    const dName = await db.oneOrNone('SELECT ' + lang + '_name as depot_name  FROM conservatoire3d_folder f ' +
                        'INNER JOIN conservatoire3d_folder_depot fd ON fd.id_folder = f.id ' +
                        'INNER JOIN conservatoire3d_depot d ON d.id = fd.id_depot ' +
                        'WHERE f.id = $1', req.params.idFolder)

                    depotName = dName
                } catch (e) {
                    responseHelper.sendError(500, "error in exploreObj depot ", e, req, res)
                }
            }

            for (let i = 0; i < obj.length; i++) {
                obj[i]['depotName'] = depotName['depot_name']
            }

            res.status(200).sendData(obj)
        } catch (e) {
            responseHelper.sendError(500, "error in exploreObj folder", e, req, res)
        }

/*    }
    catch (e) {
        responseHelper.sendError(500, "error in exploreObj folder nakala info", e, req, res)
    }

 */
}

// create url endpoint FOR /explore3dObj/:branch,:idUser
// Récupérer tous les objets dont il existe une visualisation 3d en ligne
// pour le CND3D pour commencer
exports.getObjectWith3dinfo = async function(req,res) {

    let idUser = req.params.idUser
    let branch = req.params.branch

    let condition = '',
        order = ''
    let idFolder = 0
    if ((req.query) && (req.query.idFolder)) {
        idFolder = parseInt(req.query.idFolder)
        // récupérer seulement les objets 3D d'un dépôt tout entier: ie tout ce qui est sous le folder en entrée (qui est le répertoire 3DOnline)
        condition = ' AND fo.folder_path <@ get_root_depot_folder_path('+idFolder+', \''+branch+'\')::ltree  '
    }

    let query = 'SELECT  o.id, o.name as object_name, id_nakala as nakala , ' +
        'ffi.id as id_file, fi.path, o.doi, ' +
        'ffi.hash as hash3d, ffi.id_folder as hash_folder,  ' +
        ' \'object\' as item_type ' +
        ', get_folder_access_genV2($1, fo.id, \''+branch+'\') as rights ' +
        ', CASE WHEN fi.id IS NOT NULL THEN fi.id ELSE ffi.id END as show_id   ' +
        'FROM '+branch+'_object o ' +
        'INNER JOIN '+branch+'_file_object fio ON fio.id_object = o.id ' +
        'INNER JOIN '+branch+'_file ffi ON  ffi.id = fio.id_file AND file_ext = \'3d\' ' +
        'INNER JOIN '+branch+ '_folder fo ON fo.id = ffi.id_folder ' +
        'LEFT OUTER JOIN '+branch+'_file fi ON fi.id = o.id_file_representative ' +
        'WHERE o.id IN ' +
        ' (SELECT id_object FROM '+branch+'_file_object  ' +
        '    WHERE id_file IN (SELECT id FROM '+branch+'_file WHERE file_ext = \'3d\') ' +
        ' )'

    order = ' ORDER BY ffi.id desc ' // les derniers modèles 3d entrés en premier ?
    query = query + condition + order
    try {
        const obj = await db.any(query, idUser)

        res.status(200).sendData(obj)
    } catch (e) {
        responseHelper.sendError(500, "error in explore 3D Obj", e, req, res)
    }
}

// create url endpoint FOR /exploreObjV/:root,:idFolder,:lang,:idUser GET
exports.getObjectVirtuelFromFolder = async function(req,res) {

    // On explore chaque objet virtuel d'un dépot et pour lui on va chercher les metadonnes du model virtualObject

    let root = req.params.root
    let lang = req.params.lang
    let idUser = req.params.idUser
    let depotName = []

    let metaquery = 'SELECT get_metadata_json(\''+lang+'\' , $1, \'virtualObject\', \'conservatoire3d\', \'object\') '

    try {

        const obj = await db.any('SELECT o.id as id_obj, o.doi, root_dir as foldername, o.name , f.path, o.id_nakala,  f.id,' +
            'f.id_folder, ' +
            root+ '_get_hash3d( o.id , $1) as hash3d ' + // Prendre en compte la possibilite d'avoir plusieurs representation 3D pour 1 seul objet
            //'CASE WHEN ffi.hash IS NOT NULL THEN ffi.hash ELSE \'\' END as hash,  ' +
            //'CASE WHEN ffi.hash IS NOT NULL THEN ffi.id_folder  ELSE 0 END as hash_folder   ' +
            ' FROM '+root+'_object o ' +
            'LEFT OUTER JOIN '+root+'_file f ON f.id = o.id_file_representative ' +
            //'LEFT OUTER JOIN '+root+'_file_object fio ON fio.id_object = o.id ' +
            //'LEFT OUTER JOIN '+root+'_file ffi ON ffi.id = fio.id_file AND ffi.file_ext = \'3d\'  ' +
            'WHERE o.id IN (SELECT id_object FROM '+root+'_folder_object  where id_folder = $2) ' +
            'ORDER BY o.id ', [idUser,req.params.idFolder])

        if (root === 'conservatoire3d') {
            try {
                const dName = await db.any('SELECT '+lang+'_name as depot_name  FROM conservatoire3d_folder f ' +
                    'INNER JOIN conservatoire3d_folder_depot fd ON fd.id_folder = f.id ' +
                    'INNER JOIN conservatoire3d_depot d ON d.id = fd.id_depot ' +
                    'WHERE f.id = $1', req.params.idFolder)

                depotName = dName
            }
            catch (e) {
                responseHelper.sendError(500, "error in exploreObj depot ", e, req, res)
            }
        }

        for (let i = 0; i < obj.length; i++) {
            if (root === 'conservatoire3d') {
                try {
                    const re = await db.one(
                        'SELECT get_metadata_json(\''+lang+'\' , $1, \'virtualObject\', \'conservatoire3d\', \'object\')',obj[i]['id_obj'])
                    obj[i]['metadata'] = re['get_metadata_json']
                    try {
                        const re2 = await db.oneOrNone('SELECT '+root+'_get_objectfull_thes3(\''+lang+'\', $1) as objectthes ',obj[i]['id_obj'])
                        if (re2) {
                            obj[i]['thesaurus'] = Object.assign(re2)
                        } else {
                            obj[i]['thesaurus'] = []
                        }
                    } catch (e) {
                        console.log('erreur in getting metadata thes from virtual object '+obj[i]['id_obj'])
                    }
                } catch (e) {
                    console.log('erreur in getting metadata from virtual object '+obj[i]['id_obj'])
                }
            }
            obj[i]['depotName'] = depotName[0]['depot_name']
        }

        res.status(200).sendData(obj)
    }
    catch (e) {
        responseHelper.sendError(500, "error in exploreObj folder", e, req, res)
    }
}





// /prepareObject/:idObject,:root'
exports.getPrepareObject = async function(req, res) {

    let root = req.params.root

    try {
        const object = await db.oneOrNone(
            'SELECT id, name as objectname, version, ' +
            'to_char(date_integration, \'YYYY-MM-DD\') as date_integration, get_tag($1,\''+ root +'\' , \'file\') as tag ' +
            'FROM '+root+'_object  WHERE id = $1 ' , req.params.idObject)

        res.status(200).sendData(object)

    }
    catch(e){
        responseHelper.sendError(500, "error in getPrepareObject pas d'objet trouvé", e, req, res)
    }
}

// Get url Endpoint for /ObjectRepreFile/:branche,:idObject GET
exports.getReprFileObject = async function(req, res) {


    res.status(200).sendData([])
}

// Get url Endpoint for /ObjectRepreFile/:branche,:idObject PUT
// On récupère dans la base de données l'id un fichier/file qui correspond au root_dir de l'objet
// root_dir = identité du projet/depot, unique
exports.putReprFileObject = async function(req, res) {

    let branche = req.params.branche
    let idObj = parseInt(req.params.idObject)

    try {
        const object = await db.one(
            'SELECT '+branche+'_update_repr_picture_object_id($1) ', idObj)

        res.status(200).sendData(object)

    }
    catch(e){
        responseHelper.sendError(500, "server error in putReprFileObject ", e, req, res)
    }
}

// Get url Endpoint for /ObjectReprFile/:branche,:idObject PATCH
exports.patchReprFileObject = async function(req, res) {


    res.status(200).sendData([])
}

// Get url Endpoint for /ObjectReprFile/:branche,:idObject DELETE
exports.deleteReprFileObject = async function(req, res) {

    let branche = req.params.branche
    let idObj = parseInt(req.params.idObject)

    try {
        const object = await db.oneOrNone(
            'UPDATE '+branche+'_object SET id_file_representative = NULL WHERE id = $1 ', idObj)
        res.status(200).sendData([])
    }
    catch(e){
        responseHelper.sendError(500, "server error in deleteReprFileObject ", e, req, res)
    }
}


// GET URL Endpoint for /objectId/:branche,:idObject GET
// UPDATE du 21/05/2021 pour prendre en compte la nouvelle colonne id_folder de la table object
exports.getObjectFromId = async function (req, res) {

    let branche = req.params.branche,
        idObj = parseInt(req.params.idObject),
        resu = {}

    let query =''
    if (branche === 'conservatoire3d')  {
        query = 'SELECT o.id as id_object, o.name, o.id_file_representative, o.version, ' +
            'to_char(o.date_integration,\'YYYY\')  as date_integration, o.root_dir, o.id_nakala , ' +
            'fi.path, du.id_user, array_agg(t.id_thes) as deposant, fd.id_depot ,' +
            ' o.id_folder ' +
            'FROM '+branche+'_object o ' +
            //'INNER JOIN '+branche+'_folder_object fo ON fo.id_object = o.id ' +
            'INNER JOIN '+branche+'_folder_depot fd ON fd.id_folder = o.id_folder ' +
            'INNER JOIN '+branche+'_user_depot du ON du.id_depot = fd.id_depot ' +
            'INNER JOIN '+branche+'_thesaurus_multi_item it ON it.id_item = o.id_folder AND thesaurus = \'deposant\' AND item_type = \'folder\' ' +
            'INNER JOIN '+branche+'_thesaurus_multi t ON t.thesaurus_path = it.thes_path AND t.thesaurus = \'deposant\'  ' +
            'INNER JOIN archeogrid_entity e ON e.id_thes  = t.id_thes ' +
            ' LEFT OUTER JOIN '+branche+'_file fi ON fi.id = o.id_file_representative ' +
            ' WHERE o.id = $1 GROUP BY  o.id, o.name, o.id_file_representative, o.version, o.date_integration, ' +
            'o.root_dir, o.id_nakala , fi.path, du.id_user, fd.id_depot'
    } else {
        query = 'SELECT o.id as id_object, o.name, o.id_file_representative, o.version, ' +
            'to_char(o.date_integration,\'YYYY-MM-DD\')  as date_integration, ' +
            //' o.date_integration , ' +
            'o.root_dir, o.id_nakala , o.id_folder, ' +
            'fi.path ' +
            ', CASE WHEN fi.id IS NOT NULL THEN fi.id_folder ELSE 0 END as id_folder_obj ' +
            //', du.id_user, array_agg(t.id_thes) as deposant, fd.id_depot  ' +
            'FROM '+branche+'_object o ' +
            //'INNER JOIN '+branche+'_folder_object fo ON fo.id_object = o.id ' +
            //'INNER JOIN '+branche+'_folder_depot fd ON fd.id_folder = fo.id_folder ' +
            //'INNER JOIN '+branche+'_user_depot du ON du.id_depot = fd.id_depot ' +
            //'INNER JOIN '+branche+'_thesaurus_multi_item it ON it.id_item = fo.id_folder AND thesaurus = \'deposant\' AND item_type = \'folder\' ' +
            //'INNER JOIN '+branche+'_thesaurus_multi t ON t.thesaurus_path = it.thes_path AND t.thesaurus = \'deposant\'  ' +
            //'INNER JOIN archeogrid_entity e ON e.id_thes  = t.id_thes ' +
            ' LEFT OUTER JOIN '+branche+'_file fi ON fi.id = o.id_file_representative ' +
            ' WHERE o.id = $1 GROUP BY  o.id, o.name, o.id_file_representative, o.version, o.date_integration, ' +
            'o.root_dir, o.id_nakala , fi.path , id_folder_obj, o.id_folder '
    }

    try {
        const object = await db.oneOrNone( query, idObj )
        if (object) resu = object
        res.status(200).sendData(resu)
    }
    catch (e) {
        responseHelper.sendError(500, "server error in get object from Id", e, req, res )
    }
}

// GET URL Endpoint for /objectId/:branche,:idObject PATCH
exports.changeObjectValue = async function (req, res) {

    let branche = req.params.branche,
        idObj = parseInt(req.params.idObject)

    let col = req.body.col,
        val = req.body.val

    try {
        const object = await db.one(
            'SELECT * from '+branche+'_object WHERE id = $1', idObj
        )
        // L'objet existe on peut continuer
        try {
            const updateObject = await db.oneOrNone('UPDATE '+branche+'_object SET '+ col +' =  $1 ' +
                'WHERE id = $2', [val, idObj])
            res.status(201).sendData([])
        }
        catch (e) {
            responseHelper.sendError(500, "server error in patch object from Id UPDATE", e, req, res)
        }

    }
    catch (e) {
        responseHelper.sendError(500, "server error in patch object from Id SELECT object", e, req, res)
    }
}

// get url  endpoint for /objectViewer/:branche,:idObject GET
exports.getObjectViewerFromId = async function (req, res) {

    // dans toutes les métadonnées de l'objet
    // ou du fichier qui est son représentative_file (??temporaorement pour les premiers objets claveau) ce qui n'est plus vraissemblable
    // on recherche une métadonnée dont la  fonction est "viewer3D" : dans ce cas, la valeur retournée est un lien vers une page  où est le viewer
    let branche = req.params.branche
    let query = 'SELECT p.value[1], ' +
        'CASE WHEN fi.id IS NOT NULL THEN fi.path ELSE \'\' END as path, ' +
        'CASE WHEN fi.id IS NOT NULL THEN fi.id  ELSE 0 END as id,  ' +
        'CASE WHEN fi.id IS NOT NULL THEN fi.id_folder ELSE 0 END as id_folder  ' +
        ' FROM '+branche+'_passport p ' +
        'INNER JOIN '+branche+'_object o ON o.id = p.id_item AND item_type =  \'object\'  ' +
        'LEFT OUTER JOIN '+branche+'_file  fi ON fi.id = o.id_file_representative ' +
        'WHERE o.id = $1  ' +
        'AND p.id_metadata in (SELECT id FROM '+branche+'_metadata WHERE function = \'viewer3D\' ) ' +
        //'UNION ALL ' +
        //'SELECT value FROM '+branche+'_passport '+
        //'WHERE id_item in (SELECT id_file_representative FROM '+branche+'_object WHERE id = $1 ) ' +
        //'AND item_type = \'file\'  ' +
        //'AND id_metadata in (SELECT id FROM '+branche+'_metadata WHERE function = \'viewer3D\' )' +
        'UNION ALL ' +
        'SELECT p.value[1], ' +
        'fi.path as path ,' +
        'fi.id ,' +
        'fi.id_folder ' +
        'FROM '+branche+'_passport p ' +
        'INNER JOIN '+branche+'_file_object fo ON fo.id_file = p.id_item AND item_type =  \'file\'  ' +
        'INNER JOIN '+branche+'_file fi ON fi.id = fo.id_file ' +
        'WHERE fo.id_object = $1  ' +
        'AND p.id_metadata in (SELECT id FROM '+branche+'_metadata WHERE function = \'viewer3D\' ) '


    let resu = []
    // on retourne un tableau avec tous les viewer (avant qu'on intègre également les path)
    try {
        const viewer = await db.any(query, req.params.idObject)
        if (viewer) {
            for (let i = 0; i < viewer.length; i++) {
                if (viewer[i]['value']) {
                    for (let j = 0; j < viewer[i]['value'].length; j++ ) {
                        resu.push(viewer[i]['value'][j])
                    }
                }
            }
        }
        res.status(200).sendData(viewer)
    }
    catch (e) {
        responseHelper.sendError(500, "server error in get viewer from object Id", e, req, res )
    }

}



// GET URL Endpoint for /objectsFolder/:branche,:idFolder
// Récuperer tous les objets d'un folder root => D'un projet
// pour pouvoir les proposer comme objet de référence à qui relier tous les items d'une sélection
// 21/05/2021 MODIF pour prendre en compte la nouvelle colonne id_folder
exports.getObjectsFromidFolder = async function (req, res) {

    let branche = req.params.branche,
        idFolder =req.params.idFolder

    let query = 'SELECT o.id, id_file_representative, o.name, fo.name as virtual_name, '+branche+'_get_real_virtual_path(fo.id) as completename ' +
        //'FROM '+branche+'_object o INNER JOIN '+branche+'_folder_object fo ON fo.id_object = o.id ' +
        'FROM '+branche+'_object o ' +
        'INNER JOIN '+branche+'_folder fo ON fo.id = o.id_folder ' +
        'LEFT OUTER JOIN '+branche+'_file fi ON fi.id = o.id_file_representative ' +
        ' WHERE o.id_folder IN (SELECT id FROM  '+branche+'_folder WHERE folder_path <@ \''+idFolder+'\' ) ' +
        'ORDER BY fo.folder_path, o.name '

    try {
        const objects = await db.any(query )
        res.status(200).sendData(objects)
    }
    catch (e) {
        responseHelper.sendError(500, "server error in GET objects from idFolder", e, req, res)
    }
}

// Attach item to (virtual) obbject
// Create URL ENDPOINT for /virtualObjectLink/:branche,:idUser,:idObject PUT
// TODO : prendre en compte le type de document (object file folder)
exports.createVirtualObjectLink = async function (req, res) {

    let branche = req.params.branche
    let idFolder = parseInt(req.body.idFolder) // c'est le root folder
    let idUser = req.body.idUser
    let virtualObject = parseInt(req.params.idObject)
    //On récupère un id pour un virtualObject et on y attache la sélection en cours pour le user donné pour un
    // root folder donné (on travail par projet / depot)
    // on attache d'abord les items de type file
    try {
        let query = 'INSERT INTO ' + branche + '_file_object(id_file, id_object) ' +
            'SELECT id_item , $1 from ' + branche + '_favorite_item WHERE id_user = $2 ' +
            'AND item_type = \'file\' AND id_item  in (SELECT id from ' + branche + '_file WHERE id_folder IN (' +
            'SELECT id FROM ' + branche + '_folder WHERE folder_path <@ \'' + idFolder + '\')) '

        const addVirtualFile = await db.any(query, [virtualObject, idUser])

        // Ensuite les objects
        try {
            const addVirtualobject = await db.any('INSERT INTO ' + branche + '_object_object(id_object_ref, id_object_min) ' +
                'SELECT $1, id_item from ' + branche + '_favorite_item WHERE id_user = $2 ' +
                'AND item_type = \'object\' AND id_item  in (SELECT o.id from ' + branche + '_object o ' +
                'INNER JOIN ' + branche + '_folder_object fo ON fo.id_object = o.id WHERE fo.id_folder IN (' +
                'SELECT id FROM ' + branche + '_folder WHERE folder_path <@ \'' + idFolder + '\')) ' +
                'UNION ALL ' +
                'SELECT id_item, $1 from ' + branche + '_favorite_item WHERE id_user = $2 ' +
                'AND item_type = \'object\' AND id_item  in (SELECT o.id from ' + branche + '_object o ' +
                'INNER JOIN ' + branche + '_folder_object fo ON fo.id_object = o.id WHERE fo.id_folder IN (' +
                'SELECT id FROM ' + branche + '_folder WHERE folder_path <@ \'' + idFolder + '\')) ',
                [virtualObject, idUser])

            res.status(201).sendData([])
        } catch (e) {
            responseHelper.sendError(500, 'server_error IN create link object virtualObject', e, req, res)
        }

    } catch (e) {
        responseHelper.sendError(500, 'server_error IN create link file virtualObject', e, req, res)
    }
}

//create url endpoint for /objectLinkUser/:branch,:idObject GET
exports.getLinkObjectUser = async function(req, res) {

    let branch = req.params.branch
    let idObject = parseInt(req.params.idObject)

    try {
        const linkUser = await db.oneOrNone('SELECT array_agg(id_user) as linked FROM '+branch+'_user_object ' +
            'WHERE id_object = $1 ', [ idObject]
        )
        res.status(201).sendData(linkUser['linked'])
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error in get link user_object ', e, req, res)
    }

}

//create url endpoint for /objectLinkUser/:branch,:idObject PUT
exports.linkUserObject = async function(req, res) {

    let branch = req.params.branch
    let idUser = parseInt(req.body.idUser)
    let idObject = parseInt(req.params.idObject)

    try {
        const linkUser = await db.none(
            'INSERT INTO '+branch+'_user_object(id_user, id_object) VALUES ($1, $2)', [idUser, idObject]
        )
        res.status(201).sendData([])
    }
    catch (e) {
        responseHelper.sendError(500, 'server_error in create link user_object ', e, req, res)
    }
}

// create url endpoint for /objectLinkUser/:branch,:idObject DELETE
exports.deleteLinkUserFromObject = async function(req, res) {

    let idU = req.body.idUser
    let idObject = req.params.idObject
    let branch = req.params.branch

    try {
        const userDel = await db.none('DELETE FROM '+branch+'_user_object  ' +
            'WHERE id_object = $1 AND id_user = $2 ', [idObject, idU])

        res.status(200).sendData([])
    }
    catch(e){
        responseHelper.sendError(500, "impossible delete from  _user_object", e, req, res)
    }
}

// Update info Object add id_file_representative to  object
// Create URL ENDPOINT for /objectIllustration/:branche,:idUser,:idObject PUT
exports.createObjectIllustration = async function (req, res) {

    let branche = req.params.branche
    let idFile = parseInt(req.body.idFile)
    let idObj = parseInt(req.params.idObject)
    //let idUser = req.body.idUser
    //let idFolder = parseInt(req.body.rootFolder) // c'est le root folder

    let query = 'UPDATE ' + branche + '_object SET id_file_representative = $1 WHERE id = $2 '
    // il ne doit y en avoir qu'une mais au cas où il y en ait plusieurs, on prend le max
        /*
            'SET id_file_representative = ' +
        '(SELECT max(id_item) FROM ' + branche + '_favorite_item ' +
        'WHERE id_user = $1  AND item_type = \'file\' ' +
        ' AND id_item  in (SELECT i.id from ' + branche + '_file i ' +
        ' WHERE i.id_folder IN (' +
        'SELECT id FROM ' + branche + '_folder WHERE folder_path <@ \'' + idFolder + '\'))) WHERE id = $2 '
        */

    try {
        const addVirtualobject = await db.any(query,   [idFile, idObj])

        res.status(201).sendData([])
    } catch (e) {
        responseHelper.sendError(500, 'server_error IN create illustration for object', e, req, res)
    }
}

exports.getFirstMetadata = async function(req, res) {
    const { branch, item_id, item_type } = req.params;
    const { model } = req.query;

    try {
        const result = await db.oneOrNone(
            `SELECT get_first_metadata_anymodel($1, $2, $3, $4) as title`,
            [parseInt(item_id, 10), branch, item_type, model]
        );
        res.status(200).sendData(result);
    } catch (e) {
        responseHelper.sendError(500, "server error in getFirstMetadata", e, req, res);
    }
};
