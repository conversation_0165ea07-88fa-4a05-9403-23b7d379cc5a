<script>
    window.onload = function () {
        const projectId = '<%= projectId %>';
        const id_thes = <%= data['id_thes'] %>;
        const viewType = '<%= type %>';

        const thesaurus = <% if (typeof thesaurus !== 'undefined' && thesaurus) { %> '<%= thesaurus %>' <% } else { %> '' <% } %>;
        const thesaurus_path = <% if (typeof data !== 'undefined' && data && typeof data['thesaurus_path'] !== 'undefined' && data['thesaurus_path']) { %> '<%= data['thesaurus_path'] %>' <% } else { %> '' <% } %>;

        if (viewType === 'pactols') {
            exploreThesaurusPactols(projectId, thesaurus, id_thes, thesaurus_path);
        } else if (viewType === 'pactolsgeo') {
            exploreThesaurusPactolsGeo(projectId, id_thes);
        } else if (viewType === 'simple') {
            exploreThesaurus(projectId, thesaurus, id_thes, thesaurus_path, 'simple');
        } else if (viewType === 'multi') {
            exploreThesaurus(projectId, thesaurus, id_thes, thesaurus_path, 'multi');
        }

        const saveSelectionButton = document.getElementById('save-selection');
        if (saveSelectionButton) {
            saveSelectionButton.addEventListener('click', function() {
                if (typeof registerSelection === 'function') {
                    registerSelection('<%= user.id %>', '<%= projectId %>', '<%= lng %>');
                } else {
                    console.error('registerSelection function is not defined. Ensure selectionVitrine.js is loaded.');
                }
            });
        } else {
            console.warn('Button with id="save-selection" not found. Ensure it exists in the included exploreVitrine partial.');
        }
    }
</script>

<script>
    window.i18n = {
        exploreRandomNoResult: "<%= __('exploreRandomNoResult') %>",
        noResult: "<%= __('noResult') %>",
        errorOccured: "<%= __('errorOccured') %>",
        emptyResponse: "<%= __('emptyResponse') %>",
        nbResultsText: "<%= __('nbResultsText') %>"
    };
</script>

<%- include('utils/title-content', { home: "home" }) %>

<!-- H3 Title Structure -->
<H3>Concept "<a href="<%=data['identifier']%>" class="fakelink"
       title="<%=__('see')%> <%=__('concept')%> <%=__('in')%> <%=__('thesaurus')%>"
       style="color: #0a6aa1"><%= conceptName %></a>"
    <%=__('for')%> <a href="/projectv/<%= rootF%>" class="fakelink"
        style="color: #0a6aa1"><% if (projectName.length ) {%>"<%= projectName%>"<%} else {%><%= __('project')%><%}%></a>
    :
    <span id="nb-results" class="mb-1"></span>
</H3>

<!-- Container for exploreVitrine -->
<div class="container-fluid">
    <%- include('explore/exploreVitrine') %>
</div>

<!-- Script include -->
<script src="/js/selectionVitrine.js"></script>
<script src="/js/mobile-vitrine.js"></script>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="project-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive"
         aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<!-- Toast initialization script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toastElement = document.getElementById("project-toast");
        if (toastElement && typeof bootstrap !== 'undefined') {
            bootstrap.Toast.getOrCreateInstance(toastElement);
        }

        // Scroll-to-top button logic
        if (typeof window.createScrollToTopButton === 'function') {
            window.createScrollToTopButton();
            // Initial check for button visibility
            if (typeof window.updateScrollToTopButtonVisibility === 'function') {
                window.updateScrollToTopButtonVisibility();
            }
        } else {
            console.error('createScrollToTopButton function is not defined. Ensure mobile-vitrine.js is loaded and the function is exposed.');
        }

        // Add scroll listener to update button visibility
        window.addEventListener('scroll', function() {
            if (typeof window.updateScrollToTopButtonVisibility === 'function') {
                window.updateScrollToTopButtonVisibility();
            }
        });
    });
</script>