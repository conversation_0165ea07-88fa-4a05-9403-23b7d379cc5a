<%- include('utils/title-content', { home: "home" }) %>

<ul class="nav nav-tabs nav-fill mb-3" id="profil-tab" role="tablist">
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="edituserid-tab" data-toggle="tab" href="#edituserid" role="tab"
           aria-controls="edituserid" aria-selected="false"><%=__('edit') %> <%=__('user') %></a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="searches-tab" data-toggle="tab" href="#searches" role="tab" aria-controls="searches"
           aria-selected="false"><%=__('savedSearches')%></a>
    </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade container" id="edituserid" role="tabpanel" aria-labelledby="edituserid-tab">
        <% if (success) { %>
        <div class="container alert alert-success"><%=__('success')%></div>
        <% } else { %>
        <% if (locals.flash && locals.flash.error) { %>
        <div class="alert alert-danger ">
            <%= locals.flash.error %>
        </div>
        <% } %>
        <form class="form-horizontal" id="adduser" action="/profil" method="post">

            <%  for (item in user ) { %>
            <% if (item === 'id') { %>
            <input type="hidden" value="<%= user[item]%>" name="userSelect" id="userSelect" />
            <% } else if (item === 'password') { %>
            <div class="form-group row">

                <label for="<%= item %>" class="col-4 col-form-label"><%=__('password') %></label>
                <div class="col-8">
                    <%# on fait la distinction entre les ancien user avec des mots de passe court et les nouveaux avec 8 min %>
                    <% if (user['id'] > 199 )  { %>
                    <input type="password" class="form-control" name="password" id="password" minlength="8"
                           placeholder="<%=__('8charmin')%>">
                    <% } else { %>
                    <input type="password" class="form-control" name="password" id="password"
                           placeholder="<%=__('8charmin')%>">
                    <% }%>
                </div>
            </div>
            <div class="form-group row">

                <label for="confirmpassword" class="col-4 col-form-label"><%=__('confirmpassword') %></label>
                <div class="col-8">
                    <%# on fait la distinction entre les ancien user avec des mots de passe court et les nouveaux avec 8 min %>
                    <% if (user['id'] > 199 )  { %>
                    <input type="password" class="form-control" name="confirmpassword" id="confirmpassword"
                           minlength="8" placeholder="<%=__('8charmin')%>">
                    <% } else { %>
                    <input type="password" class="form-control" name="confirmpassword" id="confirmpassword"
                           placeholder="<%=__('8charmin')%>">
                    <% }%>
                </div>
            </div>
            <% } else if (item === 'mail_address') { %>
            <div class="form-group row">

                <label for="<%= item %>" class="col-4 col-form-label"><%=__('mail') %></label>
                <div class="col-8">
                    <input type="email" class="form-control" value="<%= user[item]%>" name="<%= item %>"
                           id="<%= item %>" required />
                </div>
            </div>

            <% } else if (item === 'orcid') { %>
            <div class="form-group row">

                <label for="<%= item %>" class="col-4 col-form-label">ORCID</label>
                <div class="col-8">
                    <input type="text" class="form-control" placeholder="XXXX-XXXX-XXXX-XXXX" value="<%= user[item]%>"
                           name="<%= item %>" id="<%= item %>" />
                </div>
            </div>

            <% } else if (item === 'username') { %>
            <div class="form-group row">

                <label for="<%= item %>" class="col-4 col-form-label"><%=__('username') %></label>
                <div class="col-8">
                    <input type="text" class="form-control" value="<%= user[item]%>" name="<%= item %>" id="<%= item %>"
                           required />
                </div>
            </div>
            <% } else if (item === 'other_identifier') { %>
            <div class="form-group row">
                <label for="<%= item %>" class="col-4 col-form-label"><%=__('identifier_other') %>
                    <a href="#" data-toggle="modal"
                       title="<%=__('identifier_other_comment')%><br>https://www.idref.fr/255852127"
                       data-target="#infoModel">
                        <i class="fas fa-info-circle"></i>
                    </a>
                </label>
                <div class="col-8">
                    <input type="text" class="form-control" placeholder="http..." value="<%= user[item]%>"
                           name="<%= item %>" id="<%= item %>" />
                </div>
            </div>
            <% } else if (item === 'mode_listing') { %>
            <div class="form-group row">
                <label for="<%= item %>" class="col-4 col-form-label"><%=__('displayMode') %></label>
                <div class="col-8 checkbox">
                    <div class="checkbox"> <%# si rien n'est précisé, GRID est coché par défaut%>
                        <input type="radio" name="mode_listing" value="tabloide" id="grid"
                               <% if ((user[item] === 'tabloide') || (user[item] === '')) {%>checked<% } %>>
                        <label for="grid" class="col-sm-4 col-form-label"><%=__('displayGrid')%> <i
                               class="fas fa-th-large fa-lg"></i></label>
                    </div>
                    <div>
                        <input type="radio" name="mode_listing" value="list" id="list">
                        <label for="list" class="col-sm-4 col-form-label"><%=__('displayList')%> <i
                               class="fas fa-list-ul fa-lg"></i></label>
                    </div>

                </div>
            </div>
            <% } %>
            <% } %>
            <div class="form-group row">

                <label for="signature" class="col-4 col-form-label">Signature <a href="#" title="<%=__('signature')%>"
                       style="text-decoration: none;"><i class="fas fa-info-circle"></i></a></label>
                <div class="col-8">
                    <input type="text" class="form-control" value="<%= data['signature']%>" name="signature"
                           id="signature" />
                </div>
            </div>

            <% if (entity_id) { %>
            <div class="form-group row">

                <label for="entity" class="col-4 col-form-label"><%=__('userEntity')%></label>
                <div class="col-8">
                    <a href="/editEntity,<%= entity_id%>" class="btn btn-outline-primary"
                       title="Voir/Modifier l'entité"><%= entity %></a>
                </div>
            </div>
            <% } %>

            <div class="form-group" style="margin: 1em;">
                <div class="offset-4 col-7">
                    <input class="btn btn-vitrine-primary" type="submit" value="<%=__('submit') %>" />
                    &nbsp;<button type="button" class="btn btn-vitrine-secondary" onclick="goBackSkippingProfile()"><%=__('cancel') %></button>
                </div>
            </div>

        </form>
        <% if (pass) { %>
        <div class="container alert alert-danger"><%=__('signupbadpassword')%></div>
        <% } else if (method == 'post') { %>
        <div class="container alert alert-danger"><%=__('error')%></div>
        <% }%>
        <% } %>
    </div>

    <div class="tab-pane fade " id="searches" role="tabpanel" aria-labelledby="searches-tab">
        <% if ( searches.length > 0 ) { %>
        <%- include( 'search/savedSearches.ejs', { searches: searches } ) %>
        <% } %>
    </div>
</div>

<div class="modal" tabindex="-1" role="dialog" id="infoModel">
    <div class="modal-dialog" role="document" id="modalinfo" style="height: 20vh!important;">
        <div class="modal-content" style="height: 20vh!important;">
            <div class="modal-body">
                <%=__('identifier_other_comment')%><br>
                https://www.idref.fr/255852127
            </div>
        </div>
    </div>
</div>

<script>
    function goBackSkippingProfile() {
        // Check if the referrer exists and doesn't start with "/profile"
        if (document.referrer && !document.referrer.includes('/profile')) {
            window.location.href = document.referrer;
        } else {
            // If referrer is a profile page or doesn't exist, go to a safe default
            // You can change this to any default page you prefer
            window.location.href = '/';
        }
    }

    $(function () {
        var hash = window.location.hash;
        if (hash) {
            $('ul.nav a[href="' + hash + '"]').tab('show');
        } else {
            $('ul.nav a[href="#edituserid"]').tab('show');
        }

        $('.nav-tabs a').click(function (e) {
            $(this).tab('show');
            window.location.hash = this.hash;
        });
    });
</script>