.selection-dropdown {
  position: relative;
  display: inline-block;
}

.selection-dropdown .dropdown-container {
  height: 38px;
  padding: 0.45rem 0.75rem 0.45rem 0.45rem;
  border: 1px solid #d9d9d9;
  border-radius: 0.25rem;
  background-color: #fff;
  min-width: 70px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.selection-dropdown .dropdown-container:hover {
  background-color: #f8f9fa;
}

.selection-dropdown .form-check {
  margin: 0;
  padding: 0;
}

.selection-dropdown .form-check-input {
  margin: 0;
  cursor: pointer;
  width: 1.5em;
  height: 1.5em;
}

/* Gmail-style indeterminate checkbox */
.selection-dropdown .form-check-input:indeterminate {
  background-color: #0d6efd;
  border-color: #0d6efd;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

.selection-dropdown .selection-count {
  font-size: 1em;
  white-space: nowrap;
}

.dropdown-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-toggle-icon .fas {
  color: #666;
  font-size: 1em;
}

.selection-dropdown-menu {
  min-width: 150px;
  padding: 0;
  margin: 0;
  border: 1px solid #999999;
  border-radius: 0.25rem;
}

.selection-dropdown-menu .dropdown-item {
  padding: 0.5rem 1rem;
  text-decoration: none;
  border-radius: 0.25rem;
}

.selection-dropdown-menu .dropdown-item:hover {
  text-decoration: none !important;
}

.selection-dropdown-menu .dropdown-item:active {
  background-color: #f8f9fa !important;
  color: inherit !important;
}
