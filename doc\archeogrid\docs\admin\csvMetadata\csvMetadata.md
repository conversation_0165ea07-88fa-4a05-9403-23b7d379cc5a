---
sidebar_position: 1
---

# CSV Metadata

Set of features allowing to introduce new tables and metadata models within ArcheoGRID via a CSV file. This set of features is available for all ArcheoGRID branches (pft3d, cnd3d and corpus).

There's three main features:
- Create a table within the database from the columns of a CSV file.
- Create a metadata model from the columns of a CSV file.
- Ingest the data of the CSV into the table and/or the metadata model.

![](menuCsvMetadataEN.png) 

To be fully useable the three steps above must be executed in order (table creation -> model creation -> CSV ingestion). To access the different steps for a project you must have permissions on the project.

> **Note**: In order to extract columns from a CSV file, it must have an header.

The first column of the CSV file must be the name of the item that other columns describes. An item can be a file, an object or a folder. The csv files must be available in a folder called 'metadata' at the root of the project.
