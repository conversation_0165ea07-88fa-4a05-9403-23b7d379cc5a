import { type Request, Router } from "express";
import as<PERSON><PERSON><PERSON><PERSON> from "express-async-handler";

import { archeogrid_client } from "../tools/request";
import { branchConfig } from "../tools/globals";
import {
  deleteMetadataModel,
  getAddMetadataModel,
  getEditMetadataModel,
  getEditMetadataModelSingle,
  postAddMetadataModel,
  postEditMetadataModel,
  postEditMetadataModelSingle,
} from "./admin";

const scribe = Router();

// adminScribeSessionChecker
scribe.use((req, res, next) => {
  if (req.isAuthenticated()) {
    if (req.user.user_status === "scribe" || req.user.user_status === "admin") next();
    else {
      req.session.returnTo = req.originalUrl;
      res.redirect("/login");
    }
  } else {
    req.session.returnTo = req.originalUrl;
    res.redirect("/login");
  }
});

// ingestion json en table
scribe.route("/json").post(
  asyncHandler(
    async (
      req: Request<
        unknown,
        unknown,
        { rootproj: string; idFolder?: string; idFile: string; folderName?: string }
      >,
      res,
    ) => {
      if (!req.user) throw new Error();
      const { rootproj, idFolder, idFile } = req.body;
      const layoutUser =
        req.user.user_status === "scribe" ? "admin/layout_dashboard" : "admin/layout_admin";

      if (!idFolder) {
        // On n'a pas encore choisi le projet / le dépôt
        const paramRequest =
          req.user.user_status === "admin" ? "projectsRoot" : "limitedProjectsRoot";
        const root_folder = await req.fetchApi(`/api/${paramRequest}/${rootproj}`, {
          userId: req.user.id.toString(),
        });
        res.render("admin/json", {
          rootproj: rootproj,
          branche: "",
          listproject: root_folder,
          listfiles: "",
          idFolder: "",
          folderName: "",
          message: "",
          title: req.url,
          method: req.method,
          layout: layoutUser,
        });
        return;
      }

      const folderName = req.body.folderName ?? idFolder.split("##")[1];
      if (idFile) {
        const json_data = await req.fetchApi(
          `/api/ingestJSON/${req.body.rootproj},${req.body.idFolder}`,
          { idFile: req.body.idFile },
          "PUT",
        );
        res.render("admin/json", {
          rootproj: rootproj,
          branche: "",
          user: req.user,
          listproject: "",
          listfiles: json_data,
          idFolder: req.body.idFolder,
          folderName: folderName,
          idFile: req.body.idFile,
          message: "JSON importé",
          title: req.url,
          method: req.method,
          layout: layoutUser,
        });
        return;
      }

      const json_data = await req.fetchApi(
        `/api/ingestJSON/${req.body.rootproj},${req.body.idFolder}`,
      );
      res.render("admin/json", {
        rootproj: rootproj,
        branche: "",
        listproject: "",
        listfiles: json_data,
        idFolder: req.body.idFolder,
        folderName: folderName,
        idFile: "",
        message: "",
        title: req.url,
        method: req.method,
        layout: layoutUser,
      });
    },
  ),
);

/** gestion des droits pour user scribe */
scribe.get(
  "/dashboard",
  asyncHandler(async (req, res) => {
    if (!req.user) throw new Error();
    const root_folder =
      req.user.user_status === "admin"
        ? await archeogrid_client.projects.projectsRootList.query({
            branch: branchConfig,
            user_id: req.user.id,
          })
        : await archeogrid_client.projects.limitedProjectsRoot.query({
            branch: branchConfig,
            user_id: req.user.id,
          });
    const message =
      req.user.user_status === "scribe" && !root_folder.length
        ? `${res.__("askproject")} !`
        : "";
    res.render("admin/dashboard", {
      message: message,
      user: req.user,
      title: req.url,
      layout: "admin/layout_dashboard",
    });
  }),
);

scribe.route("/addMetadataModel").get(getAddMetadataModel).post(postAddMetadataModel);

scribe.route("/editMetadataModel").get(getEditMetadataModel).post(postEditMetadataModel);

scribe
  .route("/editMetadataModel/:branch/:model")
  .get(getEditMetadataModelSingle)
  .post(postEditMetadataModelSingle);

scribe.delete("/deleteMetadataModel/:branch/:model", deleteMetadataModel);

export default scribe;
