:root {
  /* BASE COLORS */
  --dark-color: #6e1111;
  --primary-color: #9a1818;
  --primary-color-rgb: 153, 25, 24;
  --primary-light-color: #bb1e1b;
  --secondary-color: rgb(155, 91, 43);
  --secondary-color-rgb: 155, 91, 43;
  --action-color: #EB5B2B;
  --highlight-color: #ffdec0;
  --light-color: #fff9f4;
  --item-color: #F8F8F8;

  /* LINK COLORS */
  --link-color: rgba(255, 255, 255, 0.5);
  --link-hover-color: rgba(255, 255, 255, 0.75);
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a.active {
  color: var(--action-color);
}

a:hover {
    color: var(--action-color);
}

.nav3dicons:hover {
    background-color: var(--primary-light-color) !important;
  }

.navbar-inverse {
    background: var(--primary-color) !important;
}

.nav-link {
    color: var(--link-color);
}.nav-link:hover {
    color: var(--link-hover-color);
}

#brand strong {
    background-color: #ffffff;
    color: var(--secondary-color);
    border: 0;
    display: inline-block;
    font-size: 0.8em;
    height: inherit;
    line-height: 1.65em;
    padding: 0 0.125em 0 0.375em;
}

/* contanier map + item */
.map {
    height: 80vh;
    background-color: var(--secondary-color);
}

.marker.is-active {
    z-index: 300;
    background-color: var(--action-color);
    color: inherit;
    box-shadow: none;
    text-align: inherit;
}

.marker:hover {
    z-index: 300;
    color: inherit;
    box-shadow: none;
    text-align: inherit;
}

.marker.is-active::after {
    border-top-color: var(--action-color);
}

.folder-selected,
.folder-selected-hover {
    border-radius: 5px;
}

.folder-hover {
    border-radius: 5px;
}

#btn-archeogrid-info {
    background-color: var(--secondary-color);
    border-color: var(--primary-color);
    color: white;
}

/* bouton changer de page sur explore */
.page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

.vitrine-details h4,
.vitrine-details h5,
.vitrine-details strong {
  color: var(--primary-color);
}

strong {
  color: var(--primary-color);
}

/* Project header for corpus theme */
.project-header {
  color: var(--primary-color);
}

.project-name {
  color: var(--primary-color) !important;
}
.project-name:hover {
    color: var(--action-color) !important;
  }

/* Folders header for corpus theme */
.folders-header {
  color: var(--primary-color);
}

.check_item.checked.see-select {
    background-color: var(--highlight-color);
    box-shadow: 0 0 5px rgba(var(--bs-primary-rgb), 0.5);
}

.list-vitrine-item.checked,
.list-vitrine-item.see-select.checked {
    background-color: var(--highlight-color);
    box-shadow: 0 0 5px rgba(var(--secondary-color-rgb), 0.5);
}

.list-vitrine-item.checked .list-vitrine-image,
.list-vitrine-item.see-select.checked .list-vitrine-image {
    background-color: inherit;
}

.list-vitrine-actions a {
    color: var(--primary-color);
}
    
.list-vitrine-actions a:hover {
    color: var(--primary-color);
}

.grid-vitrine-item.checked,
.grid-vitrine-item.see-select.checked {
    background-color: var(--highlight-color) !important;
    box-shadow: 0 0 5px rgba(var(--secondary-color-rgb), 0.5) !important;
}

.grid-vitrine-item.checked .grid-vitrine-body,
.grid-vitrine-item.see-select.checked .grid-vitrine-body {
    background-color: var(--highlight-color) !important;
}

.grid-vitrine-item.checked .grid-vitrine-img-link,
.grid-vitrine-item.see-select.checked .grid-vitrine-img-link {
    background-color: var(--highlight-color) !important;
}

.check_item.checked .see-select {
    box-shadow: inset 0px 0px 5px var(--bs-primary);
}

.see-select {
    transition: all 200ms cubic-bezier(0.19, 1, 0.22, 1);
}

.check_item.checked.see-select,
.check_item.checked {
    background-color: var(--highlight-color) !important;
    box-shadow: 0 0 5px rgba(var(--secondary-color-rgb), 0.5);
}

/* Ensure all child elements of a checked item also get proper styling */
.check_item.checked a,
.check_item.checked .card-body {
    background-color: inherit;
}

.check_item.checked .grid-vitrine-body {
    background-color: var(--highlight-color) !important;
}

.check_item.checked .grid-vitrine-img-link {
    background-color: var(--highlight-color) !important;
}

.check_item.checked .list-vitrine-content {
    background-color: var(--highlight-color) !important;
}

.check_item.checked .list-vitrine-image {
    background-color: var(--highlight-color) !important;
}

/* Rectangle selection style */
#drag-selection-rectangle {
    position: fixed;
    border: 1px solid var(--primary-color);
    background-color: rgba(var(--secondary-color-rgb), 0.1);
    pointer-events: none;
    display: none;
    z-index: 1000;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }.btn-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

.mx-1 {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }

/* Theme-specific styling for the chevron icon */
.nb-per-page-container::after {
    color: var(--primary-color);
}

.dropdown-menu > li > a,
.dropdown-menu > .dropdown-item {
    color: black !important;
    text-decoration: none;
    border-radius: 0.25rem;
}

.dropdown-menu > li > a:hover,
.dropdown-menu > .dropdown-item:hover {
    text-decoration: none !important;
}

/* .card-link, .list-link {
    pointer-events: auto !important;
    -webkit-user-drag: none;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

.list-link .check_item.checked {
    background-color: var(--highlight-color) !important;
} */

.custom-select-option.keyboard-selected {
    background-color: rgba(255, 182, 126, 0.2);
    border-left: 3px solid var(--secondary-color);
    padding-left: calc(0.75rem - 3px);
}

.btn-outline-secondary {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

button.btn-multi-tag, .btn-multi-tag {
    background-color: white !important;
    border-color: var(--primary-color) !important;
}

/* Vitrine button styles - Corpus theme */
.btn-vitrine-primary {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

.btn-vitrine-secondary {
    background-color: white !important;
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* Hover effects for vitrine buttons - Corpus theme */
.btn-vitrine-primary:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
    text-decoration: none !important;
}

.btn-vitrine-secondary:hover {
    background-color: white !important;
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    box-shadow: none !important;
    text-decoration: none !important;
}

/* Apply underline only to the text span - Corpus theme */
.btn-vitrine-primary:hover .btn-text {
    text-decoration: underline !important;
}

/* Remove active/click effect for buttons - Corpus theme */
.btn-vitrine-primary:active,
.btn-vitrine-primary:focus {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
    box-shadow: none !important;
    transform: none !important;
    text-decoration: none !important;
    outline: none !important;
}

.btn-vitrine-secondary:active,
.btn-vitrine-secondary:focus {
    background-color: white !important;
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    box-shadow: none !important;
    transform: none !important;
    text-decoration: none !important;
    outline: none !important;
}

/* No underline on active state - Corpus theme */
.btn-vitrine-primary:active .btn-text,
.btn-vitrine-secondary:active .btn-text
.btn-vitrine-primary:focus .btn-text,
.btn-vitrine-secondary:focus .btn-text {
    text-decoration: none !important;
}

.btn-hand.btn-multi-tag {
    background-color: white !important;
    border-color: var(--primary-color) !important;
}

/* Tag styling for corpus theme */
.tag-corpus {
    background-color: white !important;
    border-color: var(--primary-color) !important;
    border-radius: 4px;
    padding: 3px 8px;
    margin: 2px;
    display: inline-flex;
    align-items: center;
    font-size: 0.85rem;
}

/* Inverted search button in menu-gauche */
#menuGauche .form-inline .btn-secondary {
    background-color: white !important;
    border-color: #d9d9d9 !important;
}

#menuGauche .form-inline .btn-secondary .fa-search {
    background-color: transparent !important;
    color: var(--primary-color) !important;
}

.btn-active {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

.btn-active i {
    color: white !important;
}

.btn-secondary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Removed hover effect for btn-secondary */
.btn-secondary:disabled {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn:focus,
.btn-primary:focus,
.btn-secondary:focus,
.btn-hand:focus,
.form-control:focus,
.custom-select-input:focus,
select:focus,
a:focus,
.dropdown-toggle:focus,
.page-link:focus {
    box-shadow: none !important;
    outline: none !important;
}

/* Remove blue focus ring from Bootstrap components */
.form-control:focus,
.custom-select-input:focus {
    border-color: inherit !important;
}

/* Force selection color to match theme */
*::selection {
    background-color: rgba(var(--primary-color-rgb), 0.3) !important;
    color: inherit !important;
}

*::-moz-selection {
    background-color: rgba(var(--primary-color-rgb), 0.3) !important;
    color: inherit !important;
}

/* Search bar border color */
#menuGauche .input-group.no-gap .custom-select-container .custom-select-input {
    border-color: #999999 !important;
}

/* Search dropdown border color */
.custom-select-dropdown {
    border-color: #999999 !important;
}

.custom-page-item.active .custom-page-link:hover {
    background-color: var(--primary-color);
}

.form-range {
    --slider-thumb-color: var(--primary-color) !important;
    --slider-thumb-hover-color: #7a1412 !important;
}

/* .list-link .check_item.checked,
.list-link .check_item.checked .list-vitrine-content,
.list-link .check_item.checked .list-vitrine-image {
    background-color: var(--highlight-color) !important;
} */

.selection-dropdown .form-check-input:indeterminate {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}
  
.selection-dropdown .form-check-input:hover {
    border-color: var(--primary-color) !important;
}

.dropdown-toggle-icon .fas {
    color: var(--primary-color) !important;
}
  
.selection-dropdown .form-check-input:checked {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-toggle-section {
    color: var(--primary-color) !important;
}

.btn-toggle-section:hover {
    color: var(--primary-light-color) !important;
}

.primary-icon-color {
    color: white !important;
}

.secondary-icon-color {
    color: var(--primary-color) !important;
}

.create-vf-btn {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

.list-icon-btn:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.list-icon-btn {
    color: var(--primary-color);
}

.list-icon-btn:hover {
    color: var(--primary-light-color);
}

.fakelink {
    color: var(--primary-color) !important;
}

.column-resizer:hover,
.column-resizer.active {
    transition: all 0.1s ease;
    background-color: var(--primary-color);
}

#list-btn.btn-hand {
    background-color: var(--item-color) !important;
    border: none !important;
}
  
#grid-btn.btn-hand {
    background-color: var(--item-color) !important;
    border: none !important;
}

.custom-range::-webkit-slider-thumb {
    background: var(--primary-color);
  }
  
  .custom-range::-moz-range-thumb {
    background: var(--primary-color);
  }
  
  .custom-range::-ms-thumb {
    background: var(--primary-color);
  }
  
  .custom-range::-webkit-slider-thumb:active {
    background-color: var(--primary-color);
  }
  
  .custom-range:focus::-webkit-slider-thumb, 
  .custom-range:focus::-moz-range-thumb,
  .custom-range:focus::-ms-thumb {
    box-shadow: var(--primary-color);
  }