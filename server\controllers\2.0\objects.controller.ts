import { z } from "zod";

import { t } from "~/trpcContext";
import { branch, id, language, thesaurus } from "~/types/schemas";
import { metadata } from "~/types/schemas/metadata";

const objectInput = z.object({
  name: z.string(),
  id_file_representative: id.optional().nullish(),
  version: z.string(),
  root_dir: z.string(),
  id_nakala: z.string().optional(),
  doi: z.string().optional(),
  id_folder: id,
  object_type: z.string(),
})

export default t.router({
  /**
   * Get one object (cnd3d)
   * @param id The id of the object
   * @returns The object data
   */
  findOne: t.procedure
    .input(z.object({ id }))
    .output(z.string().nullable())
    .query(async ({ input, ctx }) => {
      const data = await ctx.database.oneOrNone("SELECT name FROM conservatoire3d_object WHERE id = $1", input.id);
      return data?.name ?? null;
    }),

    getObjectById: t.procedure
    .input(z.object({ branch, id_object: id }))
    .query(async ({ input, ctx }) => {
      // Récupérer les éventuels .3d pour la visualisation
      // ATTENTION au cas où il y aurait des éventuels fichiers liés à un objet pour "téléchargement" (extension variable)
      // Ici on ne les récupère pas pour garder le OneOrNone
      const object = await ctx.database.oneOrNone(`
        SELECT o.*, d.doi,
        CASE WHEN fi.hash IS NOT NULL THEN fi.hash ELSE '' END as hash3d
        FROM ${input.branch}_object o
        LEFT JOIN ${input.branch}_doi d ON d.id_item = o.id
        LEFT OUTER JOIN ${input.branch}_file_object fio ON fio.id_object = o.id AND fio.id_file IN (
            SELECT id FROM ${input.branch}_file WHERE file_ext = '3d'
            )
        LEFT OUTER JOIN ${input.branch}_file fi ON fi.id = fio.id_file AND fi.file_ext = '3d'
        WHERE o.id = $1
        AND d.item_type = 'object'`,
        [input.id_object],
      );
      return object
    }),
  /**
   * Get the info of a repository object
   * @param branch The affected branch
   * @param language The repository's display name language
   * @param folder_id The id of the root folder of the repository
   * @return The object data
   */
  exploreObj: t.procedure.input(z.object({ branch, language, folder_id: id })).query(async ({ input, ctx }) => {
    const objects = await ctx.database.manyOrNone(
      `SELECT * FROM ${input.branch}_object obj
        INNER JOIN conservatoire3d_file file ON obj.id_file_representative = file.id
        WHERE obj.id_folder = $1`,
      [input.folder_id],
    );

    const name = await ctx.database.oneOrNone(
      `SELECT ${input.language}_name as name
        FROM ${input.branch}_depot depot
        INNER JOIN ${input.branch}_folder_depot fd ON fd.id_depot = depot.id
        INNER JOIN ${input.branch}_folder folder ON folder.id = fd.id_folder
        WHERE folder.id = $1`,
      [input.folder_id],
    );

    return objects.map((object) => {
      return { depotName: name, ...object };
    });
  }),
  /**
   * Get the files list from an object
   * @param branch The affected branch
   * @param language The display name language
   * @param object_id The id of the requested object
   * @param extension The extension of the files to select (defaults to 'ply')
   * @return The list of files contained in the object
   */
  getFilesFromObject: t.procedure
    .input(z.object({ branch, object_id: id, extension: z.string().optional().default("ply") }))
    .query(async ({ ctx, input }) => {
      const file_objects: { list: number[] } = await ctx.database.one(
        `SELECT array_agg(distinct id_object) as list FROM ${input.branch}_file_object`,
      );

      if (!file_objects.list.includes(input.object_id)) {
        return [];
      }

      const { files } = await ctx.database.one(
        `SELECT array_agg(f.path) as files
        FROM ${input.branch}_object o
        INNER JOIN ${input.branch}_file_object fo ON fo.id_object = o.id
        INNER JOIN ${input.branch}_file f ON f.id = fo.id_file
        WHERE o.id = $1 AND file_ext = $2`,
        [input.object_id, input.extension],
      );

      return z.string().array().parse(files);
    }),
  getRealPath: t.procedure.input(z.object({ branch, object_id: id })).query(async ({ ctx, input }) => {
    const folder_query = await ctx.database.oneOrNone(
      "SELECT folder.id as root_folder_id FROM conservatoire3d_object obj " +
        "INNER JOIN conservatoire3d_folder folder ON folder.name = obj.root_dir " +
        "WHERE obj.id = $1",
      input.object_id,
    );

    if (!folder_query) throw new Error("Folder not found!");

    const { root_folder_id } = folder_query;

    const folder_path = await ctx.getFolderPathFromDatabase(root_folder_id);

    return folder_path;
  }),

  createObject: t.procedure
    .input(z.object({ branch, object: objectInput }))
    .output(id)
    .mutation(async ({ input, ctx }) => {

      const query = `
        SELECT id::int
        FROM ${input.branch}_object
        WHERE name = '${input.object.name}'
        AND root_dir = '${input.object.root_dir}'
        AND id_folder = ${input.object.id_folder}
        AND object_type = '${input.object.object_type}'`

      const exist = await ctx.database.oneOrNone(query)

      if(exist) {
        return id.parse(exist.id);
      }

      let field_query = [];
      let value_query = [];
      for (const [key, value] of Object.entries(input.object)) {
        if(!value) continue
        field_query.push(key);
        value_query.push(Number.isInteger(value) ? `${value}` : `'${value}'` );
      }

      const newObject = await ctx.database.one(`
        INSERT INTO ${input.branch}_object (${field_query.join(',')}, date_integration)
        VALUES (${value_query.join(',')}, now())
        RETURNING id::int`
      );

      return id.parse(newObject.id);
    }
  ),

  linkTwoObjects: t.procedure
    .input(z.object({ branch, id_object_ref: id, id_object_min: id }))
    .mutation(async ({ input, ctx }) => {
      const exist = await ctx.database.oneOrNone(`
        SELECT *
        FROM ${input.branch}_object_object
        WHERE (id_object_ref = $1 AND id_object_min = $2) OR (id_object_ref = $2 AND id_object_min = $1)`,
        [input.id_object_ref, input.id_object_min],
      );

      if(exist) return;

      await ctx.database.none(`
        INSERT INTO ${input.branch}_object_object(id_object_ref, id_object_min) 
        VALUES ($1, $2), ($2, $1)`,
        [input.id_object_ref, input.id_object_min],
      );
    }
  ),

  getObjectsByFolderWithoutValues: t.procedure
    .input(z.object({ branch, id_folder: id}))
    .query(async ({ input, ctx }) => {
      const objects = await ctx.database.manyOrNone(`
        SELECT o.id, o.id_file_representative, o.name, o.object_type
        FROM ${input.branch}_object o
        WHERE o.id_folder = $1
        ORDER BY o.name`,
        [input.id_folder],
      );

      return objects;
    }
  ),

  getObjectsByFolder: t.procedure
    .input(z.object({ branch, id_folder: id, language: z.string(), only_visible: z.boolean().default(false) }))
    .query(async ({ input, ctx }) => {
      // GET THE VIRTUALS OBJECTS
      const virtualObjects = await ctx.database.manyOrNone(`
        SELECT o.*, f_img.path as img_path, f_img.id as img_id,
        CASE WHEN ffi.id IS NOT NULL THEN ffi.hash
        ELSE '' END as hash3d
        FROM ${input.branch}_object o
        LEFT JOIN ${input.branch}_file f_img ON f_img.id = o.id_file_representative
        LEFT JOIN ${input.branch}_file_object f_obj ON f_obj.id_object = o.id AND f_obj.id_file IN (
                SELECT id FROM ${input.branch}_file WHERE file_ext = '3d'
            )
        LEFT JOIN ${input.branch}_file ffi ON ffi.id = f_obj.id_file AND ffi.file_ext = '3d'
        WHERE o.id_folder = $1
        AND o.object_type = 'virtual'
        ORDER BY o.name`,
        [input.id_folder],
      );

      // GET THE METADATA MODEL FOR VIRTUALS OBJECTS
      const vo_metadata_model = await ctx.database.manyOrNone(`
        SELECT m.*, ml.label as label_metadata, ml.description as description_metadata
        FROM ${input.branch}_metadata m
        INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
        INNER JOIN ${input.branch}_metadata_label ml ON m.id = ml.id_metadata
        WHERE mm.name = $1
        AND ml.language = $2
        ${input.only_visible ? "AND m.query = 'y'" : ''}
        ORDER BY m.rank`
        ,["virtualObjectV2", input.language],
      );

      // RETRIEVE METADATA VALUES AND UPDATE DOI VALUE
      for(const vo of virtualObjects) {
        if(!vo.doi) {
          vo.doi = (await ctx.database.oneOrNone(`SELECT doi FROM ${input.branch}_doi WHERE id_item = ${vo.id} AND item_type = 'object'`))?.doi;
        }
        vo.doi = vo.doi?.slice(vo.doi.lastIndexOf('/') + 1);
        vo.nb_po = (await ctx.database.oneOrNone(`SELECT COUNT(*) as nb FROM ${input.branch}_object_object WHERE id_object_ref = ${vo.id}`))?.nb
        vo.metadata = await ctx.metadata.retrieveMetadataValueOfModel(input.branch, vo_metadata_model, vo.id, "object")
      }

      // GET THE PHYSICALS OBJECTS
      const physicalObjects = await ctx.database.manyOrNone(`
        SELECT o.*, oo.id_object_ref as id_vo
        FROM ${input.branch}_object o
        LEFT JOIN ${input.branch}_object_object oo ON oo.id_object_min = o.id
        WHERE o.id_folder = $1
        AND o.object_type = 'physical'
        ORDER BY o.name`,
        [input.id_folder],
      );
      
      // GET THE METADATA MODEL FOR PHYSICALS OBJECTS
      const po_metadata_model = await ctx.database.manyOrNone(`
        SELECT m.*, ml.label as label_metadata, ml.description as description_metadata
        FROM ${input.branch}_metadata m
        INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
        INNER JOIN ${input.branch}_metadata_label ml ON m.id = ml.id_metadata
        WHERE mm.name = $1
        AND ml.language = $2
        ${input.only_visible ? "AND m.query = 'y'" : ''}
        ORDER BY m.rank`
        ,["physicalObject", input.language],
      );

      // RETRIEVE METADATA VALUES
      for(const po of physicalObjects) {
        if(!po.doi) {
          po.doi = (await ctx.database.oneOrNone(`SELECT doi FROM ${input.branch}_doi WHERE id_item = ${po.id} AND item_type = 'object'`))?.doi;
        }
        po.doi = po.doi?.slice(po.doi.lastIndexOf('/') + 1);
        po.metadata = await ctx.metadata.retrieveMetadataValueOfModel(input.branch, po_metadata_model, po.id, "object")
      }

      return { virtualObjects, physicalObjects };
    }),

    getFolderWithoutValues: t.procedure
      .input(z.object({ branch, id_object: id }))
      .query(async ({ input, ctx }) => {
        const folder = await ctx.database.oneOrNone(`
          SELECT f.id, f.name, f.id_representative_picture
          FROM ${input.branch}_folder f
          INNER JOIN ${input.branch}_object o ON o.id_folder = f.id
          WHERE o.id = $1`,
          [input.id_object],
        );

        return folder;
      }
    ),

    getMetadataModelOfFolder: t.procedure
    .input(z.object({branch, id_object: id}))
    .output(z.object({metadata_model_folder: z.string().nullable()}))
    .query(async ({input, ctx}) => {
      const version = await ctx.database.oneOrNone(`
        SELECT CONCAT(mm.name) as metadata_model_folder
        FROM ${input.branch}_object o
        INNER JOIN ${input.branch}_folder f ON f.id = o.id_folder
        INNER JOIN ${input.branch}_folder_metadata_model fmm ON f.id = fmm.id_folder
        INNER JOIN ${input.branch}_metadata_model mm ON mm.id = fmm.id_metadata_model
        WHERE o.id = $1`
        ,[input.id_object],
      );

      console.log(version)

      return { metadata_model_folder: z.string().nullable().parse(version?.metadata_model_folder ?? null) };
    }),

    getMetadata: t.procedure
    .input(z.object({branch, id_object: id, object_type: z.string(), language: z.string(), only_visible: z.boolean().optional().default(false)}))
    .query(async ({input, ctx}) => {

      const name_metadata_model = input.object_type === 'virtual' ? 'virtualObjectV2' : 'physicalObject';

      const metadata_model = await ctx.database.manyOrNone(`
        SELECT m.*, ml.label as label_metadata, ml.description as description_metadata
        FROM ${input.branch}_metadata m
        INNER JOIN ${input.branch}_metadata_model mm ON mm.id = m.id_metadata_model
        INNER JOIN ${input.branch}_metadata_label ml ON m.id = ml.id_metadata
        WHERE mm.name = $1
        AND ml.language = $2
        ${input.only_visible ? "AND m.query = 'y'" : ''}
        ORDER BY m.rank`
        ,[name_metadata_model, input.language],
      );

      return await ctx.metadata.retrieveMetadataValueOfModel(input.branch, metadata_model, input.id_object, 'object');
    }),

    getFoldersRights: t.procedure
    .input(z.object({branch, id_object: id}))
    .query(async ({input, ctx}) => {
      let {id_folder} = await ctx.database.oneOrNone(`
        SELECT o.id_folder
        FROM ${input.branch}_object o
        WHERE o.id = ${input.id_object}`
      )

      let theso_rights = await ctx.database.oneOrNone(`
        SELECT t.*
        FROM ${input.branch}_thesaurus_item ti
        INNER JOIN ${input.branch}_thesaurus t ON t.id = ti.id_thesaurus AND t.id_thes = ti.id_thes_thesaurus
        INNER JOIN ${input.branch}_metadata m ON m.list = t.thesaurus
        WHERE ti.id_item=${id_folder}
        AND item_type = 'folder'
        AND m.code = 'dm_rights'`
      )
      return theso_rights;
    }),

    getPhysicalObjectsByVirtualObjectId: t.procedure
    .input(z.object({branch, id_virtual_object: id}))
    .query(async ({input, ctx}) => {
      const physicalObjects = await ctx.database.manyOrNone(`
        SELECT o.*
        FROM ${input.branch}_object_object oo
        INNER JOIN ${input.branch}_object o ON oo.id_object_min = o.id
        WHERE oo.id_object_ref = $1`
        , [input.id_virtual_object]
      );
      return physicalObjects;
    }),

    getVirtualObjectByPhysicalObjectId: t.procedure
    .input(z.object({branch, id_physical_object: id}))
    .query(async ({input, ctx}) => {
      const virtualObjects = await ctx.database.oneOrNone(`
        SELECT o.*
        FROM ${input.branch}_object_object oo
        INNER JOIN ${input.branch}_object o ON oo.id_object_ref = o.id
        WHERE oo.id_object_min = $1
        AND o.object_type = 'virtual'
        LIMIT 1`
        , [input.id_physical_object]
      );

      console.log(virtualObjects)

      return virtualObjects;
    }),

    getLinkedObjects: t.procedure
    .input(z.object({branch, id_object: id}))
    .query(async ({input, ctx}) => {
      const linkedObjects = await ctx.database.manyOrNone(`
        SELECT o_to.*
        FROM ${input.branch}_object o_from
        INNER JOIN ${input.branch}_object_object oo ON o_from.id = CASE o_from.object_type WHEN 'virtual' THEN oo.id_object_ref ELSE oo.id_object_min END 
        INNER JOIN ${input.branch}_object        o_to ON o_to.id = CASE o_from.object_type WHEN 'virtual' THEN oo.id_object_min ELSE oo.id_object_ref END 
        WHERE o_from.id = $1`
        , [input.id_object]
      );

      return linkedObjects;
    }),

    getDOI: t.procedure
    .input(z.object({ branch: z.string(), id_object: z.number(), object_type: z.enum(['virtual','physical']) }))
    .output(z.string().nullish())
    .query(async ({ input, ctx }) => {
      // Si c'est un object physique on renvoit le doi de l'objet virtuel associé
      if(input.object_type === "physical"){
        input.id_object = (await ctx.database.one(`
          SELECT id_object_ref as id
          FROM ${input.branch}_object_object
          WHERE id_object_min = ${input.id_object}`
        ))?.id;

        if(!input.id_object) return null;
      }

      // Try to get the DOI from the object
      let object_doi = (await ctx.database.oneOrNone(`
        SELECT doi
        FROM ${input.branch}_object o
        WHERE o.id = ${input.id_object}`
      ))?.doi;

      if(!object_doi){
        // If the object hasn't a DOI, try to get it from the DOI table
        const doi_doi = (await ctx.database.oneOrNone(`
          SELECT d.doi
          FROM ${input.branch}_doi d
          WHERE d.id_item = ${input.id_object}
          AND d.item_type = 'object'`
        ))?.doi;

        if(!doi_doi){
          return null;
        }

        object_doi = doi_doi.slice(doi_doi.lastIndexOf('/') + 1);
      }else{
        object_doi = object_doi
      }

      return z.string().nullish().parse(object_doi);
    }),

    linkToFolder: t.procedure
    .input(z.object({ branch: z.string(), id_object: id, id_folder: id }))
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.database.none(`
        INSERT INTO ${input.branch}_folder_object (id_folder, id_object)
        VALUES ($1, $2)`
        , [input.id_folder, input.id_object]
      );

      return Boolean(result);
    }),

    // PROCEDURE USED IN THE DOI CREATION AND INFO Extraction for mapping (EDM...)
    // SHOULD ONLY BE USED ON VIRTUAL OBJECTS
    getInfoForDOICreation: t.procedure
    .input(z.object({ branch: z.string(), id_object: id }))
    .query(async ({ input, ctx }) => {
      // CREATORS FIELD
      const creators = await ctx.database.manyOrNone(`
        SELECT a.*
        FROM ${input.branch}_actor a
        INNER JOIN ${input.branch}_actor_item ai ON ai.id_actor = a.id
        INNER JOIN ${input.branch}_metadata m ON m.id = ai.id_metadata
        WHERE m.code = 'object_creator'
        AND ai.id_item = ${input.id_object}
        AND ai.item_type = 'object'`
      );

      for(const actor of creators){
        actor.info = await ctx.database.oneOrNone(`
          SELECT *
          FROM ${input.branch}_actor_${actor.actor_type} a
          INNER JOIN ${input.branch}_${actor.actor_type} i ON i.id = a.id_${actor.actor_type}
          WHERE a.id_actor = ${actor.id}
          LIMIT 1`
        );
      }

      // SUBJECTS FIELD
      const id_physicalObject = (await ctx.database.manyOrNone(`SELECT id_object_min FROM ${input.branch}_object_object WHERE id_object_ref = ${input.id_object}`))?.map(o => o.id_object_min);
      
      const po_type: { name_fr: string; identifier: string; type: string }[] = [];
      for(const id_po of id_physicalObject){
        let val = await ctx.database.oneOrNone(`
          SELECT name as name_fr, identifier, 'MainCategory' as type
          FROM ${input.branch}_thesaurus_item ti
          INNER JOIN ${input.branch}_thesaurus t ON t.id_thes = ti.id_thes_thesaurus AND t.id = ti.id_thesaurus AND t.thesaurus = 'MainCategory'
          WHERE ti.id_item = ${id_po}
          AND ti.item_type = 'object'`
        );

        if(!po_type.some((s) => s.name_fr === val.name_fr)){
          po_type.push(val)
        }
      }

      const po_subject: { name_fr: string; identifier: string; type: string }[] = [];
      for(const id_po of id_physicalObject){
        let values = await ctx.database.manyOrNone(`
          SELECT tp.name as name_fr, tp.identifier, 'PACTOLS' as type
          FROM ${input.branch}_thesaurus_pactols_item tpi
          INNER JOIN ${input.branch}_thesaurus_pactols tp ON tp.id_thes = tpi.id_thes_thesaurus AND tp.id = tpi.id_thesaurus
          WHERE tpi.id_item = ${id_po}
          AND tpi.item_type = 'object'`
        );

        for(const val of values){
          if(!po_subject.some((s) => s.name_fr === val.name_fr)){
            po_subject.push(val)
          }
        }
      }

      // on va chercher directement l'info dans la table des thesaurus_item !
      const object_nature = await ctx.database.oneOrNone(`
          SELECT name as name_fr, name_en , identifier, 'plan_classement' as type
          FROM ${input.branch}_thesaurus t
          INNER JOIN ${input.branch}_thesaurus_item ti ON ti.thesaurus = t.thesaurus AND ti.id_thes_thesaurus = t.id_thes AND t.thesaurus = 'plan_classement'
          WHERE ti.id_item = ${input.id_object}
          AND item_type = 'object'`
      );

      const nature = object_nature

      const subjects = [...po_subject, ...po_type, object_nature];

      // CONTRIBUTORS FIELD
      const contributors = await ctx.database.manyOrNone(`
        SELECT a.*, m.code as metadata_code
        FROM ${input.branch}_actor a
        INNER JOIN ${input.branch}_actor_item ai ON ai.id_actor = a.id
        INNER JOIN ${input.branch}_metadata m ON m.id = ai.id_metadata
        WHERE m.code = 'object_contributor'
        AND ai.id_item = ${input.id_object}
        AND ai.item_type = 'object'`
      );

      for(const actor of contributors){
        actor.info = await ctx.database.oneOrNone(`
          SELECT *
          FROM ${input.branch}_actor_${actor.actor_type} a
          INNER JOIN ${input.branch}_${actor.actor_type} i ON i.id = a.id_${actor.actor_type}
          WHERE a.id_actor = ${actor.id}
          LIMIT 1`
        );
      }

      // DATES FIELD
      const object = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_object WHERE id = ${input.id_object}`);

      let dates = await ctx.database.manyOrNone(`
        SELECT d.*, 'Other' as date_type, m.code
        FROM ${input.branch}_datation d
        INNER JOIN ${input.branch}_datation_item di ON di.id_datation = d.id
        INNER JOIN ${input.branch}_metadata m ON m.id = di.id_metadata
        WHERE di.id_item = ${input.id_object}
        AND di.item_type = 'object'
        UNION ALL
        SELECT d.*, 'digitization' as date_type, m.code
        FROM ${input.branch}_datation d
        INNER JOIN ${input.branch}_datation_item di ON di.id_datation = d.id
        INNER JOIN ${input.branch}_metadata m ON m.id = di.id_metadata
        WHERE di.id_item = ${object.id_folder}
        AND di.item_type = 'folder' `
      );

      dates = Array.isArray(dates) ? dates : [dates];
      dates.push({date_min: object.date_integration, date_type: "Created"})

      // PASSPORT METADATA VALUE
      const passport_query = await ctx.database.manyOrNone(`
        SELECT p.value, m.code
        FROM ${input.branch}_passport p
        INNER JOIN ${input.branch}_metadata m ON m.id = p.id_metadata
        WHERE id_item = ${input.id_object}
        AND p.item_type = 'object'`
      );

      const passportByCode = passport_query.reduce((acc, m) => {
        if(!acc[m.code]) {
          acc[m.code] = [];
        }
        acc[m.code].push(m.value);
        return acc
      }, {});

      // LANGUAGES FIELD
      const {id_folder} = await ctx.database.oneOrNone(`SELECT id_folder FROM ${input.branch}_object WHERE id = ${input.id_object}`);

      let language = "fr";
      let content = '';
      let size = '';
      let rights = '';
      if(id_folder) {
        const folder_passport_query = await ctx.database.manyOrNone(`
          SELECT p.value, m.code
          FROM ${input.branch}_passport p
          INNER JOIN ${input.branch}_metadata m ON m.id = p.id_metadata
          WHERE id_item = ${id_folder}
          AND p.item_type = 'folder'`
        );

        const folder_passportByCode = folder_passport_query.reduce((acc, m) => {
          if(!acc[m.code]) {
            acc[m.code] = [];
          }
          acc[m.code].push(m.value);
          return acc
        }, {});

        language = folder_passportByCode['dm_language'];

        // Content / Formats (content of deposit - ds_content) / Size  ds_sizee
        const unique_folder_passport_query = await ctx.database.manyOrNone(`
          SELECT p.value[1] as val, m.code
          FROM ${input.branch}_passport p
          INNER JOIN ${input.branch}_metadata m ON m.id = p.id_metadata
          WHERE id_item = ${id_folder}
          AND m.isunique = '1'
          AND (m.code = 'ds_content' OR m.code = 'ds_size')
          AND p.item_type = 'folder'`
        );
        if (unique_folder_passport_query)
            for (const item of unique_folder_passport_query) {
                if (item.code ==='ds_content' ) content = item.val
                if (item.code === 'ds_size') size = item.val
            }

        // Rights
          const rightsdeposit = await ctx.database.oneOrNone(`
          SELECT name as name_fr, name_en , t.identifier, 'license' as type
          FROM ${input.branch}_thesaurus t
          INNER JOIN ${input.branch}_thesaurus_item ti ON ti.thesaurus = t.thesaurus AND ti.id_thes_thesaurus = t.id_thes
          WHERE t.thesaurus = 'license'
          AND ti.id_item = $1
          AND item_type = 'folder' `, id_folder
          );

        if (rightsdeposit) rights = rightsdeposit.identifier
      }

      // TITLES FIELD
      const title = passportByCode['object_name'];

      // DESCRIPTION FIELD
      const descriptions = passportByCode['object_description'];

      // techno mesh info to complete description

      let description_mesh_techno = {
        object_msh_sw_name:        passportByCode['object_msh_sw_name']       ? passportByCode['object_msh_sw_name'][0][0]        : null,
        object_msh_sw_version:     passportByCode['object_msh_sw_version']    ? passportByCode['object_msh_sw_version'][0][0]     : null, 
        object_msh_hw_sensorbrand: passportByCode['object_msh_hw_sensorbrand']? passportByCode['object_msh_hw_sensorbrand'][0][0] : null,
        object_msh_hw_sensormodel: passportByCode['object_msh_hw_sensormodel']? passportByCode['object_msh_hw_sensormodel'][0][0] : null
      }

      // LOCATIONS FIELD
      const geoLocations: { id: number; name: string; longlat: string; metadata_code: string }[] = [];
      if(id_physicalObject.length > 0){
        let locations = await ctx.database.manyOrNone(`
          SELECT l.id, l.name, l.longlat, m.code as metadata_code
          FROM ${input.branch}_location l
          INNER JOIN ${input.branch}_location_item li ON li.id_location = l.id
          INNER JOIN ${input.branch}_metadata m ON m.id = li.id_metadata
          WHERE li.id_item IN (${id_physicalObject.join(', ')})
          AND li.item_type = 'object'
          AND m.code = 'po_discovery_location'`
        );
        locations = Array.isArray(locations) ? locations : [locations];
        for(const loc of locations){
          if(!geoLocations.some((s) => s.id === loc.id)){
            geoLocations.push(loc);
          }
        }
      }

      // partOf collection ?
        let collections = await ctx.database.manyOrNone(`
        SELECT m.name, m.identifier as collectionURI
        FROM ${input.branch}_thesaurus_multi m
        INNER JOIN ${input.branch}_thesaurus_multi_item mi ON mi.thesaurus = m.thesaurus AND mi.id_thes_thesaurus = m.id_thes
        WHERE m.thesaurus = 'collection'
        AND mi.id_item = $1 AND mi.item_type = 'object'
        `, input.id_object)

        collections = Array.isArray(collections) ? collections : [collections]

      // Representative image
        let imageId = await ctx.database.oneOrNone(`
        SELECT id_file_representative
        FROM ${input.branch}_object
        WHERE id = $1
        `, input.id_object)

      const info = {title, subjects, creators, contributors, dates, descriptions, description_mesh_techno, language, geoLocations, nature, collections, content, size, rights, imageId };
      return info;
    }),

    // PROCEDURE USED IN THE MAPPING EDM Info CREATION FOR CHO Cultural Heritage Object
    // SHOULD ONLY BE USED ON PHYSICAL OBJECTS
    getInfoForPhysicalObject: t.procedure
        .input(z.object({ branch: z.string(), id_object: id }))
        .query(async ({ input, ctx }) => {

          const id_physicalObject = (await ctx.database.oneOrNone(`
              SELECT id_object_min FROM ${input.branch}_object_object
              WHERE id_object_ref = ${input.id_object}`))?.id_object_min;

          let language = "fr";
          // PASSPORT METADATA VALUE
          const passport_query = await ctx.database.manyOrNone(`
          SELECT p.value, m.code
          FROM ${input.branch}_passport p
          INNER JOIN ${input.branch}_metadata m ON m.id = p.id_metadata
          WHERE p.id_item = $1
          AND p.item_type = 'object' `, id_physicalObject
          );

          const passportByCode = passport_query.reduce((acc, m) => {
            if (!acc[m.code]) {
              acc[m.code] = [];
            }
            acc[m.code].push(m.value[0])
            return acc
          }, {});
          // TITLES FIELD
          const title = passportByCode['po_name'][0];

          // DESCRIPTION FIELD
          const descriptions = passportByCode['po_description'][0];

          // TYPE (Getty)
          const po_type: { name_fr: string; name_en: string; identifier: string; type: string }[] = [];
          const thesaurus = await ctx.database.oneOrNone(`
              SELECT name as name_fr, name_en , identifier, 'MainCategory' as type
              FROM ${input.branch}_thesaurus_item ti
              INNER JOIN ${input.branch}_thesaurus t ON t.id_thes = ti.id_thes_thesaurus AND t.thesaurus = ti.thesaurus
              WHERE t.thesaurus  = 'MainCategory'
              AND ti.item_type = 'object'
              AND ti.id_item = $1 `
              , id_physicalObject
          );
          if (!po_type.some((s) => s.name_fr === thesaurus.name_fr)) {
            po_type.push(thesaurus)
          }

          const type = po_type[0]

          // SUBJECTS FIELD : Material PACTOLS
            /*
              Bone: http://vocab.getty.edu/aat/*********         => matière osseuse : 15621.15616.27630.16029
              Ceramic: http://vocab.getty.edu/aat/300235507      => pâte (céramic) / fabric  : 15621.16527
              Clay: http://vocab.getty.edu/aat/300010439         => argile 15621.16689.22054.246278.13342 // ET Terre cuite ! 15621.17266
              Concrete: http://vocab.getty.edu/aat/300010737     => type num ou num + restit ou restit ?? (pas de modélisation pur)
              Glass: http://vocab.getty.edu/aat/300010797        => 15621.17499
              Leather: http://vocab.getty.edu/aat/300011845      => 15621.15616.27549.14196 ou bien "objet en cuir": 246691.246345.178439  (entité matérielle / objet mobile / facette objet mobile par matériau)
              Metal: http://vocab.getty.edu/aat/300010900        => 15621.15844 (ou objet en metal ? 19612 et suivant)
              Mortar: http://vocab.getty.edu/aat/300014741       => mortar (macony) 15621.14070.15753 (danns matériau de construction)
              Paper: http://vocab.getty.edu/aat/300014109        => 15621.15616.246405.250849
              Papyrus: http://vocab.getty.edu/aat/300014127      => 246691.246345.266318.16111  (ce n'est pas dans material)
              Plaster: http://vocab.getty.edu/aat/300014922      => 15621.14070.16301
              Stone: http://vocab.getty.edu/aat/300011692        => rock ?? roche ? id_thes 15621.16689 ou bien mineral : 15621.22053 ou les 2 ??
              Textile: http://vocab.getty.edu/aat/300231565      => 15621.17278
              Wood: http://vocab.getty.edu/aat/300011914         => 15621.15616.246405.13589 - ou objet en bois ?? 23492 (246691.246345.266318.23492)

            */

          let po_material: { name_fr: string; name_en: string;identifier: string; type: string, getty_material: string }[] = [];
          const material = await ctx.database.manyOrNone(`
            SELECT name as name_fr, name_en, identifier, 'PACTOLS' as type,
            CASE
                  WHEN thesaurus_path <@ '15621.15616.27630.16029' THEN 'http://vocab.getty.edu/aat/*********'
                  WHEN thesaurus_path <@ '15621.16527' THEN 'http://vocab.getty.edu/aat/300235507'
                  WHEN thesaurus_path <@ '15621.16689.22054.246278.13342' THEN 'http://vocab.getty.edu/aat/300010439'
                  WHEN thesaurus_path <@ '15621.17266' THEN 'http://vocab.getty.edu/aat/300010439'
                  WHEN thesaurus_path <@ '15621.17499' THEN 'http://vocab.getty.edu/aat/300010797'
                  WHEN thesaurus_path <@ '15621.15616.27549.14196' THEN 'http://vocab.getty.edu/aat/300011845'
                  WHEN thesaurus_path <@ '246691.246345.178439' THEN 'http://vocab.getty.edu/aat/300011845'
                  WHEN thesaurus_path <@ '15621.15844' THEN 'http://vocab.getty.edu/aat/300010900'
                  WHEN thesaurus_path <@ '246691.246345.19612' THEN 'http://vocab.getty.edu/aat/300010900'
                  WHEN thesaurus_path <@ '15621.14070.15753' THEN 'http://vocab.getty.edu/aat/300014741'
                  WHEN thesaurus_path <@ '15621.15616.246405.250849' THEN 'http://vocab.getty.edu/aat/300014109'
                  WHEN thesaurus_path <@ '15621.14070.16301' THEN 'http://vocab.getty.edu/aat/300014922'
                  WHEN thesaurus_path <@ '15621.16689' THEN 'http://vocab.getty.edu/aat/300011692'
                  WHEN thesaurus_path <@ '15621.22053' THEN 'http://vocab.getty.edu/aat/300011692'
                  WHEN thesaurus_path <@ '15621.17278' THEN 'http://vocab.getty.edu/aat/300231565'
                  WHEN thesaurus_path <@ '15621.15616.246405.13589' THEN 'http://vocab.getty.edu/aat/300011914'
                  ELSE 'Other' END
                  as getty_material,
            CASE
                  WHEN thesaurus_path <@ '15621.15616.27630.16029' THEN 'Bone'
                  WHEN thesaurus_path <@ '15621.16527' THEN 'Ceramic'
                  WHEN thesaurus_path <@ '15621.16689.22054.246278.13342' THEN 'Clay'
                  WHEN thesaurus_path <@ '15621.17266' THEN 'Clay'
                  WHEN thesaurus_path <@ '15621.17499' THEN 'Glass'
                  WHEN thesaurus_path <@ '15621.15616.27549.14196' THEN 'Leather'
                  WHEN thesaurus_path <@ '178439' THEN 'Leather'
                  WHEN thesaurus_path <@ '15621.15844' THEN 'Metal'
                  WHEN thesaurus_path <@ '246691.246345.19612' THEN 'Metal'
                  WHEN thesaurus_path <@ '15621.14070.15753' THEN 'Mortar'
                  WHEN thesaurus_path <@ '15621.15616.246405.250849' THEN 'Paper'
                  WHEN thesaurus_path <@ '15621.14070.16301' THEN 'Plaster'
                  WHEN thesaurus_path <@ '15621.16689' THEN 'Stone'
                  WHEN thesaurus_path <@ '15621.22053' THEN 'Stone'
                  WHEN thesaurus_path <@ '15621.17278' THEN 'Textile'
                  WHEN thesaurus_path <@ '15621.15616.246405.13589' THEN 'Wood'
                  ELSE 'Other' END
                  as getty_material_name
            FROM ${input.branch}_thesaurus_pactols_item pi
            INNER JOIN ${input.branch}_thesaurus_pactols p ON p.id_thes = pi.id_thes_thesaurus AND p.thesaurus = pi.thesaurus
            WHERE p.thesaurus  = 'sujet'
            AND pi.item_type = 'object'
            AND p.collection = 'G120' AND pi.collection = 'G120'
            AND pi.id_item = $1 `
              , id_physicalObject
            );
            /* if (!po_subject.some((s) => s.name_fr === pactols.name_fr)) {
              po_subject.push(pactols)
            }
            */


          // SUBJECTS FIELD : sujet PACTOLS
          let po_subject: { name_fr: string; name_en: string;identifier: string; type: string }[] = [];
          const pactols = await ctx.database.manyOrNone(`
            SELECT name as name_fr, name_en, identifier, 'PACTOLS' as type
            FROM ${input.branch}_thesaurus_pactols_item pi
            INNER JOIN ${input.branch}_thesaurus_pactols p ON p.id_thes = pi.id_thes_thesaurus AND p.thesaurus = pi.thesaurus
            WHERE p.thesaurus  = 'sujet'
            AND pi.item_type = 'object'
            AND p.collection != 'G120'
            AND pi.id_item = $1 `
              , id_physicalObject
            );
            /* if (!po_subject.some((s) => s.name_fr === pactols.name_fr)) {
              po_subject.push(pactols)
            }
            */
          po_subject = pactols

          // SUBJECTS FIELD : tags libres
          let po_tag: { name_fr: string; name_en: string;identifier: string; type: string }[] = [];
          const tags = await ctx.database.manyOrNone(`
            SELECT name as name_fr, name  as name_en, identifier, 'TAG' as type
            FROM ${input.branch}_item_tag ai
            INNER JOIN ${input.branch}_tag a ON a.id = ai.id_tag
            WHERE ai.item_type = 'object'
            AND ai.id_item = $1 `
              , id_physicalObject
          );
          /*
          if (!po_tag.some((s) => s.name_fr === tags.name_fr)) {
            po_tag.push(tags)
          }

          */
          po_tag = tags
          //console.log(po_tag)

          const subjects = [...po_subject, ...tags];

            // DATES FIELD
            let dates = await ctx.database.manyOrNone(`
              SELECT d.*, m.code as code, 'Other' as date_type, CASE WHEN p.start_date IS NOT NULL THEN p.start_date ELSE d.date_min END as start_date
              FROM ${input.branch}_datation d
              INNER JOIN ${input.branch}_datation_item di ON di.id_datation = d.id
              INNER JOIN ${input.branch}_metadata m ON m.id = di.id_metadata
              INNER JOIN ${input.branch}_object o ON o.id = di.id_item AND o.object_type = 'physical'
              LEFT OUTER JOIN ${input.branch}_thesaurus_periodo p ON p.id_periodo = d.id_periodo
              WHERE di.id_item = $1
              AND di.item_type = 'object'`, id_physicalObject );

            dates = Array.isArray(dates) ? dates : [dates];

            // Locations FIELD
            let locations = await ctx.database.manyOrNone(`
              SELECT l.name, l.uri_geonames,
              CASE
                  WHEN l.longlat IS NOT NULL THEN l.longlat
                  WHEN t.latlng IS NOT NULL THEN t.latlng
                  ELSE '' END as latlng,
              m.code as code, mi.qualifier as qualifier
              FROM ${input.branch}_location l
              INNER JOIN ${input.branch}_location_item li ON li.id_location = l.id
              INNER JOIN ${input.branch}_metadata m ON m.id = li.id_metadata
              INNER JOIN ${input.branch}_object o ON o.id = li.id_item AND o.object_type = 'physical'
              LEFT OUTER JOIN ${input.branch}_thesaurus_multi_item mi ON mi.id_item = o.id AND mi.thesaurus = 'geo' AND mi.item_type = 'object' AND mi.qualifier = replace(replace(m.code,'po_', ''), '_location','')
              LEFT OUTER JOIN ${input.branch}_thesaurus_multi t ON t.id_thes = mi.id_thes_thesaurus AND t.thesaurus = mi.thesaurus AND t.id_thes = l.uri_geonames::int
              WHERE li.id_item = $1
              AND li.item_type = 'object'`, id_physicalObject );

            locations = Array.isArray(locations) ? locations : [locations];

          const info = {title, descriptions, type, subjects,  language, material, dates, locations};
          //console.log(info)
        return info;
        }),

    getRepresentativeImg: t.procedure
    .input(z.object({branch: z.string(), id_object: z.number()}))
    .output(z.number().nullable())
    .query(async ({input, ctx}) => {
        let doi = await ctx.database.oneOrNone(
            `SELECT id_file_representative FROM conservatoire3d_object WHERE id = ${input.id_object}`
        );
        return doi.id_file_representative;
    }),

    getObjectMetadataModel: t.procedure
    .input(z.object({branch: z.string(), id_object: z.number()}))
    .output(z.string().nullable())
    .query(async ({input, ctx}) => {
      const result = await ctx.database.oneOrNone(`
        SELECT object_type
        FROM ${input.branch}_object
        WHERE id = $1`, input.id_object);

      let model = "virtualObject";
      switch(result.object_type) {
       case 'virtual':
          model = "virtualObjectV2";
          break;
      case 'physical':
          model = "physicalObject";
          break;
      }

      return model;
    }),

    getKeywords: t.procedure
    .input(z.object({branch: z.string(), id_object: z.number()}))
    .output(
      z.union([
        z.object({type: z.literal("periodo"), label: z.string(), start_date: z.string(), stop_date: z.string()}),
        z.object({type: z.union([z.literal("thesaurus"), z.literal("multi")]), id_thes: z.number(), thesaurus: z.string(), name: z.string(), identifier: z.string().nullish()}),
        z.object({type: z.literal("pactols"), id: z.number(), id_thes: z.number(), thesaurus: z.string(), name: z.string(), identifier: z.string().nullish(), id_item: z.number(), item_type: z.string()}),
        z.object({type: z.literal("tag"), name: z.string()}),
      ]).array()
    )
    .query(async ({input, ctx}) => {
      const tags = await ctx.database.manyOrNone(`
        SELECT t.name, 'tag' as type
        FROM ${input.branch}_tag t
        INNER JOIN ${input.branch}_item_tag it ON it.id_tag = t.id
        WHERE it.item_type = 'object'
        AND it.id_item = ${input.id_object}`
      );

      const pactols = await ctx.database.manyOrNone(`
        SELECT p.id::int, p.name, p.id_thes::int, p.thesaurus, 'pactols' as type, p.identifier, pi.id_item::int, pi.item_type
        FROM ${input.branch}_thesaurus_pactols p
        INNER JOIN ${input.branch}_thesaurus_pactols_item pi ON pi.id_thes_thesaurus = p.id_thes
        WHERE pi.id_item = ${input.id_object}
        AND pi.item_type = 'object'`
      );

      const periodos = await ctx.database.manyOrNone(`
        SELECT p.label, p.start_date, p.stop_date, 'periodo' as type
        FROM ${input.branch}_thesaurus_periodo p
        INNER JOIN ${input.branch}_thesaurus_periodo_item pi ON pi.id_periodo = p.id
        WHERE pi.id_item = ${input.id_object}
        AND pi.item_type = 'object'`
      );

      const thesaurus = await ctx.database.manyOrNone(`
        SELECT t.name, t.id_thes::int, t.thesaurus, 'thesaurus' as type, identifier
        FROM ${input.branch}_thesaurus t
        INNER JOIN ${input.branch}_thesaurus_item ti ON ti.thesaurus = t.thesaurus AND ti.id_thes_thesaurus = t.id_thes
        WHERE ti.id_item = ${input.id_object}
        AND ti.item_type = 'object'`
      );

      const multi = await ctx.database.manyOrNone(`
        SELECT t.name, t.id_thes::int, t.thesaurus, 'multi' as type, identifier
        FROM ${input.branch}_thesaurus_multi t
        INNER JOIN ${input.branch}_thesaurus_multi_item ti ON ti.thesaurus = t.thesaurus AND ti.id_thes_thesaurus = t.id
        WHERE ti.id_item = ${input.id_object}
        AND ti.item_type = 'object'`
      );

      return [...tags, ...pactols, ...periodos, ...thesaurus, ...multi];
    }),

    defineAsPrincipalElementOfDeposit: t.procedure
    .input(z.object({branch: z.string(), id_object: z.number(), id_folder: z.number()}))
    .mutation(async ({input, ctx}) => {
      const all_deposit_object = await ctx.database.manyOrNone(`
        SELECT o.*, p_principal_element.value[1], p_principal_element.id_metadata
        FROM ${input.branch}_object o
        LEFT OUTER JOIN ${input.branch}_metadata m_principal_element ON m_principal_element.code = 'object_principalElement'
        INNER JOIN ${input.branch}_passport p_principal_element
          ON p_principal_element.id_item = o.id
          AND p_principal_element.item_type = 'object'
          AND p_principal_element.id_metadata = m_principal_element.id
        WHERE o.id_folder = $1 AND o.object_type = 'virtual'`,
        [input.id_folder]
      );

      const previous_principal_element = all_deposit_object.find((o) => o.value === 'yes');

      if(previous_principal_element && previous_principal_element.id === input.id_object){
        console.log(`OBJECT ${input.id_object} IS ALREADY THE PRINCIPAL ELEMENT OF DEPOSIT`);
        return;
      }

      if(previous_principal_element) {
        await ctx.database.none(`
          UPDATE ${input.branch}_passport
          SET value[1] = 'no'
          WHERE id_item = $1
          AND item_type = 'object'
          AND id_metadata = $2`,
          [previous_principal_element.id, previous_principal_element.id_metadata]
        );
        console.log(`UPDATE OBJECT ${previous_principal_element.id} METADATA 'object_principalElement' TO 'no'`);
      }

      await ctx.database.none(`
        UPDATE ${input.branch}_passport
        SET value[1] = 'yes'
        WHERE id_item = $1
        AND item_type = 'object'
        AND id_metadata = $2`,
        [input.id_object, all_deposit_object[0].id_metadata]
      );

      console.log(`UPDATE OBJECT ${input.id_object} METADATA 'object_principalElement' TO 'yes'`);
      const result = await ctx.database.manyOrNone(`
        SELECT o.*, p_principal_element.value[1]
        FROM ${input.branch}_object o
        LEFT OUTER JOIN ${input.branch}_metadata m_principal_element ON m_principal_element.code = 'object_principalElement'
        INNER JOIN ${input.branch}_passport p_principal_element
          ON p_principal_element.id_item = o.id
          AND p_principal_element.item_type = 'object'
          AND p_principal_element.id_metadata = m_principal_element.id
        WHERE o.id = $1`,
        [input.id_object]
      );

      console.log("RESULT => ", result)

      console.log("all_deposit_object => ", all_deposit_object)
    }),

    getNbObjects: t.procedure
    .input(z.object({branch: z.string()}))
    .output(z.number())
    .query(async ({input, ctx}) => {
      const {nb_objects} = await ctx.database.one(`
        SELECT COUNT(*)::integer as nb_objects
        FROM ${input.branch}_object`
      );

      return nb_objects;
    }),

    duplicateObject: t.procedure
    .input(z.object({branch, object_id: z.number(), user_id: z.number()}))
    .output(z.object({status: z.number(), message: z.string()}))
    .mutation(async ({input, ctx}) => {
      const objectToDuplicate = await ctx.database.oneOrNone(`
        SELECT name FROM ${input.branch}_object WHERE id = $1`,
        [input.object_id]
      );

      if(!objectToDuplicate){
        return {status: 404, message: "Object not found"};
      };

      const clear_name = objectToDuplicate.name.replace(' - Copy', '');
      const dup_index = await ctx.database.manyOrNone(`
        SELECT name FROM ${input.branch}_object WHERE name LIKE '${clear_name} - Copy%' ORDER BY name DESC`
      ).then((duplicates) => { return duplicates.map((d) => parseInt(d.name.slice(-1))) });

      const newName = `${clear_name} - Copy${dup_index.length > 0 ? dup_index[0] + 1 : 1}`;

      const modelsList = await ctx.database.manyOrNone(`
        SELECT * FROM ${input.branch}_metadata_model`
      );

      let metadataModelValues: Map<string, any[]> = new Map();
      for(const model of modelsList){
        const metadataOfModel = await ctx.database.manyOrNone(`
          SELECT * FROM ${input.branch}_metadata WHERE id_metadata_model = $1`,
          [model.id]
        );

        const metadataValues = await ctx.metadata.retrieveMetadataValueOfModel(input.branch, metadataOfModel, input.object_id, 'object')
        if(metadataValues.length > 0)
          metadataModelValues.set(model.name, metadataValues);
      }

      // USE TRANSACTION -> If one query fails, all the others are cancelled
      const result = await ctx.database.tx(async (task) => {
        const duplicateObject = await task.one(`INSERT INTO ${input.branch}_object (name, id_file_representative, version, date_integration, root_dir, id_nakala, id_folder, object_type) SELECT $1, id_file_representative, version, now(), root_dir, id_nakala, id_folder, object_type FROM ${input.branch}_object WHERE id = $2 RETURNING id`, [newName ,input.object_id]);

        const newObject_id = Number.parseInt(duplicateObject.id);

        // Duplicates metadata
        // For each models
        for(const [modelId, metadataValues] of metadataModelValues){
          // For each metadatas
          for(const metadataValue of metadataValues){
            switch(metadataValue.status){
              case "actor":
                // For each actors
                await task.batch(metadataValue.value.map((value: any) => {
                  task.none(`INSERT INTO ${input.branch}_actor_item (id_item, item_type, id_metadata, id_actor) VALUES ($1, $2, $3, $4)`, [newObject_id, "object", metadataValue.id, value.id]);
                }));
                break;
              case 'datation':
                // For each datations
                await task.batch(metadataValue.value.map((value: any) => {
                  task.none(`INSERT INTO ${input.branch}_datation_item (id_item, item_type, id_metadata, id_datation) VALUES ($1, $2, $3, $4)`, [newObject_id, "object", metadataValue.id, value.id]);
                }));
                // Check for periodo
                break;
              case 'location':
                // For each locations
                await task.batch(metadataValue.value.map((value: any) => {
                  task.none(`INSERT INTO ${input.branch}_location_item (id_item, item_type, id_metadata, id_location) VALUES ($1, $2, $3, $4)`, [newObject_id, "object", metadataValue.id, value.id]);
                }));
                break;
              case 'inventory':
                // For each inventories
                await task.batch(metadataValue.value.map((value: any) => {
                  task.none(`INSERT INTO ${input.branch}_inventory_item (id_item, item_type, id_metadata, id_inventory) VALUES ($1, $2, $3, $4)`, [newObject_id, "object", metadataValue.id, value.id]);
                }));
                break;
              case 'thesaurus':
              case 'multi':
              case 'pactols':
              case 'periodo':
                // Handle later
                break;
              default:
                // For each values
                for(const i in metadataValue.value){
                  await task.none(`INSERT INTO ${input.branch}_passport (id_item, item_type, id_metadata, value[${i}]) VALUES ($1, $2, $3, $4)`, [newObject_id, "object", metadataValue.id, metadataValue.value[i]]);
                }
            }
          }
        }

        // Duplicates thesaurus + tags
        await task.none(`INSERT INTO ${input.branch}_thesaurus_item (id_thesaurus, thesaurus, id_thes_thesaurus, id_item, item_type, public, id_user, date_modified, thes_path, qualifier, id_metadata) SELECT id_thesaurus, thesaurus, id_thes_thesaurus, $1, 'object', public, $2, now(), thes_path, qualifier, id_metadata FROM ${input.branch}_thesaurus_item WHERE id_item = $3`, [newObject_id, input.user_id, input.object_id]);
        await task.none(`INSERT INTO ${input.branch}_item_tag (id_item, item_type, id_tag, id_metadata, date_tagged) SELECT $1, 'object', id_tag, id_metadata, now() FROM ${input.branch}_item_tag WHERE id_item = $2`, [newObject_id, input.object_id]);
        await task.none(`INSERT INTO ${input.branch}_thesaurus_multi_item (id_thesaurus, thesaurus, id_thes_thesaurus, id_item, item_type, public, id_user, date_modified, thes_path, qualifier, id_metadata) SELECT id_thesaurus, thesaurus, id_thes_thesaurus, $1, 'object', public, $2, now(), thes_path, qualifier, id_metadata FROM ${input.branch}_thesaurus_multi_item WHERE id_item = $3`, [newObject_id, input.user_id, input.object_id]);
        await task.none(`INSERT INTO ${input.branch}_thesaurus_pactols_item (id_thesaurus, thesaurus, id_thes_thesaurus, id_item, item_type, public, id_user, date_modified, qualifier, collection, id_metadata) SELECT id_thesaurus, thesaurus, id_thes_thesaurus, $1, 'object', public, $2, now(), qualifier, collection, id_metadata FROM ${input.branch}_thesaurus_pactols_item WHERE id_item = $3`, [newObject_id, input.user_id, input.object_id]);
        await task.none(`INSERT INTO ${input.branch}_thesaurus_periodo_item (id_periodo, id_thes_periodo, id_item, item_type, language, public, id_user, date_modified, qualifier, id_metadata) SELECT id_periodo, id_thes_periodo, $1, 'object', language, public, $2, now(), qualifier, id_metadata FROM ${input.branch}_thesaurus_periodo_item WHERE id_item = $3`, [newObject_id, input.user_id, input.object_id]);
      }).then(() => {
        return {status: 200, message: "Object duplicated with all metadata"};
      }).catch((err) => {
        return {status: 500, message: `Failed to duplicate object: ${err.message}`};
      })

      return result;
    }),

});
