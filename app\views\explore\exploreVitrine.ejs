<!-- bool used as parameter to remove the selection dropdown to pages that dont need it -->
<%
if (typeof selection_dropdown !== 'undefined') {
    var disableSelection = !selection_dropdown;
} else if (typeof disableSelection === 'undefined') {
    var disableSelection = false;
}
%>


<div id="explore-div" class="d-flex container flex-column w-100" style="display: none !important;">
    <div class="d-flex flex-column">
        <div class="d-flex align-items-baseline justify-content-between w-100">
            <!-- Left side: Mobile placeholder + Desktop controls -->
            <div class="d-flex align-items-center">
                <!-- Mobile menu button placeholder - will be created by JavaScript -->
                <div id="mobile-menu-placeholder" class="show-on-mobile"></div>

                <!-- Desktop controls -->
                <div id="display-menu" class="hide-on-mobile d-flex flex-wrap align-items-center gap-2">
                <% if (!disableSelection) { %>
                    <% if ((typeof user !== "undefined" && user.id) && ((user.user_status === 'admin') ||(user.user_status === 'scribe') || (user.user_status === 'user'))) { %>
                    <div id="item-selection" class="d-flex flex-column">
                        <div class="dropdown selection-dropdown">
                            <div class="d-flex align-items-center dropdown-container" id="selectionDropdownContainer">
                                <div class="form-check me-2">
                                    <input class="form-check-input" type="checkbox" id="selectPageCheckbox">
                                </div>
                                <div class="selection-count me-2">
                                    <%=__('selected')%>: <span id="nb-selected">0</span>
                                </div>
                                <div class="dropdown-toggle-icon">
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                            </div>
                            <ul class="dropdown-menu selection-dropdown-menu">
                                <li id="save-selection">
                                    <a type="button" class="dropdown-item">
                                        <%=__('selectionSave')%>
                                    </a>
                                </li>
                                <li>
                                    <a type="button" class="dropdown-item" href="/selectionV/<%= projectId %>">
                                        <%=__('selectionOpen')%>
                                    </a>
                                </li>
                                <%
                                let lngParam = 'null';
                                if (typeof lng !== 'undefined' && lng !== null) {
                                    lngParam = `'${String(lng)}'`;
                                }
                                %>
                                <li>
                                    <!-- do not add quotes around lngParam -->
                                    <a type="button" class="dropdown-item" href="#" onclick="removeSelection('<%= user.id %>', '<%= projectId %>', <%= lngParam %>)">
                                        <%=__('selectionDelete')%>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <% } %>
                <% } %>
                </div>
            </div>

            <div class="ml-auto d-flex align-items-center">
                <div class="d-flex align-items-center me-2 desktop-specific-view-options">
                    <div id="grid-slider-container" class="d-flex align-items-center d-none me-2">
                        <i class="fas fa-th-large slider-icon me-2" title="<%=__('fewerItemsPerRow')%>"></i>
                        <div class="slider-wrapper" style="position: relative;">
                            <span id="grid-slider-tooltip"></span>
                            <input type="range" id="grid-slider" min="4" max="10" value="7" class="form-range custom-range" style="width: 80px;">
                        </div>
                        <i class="fas fa-th slider-icon ms-2" title="<%=__('moreItemsPerRow')%>"></i>
                    </div>

                    <div id="compact-slider-container" class="d-flex align-items-center d-none me-2">
                        <i class="fas fa-expand-arrows-alt slider-icon me-2" title="<%=__('normalView')%>"></i>
                        <input type="range" id="compact-slider" min="0" max="1" value="0" class="form-range custom-range" style="width: 60px;">
                        <i class="fas fa-compress slider-icon ms-2" title="<%=__('compactView')%>"></i>
                    </div>

                    <div class="input-group group-borders">
                        <button id="grid-btn" class="btn btn-secondary btn-active" type="button" title="<%=__('displayGrid')%>"
                                onclick="switchDisplay('grid')"><i class="fas fa-th-large fa-lg"></i></button>
                        <button id="list-btn" class="btn btn-secondary btn-hand" type="button"
                                title="<%=__('displayList')%>" onclick="switchDisplay('list')"><i
                            class="fas fa-list-ul fa-lg"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- if conceptName is defined, we are in the concept explore page, so we don't need to display this number of results -->
    <% if (typeof conceptName === 'undefined') { %>
        <span id="nb-results" class="mb-1"></span>
    <% } %>
    <div id="explore-results" class="d-flex flex-column"></div>
</div>

<link rel="stylesheet" href="/css/selection-dropdown.css">
<link rel="stylesheet" href="/css/size-sliders.css">

<script src="/js/searchable-dropdown.js"></script>
<script src="/js/selection-dropdown.js"></script>
<script src="/js/lazy-loading.js"></script>
<script src="/js/size-sliders.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('grid-slider');

    const fontSizeMap = {
        4: 16,
        5: 15,
        6: 14,
        7: 13,
        8: 12,
        9: 11,
        10: 10
    };

    function updateFontSize() {
        const sliderValue = slider.value;
        const fontSize = fontSizeMap[sliderValue] || 13; // fallback to 13 if not found
        const gridItems = document.getElementsByClassName('grid-vitrine-item-text');
        for (let i = 0; i < gridItems.length; i++) {
            gridItems[i].style.setProperty('font-size', fontSize + 'px', 'important');
        }
    }

    if (slider) {
        slider.addEventListener('input', updateFontSize);
    }

    // Observe DOM changes to update font size when new items are added
    const observer = new MutationObserver(function(mutationsList, observer) {
        updateFontSize();
    });

    observer.observe(document.body, { childList: true, subtree: true });

    // Initial call in case items are already present
    updateFontSize();
});
</script>