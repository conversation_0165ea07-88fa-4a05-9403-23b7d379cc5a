import { z } from "zod";
import { t } from "~/trpcContext";
import {create} from 'xmlbuilder2';
import { branch } from "~/types/schemas";
import { METADATA_FORMATS, OAI_PREFIXES, BRANCH_IDENTITIES, xmlStart } from "~/types/oai/global";
import { OAIDC_Record, buildV1ObjectRecordOAIDC, buildV2ObjectRecordOAIDC } from "~/types/oai/oai_dc";
import { IDatabase } from "pg-promise";
import { XMLBuilder } from "xmlbuilder2/lib/interfaces";

const maxRecords = 100;
const maxIdentifiers = 200;
const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z$/;

function errorResponse(xml: XMLBuilder, code: string, opt?: any) {
    switch(code) {
        case 'badArgument':
            xml.root().ele('error').att({ code: 'badArgument'}).txt(`The value of the '${opt}' argument is not valid.`);
            break;
        case 'noRecordsMatch':
            xml.root().ele('error').att({ code: 'badArgument'}).txt(`No records were found with the provided arguments.`);
            break;
        case 'cannotDisseminateFormat':
            xml.root().ele('error').att({ code: 'cannotDisseminateFormat'}).txt(`The metadata format provided is not supported by this server.`);
            break;
        case 'idDoesNotExist':
            xml.root().ele('error').att({ code: 'idDoesNotExist'}).txt(`The identifier provided does not exist.`);
            break;
        case 'invalidArgument':
            xml.root().ele('error').att({ code: 'invalidArgument'}).txt(`Invalid argument with verb '${opt}'.`);
            break;
        case 'badResumptionToken':
            xml.root().ele('error').att({ code: 'badResumptionToken'}).txt(`The provided resumption token is invalid.`);
            break;
        case 'badVerb':
            xml.root().ele('error').att({ code: 'badVerb'}).txt(`The provided verb is illegal or not supported by this server.`);
            break;
        default:
            xml.root().ele('error').att({ code: 'badArgument'}).txt(`Cannot process request with the provided arguments.`);
            break;
    }
    return xml.end();
}

type FilterResponse = {status: 'error', code: string, opt?: any} | {status: 'success', filters: string[]};

async function filtersFromInput(input: any, database: IDatabase<any>): Promise<FilterResponse> {
    try{
        input.from = input.from ? new Date(input.from)?.toISOString() : undefined;
    }catch(error){
        return {status: 'error', code: 'badArgument', opt: 'from'};
    }

    try{
        input.until = input.until ? new Date(input.until)?.toISOString() : undefined;
    }catch(error){
        return {status: 'error', code: 'badArgument', opt: 'until'};
    }

    let filters = [];
    if(input.from && input.until) {
        if(!input.from || !isoRegex.test(input.from)) {
            return {status: 'error', code: 'badArgument', opt: 'from'};
        }

        if(input.until && !isoRegex.test(input.until)) {
            return {status: 'error', code: 'badArgument', opt: 'until'};
        }
        filters.push(`header_datestamp BETWEEN '${input.from}' AND '${input.until}'`);
    }

    if(input.from){
        if(!isoRegex.test(input.from)) {
            return {status: 'error', code: 'badArgument', opt: 'from'};
        }
        filters.push(`header_datestamp >= '${input.from}'`);
    }

    if(input.until){
        if(!isoRegex.test(input.until)) {
            return {status: 'error', code: 'badArgument', opt: 'until'};
        }
        filters.push(`header_datestamp <= '${input.until}'`);
    }

    if(input.set){
        filters.push(`s.setspec = '${input.set}'`);
        const exists = await database.oneOrNone(`SELECT 1 FROM ${input.branch}_set WHERE setspec = '${input.set}'`);
        if(!exists){
            return {status: 'error', code: 'noRecordsMatch'};
        }
    }

    return {status: 'success', filters: filters};
}

export default t.router({
    getRecord: t.procedure
    .input(z.object({
        branch: branch,
        metadataPrefix: z.string(), 
        identifier: z.string()
    }))
    .output(z.string())
    .query(async ({ input, ctx }) => {
        let xml = create(xmlStart({ verb: 'GetRecord', ...input }, input.branch));

        let meta_format = METADATA_FORMATS.find(f => f.metadataPrefix === input.metadataPrefix);

        if(!meta_format){
            xml.root().ele('Error').ele('message').txt('Metadata prefix not supported');
            return z.string().parse(xml.end());
        }
        
        const records_table = `${input.branch}_${meta_format.metadataPrefix}_records`;

        try {
            await ctx.database.one(`SELECT 1 FROM information_schema.tables WHERE table_name = '${records_table}'`);
        } catch (error) {
            xml.root().ele('Error').ele('message').txt('Metadata prefix not supported');
            return z.string().parse(xml.end());
        }

        let SQLtoXML;
        switch(input.metadataPrefix) {
            case 'oai_dc':
                SQLtoXML = OAIDC_Record.SQLtoXML;
                break;
        }

        const record = await ctx.database.oneOrNone(`
            SELECT *, s.setspec
            FROM ${records_table}
            INNER JOIN ${input.branch}_set s ON s.id = header_id_oai_set
            WHERE header_identifier = $1`,
            [input.identifier]
        );

        if(!record) {
            xml.root().ele('Error').ele('message').txt('Record not found');
            return z.string().parse(xml.end());
        }

        const verbEle = xml.root().ele('GetRecord');

        SQLtoXML!(verbEle, record, meta_format);

        return z.string().parse(xml.end());
    }),

    identify: t.procedure
    .input(z.object({
        branch: branch
    }))
    .output(z.string())
    .query(async ({ input, ctx }) => {
        let xml = create(xmlStart({ verb: 'Identify' }, input.branch));

        const verbEle = xml.root().ele('Identify');
        for (const [key,value] of Object.entries(BRANCH_IDENTITIES[input.branch])) {
            verbEle.ele(key).txt(value);
        }

        return z.string().parse(xml.toString());    
    }),

    listIdentifiers: t.procedure
    .input(z.object({
        branch: branch,
        metadataPrefix: z.string(), 
        set: z.string().optional(), 
        from: z.string().optional(), 
        until: z.string().optional(),
        offset: z.number().default(0)
    }))
    .output(z.string())
    .query(async ({ input, ctx }) => {
        const xml = create(xmlStart({ verb: 'ListIdentifiers', ...input }, input.branch));

        const meta_format = METADATA_FORMATS.find((m) => m.metadataPrefix === input.metadataPrefix);

        if(!meta_format){
            return z.string().parse(errorResponse(xml, 'cannotDisseminateFormat'));
        }

        const records_table = `${input.branch}_${meta_format.metadataPrefix}_records`;

        const filters_from_input = await filtersFromInput(input, ctx.database);

        if(filters_from_input.status === 'error'){
            return z.string().parse(errorResponse(xml, filters_from_input.code, filters_from_input.opt));
        }

        const filters = filters_from_input.filters;

        const records = await ctx.database.manyOrNone(`
            SELECT header_identifier, header_datestamp, s.setspec
            FROM ${records_table} r
            INNER JOIN ${input.branch}_set s ON s.id = r.header_id_oai_set
            ${filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : ''}
            LIMIT ${maxIdentifiers} 
            OFFSET ${input.offset}`
        );

        if(records.length === 0){
            return z.string().parse(errorResponse(xml, 'noRecordsMatch'));
        }

        const {nb_records} = await ctx.database.one(`
            SELECT COUNT(*) AS nb_records
            FROM ${records_table} r
            INNER JOIN ${input.branch}_set s ON s.id = r.header_id_oai_set
            ${filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : ''}`
        );
        
        const verbEle = xml.root().ele('ListIdentifiers');

        for (const record of records) {
            verbEle.ele('header')
                   .ele('identifier').txt(record.header_identifier).up()
                   .ele('datestamp').txt(record.header_datestamp).up()
                   .ele('setSpec').txt(record.setspec)
        }

        if(input.offset + records.length < nb_records) {
            const cursor = input.offset;
            input.offset += maxIdentifiers;
            const resumptionToken = Buffer.from(JSON.stringify(input)).toString("base64");

            verbEle.ele('resumptionToken')
                .att('completeListSize', nb_records.toString())
                .att('cursor', cursor.toString())
                .txt(resumptionToken);
        }

        return z.string().parse(xml.end());
    }),

    /**
     * USE THE IDENTIFEIR FILTER
     */
    listMetadataFormats: t.procedure
    .input(z.object({
        branch: branch, 
        identifier: z.string().optional()
    }))
    .output(z.string())
    .query(async ({ input, ctx }) => {
        let xml = create(xmlStart({ verb: 'ListMetadataFormats', ...input }, input.branch));

        let formats = [];
        if(input.identifier) {
            for(const m of METADATA_FORMATS) {
                try{
                    const exists = await ctx.database.oneOrNone(`SELECT * FROM ${input.branch}_${m.metadataPrefix}_records WHERE header_identifier = $1 LIMIT 1`, [input.identifier]);
                    if(exists) {
                        formats.push(m);
                    }
                }catch(e) {
                    console.log(`OAI-PMH CONTROLLER WARNING: CATCH ERROR NON EXISTING TABLE (${input.branch}_${m.metadataPrefix}_records)` )
                    continue;
                }
            }
            if(formats.length === 0) {
                return z.string().parse(errorResponse(xml, 'idDoesNotExist'));
            }
        }else{
            formats = METADATA_FORMATS;
        }

        const verbEle = xml.root().ele('ListMetadataFormats');
        for (const format of formats) {
            verbEle.ele('metadataFormat')
                   .ele('metadataPrefix').txt(format.metadataPrefix).up()
                   .ele('schema').txt(format.schema).up()
                   .ele('metadataNamespace').txt(format.metadataNamespace).up()
        }

        return z.string().parse(xml.toString());    
    }),

    listMetadataPrefixes: t.procedure
    .query(async () => {
        return METADATA_FORMATS.map((mf) => mf.metadataPrefix);
    }),

    updateRecordsTable: t.procedure
    .input(z.object({
        branch: branch,
        metadataPrefix: z.string().default('oai_dc'),
        id_oai_set: z.number().default(1)
    }))
    .output(z.object({
        status: z.string(), 
        message: z.string(),
        nb_records: z.number().optional()
    }))
    .mutation(async ({ input, ctx }) => {

        const meta_format = METADATA_FORMATS.find((m) => m.metadataPrefix === input.metadataPrefix);

        if(!meta_format){
            return {
                status: 'error',
                message: `The metadata format ${input.metadataPrefix} hasn't been found in METADATA_FORMATS of server/types/oai/global.ts.`
            }
        }
        
        const records_table = `${input.branch}_${meta_format.metadataPrefix}_records`;

        try {
            await ctx.database.one(`SELECT 1 FROM information_schema.tables WHERE table_name = '${records_table}'`);
        } catch (error) {
            return {
                status: 'error',
                message: `The records table ${records_table} doesn't exist.`
            }
        }

        const object_ids: {id: number, name: string, doi: string, date_integration: Date, object_type: string, id_oai_set: number}[] = await ctx.database.manyOrNone(`
            SELECT o.id, o.name, o.doi, o.date_integration, o.object_type, r.header_id_oai_set
            FROM ${input.branch}_object o
            INNER JOIN ${input.branch}_doi d ON d.id_item = o.id AND d.item_type = 'object' AND d.doi IS NOT NULL
            LEFT JOIN ${records_table} r ON r.id_object = o.id
            WHERE o.doi IS NOT NULL
            ORDER BY id
        `);


        let records_promises = [];
        switch (meta_format.metadataPrefix) {
            case 'oai_dc':
                for (const object of object_ids) {
                    const identifier = OAI_PREFIXES[input.branch] + object.doi.replaceAll('/', ':').replaceAll('.', '_');
                    let record = new OAIDC_Record(
                        object.id, 
                        identifier, 
                        object.date_integration.toISOString().slice(0, -5) + 'Z', 
                        object.id_oai_set ?? input.id_oai_set,
                        {
                            dc_title: object.name + ' (3D Object)',
                            dc_type: '3D Object',
                            dc_language: 'fr',
                        }
                    );
        
                    if(object.object_type){
                        records_promises.push(buildV2ObjectRecordOAIDC(input.branch, ctx.database, object.id, record));
                    }else{
                        records_promises.push(buildV1ObjectRecordOAIDC(input.branch, ctx.database, object.id, record));
                    }
                }
                break;
            default:
                console.log(`OAI-PMH CONTROLLER WARNING: Metadata format ${meta_format.metadataPrefix} not supported.`);
                return {
                    status: 'error',
                    message: `The logic for metadata format ${input.metadataPrefix} is not implemented yet. Please create a new class in server/types/oai that extends ARecord.`
                }
        }

        let rejected_objects = [];
        let response: {status: string, message: string, nb_records?: number} = {
            status: 'error',
            message: 'No records created, an error occured in promises.'
        };
        await Promise.allSettled(records_promises).then(async (results)=>{
            // If some results are rejected
            if(results.some(result => result.status === 'rejected')){
                rejected_objects = results.filter(result => result.status === 'rejected');
                console.log(`OAI-PMH CONTROLLER WARNING: ${rejected_objects.length} objects rejected when creating OAI-PMH records. ${rejected_objects.map(obj => obj.reason + '\n')}`);
            }

            // If some results are fulfilled 
            if(results.some(result => result.status === 'fulfilled')){
                // Delete previous records
                ctx.database.none(`DELETE FROM ${input.branch}_${meta_format.metadataPrefix}_records`);

                const ff_records = results.filter(result => result.status === 'fulfilled').map(result => result.value);

                let ok = true, index = 0;
                while(ok && index < ff_records.length){
                    ok = await ctx.database.none(`
                        INSERT INTO ${input.branch}_${meta_format.metadataPrefix}_records
                        ${OAIDC_Record.SQLFields()}
                        VALUES ${ff_records[index].toSQLValue()}`
                    ).then(() => {
                        index++;
                        return true
                    }).catch((e) => {
                        console.log(`OAI-PMH CONTROLLER ERROR: FAILED TO CREATE OAI-PMH record (vo id: ${ff_records[index].object_id}). An error occured in database query.\n`, e);
                        return false;
                    });
                }

                if(!ok){
                    return {
                        status: 'error',
                        message: `Created/Updated ${index} records, an error occured in database query.`
                    }
                }

                let message = `Created/Updated ${ff_records.length} records.`
                
                if(rejected_objects.length > 0){
                    message += ` ${rejected_objects.length} rejected records (see logs for more details).`;
                }
                
                response = {
                    status: 'success',
                    message: message,
                    nb_records: ff_records.length
                }
            }else{
                console.log(`OAI-PMH CONTROLLER WARNING: No records created, an error occured in promises. Zero promises fullfilled and ${rejected_objects.length} rejected.`);
                response = {
                    status: 'error',
                    message: `No records created, an error occured in promises. Zero promises fullfilled and ${rejected_objects.length} rejected.`
                }
            }
        });

        return response;
    }),

    /**
     *
     */
    listRecords: t.procedure
    .input(z.object({
        branch: branch,
        metadataPrefix: z.string(), 
        set: z.string().optional(), 
        from: z.string().optional(), 
        until: z.string().optional(),
        offset: z.number().default(0)
    }))
    .output(z.string())
    .query(async ({ input, ctx }) => {
        let xml = create(xmlStart({ verb: 'ListRecords', ...input }, input.branch));

        const meta_format = METADATA_FORMATS.find((mf) => mf.metadataPrefix === input.metadataPrefix);

        if(!meta_format){
            console.log(`OAI-PMH CONTROLLER WARNING: Metadata format ${input.metadataPrefix} not supported.`);
            return z.string().parse(errorResponse(xml, 'cannotDisseminateFormat'));
        }

        let records_table = `${input.branch}_${meta_format.metadataPrefix}_records`;

        try {
            await ctx.database.one(`SELECT 1 FROM information_schema.tables WHERE table_name = '${records_table}'`);
        } catch (error) {
            return z.string().parse(errorResponse(xml, 'cannotDisseminateFormat'));
        }

        let SQLtoXML;
        switch (input.metadataPrefix) {
            case 'oai_dc':
                SQLtoXML = OAIDC_Record.SQLtoXML;
                break;
            default:
                console.log(`OAI-PMH CONTROLLER WARNING: Metadata format ${input.metadataPrefix} not fully supported.`);
                return z.string().parse(errorResponse(xml, 'cannotDisseminateFormat'));
        }

        const filters_from_input = await filtersFromInput(input, ctx.database);

        if(filters_from_input.status === 'error'){
            return z.string().parse(errorResponse(xml, filters_from_input.code, filters_from_input.opt));
        }

        const filters = filters_from_input.filters;

        const records = await ctx.database.manyOrNone(`
            SELECT *, s.setspec
            FROM ${records_table} r
            INNER JOIN ${input.branch}_set s ON r.header_id_oai_set = s.id 
            ${filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : ''}
            LIMIT ${maxRecords}
            OFFSET ${input.offset}`
        );

        const {nb_records} = await ctx.database.one(`
            SELECT count(*) AS nb_records
            FROM ${records_table} r
            INNER JOIN ${input.branch}_set s ON r.header_id_oai_set = s.id
            ${filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : ''}
        `);

        if(records.length === 0){
            return z.string().parse(errorResponse(xml, 'noRecordsMatch'));
        }

        let verbEle = xml.root().ele('ListRecords');
        
        for(let r of records){
            SQLtoXML!(verbEle, r, meta_format);
        }

        if(input.offset + records.length < nb_records){
            const cursor = input.offset;
            input.offset += maxRecords;
            const resumptionToken = Buffer.from(JSON.stringify(input)).toString("base64");

            verbEle.ele('resumptionToken')
                .att('completeListSize', nb_records.toString())
                .att('cursor', cursor.toString())
                .txt(resumptionToken);
        }

        if(records.length <= nb_records){
            verbEle.ele('resumptionToken')
                .att('completeListSize', nb_records.toString())
                .att('cursor', input.offset.toString())
        }

        return z.string().parse(xml.end());
    }),

    // Normally the resumptionToken should be necessary
    // But it's never used in our case beacause there's very few number of sets
    listSetsXML: t.procedure
    .input(z.object({
        branch: branch
    }))
    .output(z.string())
    .query(async ({ input, ctx: { database } }) => {
        let xml = create(xmlStart({ verb: 'ListSets' }, input.branch));  

        const sets_branch = await database.manyOrNone(`
            SELECT *
            FROM ${input.branch}_set
        `);

        const verbEle = xml.root().ele('ListSets');
        for (const set of sets_branch) {
            verbEle
                .ele('set')
                .ele('setSpec').txt(set.setspec).up()
                .ele('setName').txt(set.name).up()
                .ele('setDescription').txt(set.description).up()
        }

        return z.string().parse(xml.end());
    }),

    listSetsJS: t.procedure
    .input(z.object({
        branch: branch
    }))
    .output(z.object({id: z.number(), name: z.string(), setspec: z.string(), description: z.string()}).array().nullish())
    .query(async ({ input, ctx }) => {
        const sets = await ctx.database.manyOrNone(`
            SELECT id, name, description, setspec
            FROM ${input.branch}_set
        `);

        return sets;  
    }),

    getNbRecords: t.procedure
    .input(z.object({
        branch: branch,
        metadataPrefix: z.string()
    }))
    .output(z.number().nullish())
    .query(async ({ input, ctx: { database } }) => {
        switch (input.metadataPrefix) {
            case 'oai_dc':
                return (await database.oneOrNone(`SELECT count(*)::integer as nb_records FROM ${input.branch}_oai_dc_records`))?.nb_records;
            default:
                return null;
        }
    }),

    invalidArgumentsError: t.procedure
    .input(z.object({
        branch: branch,
        verb: z.string(),
        args: z.any()
    }))
    .output(z.string())
    .query(({input}) => {
        const xml = create(xmlStart({ verb: input.verb, ...input.args }, input.branch));
        return z.string().parse(errorResponse(xml, 'invalidArgument', input.verb));
    }),

    badResumptionTokenError: t.procedure
    .input(z.object({
        branch: branch,
        verb: z.string(),
        args: z.any()
    }))
    .output(z.string())
    .query(({input}) => {
        const xml = create(xmlStart({ verb: input.verb, ...input.args }, input.branch));
        return z.string().parse(errorResponse(xml, 'badResumptionToken'));
    }),

    badVerbError: t.procedure
    .input(z.object({
        branch: branch,
        verb: z.string(),
        args: z.any()
    }))
    .output(z.string())
    .query(({input}) => {
        const xml = create(xmlStart({ verb: input.verb, ...input.args }, input.branch));
        return z.string().parse(errorResponse(xml, 'badVerb'));
    }),
})
