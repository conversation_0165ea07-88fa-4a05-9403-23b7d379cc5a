<!DOCTYPE HTML>
<html>

<head>
    <%- include('head_admin.ejs') %>
    <% if (locals.branch) { %>
        <link rel="stylesheet" href="/assets/css/<%= locals.branch %>.css">
    <% } else { %>
        <link rel="stylesheet" href="/assets/css/pft3d.css">
    <% } %>
</head>

<body>
    <%- include('header_dashboard.ejs') %>

    <div class="d-flex" style="padding-top: 32px;">
        <div class="offcanvas-xl offcanvas-start overflow-y-auto border menuIndex" id="menuGaucheAdmin">
            <div class="offcanvas-body d-flex flex-column gap-4 p-2">
                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fa fa-folder-open"></i><%=__('folders')[0].toUpperCase()+__('folders').slice(1) %>
                    </h4>
                    <form id="permFoldersForm" action="/admin/permFolders" method="post" class="d-none"></form>
                    <form id="folderOrderForm" action="/admin/folderOrder" method="post" class="d-none"></form>

                    <div class="list-group">
                        <a type="button" onclick="document.getElementById('folderOrderForm').submit()"
                           class="list-group-item">
                            <i class="fas fa-arrows-alt-v me-2" aria-hidden="true"></i><%=__('changeOrder')%>
                        </a>
                        <a type="button" onclick="document.getElementById('permFoldersForm').submit()"
                           class="list-group-item">
                            <i class="fas fa-unlock me-2" aria-hidden="true"></i>Public / <%=__('private') %>
                            <%=__('status')%>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fas fa-file"></i><%=__('files')[0].toUpperCase()+__('files').slice(1)%>
                    </h4>
                    <div class="list-group">
                        <a type="button" href="/admin/synchro" class="list-group-item">
                            <i class="fas fa-sync me-2"></i><%=__('add') %> / <%=__('synchronize')%> <%=__('data')%>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center">
                        <i class="fa fa-female"></i><i class="fa fa-male me-2"></i><%= __('user')%>s
                    </h4>
                    <div class="list-group">
                        <a type="button" href="/admin/adduser" class="list-group-item">
                            <i
                               class="fas fa-user-plus me-2"></i><%=__('create')[0].toUpperCase()+__('create').slice(1)%>
                            <%=__('user')%>
                        </a>
                        <a type="button" href="/admin/editUser" class="list-group-item">
                            <i class="fas fa-edit me-2"></i><%=__('edit')%> <%=__('user')%>
                        </a>
                        <a type="button" href="/admin/permUser" class="list-group-item">
                            <i class="fas fa-lock me-2"></i>Permission <%=__('of4') %> <%=__('user')%>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fa fa-users"></i><%= __n('admin.category.group') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/groups", text: __('admin.group.list'), icon: "fas fa-list" }) %>
                        <%- include('../utils/list-link', { target: "/admin/addgroup", text: __('admin.group.add'), icon: "fa fa-plus" }) %>
                        <%- include('../utils/list-link', { target: "/admin/groupUsers", text: __('admin.group.edit'), icon: "fa fa-cogs" }) %>
                        <%- include('../utils/list-link', { target: "/admin/permGroup", text: __('admin.group.permissions'), icon: "fa fa-lock" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fas fa-file-alt"></i><%= __n('admin.category.metadata') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/scribe/addMetadataModel", text: __('admin.metadata.addModel'), icon: "fa fa-plus" }) %>
                        <%- include('../utils/list-link', { target: "/scribe/editMetadataModel", text: __('admin.metadata.editModel'), icon: "fa fa-edit" }) %>
                        <%- include('../utils/list-link', { target: "/admin/csvMetadata", text: __('admin.metadata.csv'), icon: "fas fa-file-code" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2"><i class="fas fa-book"></i>Thesaurus</h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/thesaurusAdd", text: __('addthesauruselem'), icon: "fas fa-plus" }) %>
                        <%- include('../utils/list-link', { target: "/admin/thesaurusLinkSelection", text: __('admin.thesaurus.link'), icon: "fas fa-clone" }) %>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid mt-1">
            <div class="d-flex align-items-center justify-content-between" id="admin">
                <div>
                    <button class="block d-xl-none btn" data-bs-toggle="offcanvas" data-bs-target="#menuGaucheAdmin">
                        <i class="fas fa-bars fa-lg"></i>
                    </button>
                    <button class="btn btn-primary" onclick="window.history.go(-1); return;">Back</button>
                </div>
                <div class="text-center">
                    <h2><a href="/admin"><%=__('dashboard')%> ArcheoGRID</a></h2>
                </div>
                <div class="d-flex gap-4 align-items-center">
                    <div class="text-center">
                        <%- include("../utils/language-switch", { lang_route: "admin" }) %>
                    </div>
                    <div class="d-flex flex-column gap-1" id="menuDroit">
                        <a type="button" href="/profil" class="btn btn-sm btn-light"><%= user.username %></a>
                        <a href="/logout" title="<%= __('deconnecte')%>"><%= __('deconnecte')%></a>
                    </div>
                </div>
            </div>


            <% if (typeof message !== "undefined" && message) { %>
            <div class="alert alert-success"><%= message %></div>
            <% } %>

            <%- body %>
        </div>

    </div>
    <%- include('footer_admin.ejs') %>
</body>

</html>