import { Router, static as expressStatic, type Request, type Request<PERSON><PERSON><PERSON> } from "express";
import async<PERSON><PERSON><PERSON> from "express-async-handler";
import i18n from "i18n";
import fs from "node:fs";
import path from "node:path";
import { getCsvPath, getFolderMetadataPath } from "../tools/helpers_tools";

import type { Branch } from "../../server/types/api";
import type { MetadataType } from "../../server/types/schemas/metadata";

import user_table from "../models/archeogrid_user";
import * as globals from "../tools/globals";
import { branchConfig, performRequest, validateEmail } from "../tools/globals";
import { archeogrid_client } from "../tools/request";
import { sanitizeSQLName } from "../tools/helpers_tools";

const __dirname = process.cwd();

const admin = Router();

// adminSessionChecker
admin.use((req, res, next) => {
  if (req.isAuthenticated()) {
    if (res.locals.user.user_status === "admin" || res.locals.user.user_status === "scribe") {
      next();
      return;
    }
    req.session.returnTo = req.originalUrl;
    res.redirect("/login");
    return;
  }
  req.session.returnTo = req.originalUrl;
  res.redirect("/login");
});

admin.use(
  asyncHandler(async (req, res, next) => {
    res.locals.branchList = await req.fetchApi(`/api/branche/${res.locals.lang}`);
    next();
  }),
);

admin.get(
  "/",
  asyncHandler(async (req, res) => {
    if (!res.locals.user) throw new Error();
    const renderEjs = res.locals.user.user_status === "admin" ? "admin/admin.ejs" : "admin/dashboard.ejs";
    const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    const paramRequest = res.locals.user.user_status === "admin" ? "projectsRoot" : "limitedProjectsRoot";

    res.locals.branch = branchConfig;
    const root_folder = (await req.fetchApi(`/api/${paramRequest}/${branchConfig}`, {
      userId: res.locals.user.id.toString(),
    })) as unknown[];
    let message = "Ask for a project !";
    if (res.locals.user.user_status === "scribe" && root_folder.length === 0 && res.locals.lang === "fr") {
      // TODO move to locales
      message = "Demandez un projet !";
    } else if (res.locals.user.user_status === "admin") {
      message = "";
    } else if (res.locals.user.user_status === "scribe" && root_folder.length ) {
      message = "";
    }

    res.render(renderEjs, {
      message,
      title: req.url,
      layout: layoutAdm,
    });
  }),
);

admin.use("/docs", expressStatic(path.join(process.cwd(), "..", "doc", "archeogrid", "build")));

admin
  .route("/adduser")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    if (!res.locals.user) throw new Error();
    const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    if (res.locals.user.user_status === "scribe") {
      // on demande pour quel projet créer un utilisateur, s'il n'y en n'a qu'un on le donne directement
      const paramRequest = "limitedProjectsRoot";
      const dataGet = {
        userId: res.locals.user.id,
      };
      performRequest(`/api/${paramRequest}/${branchConfig}`, "GET", dataGet, (rootfolder) => {
        res.render("admin/adduser", {
          listProjects: rootfolder,
          projects: "",
          username: "",
          emailvalue: "",
          signature: "",
          userExists: 0,
          email: 0,
          password: 0,
          success: 0,
          title: req.url,
          message: "",
          layout: layoutAdm,
        });
      });
    } else {
      res.render("admin/adduser", {
        listProjects: "",
        projects: "all",
        username: "",
        emailvalue: "",
        signature: "",
        userExists: 0,
        email: 0,
        password: 0,
        success: 0,
        title: req.url,
        message: "",
        layout: layoutAdm,
      });
    }
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      if (!res.locals.user) throw new Error();

      const projects = res.locals.user.user_status === "scribe" ? req.body.projects : "all";
      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      if (req.body.username) {
        const username = req.body.username;
        const mail = req.body.mail;
        const password2 = req.body.password2;
        const user = await user_table.findOne({ where: { username: username } });

        if (user) {
          res.render("admin/adduser", {
            listProjects: "",
            projects: projects,
            username: "",
            emailvalue: "",
            signature: req.body.signature,
            userExists: 1,
            email: 0,
            password: 0,
            success: 0,
            title: req.url,
            message: "",
            layout: layoutAdm,
          });
        } else {
          if (!validateEmail(mail)) {
            res.render("admin/adduser", {
              username: username,
              projects: projects,
              emailvalue: "",
              signature: req.body.signature,
              userExists: 0,
              email: 1,
              password: 0,
              success: 0,
              title: req.url,
              message: "",
              layout: layoutAdm,
            });
          } else {
            // check password
            if (password2 !== req.body.password) {
              res.render("admin/adduser", {
                projects: projects,
                username: username,
                emailvalue: req.body.mail,
                signature: req.body.signature,
                userExists: 0,
                email: 0,
                password: 1,
                entity: 0,
                success: 0,
                title: req.url,
                message: "",
                layout: layoutAdm,
              });
            } else {
              const dataPut = {
                username: req.body.username,
                password: req.body.password,
                email: req.body.mail,
                signature: req.body.signature,
                conservatoire_user: req.body.conservatoire,
                orcid: req.body.orcid,
                language: res.locals.lang,
              };
              // Create temp_user
              const [data, code] = await req.fetchApiCode("/api/users,id", dataPut, "POST");
              if (code === 201) {
                // si le status est scribe, il faut lier ce user à l'utilisateur qui vient d'être créé
                if (res.locals.user.user_status === "scribe") {
                  const dataPut = {
                    userId: data.id,
                  };
                  await req.fetchApi(`/api/usersCreatedByScribe/${branchConfig},${res.locals.user.id},username`, dataPut, "PUT");

                  // TODO lier le user créé au(x) projet(s) choisi
                  await req.fetchApi(`/api/userLinkProjects/${branchConfig},${data.id}`, { projects }, "PUT");
                }

                res.render("admin/adduser", {
                  projects: projects,
                  username: req.body.username,
                  emailvalue: req.body.mail,
                  signature: req.body.signature,
                  userExists: 0,
                  email: 1,
                  password: 1,
                  success: 1,
                  title: req.url,
                  message: "",
                  layout: layoutAdm,
                });
              }
            }
          }
        }
      } else {
        // on n'a pas encore les projets pour un scribe
        res.render("admin/adduser", {
          listProjects: "",
          projects: projects,
          username: "",
          emailvalue: "",
          signature: "",
          userExists: 0,
          email: 0,
          password: 0,
          success: 0,
          title: req.url,
          message: "",
          layout: layoutAdm,
        });
      }
    }),
  );

admin.route("/tempusers").get(
  asyncHandler(async (req, res) => {

    res.locals.branch = branchConfig;
    const data = await req.fetchApi("/api/tempuser");
    res.render("admin/tempusers", {
      tempusers: data,
      message: "",
      title: req.url,
      layout: "admin/layout_admin",
    });
  }),
);

admin.route("/tempuser/del/:idUser").get((req, res) => {
  res.locals.branch = branchConfig;
  performRequest(`/api/tempuser/${req.params.idUser}`, "DELETE", null, (data, code) => {
    res.sendStatus(code);
  });
});

admin.route("/user,:idUser").get(
  asyncHandler(async (req, res) => {
    res.locals.branch = branchConfig;
    // C'est un temp_user pour le conservatoire
    const tempuser = await req.fetchApi(`/api/tempuser/${req.params.idUser}`);
    const datapost = tempuser[0];
    user_table
      .create({
        username: datapost.username,
        mail_address: datapost.mail_address,
        password: datapost.password,
        user_status: "user",
        language: datapost.language,
        orcid: datapost.orcid,
        other_identifier: datapost.other_identifier,
        conservatoire_user: 1,
      })
      .then((user) => {
        const dataput = { status: "created" };
        const datalink = { id_entity: datapost.entity_id };
        performRequest(`/api/tempuser/${req.params.idUser}`, "PATCH", dataput, (_, code) => {
          if (code === 200) {
            performRequest(`/api/userEntity/${user.get().id}`, "PUT", datalink, () => {});
            res.render("admin/admin", {
              message: "User created",
              title: req.url,
              layout: "admin/layout_admin",
            });
          } else {
            req.flash("error", "Unable to update status");
            req.myroot("/admin/tempuser");
          }
        });
      })
      .catch(() => {
        req.flash("error", "Unable to create user ... ");
        req.myroot("/admin/tempuser");
      });
  }),
);

admin.route("/users").get((req, res) => {
  res.locals.branch = branchConfig;
  performRequest("/api/users,id", "GET", null, (data) => {
    res.render("admin/users", {
      users: data,
      title: req.url,
      message: "",
      layout: "admin/layout_admin",
    });
  });
});

admin.route("/userdelete,:userId").get((req, res) => {
  res.locals.branch = branchConfig;
  const userId = Number.parseInt(req.params.userId);
  performRequest(`/api/user/${userId}`, "DELETE", null, (dataDel, code) => {
    if (code === 500) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(dataDel));
    } else {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    }
  });
});

admin.route("/entitydelete,:entityId").get(
  asyncHandler(async (req, res) => {
    res.locals.branch = branchConfig;
    const [dataDel, code] = await req.fetchApiCode(`/api/entity/${req.params.entityId}`,{}, "DELETE");
    if (code === 500) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(dataDel.error_description.detail));
    } else if (code === 200) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    }
  }),
);

// ATTENTION, si on met /admin/edituser/qqch, node se perd et essaie de charger /admin/asset/...css
// Si on met /admin/edituser,qqch  c'est OK !
admin
  .route("/editUser")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    if (!res.locals.user) throw new Error();
    const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    performRequest("/api/branche", "GET", undefined, () => {
      const askUser =
        res.locals.user.user_status === "admin" ? "users," : `usersCreatedByScribe/${branchConfig},${res.locals.user.id},`;
      performRequest(`/api/${askUser}username`, "GET", undefined, (data) => {
        res.render("admin/edituser", {
          users: data,
          userData: "",
          userid: 0,
          message: "",
          title: req.url,
          entity: "",
          method: req.method,
          layout: layoutAdm,
        });
      });
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      if (!res.locals.user) throw new Error();
      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      const userid = req.body.userSelect;
      const datapatch = typeof req.body.username !== "undefined" ? req.body : null;

      const entity = await req.fetchApi("/api/entity");
      const datausers = await req.fetchApi("/api/users,name");
      const data = await req.fetchApi(`/api/user/${userid}`);
      if (typeof req.body.username !== "undefined") {
        // check si valeur du champ a été modifié pour envoyer à l'update ou non
        for (const item in req.body) {
          for (const itemOrig in data) {
            if (item === itemOrig) {
              if (req.body[item]) {
                if (req.body[item] === data[itemOrig]) {
                  delete datapatch[item];
                }
              } else {
                // les cas où le formulaire renvoie une valeur vide
                // cas du mot de pass inchangé
                if (item === "password") {
                  delete datapatch[item];
                }
                // null value
                if (!data[itemOrig]) {
                  // le body est '' et l'origine est NULL : pas d'update
                  delete datapatch[item];
                }
              }
            } else if (item === "entity") {
              if (itemOrig === "entity_id") {
                if (req.body[item] !== "" && Number.parseInt(req.body[item]) === data[itemOrig]) {
                  // l'entité de rattachement du user n'a pas changé

                  delete datapatch[item];
                }
              }
            } else if (item === "entity_id") {
              delete datapatch[item];
            }
          }
        }
        if (datapatch.mail_address && !validateEmail(datapatch.mail_address)) {
          const messageUpdate = "bad mail address";
          res.render("admin/edituser", {
            users: datausers,
            userData: data,
            userid: userid,
            message: messageUpdate,
            title: req.url,
            entity: entity,
            method: req.method,
            layout: layoutAdm,
          });
        } else {
          if (Object.keys(datapatch).length > 1) {
            // il y a des champs à mettre à jour
            performRequest(`/api/user/${userid}`, "PATCH", datapatch, (datatoupdate, code) => {
              if (code === 200) {
                const messageUpdate = "user well updated";
                res.render("admin/edituser", {
                  users: datausers,
                  userData: datatoupdate,
                  userid: userid,
                  message: messageUpdate,
                  title: req.url,
                  entity: entity,
                  method: req.method,
                  layout: layoutAdm,
                });
              } else {
                const messageUpdate = "ERROR updating User";
                res.render("admin/edituser", {
                  users: datausers,
                  userData: datatoupdate,
                  userid: userid,
                  message: messageUpdate,
                  title: req.url,
                  entity: entity,
                  method: req.method,
                  layout: layoutAdm,
                });
              }
            });
          } else {
            res.render("admin/edituser", {
              users: datausers,
              userData: data,
              userid: userid,
              message: "USER NOT UPDATED",
              title: req.url,
              entity: entity,
              method: req.method,
              layout: layoutAdm,
            });
          }
        }
      } else {
        res.render("admin/edituser", {
          users: datausers,
          userData: data,
          userid: userid,
          message: "",
          title: req.url,
          entity: entity,
          method: req.method,
          layout: layoutAdm,
        });
      }
    }),
  );

admin
  .route("/assignScribeUser")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/branche", "GET", null, (branche) => {
      performRequest("/api/scribeUsers/username", "GET", null, (data) => {
        res.render("admin/assignscribeuser", {
          branche: branche,
          userid: 0,
          users: data,
          root: "",
          userff: [],
          userperm: [],
          userWperm: [],
          message: "",
          title: req.url,
          method: req.method,
          layout: "admin/layout_admin",
        });
      });
    });
  })
  .post((req, res) => {
    res.locals.branch = branchConfig;
    const userid = Number.parseInt(req.body.userSelect);
    const root = req.body.rootSelect;
    performRequest(`/api/user/${userid}`, "GET", null, (userFull) => {
      performRequest(`/api/projectsRoot/${root}`, "GET", null, (data) => {
        performRequest(`/api/assignedProjects/${root},${userid}`, "GET", null, (dataProjects) => {
          res.render("admin/assignscribeuser", {
            branche: "",
            userid: userid,
            userScribe: userFull,
            users: "",
            root: root,
            assignedProjects: dataProjects,
            projects: data,
            message: "",
            title: req.url,
            method: req.method,
            layout: "admin/layout_admin",
          });
        });
      });
    });
  });

admin
  .route("/permUser")
  .get(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      const branche = await req.fetchApi("/api/branche");
      const askUser =
        res.locals.user.user_status === "admin" ? "users," : `usersCreatedByScribe/${branchConfig},${res.locals.user.id},`;
      performRequest(`/api/${askUser}username`, "GET", null, (data) => {
        res.render("admin/permuser", {
          branche: branche,
          userid: 0,
          users: data,
          root: "",
          userff: [],
          userperm: [],
          userWperm: [],
          message: "",
          title: req.url,
          method: req.method,
          layout: layoutAdm,
        });
      });
    }),
  )
  .post((req, res) => {
    res.locals.branch = branchConfig;
    const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    const userid = req.body.userSelect.split("_")[0];
    const username = req.body.userSelect.split("_")[1];
    const root = res.locals.user.user_status === "admin" ? req.body.rootSelect : branchConfig ;
    //const root = req.body.rootSelect;
    // pour un scribe, on propose de choisir seulement parmi les dossiers des projets auquel le user a été relié
    const forbiddenfolders =
      res.locals.user.user_status === "admin" ? "userOnlyForbiddenfolders" : "limitedUserOnlyForbiddenfolders";
    performRequest("/api/branche", "GET", null, (branche) => {
      if (
        req.body.forbid === "ok" ||
        req.body.access === "ok" ||
        req.body.writeForbid === "ok" ||
        req.body.writeAccess === "ok"
      ) {
        if (req.body.forbid === "ok") {
          performRequest(`/api/userAccessAdmin/${userid},${root}`, "DELETE", req.body, (_, code) => {
            if (code === 200) {
              // calculer les nouveaux perms du user et les afficher ... et toutes les infos des users pour pouvoir refaire
              // TODO : recalculer les droits du user connecté qui fait la manip et les remettre dans le user ??
              performRequest("/api/users,name", "GET", null, (datauser) => {
                performRequest(`/api/userAccessAdmin/${userid},${root}`, "GET", null, (dataperm) => {
                  performRequest(`/api/userWriteAccessAdmin/${userid},${root}`, "GET", null, (dataWperm) => {
                    performRequest(`/api/${forbiddenfolders}/${userid},${root}`, "GET", null, (userff) => {
                      res.render("admin/permuser", {
                        branche: branche,
                        userid: userid,
                        username: username,
                        users: datauser,
                        root: root,
                        userff: userff,
                        userperm: dataperm,
                        userWperm: dataWperm,
                        message: "OK",
                        title: req.url,
                        method: req.method,
                        layout: layoutAdm,
                      });
                    });
                  });
                });
              });
            } else {
              req.flash("error", "DELETE IMPOSSIBLE");
            }
          });
        } else if (req.body.access === "ok") {
          performRequest(`/api/userAccessAdmin/${userid},${root}`, "PUT", req.body, (datap, code) => {
            if (code === 201) {
              // calculer les nouveaux perms du user et les afficher ... et toutes les infus des users pour pouvoir refaire
              performRequest("/api/users,name", "GET", null, (datauser) => {
                performRequest(`/api/userAccessAdmin/${userid},${root}`, "GET", null, (dataperm) => {
                  performRequest(`/api/userWriteAccessAdmin/${userid},${root}`, "GET", null, (dataWperm) => {
                    performRequest(`/api/${forbiddenfolders}/${userid},${root}`, "GET", null, (userff) => {
                      res.render("admin/permuser", {
                        branche: branche,
                        userid: userid,
                        username: username,
                        users: datauser,
                        root: root,
                        userff: userff,
                        userperm: dataperm,
                        userWperm: dataWperm,
                        message: "OK",
                        title: req.url,
                        method: req.method,
                        layout: layoutAdm,
                      });
                    });
                  });
                });
              });
            } else {
              req.flash("error", "INSERT IMPOSSIBLE");
            }
          });
        } else if (req.body.writeForbid === "ok") {
          performRequest(`/api/userWriteAccessAdmin/${userid},${root}`, "DELETE", req.body, (datawp, codewf) => {
            if (codewf === 200) {
              // calculer les nouveaux perms du user et les afficher ... et toutes les infus des users pour pouvoir refaire
              performRequest("/api/users,name", "GET", null, (datauser) => {
                performRequest(`/api/userAccessAdmin/${userid},${root}`, "GET", null, (dataperm) => {
                  performRequest(`/api/userWriteAccessAdmin/${userid},${root}`, "GET", null, (dataWperm) => {
                    performRequest(`/api/${forbiddenfolders}/${userid},${root}`, "GET", null, (userff) => {
                      res.render("admin/permuser", {
                        branche: branche,
                        userid: userid,
                        username: username,
                        users: datauser,
                        root: root,
                        userff: userff,
                        userperm: dataperm,
                        userWperm: dataWperm,
                        message: "OK",
                        title: req.url,
                        method: req.method,
                        layout: layoutAdm,
                      });
                    });
                  });
                });
              });
            } else {
              req.flash("error", "DELETE IMPOSSIBLE");
            }
          });
        } else if (req.body.writeAccess === "ok") {
          performRequest(`/api/userWriteAccessAdmin/${userid},${root}`, "PUT", req.body, (_, code) => {
            if (code === 201) {
              // calculer les nouveaux perms du user et les afficher ... et toutes les infus des users pour pouvoir refaire
              performRequest("/api/users,name", "GET", null, (datauser) => {
                performRequest(`/api/userAccessAdmin/${userid},${root}`, "GET", null, (dataperm) => {
                  performRequest(`/api/userWriteAccessAdmin/${userid},${root}`, "GET", null, (dataWperm) => {
                    performRequest(`/api/${forbiddenfolders}/${userid},${root}`, "GET", null, (userff) => {
                      res.render("admin/permuser", {
                        branche: branche,
                        userid: userid,
                        username: username,
                        users: datauser,
                        root: root,
                        userff: userff,
                        userperm: dataperm,
                        userWperm: dataWperm,
                        message: "OK",
                        title: req.url,
                        method: req.method,
                        layout: layoutAdm,
                      });
                    });
                  });
                });
              });
            } else {
              req.flash("error", "INSERT IMPOSSIBLE");
            }
          });
        }
      } else {
        performRequest("/api/users,name", "GET", null, (datauser) => {
          performRequest(`/api/userAccessAdmin/${userid},${root}`, "GET", null, (dataperm) => {
            performRequest(`/api/userWriteAccessAdmin/${userid},${root}`, "GET", null, (dataWperm) => {
              performRequest(`/api/${forbiddenfolders}/${userid},${root}`, "GET", null, (userff) => {
                res.render("admin/permuser", {
                  branche: branche,
                  userid: userid,
                  username: username,
                  users: datauser,
                  root: root,
                  userff: userff,
                  userperm: dataperm,
                  userWperm: dataWperm,
                  message: "",
                  title: req.url,
                  method: req.method,
                  layout: layoutAdm,
                });
              });
            });
          });
        });
      }
    });
  });

admin.route("/editUser,:userId").get((req, res) => {
  res.locals.branch = branchConfig;
  performRequest(`/api/user/${req.params.userId}`, "GET", null, (data) => {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  });
});

admin.route("/groups").get(
  asyncHandler(async (req, res) => {
    res.locals.branch = branchConfig;
    const user = res.locals.user as CustomUser;
    const layout = user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

    const groups = await archeogrid_client.users.groups.query({ user_id: user.id });

    res.render("admin/groups", {
      groups,
      title: req.url,
      message: "",
      layout,
    });
  }),
);

admin
  .route("/addgroup")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    const layout = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    res.render("admin/addgroup", {
      groupname: "",
      group: 0,
      title: req.url,
      success: 0,
      message: "",
      layout,
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const groupname = req.body.groupname;
      const layout = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      if (groupname === "") {
        res.render("admin/addgroup", {
          groupname: "",
          group: 0,
          title: req.url,
          success: 0,
          message: req.__("emptyName"),
          layout: layout,
        });
      } else {
        const user = res.locals.user as CustomUser;
        await archeogrid_client.users.addGroup.mutate({ user_id: user.id, group_name: req.body.groupname });

        res.render("admin/addgroup", {
          groupname: req.body.groupname,
          success: true,
          layout: layout,
        });
      }
    }),
  );

// delete group from /admin/groups view
admin.route("/group/del").get(
  asyncHandler(async (req, res) => {
    res.locals.branch = branchConfig;
    const [data, code] = await req.fetchApiCode("/api/group", { groupId: req.query.groupId as string }, "DELETE");
    if (code !== 500) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify("ok"));
    } else {
      res.setHeader("Content-Type", "application/json");
      let message = data.join("");
      if (message.includes("pft3d_group_access") || message.includes("archeogrid_user_group")) {
        message += " Des utilisateurs sont encore référencés dans le groupe.";
      }
      res.send(JSON.stringify(message));
    }
  }),
);

admin
  .route("/permGroup")
  .get(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const user = res.locals.user as CustomUser;

      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      const groups = await archeogrid_client.users.groups.query({ user_id: user.id });

      res.render("admin/permgroup", {
        groupid: 0,
        groups,
        groupff: [],
        groupperm: [],
        groupWperm: [],
        message: "",
        title: req.url,
        method: req.method,
        layout: layoutAdm,
      });
    }),
  )
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const groupid = Number.parseInt(req.body.groupSelect);
      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      // Evol : si admin => on choisit la branche, si scribe, on donne la branche dans laquelle on est : pft3d ou corpus => branchConfig
      const root = res.locals.user.user_status === "admin" ? req.body.rootSelect : branchConfig ;
      const user_id = (res.locals.user as CustomUser).id;

      if (
        req.body.forbid === "ok" ||
        req.body.access === "ok" ||
        req.body.writeAccess === "ok" ||
        req.body.writeForbid === "ok"
      ) {
        if (req.body.forbid === "ok") {
          const [_, deleteGroupAccessCode] = await req.fetchApiCode(
            `/api/groupAccessAdmin/${root},${groupid}`,
            req.body,
            "DELETE",
          );

          if (deleteGroupAccessCode !== 200) {
            req.flash("error", "DELETE IMPOSSIBLE");
            return;
          }
        } else if (req.body.access === "ok") {
          const [_, putGroupAccessCode] = await req.fetchApiCode(
            `/api/groupAccessAdmin/${root},${groupid}`,
            req.body,
            "PUT",
          );

          if (putGroupAccessCode !== 201) {
            req.flash("error", "INSERT IMPOSSIBLE");
            return;
          }
        } else if (req.body.writeForbid === "ok") {
          const [_, deleteGroupWriteAccess] = await req.fetchApiCode(
            `/api/groupWriteAccessAdmin/${root},${groupid}`,
            req.body,
            "DELETE",
          );

          if (deleteGroupWriteAccess !== 200) {
            req.flash("error", "DELETE IMPOSSIBLE");
            return;
          }
        } else if (req.body.writeAccess === "ok") {
          const [_, putGroupWriteAccessCode] = await req.fetchApiCode(
            `/api/groupWriteAccessAdmin/${root},${groupid}`,
            req.body,
            "PUT",
          );

          if (putGroupWriteAccessCode !== 201) {
            req.flash("error", "INSERT IMPOSSIBLE");
            return;
          }
        }
      }

      const datagroup = await req.fetchApi("/api/group");
      const dataperm = await req.fetchApi(`/api/groupAccessAdmin/${root},${groupid}`);
      const dataWperm = await req.fetchApi(`/api/groupWriteAccessAdmin/${root},${groupid}`);
      const groupff = await archeogrid_client.users.groupForbiddenFolders.query({
        branch: root,
        group_id: groupid,
        user_id,
      });

      res.render("admin/permgroup", {
        branche: "",
        groupid: groupid,
        groups: datagroup,
        root: root,
        groupff: groupff,
        groupperm: dataperm,
        groupWperm: dataWperm,
        message: "",
        title: req.url,
        method: req.method,
        layout: layoutAdm,
      });
    }),
  );

admin
  .route("/groupUsers")
  .get(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const user = res.locals.user as CustomUser;
      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      const groups = await archeogrid_client.users.groups.query({ user_id: user.id });

      res.render("admin/groupusers", {
        group_id: 0,
        group_name: "",
        groups,
        group: "",
        users: "",
        userId: 0,
        message: "",
        title: req.url,
        method: req.method,
        layout: layoutAdm,
      });
    }),
  )
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      if (!req.body) {
        req.flash("error", "PAS DE DONNEES GROUP ");
        return;
      }
      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
      console.log(req.body);

      const user = res.locals.user as CustomUser;
      const user_id = user.id;

      const group_id = Number.parseInt(req.body.groupSelect.split("_")[0]);
      if (!group_id) throw new Error("Invalid group id");

      const group_name = req.body.groupSelect.substring(req.body.groupSelect.indexOf("_") + 1);

      const groups = await archeogrid_client.users.groups.query({ user_id });

      //const users = await archeogrid_client.users.accessibleUsers.query({ user_id });
      // on filtre seulement les users qui ne sont déjà dans le group
      const users = await archeogrid_client.users.accessibleUsersGroup.query({ user_id, group_id });

      console.log("data ok");
      if (req.body.userSelect) {
        const [_, insertCode] = await req.fetchApiCode(`/api/groupUser/${req.body.userSelect}`, req.body, "PUT");

        const group = await archeogrid_client.users.group.query({ group_id });

        if (insertCode === 500) {
          req.flash("error", " Impossible d'ajouter le user ");
          return;
        }

        res.render("admin/groupusers", {
          group_id,
          group_name,
          groups,
          group,
          users,
          userid: req.body.userSelect,
          message: "",
          title: req.url,
          method: req.method,
          layout: layoutAdm,
        });

        return;
      }

      if (req.body.userDelete) {
        const dataInput = {
          groupId: req.body.userDelete.split("_")[0],
        };
        await req.fetchApi(`/api/groupUser/${req.body.userDelete.split("_")[1]}`, dataInput, "DELETE");

        const group = await archeogrid_client.users.group.query({ group_id });

        res.render("admin/groupusers", {
          group_id,
          group_name,
          groups,
          group,
          users: users,
          userid: 0,
          message: "",
          title: req.url,
          method: req.method,
          layout: layoutAdm,
        });

        return;
      }

      const group = await archeogrid_client.users.group.query({ group_id });

      res.render("admin/groupusers", {
        group_id,
        group_name,
        groups,
        group,
        users,
        userid: 0,
        message: "",
        title: req.url,
        method: req.method,
        layout: layoutAdm,
      });
    }),
  );

admin.route("/groupUserDel").get((req, res) => {
  res.locals.branch = branchConfig;
  if (!req.query) {
    res.sendStatus(400);
    return;
  }
  performRequest(`/api/groupUser/${req.query.userId}`, "DELETE", req.query as Record<string, string>, (_, code) => {
    res.sendStatus(code);
  });
});

// DOSSIERS
admin
  .route("/permFolders")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    performRequest("/api/branche", "GET", null, (branche) => {
      res.render("admin/permfolder", {
        publicfolder: "",
        privatefolder: "",
        branche: branche,
        rootproj: "",
        userStatus: res.locals.user.user_status,
        message: "",
        title: req.url,
        method: req.method,
        layout: layoutAdm,
      });
    });
  })
  .post((req, res) => {
    res.locals.branch = branchConfig;
    const rootProjet = res.locals.user.user_status === "admin" ? req.body.rootSelect : branchConfig;
    // let rootProjet = req.body.rootSelect
    const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    const publicFolders = res.locals.user.user_status === "admin" ? "publicFolders" : "limitedPublicFolders";
    const privateFolders = res.locals.user.user_status === "admin" ? "privateFolders" : "limitedPrivateFolders";
    const dataGet = {
      userId: res.locals.user.id,
    };
    //performRequest('/api/branche', 'GET', null, function (branche) {
    if (req.body.private === "ok" || req.body.public === "ok") {
      if (req.body.public === "ok") {
        // prendre des dossier private et les rendre public
        performRequest(`/api/publicFolders/${rootProjet}`, "PATCH", req.body, (_, code) => {
          if (code === 200) {
            performRequest(`/api/${publicFolders}/${rootProjet}`, "GET", dataGet, (datapublic) => {
              performRequest(`/api/${privateFolders}/${rootProjet}`, "GET", dataGet, (dataprivate) => {
                res.render("admin/permfolder", {
                  publicfolder: datapublic,
                  privatefolder: dataprivate,
                  branche: "",
                  userStatus: res.locals.user.user_status,
                  rootproj: rootProjet,
                  message: " OK",
                  title: req.url,
                  method: req.method,
                  layout: layoutAdm,
                });
              });
            });
          } else {
            req.flash("error", "Unable to rendre public the choosen folder(s)");
          }
        });
      } else if (req.body.private === "ok") {
        performRequest(`/api/privateFolders/${rootProjet}`, "PATCH", req.body, (_, code) => {
          if (code === 200) {
            performRequest(`/api/${publicFolders}/${rootProjet}`, "GET", dataGet, (datapublic) => {
              performRequest(`/api/${privateFolders}/${rootProjet}`, "GET", dataGet, (dataprivate) => {
                res.render("admin/permfolder", {
                  publicfolder: datapublic,
                  privatefolder: dataprivate,
                  branche: "",
                  userStatus: res.locals.user.user_status,
                  rootproj: rootProjet,
                  message: "Dossier concerne rendu private",
                  title: req.url,
                  method: req.method,
                  layout: layoutAdm,
                });
              });
            });
          } else {
            req.flash("error", "Unable to rendre private the choosen folder(s)");
          }
        });
      }
    } else {
      performRequest(`/api/${publicFolders}/${rootProjet}`, "GET", dataGet, (datapublic) => {
        performRequest(`/api/${privateFolders}/${rootProjet}`, "GET", dataGet, (dataprivate) => {
          res.render("admin/permfolder", {
            publicfolder: datapublic,
            privatefolder: dataprivate,
            branche: "",
            userStatus: res.locals.user.user_status,
            rootproj: rootProjet,
            message: "",
            title: req.url,
            method: req.method,
            layout: layoutAdm,
          });
        });
      });
    }
    //})
  });

admin
  .route("/downloadFolders")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/branche", "GET", null, (branche) => {
      res.render("admin/downloadfolder", {
        downloadablefolder: "",
        nondownloadablefolder: "",
        user: res.locals.user,
        branche: branche,
        message: "",
        title: req.url,
        method: req.method,
        layout: "admin/layout_admin",
      });
    });
  })
  .post((req, res) => {
    res.locals.branch = branchConfig;
    const rootProjet = req.body.rootSelect;
    performRequest("/api/branche", "GET", null, (branche) => {
      if (req.body.downloadable === "ok" || req.body.nondownloadable === "ok") {
        if (req.body.downloadable === "ok") {
          // prendre des dossier nondownloadable et les rendre downloadable
          performRequest(`/api/downloadableFolders/${rootProjet}`, "PATCH", req.body, (_, code) => {
            if (code === 200) {
              performRequest(`/api/downloadableFolders/${rootProjet}`, "GET", null, (datadownloadable) => {
                performRequest(`/api/nondownloadableFolders/${rootProjet}`, "GET", null, (datanondownloadable) => {
                  res.render("admin/downloadfolder", {
                    downloadablefolder: datadownloadable,
                    nondownloadablefolder: datanondownloadable,
                    branche: branche,
                    rootproj: rootProjet,
                    message: 'Dossier(s) concerné(s) sont bien devenus "Downloadable"',
                    title: req.url,
                    method: req.method,
                    layout: "admin/layout_admin",
                  });
                });
              });
            } else {
              req.flash("error", "Unable to render downloadable the choosen folder(s)");
            }
          });
        } else if (req.body.nondownloadable === "ok") {
          // prendre des dossier downloadable et les rendre nondownloadable
          performRequest(`/api/nondownloadableFolders/${rootProjet}`, "PATCH", req.body, (_, code) => {
            if (code === 200) {
              performRequest(`/api/downloadableFolders/${rootProjet}`, "GET", null, (datadownloadable) => {
                performRequest(`/api/nondownloadableFolders/${rootProjet}`, "GET", null, (datanondownloadable) => {
                  res.render("admin/downloadfolder", {
                    downloadablefolder: datadownloadable,
                    nondownloadablefolder: datanondownloadable,
                    branche: branche,
                    rootproj: rootProjet,
                    message: "OK",
                    title: req.url,
                    method: req.method,
                    layout: "admin/layout_admin",
                  });
                });
              });
            } else {
              req.flash("error", "Unable to render non downloadable the choosen folder(s)");
            }
          });
        }
      } else {
        performRequest(`/api/downloadableFolders/${rootProjet}`, "GET", null, (datadownloadable) => {
          performRequest(`/api/nondownloadableFolders/${rootProjet}`, "GET", null, (datanondownloadable) => {
            res.render("admin/downloadfolder", {
              downloadablefolder: datadownloadable,
              nondownloadablefolder: datanondownloadable,
              branche: branche,
              rootproj: rootProjet,
              message: "",
              title: req.url,
              method: req.method,
              layout: "admin/layout_admin",
            });
          });
        });
      }
    });
  });

admin
  .route("/manageFolders")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/branche", "GET", null, (data) => {
      res.render("admin/managefolder", {
        folders: "",
        rootproj: "",
        message: "",
        user: res.locals.user,
        branche: data,
        title: req.url,
        method: req.method,
        layout: "admin/layout_admin",
      });
    });
  })
  .post((req, res) => {
    res.locals.branch = branchConfig;
    const rootProjet = req.body.rootSelect;

    performRequest(`/api/rootFolders/${rootProjet}`, "GET", null, (datafolders) => {
      res.render("admin/managefolder", {
        folders: datafolders,
        rootproj: rootProjet,
        user: res.locals.user,
        message: "",
        branche: "",
        title: req.url,
        method: req.method,
        layout: "admin/layout_admin",
      });
    });
  });

admin.route("/reorder_folders").post((req, res) => {
  res.locals.branch = branchConfig;
  performRequest(`/api/rootFolders/${req.query.rootFolder}`, "PATCH", req.body, (_, code) => {
    if (code === 200) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify("L'ordre des dossiers a été bien modifié"));
    } else {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify("Bad change Order"));
    }
  });
});

admin.route("/reorder").post((req, res) => {
  res.locals.branch = branchConfig;
  let cpt = 0;
  const folderPerm = [];

  for (const i in req.body) {
    cpt++; // on commence avec le compteur à 1 (il est sensé coïncider avec le rank après permutation
    if (Number.parseInt(i.split("_")[1]) !== cpt) {
      folderPerm.push({
        idF: Number.parseInt(req.body[i]),
        rank: cpt,
      });
    }
  }
  let erreur = 0;
  for (let i = 0; i < folderPerm.length; i++) {
    performRequest(`/api/changeFolderOrder/${req.query.rootFolder}`, "PATCH", folderPerm[i], (_, code) => {
      if (code !== 200) {
        erreur = 1;
      }
      if (i === folderPerm.length - 1) {
        performRequest(`/api/updateGlobalRankFolderOrder/${req.query.rootFolder}`, "PATCH", null, () => {});
      }
    });
  }
  if (erreur) {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify("Bad change Order"));
  } else {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify("L'ordre des dossiers a été bien modifié"));
  }
});

admin.route("/reorder_thesaurus/:branche/:thesaurus").post((req, res) => {
  res.locals.branch = branchConfig;
  performRequest(`/api/orderThesaurus/${req.params.branche},${req.params.thesaurus}`, "PATCH", req.body, (_, code) => {
    if (code === 200) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify("L'ordre des item de thesaurus a été bien modifié"));
    } else {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify("Bad change Order"));
    }
  });
});

admin
  .route("/editFolder,:branch,:folderId")
  .get(
    asyncHandler(async (req: Request<{ branch: Branch; folderId: string }>, res) => {
      res.locals.branch = branchConfig;
      const folder_id = Number.parseInt(req.params.folderId);
      const branch = req.params.branch;
      const language = res.locals.lang;
      // Edit : Nom du dossier, Nom du répertoire, Nom du passeport data folder, Nom du passeport data file...
      // Récupérer tous les modèles de metadonnées au niveau du dossier (folder ) et au niveau du fichier (file)
      // Attention, la nouvelle version ne donne que les modeles lié au projet et non plus tous les modèles de la base de données
      // Pour avoir tous les modèles utiliser la nouvelle route : getMetadataModelGeneral
      const metadataModel = await archeogrid_client.metadata.getMetadataModelProject.query({
        branch,
        language,
        folder_id,
      });

      const modelFile = metadataModel.filter((m) => m.metadata_type === "file");
      const modelFolder = metadataModel.filter((m) => m.metadata_type === "folder");
      const modelobj = metadataModel.filter((m) => m.metadata_type === "object");

      const datafolder = await req.fetchApi(`/api/folderSimple/${folder_id},${branch}`);

      res.render("admin/editfolder", {
        folder: datafolder,
        rootproj: branch,
        modelFile,
        modelFolder,
        modelobj,
        message: "",
        title: req.url,
        method: req.method,
        layout: "admin/layout_admin",
      });
    }),
  )
  .post((req, res) => {
    res.locals.branch = branchConfig;
    // Pour l'instant les 4 champs sont mis à jour même si rien n'est modifié
    // TODO : verifier si la valeur a été modifiée et si non, ne pas la renvoyer dans les données à mettre à jour
    performRequest(`/api/folderSimple/${req.body.idFolder},${req.body.rootproj}`, "PATCH", req.body, (_, code) => {
      if (code === 200) {
        res.render("admin/editfolder", {
          folder: "",
          rootproj: "",
          modelFile: "",
          modelFolder: "",
          modelobj: "",
          message: `Dossier ${req.body.name} mis à jour !`,
          title: req.url,
          method: req.method,
          layout: "admin/layout_admin",
        });
      }
    });
  });

admin
  .route("/mainGenerateMini,:type")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/branche", "GET", null, (branche) => {
      res.render("admin/generatemini", {
        type: req.params.type,
        branche: branche,
        rootproj: "",
        listfolder: "",
        idFolder: "",
        listproject: "",
        idProject: "",
        nbmissing: "",
        message: "",
        title: "",
        layout: "admin/layout_admin",
      });
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      let rootproj = "";
      if (req.body.rootproj) rootproj = req.body.rootproj;
      if (req.body.idProject) {
        if (req.body.idFolder) {
          const datat = await req.fetchApi(`/api/fsdirImages/${req.body.rootproj},${req.body.idFolder[1]}`);

          let dataview = [];
          if (req.params.type === "thumbnail") {
            dataview = datat.thumbMissing;
          } else if (req.params.type === "small") {
            dataview = datat.smallMissing;
          }
          if (dataview.length > 0) {
            res.render("admin/generatemini", {
              type: req.params.type,
              branche: "",
              rootproj: rootproj,
              listfolder: "",
              idProject: req.body.idProject,
              listproject: "",
              idFolder: req.body.idFolder,
              nbmissing: dataview.length,
              data: dataview,
              message: "",
              title: "",
              layout: "admin/layout_admin",
            });
            return;
          }
          res.render("admin/generatemini", {
            type: req.params.type,
            branche: "",
            rootproj: rootproj,
            listfolder: "",
            idProject: req.body.idProject,
            listproject: "",
            idFolder: req.body.idFolder,
            nbmissing: "",
            message: "",
            title: "",
            layout: "admin/layout_admin",
          });
          return;
        }

        const data = await req.fetchApi(`/api/dbPathFolder/${req.body.rootproj},${req.body.idProject}`, req.body);

        res.render("admin/generatemini", {
          type: req.params.type,
          branche: "",
          rootproj: rootproj,
          listfolder: data,
          idFolder: data[0].path,
          listproject: "",
          idProject: req.body.idProject,
          nbmissing: "",
          message: "",
          title: "",
          layout: "admin/layout_admin",
        });
        return;
      }
      const rootfolder = await req.fetchApi(`/api/projectsRoot/${req.body.rootproj}`);

      res.render("admin/generatemini", {
        type: req.params.type,
        branche: "",
        rootproj: rootproj,
        listfolder: "",
        idFolder: "",
        listproject: rootfolder,
        idProject: "",
        nbmissing: "",
        message: "",
        title: "",
        layout: "admin/layout_admin",
      });
    }),
  );

admin.route("/generate").get((req, res) => {
  res.locals.branch = branchConfig;
  if (!req.query) {
    res.sendStatus(400);
    return;
  }
  performRequest("/api/generateMini", "GET", req.query as Record<string, string>, (data) => {
    req.flash("ok", `${data} ${req.query.type} generated`);
    res.redirect(`/admin/mainGenerateMini,${req.query.type}`);
  });
});

admin
  .route("/metadata")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    res.render("admin/metadata", {
      branche: "",
      user: res.locals.user,
      listfolders: "",
      idFolder: "",
      rootDir: "",
      listmodels: "",
      idModel: "",
      modelName: "",
      message: "",
      bad: "",
      title: req.url,
      method: req.method,
      layout: "admin/layout_admin",
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      let branche = "";
      let rootDir = "";
      let idFolder = 0;
      let model = "";
      let idModel = 0;
      let usedApi = "";
      let itemType = "file";

      if (req.body.branche) branche = req.body.branche;

      if (!req.body.rootDir) {
        // on n'a pas encore choisi le projet / root_dir
        const rootfolders = await req.fetchApi(`/api/projectsRoot/${req.body.branche}`);
        res.render("admin/metadata", {
          branche: branche,
          user: res.locals.user,
          listfolders: rootfolders,
          idFolder: "",
          rootDir: "",
          listmodels: "",
          idModel: "",
          modelName: "",
          message: "",
          bad: "",
          title: req.url,
          method: req.method,
          layout: "admin/layout_admin",
        });
        return;
      }

      if (req.body.rootDir.includes("/")) {
        rootDir = req.body.rootDir.split("/")[1];
        idFolder = req.body.rootDir.split("/")[0];
      } else {
        rootDir = req.body.rootDir;
      }

      if (!req.body.idModel) {
        // on a le projet mais pas encore le model
        const data = await req.fetchApi(`/api/metadatamodel/${branche},${res.locals.lang}`);

        res.render("admin/metadata", {
          branche: branche,
          user: res.locals.user,
          listfolders: "",
          idFolder: idFolder,
          rootDir: rootDir,
          listmodels: data,
          idModel: "",
          modelName: "",
          message: "",
          bad: "",
          title: req.url,
          method: req.method,
          layout: "admin/layout_admin",
        });

        return;
      }

      if (req.body.idModel.includes("/")) {
        model = req.body.idModel.split("/")[1];
        idModel = req.body.idModel.split("/")[0];
      } else {
        idModel = req.body.idModel.split("/")[0];
        model = req.body.idModel;
      }

      if (req.body.itemType) itemType = req.body.itemType;
      // on a tout : la branche, le rootDir, le model
      // le fichier CSV a déjà été posé dans la table <branche>_<model_de metadonnee en minuscule>
      // exemple : pft3d_photo_lrmh
      if (itemType === "object") {
        usedApi = "MetadataFromCSVObject";
      } else if (itemType === "file") {
        usedApi = "MetadataFromCSV";
      }

      const [putMetadataData, putMetadataCode] = await req.fetchApiCode(
        `/api/${usedApi}/${branche},${idModel},${rootDir}`, {}, "PUT",
      );

      if (putMetadataCode === 201) {
        res.render("admin/metadata", {
          branche: branche,
          listfolders: "",
          idFolder: idFolder,
          rootDir: rootDir,
          listmodels: "alreadySet",
          idModel: req.body.idModel,
          modelName: model,
          message: putMetadataData.length,
          bad: "",
          title: req.url,
          method: req.method,
          layout: "admin/layout_admin",
        });
        return;
      }

      res.render("admin/metadata", {
        branche: branche,
        listfolders: "",
        idFolder: idFolder,
        rootDir: rootDir,
        listmodels: "alreadySet",
        idModel: req.body.idModel,
        modelName: model,
        message: "",
        bad: "Error à l'ingestion des métadonnées",
        title: req.url,
        method: req.method,
        layout: "admin/layout_admin",
      });

    }),
  );

admin
  .route('/csvMetadata')
  .get(async (req, res) => {
    const branches = await archeogrid_client.archeogrid.getBranches.query({
      language: res.locals.lang,
    });

    const format_user = {
      id: res.locals.user.id,
      user_status: res.locals.user.user_status
    };

    const csv_metadata = await archeogrid_client.csv.getAllCSVMetadata.query({user: format_user});

    const bad = req.session.csvError ?? null;
    req.session.csvError = null;

    console.log(branchConfig);

    res.render('admin/csvMetadata',{
      layout: "admin/layout_admin",
      branch: branchConfig,
      branches,
      csv_metadata,
      bad // COMING FROM POST METHODS
    });
  })
  .post(async (req, res) => {
    let action: string = req.body.action;
    let step: number = parseInt(req.body.step);

    if(!action || Number.isNaN(step)){
      console.log("NO ACTION OR STEP PROVIDED");
      res.redirect('/admin/csvMetadata');
      return;
    }

    let action_view: string = "";
    let params: any = {};
    switch(action){
      case 'quickIngest':
        action_view = 'admin/csvMetadata_actions/quickIngest';
        switch(step){
          case 0:
            let listprojectByBranch: {branch: "pft3d" | "conservatoire3d" | "corpus", listproject: string[]}[] = [
              {branch: 'pft3d', listproject: []},
              {branch: 'conservatoire3d', listproject: []},
              {branch: 'corpus', listproject: []}
            ];

            for(let b_list of listprojectByBranch){
              let fullList: {folder_name: string}[];
              switch(res.locals.user.user_status){
                case "admin":
                  fullList = await archeogrid_client.projects.projectsRootList.query({
                    branch: b_list.branch, 
                    user_id: res.locals.user.id
                  });
                  break;
                case "scribe":
                  fullList = await archeogrid_client.projects.limitedProjectsRoot.query({
                    branch: b_list.branch,
                    user_id: res.locals.user.id
                  });
                  break;
                default:
                  console.log(`CSV CONTROLLER ERROR: USER STATUS ${res.locals.user.user_status} NOT SUPPORTED!`);
                  fullList = [];
              }

              b_list.listproject = fullList?.map(p => p.folder_name) || [];
            }

            const branches = await archeogrid_client.archeogrid.getBranches.query({
              language: res.locals.lang,
            });

            params.listprojectByBranch = listprojectByBranch;
            params.branches = branches;
            break;
          case 1:
            let requestedBranch: string = req.body.requestedBranch, projectFolder:string = req.body.project;

            const pathMetadata = await getFolderMetadataPath({
              requestedBranch: requestedBranch as Branch,
              projectFolder,
            });

            console.log(pathMetadata);

            if(!fs.existsSync(pathMetadata)){
              req.session.csvError = `Dossier de csv-metadata introuvable. Assurez vous que le répetoire 'metadata' existe à la racine du projet/dépôt ${projectFolder}.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const listFiles = fs.readdirSync(pathMetadata, {withFileTypes: true});
            const csvFiles = listFiles.filter(file => file.isFile() && file.name.endsWith('.csv'));
            params.csvFiles = csvFiles;
            params.projectFolder = projectFolder;
            params.requestedBranch = requestedBranch;
            break;
          case 2:
            const itemName = sanitizeSQLName(req.body.itemName);
            const itemType = req.body.itemType;
            const projectFolder2 = req.body.projectFolder;
            const requestedBranch2 = req.body.requestedBranch;
            const delimiterIsTab = req.body.delimiterIsTab;
            const delimiter = delimiterIsTab ? '\t' : req.body.delimiter;
            const csvFile = req.body.csvFile;
            params.summary = {
              table_creation: false,
              model_creation: false,
              metadata_creation: false,
              link_creation: false,
              table_ingestion: false,
              table_ingestion_error: "",
              model_ingestion: false,
              model_ingestion_error: ""
            };

            if(!itemName){
              req.session.csvError = `Le nom de la table n'a pas été renseigné.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const pathToCSV = await getCsvPath({
              requestedBranch: requestedBranch2 as Branch,
              projectFolder: projectFolder2,
              csvFile,
            });

            if(!fs.existsSync(pathToCSV)){
              req.session.csvError = `Fichier csv introuvable. Assurez vous qu'il existe et accesible à la racine du projet/dépôt ${projectFolder2}.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const csv = fs.readFileSync(pathToCSV, 'utf8');
            let csvByRow = csv.split('\n');
            
            if(!csvByRow || csvByRow.length <= 1){
              csvByRow = csv.split('\r\n');
              if(!csvByRow || csvByRow.length <= 1){
                req.session.csvError = `Le fichier csv semble vide.`;
                res.redirect('/admin/csvMetadata');
                return;
              }
            }
            
            const columnInfo = csvByRow[0].split(delimiter);

            if(columnInfo.length === 1){
              req.session.csvError = `Le délimiteur de votre fichier csv semble incorrect, il n'est pas présent dans le header du fichier csv.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            let columnInfo_formatted: string[] = [];
            for(let column of columnInfo){
              let cf = sanitizeSQLName(column);

              if(cf === '' || cf === undefined){
                req.session.csvError = `Une des colonnes du header ne possède pas de nom.`;
                res.redirect('/admin/csvMetadata');
                return;
              }

              if(columnInfo_formatted.includes(cf)){
                req.session.csvError = `La colonne ${cf} est présente plusieurs fois dans le header du fichier csv.`;
                res.redirect('/admin/csvMetadata');
                return;
              }

              columnInfo_formatted.push(cf);
            }

            const result_table = await archeogrid_client.csv.createOrUpdateTableFromCSV.mutate({
              branch: requestedBranch2,
              tableName: itemName, 
              item_type: itemType,
              columnInfo: columnInfo_formatted,
              csv_file_name: csvFile,
              project_name: req.body.projectFolder,
              csv_delimiter: delimiterIsTab ? 'TAB' : req.body.delimiter
            });

            if(result_table.status === false || !result_table.csv_metadata_id){
              console.log("ERROR TABLE CREATION");
              break;
            }else{
              params.summary.table_creation = true;
            }

            const csv_metadata_id = result_table.csv_metadata_id;
            
            await archeogrid_client.csv.deleteModel.mutate({csv_metadata_id});

            let metadatas = columnInfo_formatted.map((code, index) => ({
              code,
              label: columnInfo[index],
              description: "",
              type: 'char',
              y: 1,
              isunique: 1,
              rank: index,
              query: 'y'
            }));

            const metadata_model = await archeogrid_client.metadata.createMetadataModel.mutate({
              branch: requestedBranch2, 
              language: res.locals.lang,
              name: itemName,
              type: itemType,
              desc: `CSV metadata model created from CSV file ${csvFile}.`,
              visible: 1,
              projects: [req.body.projectFolder]
            })

            if(!metadata_model){
              console.log("ERROR METADATA MODEL CREATION");
              break;
            }else{
              params.summary.model_creation = true;
            }
 
            await archeogrid_client.metadata.createMetadatasOfModel.mutate({
              branch: requestedBranch2,
              language: res.locals.lang,
              metadata_model_id: metadata_model.id,
              metadatas
            });

            const result_model = await archeogrid_client.csv.updateCSVMetadataModel.mutate({
              csv_metadata_id,
              csv_metadata_model_id: metadata_model.id
            })

            if(result_model.status === false){
              console.log("ERROR METADATA CREATION WITH MODEL");
              break;
            }else{
              params.summary.metadata_creation = true;
              params.summary.link_creation = true;
            }

            const uptade_table = await archeogrid_client.csv.CSVMetadataTableIngestion.mutate({csv_metadata_id});

            if(uptade_table.status === false){
              console.log("ERROR TABLE INGESTION : ", uptade_table.message);
              // params.summary.table_ingestion = false;
              params.summary.table_ingestion_error = uptade_table.message;
              break;
            }else{
              params.summary.table_ingestion = true;
            }

            const update_model = await archeogrid_client.csv.CSVMetadataModelIngestion.mutate({csv_metadata_id, user_id: res.locals.user.id});

            if(update_model.status === false){
              console.log("ERROR MODEL INGESTION : ", update_model.message);
              // params.summary.model_ingestion = false;
              params.summary.model_ingestion_error = update_model.message;
              break;
            }else{
              params.summary.model_ingestion = true;
            }

            params.csv_metadata_id = csv_metadata_id;
            break;          
        }
        break;
      case 'createTable':
        action_view = 'admin/csvMetadata_actions/createTable';
        switch(step){
          case 0:
            let listprojectByBranch: {branch: "pft3d" | "conservatoire3d" | "corpus", listproject: string[]}[] = [
              {branch: 'pft3d', listproject: []},
              {branch: 'conservatoire3d', listproject: []},
              {branch: 'corpus', listproject: []}
            ];

            for(let b_list of listprojectByBranch){
              let fullList: {folder_name: string}[];
              switch(res.locals.user.user_status){
                case "admin":
                  fullList = await archeogrid_client.projects.projectsRootList.query({
                    branch: b_list.branch, 
                    user_id: res.locals.user.id
                  });
                  break;
                case "scribe":
                  fullList = await archeogrid_client.projects.limitedProjectsRoot.query({
                    branch: b_list.branch,
                    user_id: res.locals.user.id
                  });
                  break;
                default:
                  console.log(`CSV CONTROLLER ERROR: USER STATUS ${res.locals.user.user_status} NOT SUPPORTED!`);
                  fullList = [];
              }

              b_list.listproject = fullList?.map(p => p.folder_name) || [];
            }

            const branches = await archeogrid_client.archeogrid.getBranches.query({
              language: res.locals.lang,
            });

            params.listprojectByBranch = listprojectByBranch;
            params.branches = branches;
            break;
          case 1:
            let requestedBranch: string, projectFolder:string;
            // COMING FROM CREATE TABLE
            if(req.body['csv_metadata_id']){
              const csv_metadata = await archeogrid_client.csv.getOneCSVMetadata.query({ csv_metadata_id: Number.parseInt(req.body['csv_metadata_id']) });

              if(!csv_metadata){
                req.session.csvError = 'CSV-Metadata introuvable';
                res.redirect('/admin/csvMetadata');
                return;
              }

              const default_values = {
                csv_file_name: csv_metadata.csv_file_name,
                delimiterIsTab: csv_metadata.csv_delimiter.trim() == 'TAB',
                csv_delimiter: csv_metadata.csv_delimiter.trim() == 'TAB' ? '\t' : csv_metadata.csv_delimiter,
                table_name: csv_metadata.table_name ?? csv_metadata.branch + '_' + csv_metadata.csv_file_name.replace('.csv', ''),
                tableItemType: csv_metadata.item_type
              };

              params.default_values = default_values;

              requestedBranch = csv_metadata.branch;
              projectFolder = csv_metadata.project_name;
            }else{
              params.default_values = {};
              requestedBranch = req.body.requestedBranch;
              projectFolder = req.body.project; 
            }

            const pathMetadata = await getFolderMetadataPath({
              requestedBranch: requestedBranch as Branch,
              projectFolder,
            });

            if(!fs.existsSync(pathMetadata)){
              req.session.csvError = `Dossier de csv-metadata introuvable. Assurez vous que le répetoire 'metadata' existe à la racine du projet/dépôt ${projectFolder}.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const listFiles = fs.readdirSync(pathMetadata, {withFileTypes: true});
            const csvFiles = listFiles.filter(file => file.isFile() && file.name.endsWith('.csv'));
            params.csvFiles = csvFiles;
            params.projectFolder = projectFolder;
            params.requestedBranch = requestedBranch;
            break;
          case 2:
            const tableName = sanitizeSQLName(req.body.tableName);
            const tableItemType = req.body.tableItemType;
            const projectFolder2 = req.body.projectFolder;
            const requestedBranch2 = req.body.requestedBranch;
            const delimiterIsTab = req.body.delimiterIsTab;
            const delimiter = delimiterIsTab ? '\t' : req.body.delimiter;
            const csvFile = req.body.csvFile;

            if(!tableName){
              req.session.csvError = `Le nom de la table n'a pas été renseigné.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const pathToCSV = await getCsvPath({
              requestedBranch: requestedBranch2 as Branch,
              projectFolder: projectFolder2,
              csvFile,
            });

            if(!fs.existsSync(pathToCSV)){
              req.session.csvError = `Fichier csv introuvable. Assurez vous qu'il existe et accesible à la racine du projet/dépôt ${projectFolder2}.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const csvReader = fs.readFileSync(pathToCSV, 'utf8');
            let csvByRow = csvReader.split('\n');
            
            if(!csvByRow || csvByRow.length <= 1){

              csvByRow = csvReader.split('\r\n');

              if(!csvByRow || csvByRow.length <= 1){
                req.session.csvError = `Le fichier csv semble vide.`;
                res.redirect('/admin/csvMetadata');
                return;
              }
            }
            
            const columnInfo = csvByRow[0].split(delimiter);

            if(columnInfo.length === 1){
              req.session.csvError = `Le délimiteur de votre fichier csv semble incorrect, il n'est pas présent dans le header du fichier csv.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            let columnInfo_formatted: string[] = [];
            for(let column of columnInfo){
              let cf = sanitizeSQLName(column);

              if(cf === '' || cf === undefined){
                req.session.csvError = `Une des colonnes du header ne possède pas de nom.`;
                res.redirect('/admin/csvMetadata');
                return;
              }

              if(columnInfo_formatted.includes(cf)){
                req.session.csvError = `La colonne ${cf} est présente plusieurs fois dans le header du fichier csv.`;
                res.redirect('/admin/csvMetadata');
                return;
              }

              columnInfo_formatted.push(cf);
            }

            const result = await archeogrid_client.csv.createOrUpdateTableFromCSV.mutate({
              branch: requestedBranch2,
              tableName, 
              item_type: tableItemType,
              columnInfo: columnInfo_formatted,
              csv_file_name: csvFile,
              project_name: req.body.projectFolder,
              csv_delimiter: delimiterIsTab ? 'TAB' : req.body.delimiter
            });

            params.status = result.status;

            if(result.status){
              params.message = result.message;
              params.csv_metadata_id = result.csv_metadata_id
            }else{
              params.bad = result.message
              params.csv_metadata_id = null;
            }
            break;
        }
        break;
      case 'createModel':
        action_view = 'admin/csvMetadata_actions/createModel';
        switch(step){
          case 0:
            let listprojectByBranch: {branch: "pft3d" | "conservatoire3d" | "corpus", listproject: string[]}[] = [
              {branch: 'pft3d', listproject: []},
              {branch: 'conservatoire3d', listproject: []},
              {branch: 'corpus', listproject: []}
            ];

            for(let b_list of listprojectByBranch){
              let fullList: {folder_name: string}[];
              switch(res.locals.user.user_status){
                case "admin":
                  fullList = await archeogrid_client.projects.projectsRootList.query({
                    branch: b_list.branch, 
                    user_id: res.locals.user.id
                  });
                  break;
                case "scribe":
                  fullList = await archeogrid_client.projects.limitedProjectsRoot.query({
                    branch: b_list.branch,
                    user_id: res.locals.user.id
                  });
                  break;
                default:
                  console.log(`CSV CONTROLLER ERROR: USER STATUS ${res.locals.user.user_status} NOT SUPPORTED!`);
                  fullList = [];
              }

              b_list.listproject = fullList?.map(p => p.folder_name) || [];
            }

            const branches = await archeogrid_client.archeogrid.getBranches.query({
              language: res.locals.lang,
            });

            params.listprojectByBranch = listprojectByBranch;
            params.branches = branches;
            break;
          case 1:
            let requestedBranch: string, projectFolder:string;
            // COMING FROM CREATE TABLE
            if(req.body['csv_metadata_id']){
              const csv_metadata = await archeogrid_client.csv.getOneCSVMetadata.query({ csv_metadata_id: Number.parseInt(req.body['csv_metadata_id']) });

              if(!csv_metadata){
                req.session.csvError = 'CSV-Metadata introuvable.';
                res.redirect('/admin/csvMetadata');
                return;
              }

              const default_values = {
                csv_file_name: csv_metadata.csv_file_name,
                delimiterIsTab: csv_metadata.csv_delimiter.trim() == 'TAB',
                csv_delimiter: csv_metadata.csv_delimiter.trim() == 'TAB' ? '\t' : csv_metadata.csv_delimiter,
                model_name: csv_metadata.branch + '_' +  csv_metadata.csv_file_name.replace('.csv', ''),
                modelItemType: csv_metadata.item_type
              };

              params.default_values = default_values;

              requestedBranch = csv_metadata.branch;
              projectFolder = csv_metadata.project_name;
            }else{
              params.default_values = {};
              requestedBranch = req.body.requestedBranch;
              projectFolder = req.body.project; 
            }
            

            const pathMetadata = await getFolderMetadataPath({
              requestedBranch: requestedBranch as Branch,
              projectFolder,
            });

            if(!fs.existsSync(pathMetadata)){
              req.session.csvError = `Dossier de csv-metadata introuvable. Assurez vous que le répetoire 'metadata' existe à la racine du projet/dépôt ${projectFolder}.`;
              res.redirect('/admin/csvMetadata');
              return;
            }
            const listFiles = fs.readdirSync(pathMetadata, {withFileTypes: true});
            const csvFiles = listFiles.filter(file => file.isFile() && file.name.endsWith('.csv'));
            params.csvFiles = csvFiles;
            params.projectFolder = projectFolder;
            params.requestedBranch = requestedBranch;
            break;
          case 2:
            const requestedBranch2 = req.body.requestedBranch;
            const projectFolder2 = req.body.projectFolder;
            const modelName = sanitizeSQLName(req.body.modelName);
            const modelItemType = req.body.modelItemType;
            const delimiter = req.body.delimiter?.trim();
            const delimiterIsTab = req.body.delimiterIsTab;
            const csvFile = req.body.csvFile;

            if(!modelName){
              req.session.csvError = `Le nom du modèle n'a pas été renseigné.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const exists_model = await archeogrid_client.csv.CSVMetadataModelExists.query({
              branch: requestedBranch2,
              model_name: modelName,
              language: res.locals.lang
            });       
            
            const exists_entry = await archeogrid_client.csv.CSVMetadataEntryExists.query({
              branch: requestedBranch2,
              csv_file_name: csvFile,
              project_name: projectFolder2
            })
            
            const pathToCSV = await getCsvPath({
              requestedBranch: requestedBranch2,
              projectFolder: projectFolder2,
              csvFile: csvFile
            });

            if(!fs.existsSync(pathToCSV)){
              req.session.csvError = `Fichier csv introuvable. Assurez vous qu'il existe et accesible à la racine du projet/dépôt ${projectFolder2}.`;
              res.redirect('/admin/csvMetadata');
              return;
            }

            const csvReader = fs.readFileSync(pathToCSV, 'utf8');
            let csvByRow = csvReader.split('\n');
            
            if(!csvByRow || csvByRow.length <= 1){

              csvByRow = csvReader.split('\r\n');

              if(!csvByRow || csvByRow.length <= 1){
                req.session.csvError = `Le fichier csv semble vide.`;
                res.redirect('/admin/csvMetadata');
                return;
              }
            }
            
            const columnInfo = csvByRow[0].split(delimiterIsTab ? '\t' : delimiter);

            console.log(csvByRow, columnInfo);

            let columnInfo_formatted: {code: string, label: string}[] = [];
            for(let column of columnInfo){
              // REPLACE WHITE SPACE, COMMA AND SEMICOLON BY UNDERSCORE
              let cf = sanitizeSQLName(column);

              if(cf === '' || cf === undefined){
                req.session.csvError = `Une des colonnes du header ne possède pas de nom.`;
                res.redirect('/admin/csvMetadata');
                return;
              }

              if(columnInfo_formatted.map(c => c.code).includes(cf)){
                req.session.csvError = `La colonne ${cf} est présente plusieurs fois dans le header du fichier csv.`;
                res.redirect('/admin/csvMetadata');
                return;
              }

              columnInfo_formatted.push({
                code: cf,
                label: column
              });
            }

            const metadataStatusList = await archeogrid_client.metadata.metadataStatusList.query({branch: requestedBranch2});

            const projectId = await archeogrid_client.projects.getProjectIdByName.query({branch: requestedBranch2, name: projectFolder2});

            const thesaurusByType = {
              thesaurus: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: requestedBranch2, thesaurus: 'simple', project_id: projectId ?? undefined}),
              multi: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: requestedBranch2, thesaurus: 'multi', project_id: projectId ?? undefined}),
              pactols: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: requestedBranch2, thesaurus: 'pactols', project_id: projectId ?? undefined})
            } 

            params.exists_model = exists_model;
            params.exists_entry = exists_entry;
            params.modelName = modelName;
            params.modelType = modelItemType;
            params.columnInfo = columnInfo_formatted;
            params.csvFile = csvFile;
            params.projectFolder = req.body.projectFolder;
            params.metadataStatusList = metadataStatusList;
            params.requestedBranch = requestedBranch2;
            params.csvDelimiterIsTab = delimiterIsTab;
            params.csvDelimiter = delimiterIsTab ? 'TAB' : delimiter;
            params.thesaurusByType = thesaurusByType;
            break;
          case 3:
            const projectFolder3 = req.body.projectFolder;
            const csvFile2 = req.body.csvFile;
            const modelName2 = req.body.modelName;
            const modelDesc = req.body.modelDesc;
            const modelItemType2 = req.body.modelItemType;
            const requestedBranch3 = req.body.requestedBranch;
            const csvDelimiter2 = req.body.delimiter;
            const metadataModelId = req.body.metadataModelId;
            const csvMetadataId = Number.parseInt(req.body.csvMetadataId);

            if(metadataModelId){
              const csv_metadata_id = Number.parseInt(req.body.csvMetadataId);
              await archeogrid_client.csv.deleteModel.mutate({csv_metadata_id});
            }

            const columnCodes: string[] = Object.entries(req.body).filter(([key, value]) => key.endsWith('code')).map(([key, value]) => value as string);
            let rank = 0;
            let metadatas = columnCodes.map(code => ({
              code,
              label: req.body[`${code}-label`],
              description: req.body[`${code}-description`],
              type: req.body[`${code}-type`],
              list: req.body[`${code}-list`], 
              y: req.body[`${code}-isrequired`] === 'on' ? 1 : 0,
              isunique: req.body[`${code}-isunique`] === 'on' ? 1 : 0,
              rank: rank++,
              query: 'y'
            }));

            const metadata_model = await archeogrid_client.metadata.createMetadataModel.mutate({
              branch: requestedBranch3, 
              language: res.locals.lang,
              name: modelName2,
              type: modelItemType2,
              desc: modelDesc,
              visible: 1,
              projects: [projectFolder3]
            })

            if(!metadata_model){
              req.session.csvError = `Une erreur est survenue lors de la création de la modèle de metadata.`;
              res.redirect('/admin/csvMetadata');
              return;
            }
 
            // Create the expected model with all displayable metadatas
            await archeogrid_client.metadata.createMetadatasOfModel.mutate({
              branch: requestedBranch3,
              language: res.locals.lang,
              metadata_model_id: metadata_model.id,
              metadatas
            });

            // If exists, create the raw metadata for thesaurus, not displayable
            const raw_metadatas = metadatas.filter(metadata => ['thesaurus', 'multi', 'pactols'].includes(metadata.type));

            for(const rmeta of raw_metadatas){
              rmeta.code = `${rmeta.code}_raw`;
              rmeta.label = `${rmeta.label} (raw)`;
              rmeta.description = `Raw value of ${rmeta.label}, do not modify. ${rmeta.description}`;
              rmeta.query = 'n';
              rmeta.type = 'char'
            }

            if(raw_metadatas.length > 0){
              await archeogrid_client.metadata.createMetadatasOfModel.mutate({
                branch: requestedBranch3,
                language: res.locals.lang,
                metadata_model_id: metadata_model.id,
                metadatas: raw_metadatas
              });
            }

            if(csvMetadataId){
              const result = await archeogrid_client.csv.updateCSVMetadataModel.mutate({
                csv_metadata_id: csvMetadataId,
                csv_metadata_model_id: metadata_model.id
              })

              params.status = result.status;
              if(result.status){
                params.message = result.message;
                params.csv_metadata_id = csvMetadataId;
              }else{
                params.bad = result.message;
              }
            }else{
              const result = await archeogrid_client.csv.createCSVMetadataWithModel.mutate({
                branch: requestedBranch3,
                csv_metadata_model_id: metadata_model.id,
                project_name: projectFolder3,
                item_type: modelItemType2,
                csv_file_name: csvFile2,
                csv_delimiter: csvDelimiter2
              });

              params.status = result.status;
              if(result.status){
                params.message = result.message;
                params.csv_metadata_id = result.csv_metadata_id;
              }else{
                params.bad = result.message;  
              }
              
              params.metadata_model_id = metadata_model.id;
            }
            break;
        }
        break;
      case 'ingestCSV':
        action_view = 'admin/csvMetadata_actions/ingestCSV';
        switch(step){
          case 0:
            params.selected_csv_metadata_id = Number.parseInt(req.body.csv_metadata_id ?? '');

            const format_user = {
              id: res.locals.user.id,
              user_status: res.locals.user.user_status
            };

            params.branches = await archeogrid_client.archeogrid.getBranches.query({
              language: res.locals.lang,
            });

            params.csv_metadata = await archeogrid_client.csv.getAllCSVMetadata.query({user: format_user});
            break;
          case 1:
            const csvMetadataId = Number.parseInt(req.body['csv-metadata-id'] ?? '');

            if(!csvMetadataId || isNaN(csvMetadataId)){
              console.log('ERROR NO CSV METADATA ID');
              res.sendStatus(404);
              return;
            }

            const updateTable = req.body.updateTable === 'on';
            const updateModel = req.body.updateModel === 'on'; 

            const result: {updateTable?: {status: boolean, message: string, csv_metadata_id?: number}, updateModel?: {status: boolean, message: string, csv_metadata_id?: number} | null} = {};
            if(updateTable){
              const ut = await archeogrid_client.csv.CSVMetadataTableIngestion.mutate({
                csv_metadata_id: csvMetadataId
              });

              result.updateTable = ut;
            }
          
            if(updateModel){
              const um = await archeogrid_client.csv.CSVMetadataModelIngestion.mutate({
                csv_metadata_id: csvMetadataId,
                user_id: res.locals.user.id
              });

              result.updateModel = um;
            }

            params.updateTable = result.updateTable;
            params.updateModel = result.updateModel;

            res.status(200);
            res.contentType('application/json');
            res.send(JSON.stringify(params));
            return;
        }
        break;
      case 'addKeywordsFromMetadata':
        action_view = 'admin/csvMetadata_actions/addKeywordsFromMetadata';
        switch(step){
          case 0:

            const csv_metadata_id = Number.parseInt(req.body.csv_metadata_id ?? '');
            if(!csv_metadata_id || isNaN(csv_metadata_id)){
              req.session.csvError = 'CSV-Metadata introuvable.';
              res.redirect('/admin/csvMetadata');
              return;
            }

            const csv_metadata = await archeogrid_client.csv.getOneCSVMetadata.query({csv_metadata_id});

            if(!csv_metadata){
              req.session.csvError = 'CSV-Metadata introuvable.';
              res.redirect('/admin/csvMetadata');
              return;
            }

            const In_metadataForKeywords = await archeogrid_client.csv.InMetadataForKeywordsIndexMulti.query({
              csv_metadata_id,
              language: res.locals.lang
            });

            const Out_metadataForKeywords = await archeogrid_client.csv.OutMetadataForKeywordsIndexMulti.query({
              csv_metadata_id,
              language: res.locals.lang
            });

            const thesaurusByType = {
              simple: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'simple'}),
              multi: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'multi'}),
              pactols: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'pactols'})
            }

            params = {
              idItem: 0,
              type: csv_metadata.item_type,
              branch: csv_metadata.branch,
              idFolder: 0,
              In_metadataForKeywords,
              Out_metadataForKeywords,
              thesaurusByType,
              csv_metadata_id
            }
            break;
        }
        break;
      default: 
        action_view = 'admin/csvMetadata';
        step = 0;
    }

    const next_step = step + 1;
    res.render(action_view,{
      ...params,
      branch: branchConfig,
      layout: "admin/layout_admin",
      action: action,
      step: next_step
    });
  })

admin
  .route("/csvMetadata/:csv_metadata_id/csvTable")
  .get(async (req, res) => {
    const csv_metadata_id = Number.parseInt(req.params.csv_metadata_id ?? '');
    if(!csv_metadata_id || isNaN(csv_metadata_id)){
      res.sendStatus(404);
    }

    const csv_table = await archeogrid_client.csv.getEntryCSVMetadataTable.query({branch: branchConfig, csv_metadata_id});

    res.contentType('application/json');
    res.send(JSON.stringify(csv_table));
  })

admin
  .route("/csvMetadata/:csv_metadata_id/csvModel")
  .get(async (req, res) => {
    const csv_metadata_id = Number.parseInt(req.params.csv_metadata_id ?? '');
    if(!csv_metadata_id || isNaN(csv_metadata_id)){
      res.sendStatus(404);
    }

    const csv_model = await archeogrid_client.csv.getEntryCSVMetadataModel.query({csv_metadata_id, language: res.locals.lang});

    res.contentType('application/json');
    res.send(JSON.stringify(csv_model));
  })

admin
  .route("/csvMetadata/tableExists,:tableName")
  .get(async (req, res) => {
    const table_name = sanitizeSQLName(req.params.tableName);

    if(!table_name){
      console.log(`ERROR IN CSVMETADATA TABLE NAME NOT PROVIDED : ${table_name}`);
      res.sendStatus(404);
      return;
    }

    const result = await archeogrid_client.csv.CSVMetadataTableExists.query({
      table_name
    });

    console.log(result);

    res.status(200);
    res.contentType('application/json');
    res.send(JSON.stringify(result));
    return;
  })

admin
  .route("/synchro")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    const layoutUser = res.locals.user.user_status === "scribe" ? "admin/layout_dashboard" : "admin/layout_admin";
    performRequest("/api/branche", "GET", null, (data) => {
      res.render("admin/synchro", {
        rootproj: "",
        branche: data,
        message: "",
        listproject: "",
        listfolder: "",
        idFolder: "",
        folderName: "",
        title: req.url,
        method: req.method,
        profondeur: 0,
        profMax: 0,
        mode: 0,
        recursive: 1,
        //del: 2, // ON verra plus tard pour traiter les suppressions donc, del = 0 ou 1 n'est pas pris en compte
        fin: 0,
        userStatus: res.locals.user.user_status,
        layout: layoutUser,
      });
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      const layoutUser = res.locals.user.user_status === "scribe" ? "admin/layout_dashboard" : "admin/layout_admin";
      let rootproj = "";
      let profondeur = Number.parseInt(req.body.profondeur);
      if (req.body.rootproj) rootproj = req.body.rootproj;

      if (!req.body.idFolder) {
        // On n'a pas encore choisi le projet / le dépôt / corpus. Avant tout :
        // etape préalable à la synchro, réalisées dans fsdirSynchFirst :
        // 1/ on raz la table de synchro
        // 2/ on desactive le trigger qui met à jour les nb_tot_images des folders

        await req.fetchApi(`/api/fsdirSynchFirst/${req.body.rootproj}`);
        const paramRequest = res.locals.user.user_status === "admin" ? "projectsRoot" : "limitedProjectsRoot";
        const rootfolder = await req.fetchApi(`/api/${paramRequest}/${req.body.rootproj}`, {
          userId: res.locals.user.id,
        });

        res.render("admin/synchro", {
          rootproj: rootproj,
          branche: "",
          listproject: rootfolder,
          listfolder: "",
          idFolder: "",
          folderName: "",
          message: "",
          title: req.url,
          method: req.method,
          profondeur: 0,
          profMax: 0,
          mode: 0,
          recursive: 1,
          del: 2,
          fin: 0,
          userStatus: res.locals.user.user_status,
          layout: layoutUser,
        });

        return;
      }

      if (!req.body.folderName) {
        // On a choisi le projet mais pas le dossier de synchro
        const data = await req.fetchApi(`/api/dbPathFolder/${req.body.rootproj},${req.body.idFolder}`, req.body);

        res.render("admin/synchro", {
          rootproj: rootproj,
          branche: "",
          listproject: "",
          listfolder: data,
          idFolder: req.body.idFolder,
          folderName: data[0].path,
          message: "",
          title: req.url,
          method: req.method,
          profondeur: 0,
          profMax: 0,
          mode: 0,
          recursive: 1,
          nbfolder: "",
          del: 2,
          fin: 0,
          userStatus: res.locals.user.user_status,
          layout: layoutUser,
        });

        return;
      }

      const branch = req.body.rootproj;
      const folder_id = Number.parseInt(req.body.folderName);
      const mode = req.body.mode === "1" ? "FOLDERS" : req.body.mode === "2" ? "FILES" : "FILES_FOLDERS";
      const del = Boolean(Number.parseInt(req.body.del));
      const recursive = Boolean(Number.parseInt(req.body.recursive));

      const new_folder = [];
      // On a tout pour faire la synchro ! Premier tour : profondeur = 0
      // Nouvelle procédure : on distingue le premier tour des autres
      if (profondeur === 0) {
        const sync_data = await archeogrid_client.fs.getFsDirSync.query({
          branch,
          folder_id,
          mode,
          recursive,
        });

        // si des folder ont été intégrés en BDD il faut les prendre en compte pour les droits du scribe si user pas admin
        if (res.locals.user.user_status === "scribe") {
          for (let i = 0; i < sync_data.src_data.length; i++) {
            const fol = sync_data.src_data[i];
            if (fol.type === "folder") {
              const id = typeof fol.id === "string" ? Number.parseInt(fol.id) : fol.id;
              new_folder.push({ id, name: fol.name });
              await archeogrid_client.users.createUserAccessAdmin.mutate({
                branch,
                user_id: res.locals.user.id,
                forbidden_folder: id,
              });
            }
          }
        }

        profondeur++;
        const profondeurTemp = sync_data.max_depth - profondeur;

        if (profondeurTemp >= 1) {
          // on propose de relancer
          res.render("admin/synchro", {
            rootproj: rootproj,
            branche: "",
            listproject: "",
            listfolder: "",
            idFolder: req.body.idFolder,
            folderName: req.body.folderName,
            message: "",
            sync_data,
            title: req.url,
            method: req.method,
            profondeur: profondeur,
            profMax: sync_data.max_depth,
            mode: req.body.mode,
            del: req.body.del,
            recursive: req.body.recursive,
            dataclean: [],
            fin: 0,
            userStatus: res.locals.user.user_status,
            layout: layoutUser,
          });

          return;
        }

        // TODO: menage / Clean
        const clean = await archeogrid_client.fs.getFsDirSyncClean.query({
          branch,
          folder_id,
          mode,
          del,
          recursive,
        });

        // mettre les infos de dataclean dans la page résultat
        // et enfin on lance la procedure d'update dans le synchFin après voir lancé le dernier synch
        await archeogrid_client.fs.getFsDirSyncEnd.query({ branch, folder_id });
        await archeogrid_client.fs.getFsSyncInfoPath.query({ branch });

        res.render("admin/synchro", {
          rootproj: rootproj,
          branche: "",
          listproject: "",
          listfolder: "",
          idFolder: req.body.idFolder,
          folderName: req.body.folderName,
          message: "OK",
          sync_data,
          title: req.url,
          method: req.method,
          profondeur: profondeur,
          profMax: 0,
          mode: req.body.mode,
          del: req.body.del,
          recursive: req.body.recursive,
          dataclean: clean,
          fin: 1,
          userStatus: res.locals.user.user_status,
          layout: layoutUser,
        });

        return;
      }

      // la profondeur n'est pas 0, on a déjà fait la lecture sur disque et le remplissage du tableau donc on fait continu
      const syncNextData = await archeogrid_client.fs.getFsDirSyncNext.query({
        branch,
        depth: profondeur,
        mode,
      });

      profondeur++;

      const profondeurTemp = req.body.profMax - profondeur;
      const tour = Number.parseInt(req.body.profMax);
      if (profondeurTemp >= 1) {
        if (res.locals.user.user_status === "scribe") {
          const access_data = await archeogrid_client.users.createUserAccessAdmin.mutate({
            branch,
            user_id: res.locals.user.id,
            forbidden_folder: syncNextData.new_folders,
          });
        }
        // on propose de relancer
        // TODO : faire une pause entre les tours pour assurer que tous les insert ont bien été faits...
        res.render("admin/synchro", {
          rootproj: rootproj,
          branche: "",
          listproject: "",
          listfolder: "",
          idFolder: req.body.idFolder,
          folderName: req.body.folderName,
          message: "",
          sync_data: syncNextData,
          title: req.url,
          method: req.method,
          profondeur: profondeur,
          profMax: tour,
          mode: req.body.mode,
          del: req.body.del,
          recursive: req.body.recursive,
          nbfolder: req.body.nbfolder,
          nbfile: req.body.nbfile,
          dataclean: [],
          fin: 0,
          userStatus: res.locals.user.user_status,
          layout: layoutUser,
        });

        return;
      }

      const clean = await archeogrid_client.fs.getFsDirSyncClean.query({
        branch,
        folder_id,
        mode,
        del,
        recursive,
      });

      // et enfin on lance la procedure d'update dans le synchFin après voir lancé le dernier synch
      await archeogrid_client.fs.getFsDirSyncEnd.query({ branch, folder_id });

      const end_data = await archeogrid_client.fs.getFsSyncInfoPath.query({ branch });

      res.render("admin/synchro", {
        rootproj: rootproj,
        branche: "",
        listproject: "",
        listfolder: "",
        idFolder: req.body.idFolder,
        folderName: req.body.folderName,
        message: "OK",
        sync_data: end_data,
        title: req.url,
        method: req.method,
        profondeur: profondeur,
        profMax: 0,
        mode: req.body.mode,
        recursive: req.body.recursive,
        del: req.body.del,
        dataclean: clean,
        fin: 1,
        userStatus: res.locals.user.user_status,
        layout: layoutUser,
      });
    }),
  );

// ingestion de mots-clés en masse
admin
  .route("/tag")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    res.render("admin/tag", {
      branche: "",
      listfolders: "",
      idFolder: "",
      rootDir: "",
      listthesaurus: "",
      idThesaurus: "",
      thesaurusName: "",
      message: "",
      bad: "",
      title: req.url,
      method: req.method,
      layout: "admin/layout_admin",
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      let branche = "";
      let rootDir = "";
      let idFolder = 0;
      let thesaurus = "";
      let itemType = "file";

      if (req.body.branche) branche = req.body.branche;
      if (req.body.rootDir) {
        if (req.body.rootDir.indexOf("/") !== -1) {
          rootDir = req.body.rootDir.split("/")[1];
          idFolder = req.body.rootDir.split("/")[0];
        } else {
          rootDir = req.body.rootDir;
        }
        if (req.body.idThesaurus) {
          thesaurus = req.body.idThesaurus;
          if (req.body.itemType) itemType = req.body.itemType;
          const datapatch = {
            idUser: res.locals.user.id,
          };

          const renderBaseData = {
            branche: branche,
            listfolders: "",
            idFolder: idFolder,
            rootDir: rootDir,
            listthesaurus: "alreadySet",
            idThesaurus: req.body.idThesaurus,
            thesaurusName: thesaurus,
            title: req.url,
            method: req.method,
            layout: "admin/layout_admin",
            message: "",
            bad: "",
          };

          const [data, code] = await req.fetchApiCode(
            `/api/tags/${branche},${itemType},${thesaurus}`,
            datapatch,
            "PATCH",
          );

          if (code === 201) {
            res.render("admin/tag", {
              ...renderBaseData,
              message: data.length,
            });
          } else {
            res.render("admin/tag", {
              ...renderBaseData,
              bad: "Error à l'ingestion des tags",
            });
          }
        } else {
          // on a le projet mais pas encore le thesaurus
          const data = [
            {
              name: "pactols",
              description: "les thesaurus pactols",
            },
            {
              name: "multi",
              description: "Thesaurus multi Notre-Dame",
            },
          ];
          res.render("admin/tag", {
            branche: branche,
            listfolders: "",
            idFolder: idFolder,
            rootDir: rootDir,
            listthesaurus: data,
            idThesaurus: "",
            thesaurusName: "",
            message: "",
            bad: "",
            title: req.url,
            method: req.method,
            layout: "admin/layout_admin",
          });
        }
      } else {
        // on n'a pas encore choisi le projet / root_dir
        performRequest(`/api/projectsRoot/${req.body.branche}`, "GET", null, (rootfolders) => {
          res.render("admin/tag", {
            branche: branche,
            listfolders: rootfolders,
            idFolder: "",
            rootDir: "",
            listthesaurus: "",
            idThesaurus: "",
            thesaurusName: "",
            message: "",
            bad: "",
            title: req.url,
            method: req.method,
            layout: "admin/layout_admin",
          });
        });
      }
    }),
  );

// ingestion json en table
admin.route("/json").get((req, res) => {
  res.locals.branch = branchConfig;
  const layoutUser = res.locals.user.user_status === "scribe" ? "admin/layout_dashboard" : "admin/layout_admin";
  performRequest("/api/branche", "GET", null, (data) => {
    res.render("admin/json", {
      rootproj: "",
      branche: data,
      message: "",
      listproject: "",
      listfiles: "",
      idFolder: "",
      folderName: "",
      title: req.url,
      method: req.method,
      layout: layoutUser,
    });
  });
});

admin.route("/addIPTC_EXIF").get((req, res) => {
  res.locals.branch = branchConfig;
  const layoutUser = res.locals.user.user_status === "scribe" ? "admin/layout_dashboard" : "admin/layout_admin";
  res.render("admin/addIPTC_EXIF", {
    rootproj: "",
    layout: layoutUser,
    step: 1,
  });
});

admin.route("/addIPTC_EXIF").post(async (req, res) => {
  const layoutUser = res.locals.user.user_status === "scribe" ? "admin/layout_dashboard" : "admin/layout_admin";
  const { rootproj, idFolder } = req.body;

  // CASE 1 => SELECT BRANCH
  // CASE 2 => SELECT ROOT PROJECT
  // CASE 3 => SELECT FOLDER
  // CASE 4 => SYNCHRONIZE FILES
  switch(req.body.step){
    case "2":
      const paramRequest = req.user?.user_status === "admin" ? "projectsRoot" : "limitedProjectsRoot";
      let listproject = await req.fetchApi(`/api/${paramRequest}/${rootproj}`, {
        userId: req.user?.id.toString() ?? "",
      });

      res.render("admin/addIPTC_EXIF", {
        rootproj: rootproj,
        layout: layoutUser,
        listproject,
        step: 2,
      });
      return;
    case "3":
      const path = await archeogrid_client.folders.getRealPath.query({ branch: rootproj, id_folder: parseInt(idFolder.split('##')[0]) });
      const id_parent = parseInt(idFolder.split('##')[0]);
      const folderList = await archeogrid_client.folders.getDirectSubFolders.query({
        branch: rootproj,
        id_parent,
      });

      const files = await archeogrid_client.files.getFilesFromFolder.query({
        branch: rootproj,
        id_folder: id_parent
      })

      const rootFolder = path.split('/')[0];

      res.render("admin/addIPTC_EXIF", {
        rootproj: rootproj,
        layout: layoutUser,
        listfolder: folderList,
        pathFolder: path,
        files,
        currentFolder: id_parent,
        rootFolder,
        step: 3
      });
      return;
    case "4":
      // TODO TRAITEMENT DE L'EXIF et IPTC
      const file = await archeogrid_client.files.getFilesFromFolder.query({
        branch: req.body.rootproj,
        id_folder: parseInt(req.body.idFolder)
      });

      const newMetadataByFiles = await archeogrid_client.files.addIptcExifMetadataPassport.query({
        branch: req.body.rootproj,
        id_files: file.map((f) => parseInt(f.id)),
        rootFolder: req.body.rootFolder,
        id_user: req.user?.id
      });

      res.render("admin/addIPTC_EXIF_steps/addIPTC_EXIF_step4", {
        rootproj: rootproj,
        layout: layoutUser,
        files: file,
        step: 4,
        newMetadataByFiles
      });
      return;
  }
});

// ordre des dossiers à l'intérieur d'une arborescence
admin
  .route("/folderOrder")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    performRequest("/api/branche", "GET", null, (data) => {
      res.render("admin/folderorder", {
        rootproj: "",
        branche: data,
        userStatus: res.locals.user.user_status,
        message: "",
        listproject: "",
        listfolder: "",
        idFolder: "",
        folderName: "",
        title: req.url,
        method: req.method,
        profondeur: 0,
        profMax: 0,
        mode: 0,
        fin: 0,
        layout: layoutAdm,
      });
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const layoutAdm = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

      let rootproj = res.locals.user.user_status === "admin" ? "" : branchConfig;
      if (req.body.rootproj) rootproj = req.body.rootproj;

      if (req.body.idFolder) {
        // On a choisi le projet mais pas le dossier de reorder
        const datap = await req.fetchApi(`/api/profondeurMaxFolder/${req.body.rootproj},${req.body.idFolder}`);
        const data = await req.fetchApi(`/api/dbAllPathFolder/${req.body.rootproj},${req.body.idFolder}`, req.body);

        res.render("admin/folderorder", {
          rootproj: rootproj,
          branche: "",
          userStatus: res.locals.user.user_status,
          listproject: "",
          listfolder: data,
          idFolder: req.body.idFolder,
          folderName: data[0].path,
          message: "",
          title: req.url,
          method: req.method,
          profMax: 0,
          mode: 0,
          fin: 0,
          profondeur: Number.parseInt(datap.profondeur),
          layout: layoutAdm,
        });

        return;
      }

      // On n'a pas encore choisi le projet / le dépôt
      // choix restreint si pas admin
      const paramRequest = res.locals.user.user_status === "admin" ? "projectsRoot" : "limitedProjectsRoot";
      const dataGet = {
        userId: res.locals.user.id,
      };

      const rootfolder = await req.fetchApi(`/api/${paramRequest}/${rootproj}`, dataGet);
      performRequest(`/api/${paramRequest}/${rootproj}`, "GET", dataGet, (rootfolder) => {
        res.render("admin/folderorder", {
          rootproj: rootproj,
          branche: "",
          userStatus: res.locals.user.user_status,
          listproject: rootfolder,
          listfolder: "",
          idFolder: "",
          folderName: "",
          message: "",
          title: req.url,
          method: req.method,
          profondeur: 0,
          profMax: 0,
          mode: 0,
          fin: 0,
          layout: layoutAdm,
        });
      });
    }),
  );

// ordre des elements thesaurus à l'intérieur d'une arborescence de thesaurus
admin
  .route("/thesaurusOrder")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/branche", "GET", null, (data) => {
      res.render("admin/thesaurusorder", {
        brancheThes: "",
        branche: data,
        message: "",
        listthesaurus: "",
        listthesitem: "",
        idThes: "",
        thesaurusName: "",
        title: req.url,
        method: req.method,
        layout: "admin/layout_admin",
      });
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const thesaurus = "";
      let brancheThes = "";
      if (req.body.brancheThes) brancheThes = req.body.brancheThes;
      if (req.body.idThes) {
        // On a choisi le thesaurus
        const data = await req.fetchApi(`/api/orderThesaurus/${req.body.brancheThes},${req.body.idThes}`);
        res.render("admin/thesaurusorder", {
          brancheThes: req.body.brancheThes,
          branche: "",
          listthesaurus: "",
          listitemthes: data,
          idThes: req.body.idThes,
          thesaurusName: data[0].name,
          message: "",
          title: req.url,
          method: req.method,
          layout: "admin/layout_admin",
        });
      } else {
        // On n'a pas encore choisi le thesaurus
        const thesaurusList = await req.fetchApi(`/api/thesaurusOrigin/${brancheThes}`);
        res.render("admin/thesaurusorder", {
          brancheThes: req.body.brancheThes,
          branche: "",
          listthesaurus: thesaurusList,
          listitemthes: "",
          idThes: "",
          thesaurusName: "",
          message: "",
          title: req.url,
          method: req.method,
          layout: "admin/layout_admin",
        });
      }
    }),
  );

// Ajouter un element  de thesaurus au départ pour récupérer les entités nouvellement créées
admin
  .route("/thesaurusAdd")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    let brancheThes = "";
    const layout = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
    if (res.locals.user.user_status !== "admin") brancheThes = branchConfig;

    res.render("admin/thesaurusadd", {
      brancheThes,
      message: "",
      typeThes: "",
      typesThes: ["thesaurus", "multi", "pactols"],
      listthesaurus: "",
      listthesitem: "",
      idThes: "",
      labelThes: "",
      tree: "",
      thesaurusName: "",
      inputThesName: "",
      title: req.url,
      method: req.method,
      layout,
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const layout = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

      let brancheThes = "";
      let labelThes = "";
      if (res.locals.user.user_status !== "admin") req.body.brancheThes = branchConfig;
      if (req.body.brancheThes) brancheThes = req.body.brancheThes;

      if (!req.body.typeThes) {
        // on choisit quel type de thesaurus : simple ou multi
        res.render("admin/thesaurusadd", {
          brancheThes: req.body.brancheThes,
          branche: "",
          listthesaurus: "",
          listitemthes: "",
          idThes: "",
          labelThes: "",
          tree: "",
          thesaurusName: "",
          inputThesName: "",
          typesThes: ["thesaurus", "multi", "pactols"],
          typeThes: "",
          message: "",
          title: req.url,
          method: req.method,
          layout,
        });
        return;
      }

      // on a choisi le type de thesaurus : simple ou multi
      if (req.body.idThes) {
        // idThes est le nom du thesaurus suivi du label pour affichate séparé par _#_
        // et maintenant on récupère aussi l'id_thes la tête du thesaurus qui peut ne pas toujours être 1 (cf corpus)!
        labelThes = req.body.idThes.split("_#_")[1];
        req.body.headThes = req.body.idThes.split("_#_")[2];
        req.body.idThes = req.body.idThes.split("_#_")[0];

        let datatree: unknown;
        if (req.body.typeThes === "multi") {
          // il faut récupérer la tête du thesaurus ! headThes OK
          datatree = await req.fetchApi(
            `/api/thesaurusTreeMultiLng/${req.body.brancheThes},${req.body.idThes},${req.body.headThes},${res.locals.lang}`,
          );
        } else if (req.body.typeThes === "thesaurus") {
          datatree = await req.fetchApi(`/api/thesaurusTree/${req.body.brancheThes},${req.body.idThes},${req.body.headThes}`);
        } else if (req.body.typeThes === "pactols") {
          datatree = await req.fetchApi(`/api/thesaurusTreePactols/${req.body.brancheThes},${req.body.idThes},${req.body.headThes}`);
        }

        // On a deja choisi le thesaurus
        if (req.body.inputThesName) {
          // On a choisi le nom pour le nouvel item => on l'ajoute au thesaurus
          // add thesitem
          // Et on récupère l'entité qui a été cochée au départ dans la liste pour récupérer son id
          // Dans le listEntity, on récupère <name>#<id>
          const dataput = {
            name: req.body.inputThesName,
            parent: req.body.idParent ? req.body.idParent : 1, // on ne peut pas insérer un élément de thesaurus ailleurs qu'au premier niveau ?
            type: req.body.typeThes, // thesaurus ou thesaurus_multi
            identifier: req.body.listEntity ? req.body.listEntity.split("#")[1] : " ",
            lng: res.locals.lang,
          };

          const [putThesaurusData, putThesaurusCode] = await req.fetchApiCode(
            `/api/thesaurus/${req.body.brancheThes},${req.body.idThes}`,
            dataput,
            "PUT",
          );

          if (putThesaurusCode !== 201 || !req.body.listEntity) {
            res.render("admin/thesaurusadd", {
              branche: branchConfig,
              brancheThes: req.body.brancheThes,
              message: "",
              listthesaurus: "",
              listitemthes: "",
              listthesitem: "",
              labelThes: labelThes,
              tree: datatree,
              typesThes: ["thesaurus", "multi", "pactols"],
              typeThes: req.body.typeThes,
              idThes: req.body.idThes,
              thesaurusName: req.body.idThes,
              inputThesName: "",
              title: req.url,
              method: req.method,
              layout,
            });
            return;
          }

          const datapatch = {
            // si on a modifié le nom de l'entité pour la faire figurer autrement dans le thesaurus
            // il faut quand meme récupérer son vrai nom opur faire la correspondante avec la table des entités
            name: req.body.inputThesName,
            idElement: req.body.listEntity.split("#")[1],
            thesaurusName: req.body.idThes,
            idThes: putThesaurusData.id_thes,
            type: req.body.typeThes,
          };

          // Dans le cas des entités: il faut rattacher l'entité à ce nouvel élément de thesaurus
          // on généralise : api/entityNameLinkThes devient api/<nom du thesaurus>NameLinkThes
          // pour le moment on traite soit le thesaurus deposant pour le conservatoire, soit le thesaurus person pour les projets
          const [patchThesaurusData, patchThesaurusCode] = await req.fetchApiCode(
            `/api/${req.body.idThes}NameLinkThes/${req.body.brancheThes}`,
            datapatch,
            "PATCH",
          );

          if (patchThesaurusCode !== 200) {
            req.flash("error", "Impossible de rattacher l'élément demandé au nouvel item de thesaurus");
            res.render("admin/thesaurusadd", {
              brancheThes: req.body.brancheThes,
              branche: "",
              listthesaurus: "",
              listitemthes: putThesaurusData,
              typesThes: ["thesaurus", "multi", "pactols"],
              typeThes: req.body.typeThes,
              idThes: req.body.idThes,
              labelThes: labelThes,
              tree: datatree,
              thesaurusName: req.body.idThes,
              inputThesName: "",
              message: "KO",
              title: req.url,
              method: req.method,
              layout,
            });
            return;
          }

          res.render("admin/thesaurusadd", {
            brancheThes: req.body.brancheThes,
            branche: "",
            listthesaurus: "",
            listitemthes: patchThesaurusData,
            typesThes: ["thesaurus", "multi", "pactols"],
            typeThes: req.body.typeThes,
            idThes: req.body.idThes,
            labelThes: labelThes,
            tree: datatree,
            thesaurusName: req.body.idThes,
            inputThesName: "",
            message: "OK",
            title: req.url,
            method: req.method,
            layout,
          });
          return;
        }

        if (
          !(
            req.body.typeThes === "multi" &&
            (req.body.idThes.includes("deposant")
                //|| req.body.idThes.includes("organization") ||
              //req.body.idThes.includes("person")
            )
          )
        ) {
          res.render("admin/thesaurusadd", {
            brancheThes: req.body.brancheThes,
            branche: "",
            listthesaurus: "",
            listitemthes: "",
            typesThes: ["thesaurus", "multi", "pactols"],
            typeThes: req.body.typeThes,
            idThes: req.body.idThes,
            labelThes: labelThes,
            tree: datatree,
            thesaurusName: req.body.idThes,
            inputThesName: "",
            message: "",
            title: req.url,
            method: req.method,
            layout,
          });
          return;
        }

        // TODO improve result: pour le moment, si le thesaurus est un de ceux qu'il est prévu de modifier
        // (deposant pour conservatoire, organization ou person pour pft3d et conservatoire, alors on va plus loin
        // sinon rien de plus

        // Nouveauté : on choisi quels potentiels elements on va choisir pour intégrer un thesaurus :
        // jusqu'à présent seulement les entités d'archeoGRID (organization)
        // maintenant, on peut en avoir d'autre en fonction de ce qu'on a choisi comme thesaurus
        // multi + deposant => entityName
        // multi + person => person
        // multi + organization => organization
        // Pour ajouter un item : on récupère la liste des entités qui ne sont pas encore dans le thésaurus des dépôsants
        // Pour avoir les noms sous les yeux pour proposer le nom de la nouvelle entrée du thesaurus
        const data = await req.fetchApi(`/api/${req.body.idThes}WithoutIdthes/${req.body.brancheThes}`);

        res.render("admin/thesaurusadd", {
          brancheThes: req.body.brancheThes,
          branche: "",
          listthesaurus: "",
          listitemthes: data,
          typesThes: ["thesaurus", "multi", "pactols"],
          typeThes: req.body.typeThes,
          idThes: req.body.idThes,
          labelThes: labelThes,
          tree: datatree,
          thesaurusName: req.body.idThes,
          inputThesName: "",
          message: "",
          title: req.url,
          method: req.method,
          parent: 1,
          layout,
        });
        return;
      }

      if (req.body.typeThes === "multi") {
        const thesaurusList = await req.fetchApi(`/api/thesaurusMultiOrigin/${brancheThes},0`);
        res.render("admin/thesaurusadd", {
          brancheThes: req.body.brancheThes,
          branche: "",
          listthesaurus: thesaurusList,
          listitemthes: "",
          typesThes: ["thesaurus", "multi", "pactols"],
          typeThes: req.body.typeThes,
          idThes: "",
          labelThes: "",
          tree: "",
          thesaurusName: "",
          inputThesName: "",
          message: "",
          title: req.url,
          method: req.method,
          layout,
        });
        return;
      }

      if (req.body.typeThes === "pactols") {
        res.render("admin/thesaurusadd", {
          brancheThes: req.body.brancheThes,
          branche: "",
          user: res.locals.user,
          listthesaurus: [{ name: "All", thesaurus: "sujet" , id: "0"}],
          listitemthes: "",
          typesThes: ["thesaurus", "multi", "pactols"],
          typeThes: req.body.typeThes,
          idThes: "",
          labelThes: "",
          tree: "",
          thesaurusName: "",
          inputThesName: "",
          message: "",
          title: req.url,
          method: req.method,
          layout,
        });
        return;
      }

      // thesaurus simple
      // On n'a pas encore choisi le thesaurus
      const thesaurusList = await req.fetchApi(`/api/thesaurusOrigin/${brancheThes}`);
      res.render("admin/thesaurusadd", {
        brancheThes: req.body.brancheThes,
        branche: "",
        listthesaurus: thesaurusList,
        listitemthes: "",
        typesThes: ["thesaurus", "multi", "pactols"],
        typeThes: req.body.typeThes,
        idThes: "",
        labelThes: "",
        tree: "",
        thesaurusName: "",
        inputThesName: "",
        message: "",
        title: req.url,
        method: req.method,
        layout,
      });
    }),
  );

// translation of Concept
admin.route("/thesaurusTranslate").post((req, res) => {
  res.locals.branch = branchConfig;
  const layout = res.locals.user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";
  const labelThes = req.body.idThes.split("_#_")[1];
  req.body.idThes = req.body.idThes.split("_#_")[0];

  const dataPatch = {
    idThes: req.body.translateID_id.split("_")[1],
    lng: req.body.translateLng,
    translation: req.body.translation,
  };
  performRequest(
    `/api/thesaurusTreeMultiLng/${req.body.brancheThes},${req.body.idThes},1,${i18n.getLocale(req)}`,
    "GET",
    undefined,
    (datatree) => {
      performRequest(
        `/api/thesaurusTranslate/${req.body.brancheThes},${req.body.idThes},${req.body.typeThes}`,
        "PATCH",
        dataPatch,
        (data, code) => {
          if (code !== 500) {
            res.render("admin/thesaurusadd", {
              brancheThes: req.body.brancheThes,
              branche: "",
              user: res.locals.user,
              listthesaurus: "",
              listitemthes: "",
              typesThes: ["thesaurus", "multi", "pactols"],
              typeThes: req.body.typeThes,
              idThes: req.body.idThes,
              thesaurusName: req.body.idThes,
              labelThes: labelThes,
              inputThesName: "",
              tree: datatree,
              message: "OK",
              title: req.url,
              parent: req.body.idParent,
              method: req.method,
              layout,
            });
          } else {
            res.render("admin/thesaurusadd", {
              brancheThes: req.body.brancheThes,
              branche: "",
              user: res.locals.user,
              listthesaurus: "",
              listitemthes: "",
              typesThes: ["thesaurus", "multi", "pactols"],
              typeThes: req.body.typeThes,
              idThes: req.body.idThes,
              thesaurusName: req.body.idThes,
              labelThes: labelThes,
              inputThesName: "",
              tree: datatree,
              message: "KO",
              title: req.url,
              parent: req.body.idParent,
              method: req.method,
              layout,
            });
          }
        },
      );
    },
  );
});

// entity
admin
  .route("/entity")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/entity", "GET", null, (data) => {
      res.render("admin/entity", {
        entity: data,
        data: "",
        message: "",
        method: req.method,
        title: req.url,
        idEntity: "",
        layout: "admin/layout_admin",
      });
    });
  })
  .post((req, res) => {
    res.locals.branch = branchConfig;
    if (req.body.idEntity) {
      if (typeof req.body.nom === "undefined") {
        performRequest(`/api/entity/${req.body.idEntity}`, "GET", null, (data) => {
          res.render("admin/entity", {
            entity: "",
            data: data,
            message: "",
            method: req.method,
            title: req.url,
            idEntity: req.body.idEntity,
            layout: "admin/layout_admin",
          });
        });
      } else {
        // l'entity est modifiée on lance l'update
        performRequest(`/api/entity/${req.body.idEntity}`, "PATCH", req.body, (dateupdate, code) => {
          if (code === 200) {
            // Si le folder n'a pas déjà été créé , il faut le faire, Dans tous les cas, on renvoie un code 201
            const dataput = {
              folderPassport: "entity",
              folderName: req.body.folder_name,
            };
            performRequest("/api/rootFolders/conservatoire3d", "PUT", dataput, (_, codeter) => {
              if (codeter === 201) {
                performRequest("/api/entity", "GET", null, (dataglobale) => {
                  res.render("admin/entity", {
                    entity: dataglobale,
                    data: "",
                    message: `Entité ${req.body.idEntity}, ${req.body.nom} modifiée OK`,
                    method: "GET",
                    title: req.url,
                    idEntity: req.body.idEntity,
                    layout: "admin/layout_admin",
                  });
                });
              }
            });
          }
        });
      }
    } else {
      if (req.body.entityname) {
        // ajouter l'entité
        performRequest("/api/entity/0", "PUT", req.body, (_, code) => {
          if (code === 201) {
            // Il faut créer le folder qui désigne l'entité également pour qu'elle puisse être le parent de futurs dépots
            // ATTENTION : CELA IMPLIQUE QUE LE FOLDER EXISTE SUR LE DISQUE, QU'IL A ETE CREE PAR UN AUTRE MOYEN AVANT
            //let dir = '../web-archeogrid/conservatoire_data/'+req.body.folder_name
            //const uploadPath = path.join(__dirname, dir);
            // fs.ensureDir(uploadPath) // ?? Attention cela crée le répertoire s'il n'existe pas ...
            const dataput = {
              folderPassport: "entity",
              folderName: req.body.folder_name,
            };
            performRequest("/api/rootFolders/conservatoire3d", "PUT", dataput, (_, codebis) => {
              if (codebis === 201) {
                performRequest("/api/entity", "GET", null, (dataglobal) => {
                  res.render("admin/entity", {
                    entity: dataglobal,
                    data: "",
                    message: `Entité Créée (entity et folder) - VERIFIER l'existence du folder ${req.body.folder_name} sur disque !!!`,
                    method: "GET",
                    title: req.url,
                    idEntity: 0,
                    layout: "admin/layout_admin",
                  });
                });
              }
            });
          }
        });
      } else {
        // remplir le formulaire
        performRequest("/api/columnTable/archeogrid_entity", "GET", null, (column) => {
          res.render("admin/entity", {
            entity: 0,
            data: column,
            message: "",
            method: req.method,
            title: req.url,
            idEntity: 0,
            layout: "admin/layout_admin",
          });
        });
      }
    }
  });

admin.route("/actordelete,:actorId").get((req, res) => {
  res.locals.branch = branchConfig;
  const actorId = Number.parseInt(req.params.actorId);
  performRequest(`/api/actor/${branchConfig},${actorId}`, "DELETE", null, (dataDel, code) => {
    if (code === 500) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(dataDel));
    } else {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    }
  });
});

admin.route("/organizationdelete,:orgaId").get((req, res) => {
  res.locals.branch = branchConfig;
  const orgaId = Number.parseInt(req.params.orgaId);
  performRequest(`/api/organization/${branchConfig},${orgaId}`, "DELETE", null, (dataDel, code) => {
    if (code === 500) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(dataDel));
    } else {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    }
  });
});

admin.route("/organizationsort/:key/:order").get((req, res) => {
  res.locals.branch = branchConfig;
  let order = Number.parseInt(req.params.order); // pour gérer By order ... ASC ou DESC ! ASC = 1 DESC = 0 et on alterne
  // si order était 1 on le met à 0 et inversement
  // if (order) order = 0 else order = 1
  performRequest(`/api/organizations,${branchConfig},${req.params.key},${order}`, "GET", null, (datasort, code) => {
    // on trie les actors par la key passé en paramètre
    order = order ? 0 : 1;
    if (code === 500) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(datasort));
    } else {
      res.render("admin/organizations", {
        sortorder: order,
        organizations: datasort,
        //title: req.url,
        title: "Organisations",
        message: "",
        layout: "admin/layout_admin",
      });
    }
  });
});

admin.route("/actorsort/:key/:order").get((req, res) => {
  res.locals.branch = branchConfig;
  let order = Number.parseInt(req.params.order);
  performRequest(`/api/actors/${branchConfig},${req.params.key},${order}`, "GET", null, (datasort, code) => {
    // on trie les actors par la key passé en paramètre
    order = order ? 0 : 1;
    if (code === 500) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(datasort));
    } else {
      res.render("admin/actors", {
        actors: datasort,
        sortorder: order,
        title: "Acteurs / Agents",
        message: "",
        layout: "admin/layout_admin",
      });
    }
  });
});

admin
  .route("/editActor")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/actors/"+branchConfig+",family_name,0", "GET", null, (data) => {
      res.render("admin/editactor", {
        actors: data,
        actor: "",
        sortorder: 0,
        actorid: 0,
        message: "",
        title: req.url,
        entity: "",
        col: [],
        layout: "admin/layout_admin",
      });
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const actorid = req.body.actorSelect;
      let messageUpdate = "";
      const datapatch = typeof req.body.family_name !== "undefined" ? req.body : null;

      const organization = await req.fetchApi("/api/organizations,"+branchConfig+",name,1");
      const dataactors = await req.fetchApi("/api/actors/pft3c,family_name,0");
      const data = await req.fetchApi(`/api/actor/${branchConfig},{actorid}`);
      const datacol = await req.fetchApi("/api/columnTableLabel/"+branchConfig+",actor");

      if (typeof req.body.family_name !== "undefined") {
        // check si valeur du champ a été modifié pour envoyer à l'update ou non
        for (const item in req.body) {
          for (const itemOrig in data) {
            if (item === itemOrig) {
              if (req.body[item] !== "") {
                if (req.body[item] === data[itemOrig]) {
                  delete datapatch[item];
                }
              } else {
                // les cas où le formulaire renvoie une valeur vide
                // null value
                if (!data[itemOrig]) {
                  // le body est '' et l'origine est NULL : pas d'update
                  delete datapatch[item];
                }
              }
            } else if (item === "org") {
              if (itemOrig === "id_organization") {
                if (req.body[item] !== "" && Number.parseInt(req.body[item]) === Number.parseInt(data[itemOrig])) {
                  // l'entité de rattachement de l'acteur n'a pas changé
                  delete datapatch[item];
                  delete datapatch[itemOrig];
                }
              } else if (req.body[item] === "") {
                if (itemOrig === "id_organization" && data[itemOrig] === "") {
                  delete datapatch[item];
                } else {
                  // Avant il y avait une valeur, maintenant il n'y en a pplus : il faut supprimer la liaison entre actor et orga
                  datapatch.org_to_delete = req.body.id_organization;
                }
              }
            } else if (item === "id_entity") {
              delete datapatch[item];
            } else if (item === "ORCID") {
              if (itemOrig === "identifier" && data[itemOrig]) {
                for (let i = 0; i < data[itemOrig].length; i++) {
                  if (data[itemOrig][i].type === "ORCID" && data[itemOrig][i].value === req.body[item]) {
                    delete datapatch[item];
                  }
                }
              }
            } else if (item === "VIAF") {
              if (itemOrig === "identifier" && data[itemOrig]) {
                for (let i = 0; i < data[itemOrig].length; i++) {
                  if (data[itemOrig][i].type === "VIAF" && data[itemOrig][i].value === req.body[item]) {
                    delete datapatch[item];
                  }
                }
              }
            } else if (item.includes("ident_")) {
              // tous les autres cas ou item n'est pas connu à l'avance , un identifier déjà en place l'item est prefixé par ident_
              if (itemOrig === "identifier" && data[itemOrig]) {
                for (let i = 0; i < data[itemOrig].length; i++) {
                  if (
                    data[itemOrig][i].type === item.replace("ident_", "") &&
                    data[itemOrig][i].value === req.body[item]
                  ) {
                    delete datapatch[item];
                  }
                }
              }
            }
          }
        }
        //transformer le datapatch pour recoller type/value
        if (Object.keys(datapatch).length > 1) {
          // il y a des champs à mettre à jour
          performRequest(`/api/actor/${branchConfig},{actorid}`, "PATCH", datapatch, (datatoupdate, code) => {
            if (code === 200) {
              messageUpdate = "Actor well updated";
              res.render("admin/editactor", {
                actors: dataactors,
                actor: datatoupdate,
                actorid: actorid,
                message: messageUpdate,
                title: req.url,
                organization: organization,
                col: datacol,
                layout: "admin/layout_admin",
              });
            } else {
              messageUpdate = "ERROR updating Actor";
              res.render("admin/editactor", {
                actors: dataactors,
                actor: datatoupdate,
                actorid: actorid,
                message: messageUpdate,
                title: req.url,
                organization: organization,
                col: datacol,
                layout: "admin/layout_admin",
              });
            }
          });
        } else {
          // il n'y a pas de champs à mettre à jour ?
          res.render("admin/editactor", {
            actors: dataactors,
            actor: data,
            actorid: actorid,
            message: "ACTOR NOT UPDATED",
            title: req.url,
            organization: organization,
            col: datacol,
            layout: "admin/layout_admin",
          });
        }
      } else {
        res.render("admin/editactor", {
          actors: dataactors,
          actor: data,
          actorid: actorid,
          message: "",
          title: req.url,
          organization: organization,
          col: datacol,
          layout: "admin/layout_admin",
        });
      }
    }),
  );

admin
  .route("/editOrganization")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    performRequest("/api/organizations,"+branchConfig+",name,1", "GET", null, (data) => {
      performRequest("/api/columnTableLabel/"+branchConfig+",organization", "GET", null, (datacol) => {
        res.render("admin/editorganization", {
          organizations: data,
          organizationid: 0,
          message: "",
          title: req.url,
          entity: "",
          col: datacol,
          layout: "admin/layout_admin",
        });
      });
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const orgaid = req.body.organizationSelect;
      const datapatch = typeof req.body.name !== "undefined" ? req.body : null;
      let messageUpdate = "";

      const dataorganizations = await req.fetchApi("/api/organizations,"+branchConfig+",name,1");
      const data = await req.fetchApi(`/api/organization/${branchConfig},{orgaid}`);
      const datacol = await req.fetchApi("/api/columnTableLabel/"+branchConfig+",organization");

      if (typeof req.body.name !== "undefined") {
        // check si valeur du champ a été modifié pour envoyer à l'update ou non
        for (const item in req.body) {
          for (const itemOrig in data) {
            if (item === itemOrig) {
              if (req.body[item] !== "") {
                if (req.body[item] === data[itemOrig]) {
                  delete datapatch[item];
                }
              } else {
                // les cas où le formulaire renvoie une valeur vide
                // null value
                if (!data[itemOrig]) {
                  // le body est '' et l'origine est NULL : pas d'update
                  delete datapatch[item];
                }
              }
            } else if (item === "VIAF") {
              if (itemOrig === "identifier" && data[itemOrig]) {
                for (let i = 0; i < data[itemOrig].length; i++) {
                  if (data[itemOrig][i].type === "VIAF" && data[itemOrig][i].value === req.body[item]) {
                    delete datapatch[item];
                  }
                }
              }
            } else if (item.includes("ident_")) {
              // tous les autres cas ou item n'est pas connu à l'avance , un identifier déjà en place l'item est prefixé par ident_
              if (itemOrig === "identifier" && data[itemOrig]) {
                for (let i = 0; i < data[itemOrig].length; i++) {
                  if (
                    data[itemOrig][i].type === item.replace("ident_", "") &&
                    data[itemOrig][i].value === req.body[item]
                  ) {
                    delete datapatch[item];
                  }
                }
              }
            }
          }
        }
        //transformer le datapatch pour recoller type/value
        if (Object.keys(datapatch).length > 1) {
          // il y a des champs à mettre à jour
          performRequest(`/api/organization/${branchConfig},{orgaid}`, "PATCH", datapatch, (datatoupdate, code) => {
            if (code === 200) {
              messageUpdate = "Organization well updated";
              res.render("admin/editorganization", {
                organizations: dataorganizations,
                organization: datatoupdate,
                organizationid: orgaid,
                message: messageUpdate,
                title: req.url,
                col: datacol,
                layout: "admin/layout_admin",
              });
            } else {
              messageUpdate = "ERROR updating Organization";
              res.render("admin/editorganization", {
                organizations: dataorganizations,
                organization: datatoupdate,
                organizationid: orgaid,
                message: messageUpdate,
                title: req.url,
                col: datacol,
                layout: "admin/layout_admin",
              });
            }
          });
        } else {
          // il n'y a pas de champs à mettre à jour ?
          res.render("admin/editorganization", {
            organizations: dataorganizations,
            organization: data,
            organizationid: orgaid,
            message: "ORGANIZATION NOT UPDATED",
            title: req.url,
            col: datacol,
            layout: "admin/layout_admin",
          });
        }
      } else {
        res.render("admin/editorganization", {
          organizations: dataorganizations,
          organization: data,
          organizationid: orgaid,
          message: "",
          title: req.url,
          col: datacol,
          layout: "admin/layout_admin",
        });
      }
    }),
  );

admin
  .route("/addactor")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    res.render("admin/addactor", {
      user: res.locals.user,
      emailvalue: "",
      signature: "",
      actor: 0,
      email: 0,
      success: 0,
      title: "Ajouter un acteur / agent / une personne",
      message: "",
      layout: "admin/layout_admin",
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const familyname = req.body.familyname;
      const givenname = req.body.givenname;
      const mail = req.body.mail;

      const dataGet = {
        familyname: familyname,
        givenname: givenname,
      };

      const actor = await req.fetchApi("/api/actor/"+branchConfig+",0", dataGet);
      if (actor && actor.nb !== "0") {
        res.render("admin/addactor", {
          emailvalue: "",
          signature: req.body.signature,
          actor: 1,
          email: 0,
          success: 0,
          title: req.url,
          titleBis: "Ajouter un acteur / agent",
          message: "",
          layout: "admin/layout_admin",
        });
      } else {
        if (mail !== "") {
          if (!validateEmail(mail)) {
            res.render("admin/addactor", {
              emailvalue: "",
              signature: req.body.signature,
              actor: 0,
              email: 1,
              success: 0,
              title: req.url,
              titleBis: "Ajouter un acteur / agent",
              message: "",
              layout: "admin/layout_admin",
            });
          }
        }
        // TODO : vérifier les formats des ORCID et VIAF valid
        const dataPut = {
          email: req.body.mail,
          signature: req.body.signature,
          familyname: req.body.familyname,
          givenname: req.body.givenname,
          title: req.body.title,
          ORCID: req.body.orcid,
          VIAF: req.body.viaf,
        };

        // Create temp_user
        const [_, code] = await req.fetchApiCode("/api/actor/"+branchConfig, dataPut, "PUT");
        if (code === 201) {
          res.render("admin/addactor", {
            emailvalue: req.body.mail,
            signature: req.body.signature,
            actor: 0,
            email: 0,
            success: 1,
            title: req.url,
            titleBis: "Ajouter un acteur / agent",
            message: "",
            layout: "admin/layout_admin",
          });
        }
      }
    }),
  );

admin
  .route("/addorganization")
  .get((req, res) => {
    res.locals.branch = branchConfig;
    res.render("admin/addorganization", {
      user: res.locals.user,
      emailvalue: "",
      organization: 0,
      email: 0,
      success: 0,
      title: req.url,
      titleBis: "Ajouter une organisation",
      message: "",
      layout: "admin/layout_admin",
    });
  })
  .post(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig;
      const name = req.body.name;
      const country = req.body.country;
      const mail = req.body.mail;
      const dataGet = {
        name: name,
        country: country,
      };

      const organization = await req.fetchApi("/api/organization/"+branchConfig+",0", dataGet);
      if (organization && organization.nb !== "0") {
        res.render("admin/addorganization", {
          user: res.locals.user,
          emailvalue: "",
          organization: 1,
          email: 0,
          success: 0,
          title: req.url,
          titleBis: "Ajouter une organisation",
          message: "",
          layout: "admin/layout_admin",
        });
      } else {
        if (mail !== "") {
          if (!validateEmail(mail)) {
            res.render("admin/addorganization", {
              emailvalue: "",
              organization: 0,
              email: 1,
              success: 0,
              title: req.url,
              titleBis: "Ajouter une organisation",
              message: "",
              layout: "admin/layout_admin",
            });
          }
        }

        // TODO : vérifier les formats VIAF valid ??
        const dataPut = {
          email: req.body.mail,
          name: req.body.name,
          country: req.body.country,
          VIAF: req.body.viaf,
          building_name: req.body.building_name,
          number_in_road: req.body.number_in_road,
          road_name: req.body.road_name,
          city: req.body.city,
          zipcode: req.body.zipcode,
          locality: req.body.locality,
          admin_area: req.body.admin_area,
        };

        // Create temp_user
        const [_, code] = await req.fetchApiCode("/api/organization/"+branchConfig, dataPut, "PUT");
        if (code === 201) {
          res.render("admin/addorganization", {
            emailvalue: req.body.mail,
            organization: 0,
            email: 0,
            success: 1,
            title: req.url,
            titleBis: "Ajouter une organisation",
            message: "",
            layout: "admin/layout_admin",
          });
        }
      }
    }),
  );

admin.route("/doiToRunXmlGeneration/:idFolder").get(
  asyncHandler(async (req, res) => {
    res.locals.branch = branchConfig;
    let gene_sh = "#.bashrc\n\n";

    // On supprime le fichier makemepretty.sh et on le recrée avec la première ligne
    const makemeprettyOrig = "#.bashrc\n\n";

    // On récupère tous les fichiers d'un répertoire
    const data = await req.fetchApi(`/api/DOIListId/${req.params.idFolder}`);
    for (const info of data) {
      gene_sh += `/usr/bin/wget -qO- ${globals.wgetDOI}xmlForDOI/${info.info_id_file.id_file} > ${globals.logPath}log_doi_file_${info.info_id_file.id_file}_${info.info_id_file.id_folder}.log 2>&1\n`;
    }

    fs.writeFileSync(`${globals.outilsPath}gene.sh`, gene_sh);
    fs.writeFileSync(`${globals.outilsPath}makemepretty.sh`, makemeprettyOrig);

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify([data.length]));
  }),
);

export const getAddMetadataModel = asyncHandler(
  async (req: Request<unknown, unknown, unknown, { branch?: Branch; type?: "folder" | "object" | "file" }>, res) => {
    const { query } = req;
    const { branch, type } = query;

    const user = res.locals.user as CustomUser;
    res.locals.branch = branchConfig;

    const branches = await archeogrid_client.archeogrid.getBranches.query({
      language: res.locals.lang,
    });
    const requireParamsSelection = !branch || !type;
    let selectableProjects: {
      id: number;
      name: string;
      folder_name: string;
      path?: string;
    }[] = [];
    if (branch) {
      if (user.user_status === "admin") {
        selectableProjects = await archeogrid_client.projects.projectsRootList.query({
          branch,
          user_id: user.id,
        });
      } else if (user.user_status === "scribe") {
        selectableProjects = await archeogrid_client.users.assignedProjects.query({
          branch,
          user_id: user.id,
        });
      }
    }
    const layout = user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

    res.render("admin/addMetadataModel", {
      branches,
      requireParamsSelection,
      selectableProjects,
      layout,
    });
  },
);

export const postAddMetadataModel = asyncHandler(
  async (
    req: Request<
      unknown,
      unknown,
      {
        branch: Branch;
        type: "folder" | "object" | "file";
        name: string;
        desc: string;
        visible: string;
        projects: "all" | "select";
        selection?: string[] | string;
      }
    >,
    res,
  ) => {
    const language = res.locals.lang;
    const { branch, type, name, desc } = req.body;
    const visible = Number(req.body.visible);
    let projects: number[] = [];
    if (req.body.projects === "select") {
      projects =
        typeof req.body.selection === "string"
          ? [Number.parseInt(req.body.selection)]
          : req.body.selection?.map(Number) ?? [];
    }

    const model = await archeogrid_client.metadata.createMetadataModel.mutate({
      branch,
      language,
      name,
      type,
      desc,
      visible,
      projects,
    });

    res.redirect(`/admin/editMetadataModel/${branch}/${model.id}`);
  },
);

export const getEditMetadataModel = asyncHandler(async (req, res) => {
  const user = res.locals.user as CustomUser;
  const language = res.locals.lang;
  res.locals.branch = branchConfig;
  const branches = await archeogrid_client.archeogrid.getBranches.query({
    language: res.locals.lang,
  });

  const layout = user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

  let pft3d_models = [];
  let conservatoire3d_models = [];
  let corpus_models = [];

  if (user.user_status === "admin") {
    pft3d_models = await archeogrid_client.metadata.findAll.query({
      branch: 'pft3d',
      language,
    });
    conservatoire3d_models = await archeogrid_client.metadata.findAll.query({
      branch: "conservatoire3d",
      language,
    });
    corpus_models = await archeogrid_client.metadata.findAll.query({
      branch: "corpus",
      language,
    });
  } else {
    pft3d_models = await archeogrid_client.metadata.userModels.query({
      branch: 'pft3d',
      user_id: user.id,
    });
    conservatoire3d_models = await archeogrid_client.metadata.userModels.query({
      branch: "conservatoire3d",
      user_id: user.id,
    });
    corpus_models = await archeogrid_client.metadata.userModels.query({
      branch: "corpus",
      user_id: user.id,
    });
  }

  res.render("admin/editMetadataModel", {
    selected_branch: branchConfig,
    branches,
    pft3d_models,
    conservatoire3d_models,
    corpus_models,
    layout,
  });
});

export const postEditMetadataModel: RequestHandler = (req, res) => {
  const { branch } = req.body;
  let model_id = NaN;
  switch (branch) {
    case 'pft3d':
      model_id = Number.parseInt(req.body.pft3d_model);
      break;
    case 'conservatoire3d':
      model_id = Number.parseInt(req.body.cnd3d_model);
      break;
    case 'corpus':
      model_id = Number.parseInt(req.body.corpus_model);
      break;
  }

  res.redirect(`/admin/editMetadataModel/${branch}/${model_id}`);
};

export const getEditMetadataModelSingle = asyncHandler(async (req: Request<{ branch: Branch; model: string }>, res) => {
  const { branch } = req.params;
  const model_id = Number.parseInt(req.params.model);
  const language = res.locals.lang;
  res.locals.branch = branchConfig;

  const user = res.locals.user as CustomUser;
  const layout = user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

  const metadata_model = await archeogrid_client.metadata.findOne.query({
    branch,
    language,
    model_id,
  });

  const delete_allowed = await archeogrid_client.metadata.isDeletionPossible.query({
    branch,
    model_id,
  });

  const metadata = await archeogrid_client.metadata.getMetadata.query({
    branch,
    language,
    model: metadata_model.name,
  }) ?? {};

  function cleanString(str: string) {
    return str
      .replace('\n', " ")
      .replace('\r', " ")
      .replace(/\s+/g, " ");
  }

  for (const [idx, m] of Object.entries(metadata)) {
    m.description = cleanString(m.description);
  }

  const metadata_list = (await archeogrid_client.metadata.getMetadataList.query({ branch })).reverse();
  const list_values = metadata_list.filter((l) => l.id_metadata_model === metadata_model.id);

  const metadata_list_values = new Map<number, string[]>();

  for (const value of list_values) {
    const array = metadata_list_values.get(value.id_list);
    if (array) {
      metadata_list_values.set(value.id_list, [value.name, ...array]);
    } else {
      metadata_list_values.set(value.id_list, [value.name]);
    }
  }

  const metadata_types = globals.metadata_types.sort();

  const map_subthesaurus: { [key: string]: string[] } = {
    thesaurus: [],
    multi: [],
    pactols: []
  };
  for(let key in map_subthesaurus){
    let subT = await archeogrid_client.thesaurus.getSubThesaurus.query({branch, thesaurus: key});
    map_subthesaurus[key] = subT;
  }

  const map_thesaurus_pactols_collections = await archeogrid_client.thesaurus.getThesaurusPactolsCollectionNames.query({branch});

  res.render("admin/addMetadataModelDetails", {
    branch,
    metadata_model,
    delete_allowed,
    metadata,
    metadata_list_values,
    layout,
    metadata_types,
    map_subthesaurus,
    map_thesaurus_pactols_collections
  });
});

export const postEditMetadataModelSingle = asyncHandler(
  async (req: Request<{ branch: Branch; model: string }>, res) => {
    const keys = new Set(Object.keys(req.body).map((key) => Number(key.slice(0, key.indexOf("_")))));
    const metadata_count = keys.size;
    const language = res.locals.lang;
    const branch = req.params.branch;
    const model_id = Number.parseInt(req.params.model);
    res.locals.branch = branchConfig;

    type Metadata = {
      id?: number;
      label: string;
      description: string;
      type: MetadataType;
      rank: number;
      x: number;
      mandatory: boolean;
      unique: boolean;
      optional: boolean;
      min?: number;
      max?: number;
      list?: string;
      choices: string[];
      query?: string;
      function?: string;
    };

    const values: Metadata[] = [];

    console.log(req.body)

    const keys_as_array = Array.from(keys);
    for (const i of keys) {
      const real_rank = keys_as_array.indexOf(i) + 1;
      let choices = req.body[`${i}_choices`] ?? [];
      if (typeof choices === "string") choices = [choices];
      choices = choices.filter(Boolean);
      const element: Metadata = {
        id: Number.parseInt(req.body[`${i}_id`]),
        label: req.body[`${i}_label`],
        description: req.body[`${i}_description`],
        type: req.body[`${i}_type`],
        rank: real_rank,
        x: Number.parseInt(req.body[`${i}_x`]) || 1,
        mandatory: req.body[`${i}_Mandatory`] === "on",
        unique: req.body[`${i}_Unique`] === "on",
        optional: req.body[`${i}_Optional`] === "on",
        query: req.body[`${i}_query`] || 'y',
        function: req.body[`${i}_function`],
        choices,
      };

      if (element.type === "int") {
        element.min = Number.parseInt(req.body[`${i}_min`]);
        element.max = Number.parseInt(req.body[`${i}_max`]);
      }

      // if status is list and data is unique, status is "choice"
      if (element.type === "list" && element.unique) {
        element.type = "choice";
      }

      if(element.type === 'thesaurus' || element.type === 'pactols' || element.type === 'multi'){
        element.list = req.body[`${i}_thesaurus`];
      }

      values.push(element);
    }

    // result = a status and possibly an error message
    const result = await archeogrid_client.metadata.patchModel.mutate({ branch, language, model_id, values });
    res.send({values, ...result});
  },
);

export const deleteMetadataModel = asyncHandler(async (req: Request<{ branch: Branch; model: string }>, res) => {
  const { branch } = req.params;
  const model_id = Number.parseInt(req.params.model);
  res.locals.branch = branchConfig;

  await archeogrid_client.metadata.deleteModel.mutate({ branch, model_id });

  res.sendStatus(202);
});

export const getThesaurusLinkForm = asyncHandler(async (req, res) => {
  const user = res.locals.user as CustomUser;
  const language = res.locals.lang;
  res.locals.branch = branchConfig;
  const branches = await archeogrid_client.archeogrid.getBranches.query({
    language: res.locals.lang,
  });

  const layout = user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

  type Projects = { id: number; name: string; folder_name: string; path?: string }[];
  const projects = {
    pft3d: [] as Projects,
    corpus: [] as Projects,
  };

  if (user.user_status === "admin") {
    projects.pft3d = await archeogrid_client.projects.projectsRootList.query({
      branch: branchConfig,
      user_id: user.id,
    });
    projects.corpus = await archeogrid_client.projects.projectsRootList.query({
      branch: branchConfig,
      user_id: user.id,
    });
  } else if (user.user_status === "scribe") {
    projects.pft3d = await archeogrid_client.users.assignedProjects.query({
      branch: branchConfig,
      user_id: user.id,
    });
    projects.corpus = await archeogrid_client.users.assignedProjects.query({
      branch: branchConfig,
      user_id: user.id,
    });
  }

  res.render("admin/thesaurusLinkForm", { branches, projects, layout });
});

export const getThesaurusLink = asyncHandler(
  async (
    req: Request<unknown, unknown, unknown, { branch: Branch; project: string; type: "simple" | "multi" }>,
    res,
  ) => {
    const { query } = req;
    const { branch, project, type } = query;

    const language = res.locals.lang;
    const project_id = Number.parseInt(project);
    res.locals.branch = branchConfig;

    const user = req.user as CustomUser;
    const layout = user.user_status === "admin" ? "admin/layout_admin" : "admin/layout_dashboard";

    const unique = <T extends Record<string, unknown> & { id: number }>(array: T[]) => {
      const unique_keys = new Set(array.map((el) => el.id));
      const filtered_array: T[] = [];
      for (const unique_key of unique_keys) {
        const unique_element = array.find((el) => el.id === unique_key);
        if (!unique_element) throw new Error(); // impossible?
        filtered_array.push(unique_element);
      }
      return filtered_array;
    };

    const linked_thesaurus =
      type === "simple"
        ? unique(await archeogrid_client.thesaurus.simpleOrigin.query({ branch, project_id }))
        : await archeogrid_client.thesaurus.multiOrigin.query({ branch, language, project_id });

    const thesaurus =
      type === "simple"
        ? await archeogrid_client.thesaurus.listSimple.query({ branch, language })
        : await archeogrid_client.thesaurus.listMulti.query({ branch, language });

    const project_full = await archeogrid_client.projects.projectsFull.query({
      branch,
      project_id,
    });
    if (!project_full) throw new Error("Project not found");
    const project_name = project_full.data.name;

    res.render("admin/thesaurusLink", { project_name, linked_thesaurus, thesaurus, layout });
  },
);

export const createThesaurusLink = asyncHandler(
  async (
    req: Request<unknown, unknown, { branch: Branch; project: string; type: "simple" | "multi"; thesaurus: string }>,
    res,
  ) => {
    const { branch, type, thesaurus } = req.body;
    const project_id = Number.parseInt(req.body.project);
    res.locals.branch = branchConfig;
    await archeogrid_client.thesaurus.linkProject.mutate({
      branch,
      type,
      project_id,
      thesaurus,
    });
    res.sendStatus(200);
  },
);

export const deleteThesaurusLink = asyncHandler(
  async (
    req: Request<unknown, unknown, { branch: Branch; project: string; type: "simple" | "multi"; thesaurus: string }>,
    res,
  ) => {
    const { branch, type, thesaurus } = req.body;
    const project_id = Number.parseInt(req.body.project);
    res.locals.branch = branchConfig;
    await archeogrid_client.thesaurus.unlinkProject.mutate({
      branch,
      type,
      project_id,
      thesaurus,
    });
    res.sendStatus(200);
  },
);

export const getUserHistoryForm = asyncHandler(async (req, res) => {
  const user = req.user as CustomUser;
  const user_id = user.id;
  const is_admin = user.user_status === "admin";
  res.locals.branch = branchConfig;
  const layout = is_admin ? "admin/layout_admin" : "admin/layout_dashboard";

  const accessible_users = await archeogrid_client.users.findWithModifications.query({
    user_id,
  });

  res.render("admin/userHistoryForm", { accessible_users, layout });
});

export const getUserHistory = asyncHandler(
  async (
    req: Request<
      unknown,
      unknown,
      unknown,
      { user: string; type: "item" | "thesaurus"; page: string; page_size: string }
    >,
    res,
  ) => {
    const user = req.user as CustomUser;
    const is_admin = user.user_status === "admin";
    res.locals.branch = branchConfig;
    const layout = is_admin ? "admin/layout_admin" : "admin/layout_dashboard";

    const user_id = Number.parseInt(req.query.user);
    const page = Number.parseInt(req.query.page) || 1;
    const page_size = Number.parseInt(req.query.page_size) || 100;
    const type = req.query.type;

    const selected_user = await archeogrid_client.users.findOne.query({ user_id });

    const history = await archeogrid_client.users.history.query({
      user_id,
      type,
      page,
      page_size,
    });
    const page_count = Math.ceil((await archeogrid_client.users.historyLength.query({ type, user_id })) / page_size);

    res.render("admin/userHistory", { selected_user, history, page_count, layout });
  },
);

admin.route("/addMetadataModel").get(getAddMetadataModel).post(postAddMetadataModel);

admin.route("/editMetadataModel").get(getEditMetadataModel).post(postEditMetadataModel);

admin.route("/editMetadataModel/:branch/:model").get(getEditMetadataModelSingle).post(postEditMetadataModelSingle);

admin.delete("/deleteMetadataModel/:branch/:model", deleteMetadataModel);

admin.route("/thesaurusLinkSelection").get(getThesaurusLinkForm);

admin.route("/thesaurusLink").get(getThesaurusLink).post(createThesaurusLink).delete(deleteThesaurusLink);

admin.get("/userHistoryForm", getUserHistoryForm);
admin.get("/userHistory", getUserHistory);

export default admin;
