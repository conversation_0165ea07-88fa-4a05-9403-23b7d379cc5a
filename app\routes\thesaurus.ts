import { Router, type Request } from "express";
import { default as async<PERSON><PERSON><PERSON> } from "express-async-handler";

import type { Branch } from "../../server/types/api";
import { archeogrid_client } from "../tools/request";

import * as helpers from "../tools/helpers_tools";
import {
    branchConfig,
    paginer,
} from "../tools/globals";
const thesaurus = Router();

/**
 *
 *  _____ _
 * |_   _| |__   ___  ___  __ _ _   _ _ __ _   _ ___
 *  | | | '_ \ / _ \/ __|/ _` | | | | '__| | | / __|
 *  | | | | | |  __/\__ \ (_| | |_| | |  | |_| \__ \
 *  |_| |_| |_|\___||___/\__,_|\__,_|_|   \__,_|___/
 *
 *
 *
 */

thesaurus.get(
  "/Thesaurus/:branche,:projectId",
  asyncHandler(async (req: Request<{ branche: Branch; projectId: string }>, res) => {
    if (req.session)
      req.session.root = `thesaurus/${req.params.branche},${req.params.projectId}`;
    const branch = req.params.branche;
    const project_id = Number.parseInt(req.params.projectId);
    const language = res.locals.lang;

    const project = await archeogrid_client.projects.projectsFull.query({
      branch: branch,
      language: res.locals.lang,
      project_id: Number.parseInt(req.params.projectId),
    });

    const thesaurus = await archeogrid_client.thesaurus.multiOrigin.query({
      branch,
      language,
      project_id,
    });

    const new_array = thesaurus; // pour supprimer l'info label qui remonte plusieurs fois le même thesaurus
    for (const array_item of new_array) {
      for (const i in array_item) {
        if (i === "label") {
          delete array_item[i];
        }
      }
    }
    // on dédoublonne le tableau d'objets
    // probleme  avec les corpus et id thes qui sont tous identique à 0
    let  thesaurusUnique = []
    if (branch === 'pft3d' || branch === 'corpus') {
        thesaurusUnique = helpers.getuniqueObjectArray(new_array);
    } else // corpus
        thesaurusUnique = new_array

    const thesaurusSimple = await archeogrid_client.thesaurus.originFolder.query({
      branch,
      language,
      folder_id: project_id,
    });

    let projectName = "";
    let info = "no";

    if (thesaurus.length) {
      info = "yes";
      projectName = thesaurus[0].project_name ?? "";
    }
    const infothesaurus = thesaurusSimple.length ? "yes" : "no";

    const view_data = {
      root: branch,
      branch: branch,
      projectId: project_id,
      project: project,
      projectName,
      thesaurus: thesaurusUnique, // thesaurus multi dédoublonné (au cas où indexation sur plusieurs champs, il n'apparait qu'une seule fois
      thesaurusMulti: thesaurus, // ancienne version du thesaurus multi non dédoublonné, cas projet Karnak avec 2 indexation geo => apparait en double
      thesaurusSimple,
      info,
      infothesaurus,
    };

    res.render("thesaurus", view_data);
  }),
);

thesaurus.get(
  "/thesaurusP/:branche,:projectId",
  asyncHandler(async (req: Request<{ branche: Branch; projectId: string }>, res) => {
    req.session.root = `thesaurusP/${req.params.branche},${req.params.projectId}`;

    const user = req.user ?? { id: 0 };

    const mainfolder = await req.fetchApi(
      `/api/mainFolder/${req.params.branche},${req.params.projectId},${res.locals.lang}`,
    );
    const data = await req.fetchApi(
      `/api/thesaurusPactolsOrigin/${req.params.branche},${req.params.projectId}`,
      {
        lng: res.locals.lang,
      },
    );

    for (let index = 0; index < data.length; index++) {
      const tree = await req.fetchApi(
        `/api/thesaurusTreePactols/${req.params.branche},sujet,${data[index].id}`,
        {
          lng: res.locals.lang,
        },
      );
      if (index === data.length - 1) {
        // on n'affiche que les level 1 qui ont des éléments taggé
        res.render("thesaurusp", {
          user,
          root: req.params.branche,
          branch: req.params.branche,
          projectId: req.params.projectId,
          projectName: mainfolder.folder_name,
          thesaurus: data,
          tree,
        });
      }
    }
  }),
);

thesaurus.get(
  "/thesaurusPactols/:branche,:projectId",
  asyncHandler(async (req: Request<{ branche: Branch; projectId: string }>, res) => {
    if (req.session)
      req.session.root = `thesaurusP/${req.params.branche},${req.params.projectId}`;

    const branch = req.params.branche;
    const project_id = Number.parseInt(req.params.projectId);
    const language = res.locals.lang;

    const thesaurus = await archeogrid_client.thesaurus.pactolsOrigin.query({
      branch,
      language,
      project_id,
    });

    res.render("thesauruspactolsAll", {
      root: branch,
      branch: req.params.branche,
      projectId: project_id,
      thesaurus,
    });
  }),
);

thesaurus.get(
  "/thesaurusPactolsOne/:branche,:projectId,:idThes",
  asyncHandler(
    async (req: Request<{ branche: Branch; projectId: string; idThes: string }>, res) => {
      if (req.session)
        req.session.root = `thesaurusP/${req.params.branche},${req.params.projectId}`;
      const branch = req.params.branche;
      const project_id = Number.parseInt(req.params.projectId);
      const thes_id = Number.parseInt(req.params.idThes);
      const language = res.locals.lang;

      const mainFolder = await archeogrid_client.folders.mainFolder.query({
        branch,
        language,
        folder_id: project_id,
      });

      const data = await archeogrid_client.thesaurus.pactolsOrigin.query({
        branch,
        project_id,
      });
      const thesaurus = data.find((d) => d.id === thes_id);

      if (!thesaurus) throw new Error("Thesaurus not found!");

      const tree = await archeogrid_client.thesaurus.getTreePactols.query({
        branch,
        language,
        thesaurus: "sujet",
        thesaurus_path: thesaurus.id.toString(),
      });

      res.render("thesauruspactolsOne", {
        branch: req.params.branche,
        idThes: req.params.idThes,
        root: req.params.branche,
        projectId: req.params.projectId,
        projectName: mainFolder?.folder_name,
        thesaurus,
        tree,
      });
    },
  ),
);

// VITRINE ROUTES - New consolidated thesaurus vitrine routes

thesaurus.get(
  "/thesaurusV/:branche,:projectId",
  asyncHandler(async (req: Request<{ branche: Branch; projectId: string }>, res) => {
    if (req.session)
      req.session.root = `thesaurusV/${req.params.branche},${req.params.projectId}`;
    const branch = req.params.branche;
    const project_id = Number.parseInt(req.params.projectId);
    const language = res.locals.lang;

    // Set branch for vitrine compatibility
    res.locals.branch = branch;

    const project = await archeogrid_client.projects.projectsFull.query({
      branch: branch,
      language: res.locals.lang,
      project_id: Number.parseInt(req.params.projectId),
    });

    const thesaurus = await archeogrid_client.thesaurus.multiOrigin.query({
      branch,
      language,
      project_id,
    });

    const new_array = thesaurus;
    for (const array_item of new_array) {
      for (const i in array_item) {
        if (i === "label") {
          delete array_item[i];
        }
      }
    }

    let thesaurusUnique = []
    if (branch === 'pft3d' || branch === 'corpus') {
        thesaurusUnique = helpers.getuniqueObjectArray(new_array);
    } else {
        thesaurusUnique = new_array
    }

    const thesaurusSimple = await archeogrid_client.thesaurus.originFolder.query({
      branch,
      language,
      folder_id: project_id,
    });

    let projectName = "";
    let info = "no";

    if (thesaurus.length) {
      info = "yes";
      projectName = thesaurus[0].project_name ?? "";
    }
    const infothesaurus = thesaurusSimple.length ? "yes" : "no";

    const view_data = {
      root: branch,
      branch: branch,
      projectId: project_id,
      project: project,
      projectName,
      thesaurus: thesaurusUnique,
      thesaurusMulti: thesaurus,
      thesaurusSimple,
      info,
      infothesaurus,
      lng: res.locals.lang,
      user: req.user || { user_status: 'guest', read: [], write: [] },
      layout: 'layout'
    };

    res.render("thesaurusVitrine", view_data);
  }),
);

thesaurus.get(
  "/thesaurusPactolsV/:branche,:projectId",
  asyncHandler(async (req: Request<{ branche: Branch; projectId: string }>, res) => {
    if (req.session)
      req.session.root = `thesaurusPactolsV/${req.params.branche},${req.params.projectId}`;

    const branch = req.params.branche;
    const project_id = Number.parseInt(req.params.projectId);
    const language = res.locals.lang;

    // Set branch for vitrine compatibility
    res.locals.branch = branch;

    const project = await archeogrid_client.projects.projectsFull.query({
      branch,
      language,
      project_id,
    });

    const thesaurus = await archeogrid_client.thesaurus.pactolsOrigin.query({
      branch,
      language,
      project_id,
    });

    res.render("thesaurusPactolsAllVitrine", {
      root: branch,
      branch: req.params.branche,
      projectId: project_id,
      project: project,
      thesaurus,
      lng: res.locals.lang,
      user: req.user || { user_status: 'guest', read: [], write: [] },
      layout: 'layout'
    });
  }),
);

thesaurus.get(
  "/thesaurusPactolsOneV/:branche,:projectId,:idThes",
  asyncHandler(
    async (req: Request<{ branche: Branch; projectId: string; idThes: string }>, res) => {
      if (req.session)
        req.session.root = `thesaurusPactolsOneV/${req.params.branche},${req.params.projectId}`;
      const branch = req.params.branche;
      const project_id = Number.parseInt(req.params.projectId);
      const thes_id = Number.parseInt(req.params.idThes);
      const language = res.locals.lang;

      // Set branch for vitrine compatibility
      res.locals.branch = branch;

      const project = await archeogrid_client.projects.projectsFull.query({
        branch,
        language,
        project_id,
      });

      const mainFolder = await archeogrid_client.folders.mainFolder.query({
        branch,
        language,
        folder_id: project_id,
      });

      const data = await archeogrid_client.thesaurus.pactolsOrigin.query({
        branch,
        project_id,
      });
      const thesaurus = data.find((d) => d.id === thes_id);

      if (!thesaurus) throw new Error("Thesaurus not found!");

      const tree = await archeogrid_client.thesaurus.getTreePactols.query({
        branch,
        language,
        thesaurus: "sujet",
        thesaurus_path: thesaurus.id.toString(),
      });

      res.render("thesaurusPactolsOneVitrine", {
        branch: req.params.branche,
        idThes: req.params.idThes,
        root: req.params.branche,
        projectId: req.params.projectId,
        project: project,
        projectName: mainFolder?.folder_name,
        thesaurus,
        thesaurusId: thes_id,
        tree,
        lng: res.locals.lang,
        user: req.user || { user_status: 'guest', read: [], write: [] },
        layout: 'layout'
      });
    },
  ),
);

thesaurus.get(
  "/getThesaurusTreeData/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;

    const data = await archeogrid_client.thesaurus.getTree.query({
      branch,
      thesaurus,
      thesaurus_path,
    });

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/getThesaurusTreeDataMulti/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;
    const language = res.locals.lang;

    const data = await archeogrid_client.thesaurus.getTreeMulti.query({
      branch,
      thesaurus,
      thesaurus_path,
      language,
    });

    // TODO : construire une vue directement ici et la renvoyer (comme on fait avec les thesaurus par page ???
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/getThesaurusTreeDataPactols/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const data = await req.fetchApi(
      `/api/thesaurusTreePactols/${req.params.root},${req.params.name},${req.params.thes}`,
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

// Vitrine-specific tree data endpoints that include additional metadata for the new component
thesaurus.get(
  "/getThesaurusTreeDataMultiVitrine/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;
    const language = res.locals.lang;

    const data = await archeogrid_client.thesaurus.getTreeMulti.query({
      branch,
      thesaurus,
      thesaurus_path,
      language,
    });

    // Transform data for vitrine component compatibility
    const vitrineData = data.map(item => ({
      ...item,
      // Ensure compatibility with folders tree structure
      folder_name: item.name || (item as any).short_name || `Item ${item.id}`,
      virtual: 0, // Thesaurus items are not virtual folders
      nb_images: item.nb_item || 0, // Map thesaurus items to image count
      nb_tot_images: (item as any).nb_tot_item || item.nb_item || 0, // Map total thesaurus items
      // Keep original thesaurus fields
      thesaurus_path: item.path,
      get_children: item.get_children
    }));

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(vitrineData));
  }),
);

thesaurus.get(
  "/getThesaurusTreeDataVitrine/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;
    const language = res.locals.lang;

    const data = await archeogrid_client.thesaurus.getTree.query({
      branch,
      thesaurus,
      thesaurus_path,
      language,
    });

    // Transform data for vitrine component compatibility
    const vitrineData = data.map(item => ({
      ...item,
      // Ensure compatibility with folders tree structure
      folder_name: item.name || (item as any).short_name || `Item ${item.id}`,
      virtual: 0, // Thesaurus items are not virtual folders
      nb_images: item.nb_item || 0, // Map thesaurus items to image count
      nb_tot_images: (item as any).nb_tot_item || item.nb_item || 0, // Map total thesaurus items
      // Keep original thesaurus fields
      thesaurus_path: item.path,
      get_children: item.get_children
    }));

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(vitrineData));
  }),
);

thesaurus.get(
  "/getThesaurusTreeDataPactolsVitrine/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;
    const language = res.locals.lang;

    const data = await archeogrid_client.thesaurus.getTreePactols.query({
      branch,
      thesaurus,
      thesaurus_path,
      language,
    });

    // Transform data for vitrine component compatibility
    const vitrineData = data.map(item => ({
      ...item,
      // Ensure compatibility with folders tree structure
      folder_name: item.name || (item as any).short_name || `Item ${item.id}`,
      virtual: 0, // Thesaurus items are not virtual folders
      nb_images: item.nb_item || 0, // Map thesaurus items to image count
      nb_tot_images: (item as any).nb_tot_item || item.nb_item || 0, // Map total thesaurus items
      // Keep original thesaurus fields
      thesaurus_path: item.path,
      get_children: item.get_children
    }));

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(vitrineData));
  }),
);

// NEW: Routes that return rendered foldersTreeVitrine HTML instead of JSON data
thesaurus.get(
  "/renderThesaurusTreeVitrineMulti/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;
    const language = res.locals.lang;

    const data = await archeogrid_client.thesaurus.getTreeMulti.query({
      branch,
      thesaurus,
      thesaurus_path,
      language,
    });

    // Transform data for foldersTreeVitrine component
    const tree = data.map(item => ({
      id: item.id,
      name: item.name || `Item ${item.id}`,
      folder_name: item.name,
      nb_images: item.nb_item || 0,
      nb_objects: 0,
      nb_unicos: 0,
      nb_tot_images: (item as any).nb_tot_item || item.nb_item || 0,
      nb_tot_objects: 0,
      nb_tot_unicos: 0,
      nb_tot_items: (item as any).nb_tot_item || item.nb_item || 0,
      virtual: 0,
      id_parent: item.id_parent,
      depth: item.depth,
      global_rank: (item as any).global_rank || 0
    }));

    // Get project data for the component
    const project = await archeogrid_client.projects.projectsFull.query({
      branch,
      language,
      project_id: Number.parseInt(req.query.projectId as string || '0'),
    });

    // Render the foldersTreeVitrine component with thesaurus data
    const renderedHTML = await new Promise<string>((resolve, reject) => {
      res.render('search/foldersTreeVitrine', {
        tree,
        user: req.user || { user_status: 'guest', read: [], write: [] },
        selectedFolder: null,
        isfolder: null,
        lng: language,
        accessProject: true,
        comment: { length: 0 },
        projectId: req.query.projectId || '0',
        project,
        branch,
        thesinfo: 'no',
        thesSimpleinfo: 'no',
        unicosNb: null
      }, (err, html) => {
        if (err) reject(err);
        else resolve(html || '');
      });
    });

    res.setHeader("Content-Type", "text/html");
    res.send(renderedHTML);
  }),
);

thesaurus.get(
  "/renderThesaurusTreeVitrineSimple/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;
    const language = res.locals.lang;

    const data = await archeogrid_client.thesaurus.getTree.query({
      branch,
      thesaurus,
      thesaurus_path,
      language,
    });

    // Transform data for foldersTreeVitrine component
    const tree = data.map(item => ({
      id: item.id,
      name: item.name || `Item ${item.id}`,
      folder_name: item.name,
      nb_images: item.nb_item || 0,
      nb_objects: 0,
      nb_unicos: 0,
      nb_tot_images: (item as any).nb_tot_item || item.nb_item || 0,
      nb_tot_objects: 0,
      nb_tot_unicos: 0,
      nb_tot_items: (item as any).nb_tot_item || item.nb_item || 0,
      virtual: 0,
      id_parent: item.id_parent,
      depth: item.depth,
      global_rank: (item as any).global_rank || 0
    }));

    // Get project data for the component
    const project = await archeogrid_client.projects.projectsFull.query({
      branch,
      language,
      project_id: Number.parseInt(req.query.projectId as string || '0'),
    });

    // Render the foldersTreeVitrine component with thesaurus data
    const renderedHTML = await new Promise<string>((resolve, reject) => {
      res.render('search/foldersTreeVitrine', {
        tree,
        user: req.user || { user_status: 'guest', read: [], write: [] },
        selectedFolder: null,
        isfolder: null,
        lng: language,
        accessProject: true,
        comment: { length: 0 },
        projectId: req.query.projectId || '0',
        project,
        branch,
        thesinfo: 'no',
        thesSimpleinfo: 'no',
        unicosNb: null
      }, (err, html) => {
        if (err) reject(err);
        else resolve(html || '');
      });
    });

    res.setHeader("Content-Type", "text/html");
    res.send(renderedHTML);
  }),
);

thesaurus.get(
  "/renderThesaurusTreeVitrinePactols/:root,:name,:thes",
  asyncHandler(async (req: Request<{ root: Branch; name: string; thes: string }>, res) => {
    const branch = req.params.root;
    const thesaurus = req.params.name;
    const thesaurus_path = req.params.thes;
    const language = res.locals.lang;

    const data = await archeogrid_client.thesaurus.getTreePactols.query({
      branch,
      thesaurus,
      thesaurus_path,
      language,
    });

    // Transform data for foldersTreeVitrine component
    const tree = data.map(item => ({
      id: item.id,
      name: item.name || `Item ${item.id}`,
      folder_name: item.name,
      nb_images: item.nb_item || 0,
      nb_objects: 0,
      nb_unicos: 0,
      nb_tot_images: (item as any).nb_tot_item || item.nb_item || 0,
      nb_tot_objects: 0,
      nb_tot_unicos: 0,
      nb_tot_items: (item as any).nb_tot_item || item.nb_item || 0,
      virtual: 0,
      id_parent: item.id_parent,
      depth: item.depth,
      global_rank: (item as any).global_rank || 0
    }));

    // Get project data for the component
    const project = await archeogrid_client.projects.projectsFull.query({
      branch,
      language,
      project_id: Number.parseInt(req.query.projectId as string || '0'),
    });

    // Render the foldersTreeVitrine component with thesaurus data
    const renderedHTML = await new Promise<string>((resolve, reject) => {
      res.render('search/foldersTreeVitrine', {
        tree,
        user: req.user || { user_status: 'guest', read: [], write: [] },
        selectedFolder: null,
        isfolder: null,
        lng: language,
        accessProject: true,
        comment: { length: 0 },
        projectId: req.query.projectId || '0',
        project,
        branch,
        thesinfo: 'no',
        thesSimpleinfo: 'no',
        unicosNb: null
      }, (err, html) => {
        if (err) reject(err);
        else resolve(html || '');
      });
    });

    res.setHeader("Content-Type", "text/html");
    res.send(renderedHTML);
  }),
);

thesaurus.get(
  "/thesaurusPactols/:root,:thesaurus",
  asyncHandler(async (req: Request<{ root: Branch; thesaurus: string }>, res) => {
    const data = await req.fetchApi(
      `/api/thesaurusPactols/${req.params.root},${req.params.thesaurus}`,
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/thesaurusPactolsName/:root,:thesaurus,:name",
  asyncHandler(async (req: Request<{ root: Branch; thesaurus: string; name: string }>, res) => {
    const data = await req.fetchApi(
      `/api/thesaurusPactolsName/${req.params.root},${req.params.thesaurus},${encodeURI(req.params.name)}`,
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/thesaurusPactolsName/:thesaurus,:name",
  asyncHandler(async (req: Request<{ thesaurus: string; name: string }>, res) => {
    // PACTOLS V2 : le thesaurus est toujours sujet
    const data = await req.fetchApi(
      `/api/ThesaurusPactolsName/${branchConfig},sujet,${encodeURI(req.params.name)}`,
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);
thesaurus.get(
  "/thesaurusPactolsNameFilter/:thesaurus,:filter,:name",
  asyncHandler(
    async (
      req: Request<{ thesaurus: string; filter: string; name: string }>,
      res,
    ) => {
      // PACTOLS V2 : le thesaurus est toujours sujet
      const data = await req.fetchApi(
        `/api/ThesaurusPactolsNameFilter/${branchConfig},${req.params.thesaurus},${
          req.params.filter
        },${encodeURI(req.params.name)}`,
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data));
    },
  ),
);

thesaurus.get(
  "/thesaurusPactolsGeoName/:thesaurus,:name",
  asyncHandler(async (req: Request<{ thesaurus: string; name: string }>, res) => {
    const data = await req.fetchApi(
      `/api/ThesaurusPactolsGeoName/${branchConfig},${encodeURI(req.params.name)}`,
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/thesaurusSimpleName/:thesaurus,:name",
  asyncHandler(async (req: Request<{ thesaurus: string; name: string }>, res) => {
    const data = await req.fetchApi(
      `/api/ThesaurusSimpleName/${branchConfig},${req.params.thesaurus},${
        res.locals.lang
      },${encodeURIComponent(req.params.name)}`,
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/thesaurusMultiName/:thesaurus,:name",
  asyncHandler(async (req: Request<{ thesaurus: string; name: string }>, res) => {
    const data = await req.fetchApi(
      `/api/ThesaurusMultiName/${branchConfig},${req.params.thesaurus},${encodeURI(
        req.params.name,
      )}`,
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/thesaurusMultiNameGeneral/:root,:projectId,:name",
  asyncHandler(async (req: Request<{ root: Branch; projectId: string; name: string }>, res) => {
    const data = await req.fetchApi(
      `/api/ThesaurusMultiNameGeneral/${req.params.root},${encodeURI(req.params.name)}`,
      { projectId: req.params.projectId },
    );
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.get(
  "/deleteThesaurusPactolsItem/:root,:idItem,:item_type,:id_thesaurus,:id_thes_thesaurus,:thesaurus",
  asyncHandler(
    async (
      req: Request<{
        root: Branch;
        idItem: string;
        item_type: string;
        id_thesaurus: string;
        id_thes_thesaurus: string;
        thesaurus: string;
      }>,
      res,
    ) => {
      const datadelete = {
        id_thesaurus: req.params.id_thesaurus,
        id_thes_thesaurus: req.params.id_thes_thesaurus,
        thesaurus: req.params.thesaurus,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/ThesaurusPactolsItem/${req.params.root},${req.params.item_type},${req.params.idItem}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);

thesaurus.get(
  "/deleteThesaurusPactolsGeoItem/:root,:idItem,:item_type,:id_thesaurus",
  asyncHandler(
    async (
      req: Request<{ root: Branch; idItem: string; item_type: string; id_thesaurus: string }>,
      res,
    ) => {
      const datadelete = {
        id_thesaurus: req.params.id_thesaurus,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/ThesaurusPactolsGeoItem/${req.params.root},${req.params.item_type},${req.params.idItem}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);

thesaurus.get(
  "/deleteThesaurusMultiConcept/:idThes,:thesaurus,:idParent,:thesPath",
  asyncHandler(
    async (
      req: Request<{
        idThes: string;
        thesaurus: string;
        idParent: string;
        thesPath: string;
      }>,
      res,
    ) => {
      const datadelete = {
        id_parent: req.params.idParent,
        path: req.params.thesPath,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/ThesaurusMultiConcept/${branchConfig},${req.params.thesaurus},${req.params.idThes}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);

thesaurus.get(
  "/deleteThesaurusMultiItem/:root,:idItem,:itemType,:thes_path,:thesaurus",
  asyncHandler(
    async (
      req: Request<{
        root: Branch;
        idItem: string;
        itemType: string;
        thes_path: string;
        thesaurus: string;
      }>,
      res,
    ) => {
      const datadelete = {
        thes_path: req.params.thes_path,
        thesaurus: req.params.thesaurus,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/ThesaurusMultiItem/${req.params.root},${req.params.itemType},${req.params.idItem}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);

thesaurus.get(
  "/deleteThesaurusMultiItemQualifier/:root,:idItem,:itemType,:thes_path,:thesaurus,:qualifier",
  asyncHandler(
    async (
      req: Request<{
        root: Branch;
        idItem: string;
        itemType: string;
        thes_path: string;
        thesaurus: string;
        qualifier: string;
      }>,
      res,
    ) => {
      const datadelete = {
        thes_path: req.params.thes_path,
        thesaurus: req.params.thesaurus,
        qualifier: req.params.qualifier,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/ThesaurusMultiItemQualifier/${req.params.root},${req.params.itemType},${req.params.idItem}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);

thesaurus.get(
  "/deleteThesaurusItem/:root,:idItem,:item_type,:thes_path,:thesaurus",
  asyncHandler(
    async (
      req: Request<{
        root: Branch;
        idItem: string;
        item_type: string;
        thes_path: string;
        thesaurus: string;
      }>,
      res,
    ) => {
      const datadelete = {
        thes_path: req.params.thes_path,
        thesaurus: req.params.thesaurus,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/ThesaurusMaisonItem/${req.params.root},${req.params.item_type},${req.params.idItem}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);
thesaurus.get(
    "/deleteThesaurusItemQualifier/:root,:idItem,:itemType,:id_thes,:thesaurus,:qualifier",
    asyncHandler(
        async (
            req: Request<{
                root: Branch;
                idItem: string;
                itemType: string;
                id_thes: string;
                thesaurus: string;
                qualifier: string;
            }>,
            res,
        ) => {
            const datadelete = {
                id_thes: req.params.id_thes,
                thesaurus: req.params.thesaurus,
                qualifier: req.params.qualifier,
            };
            const [_, code] = await req.fetchApiCode(
                `/api/ThesaurusItemQualifier/${req.params.root},${req.params.itemType},${req.params.idItem}`,
                datadelete,
                "DELETE",
            );
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(code));
        },
    ),
);
thesaurus.get(
  "/deleteTagItem/:root,:idItem,:item_type,:idTag",
  asyncHandler(
    async (
      req: Request<{ root: Branch; idItem: string; item_type: string; idTag: string }>,
      res,
    ) => {
      const datadelete = {
        id_tag: req.params.idTag,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/tag/${req.params.root},${req.params.item_type},${req.params.idItem}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);

thesaurus.get(
  "/deleteSiteProject/:idOverallProject,:idSiteProject",
  asyncHandler(
    async (req: Request<{ idOverallProject: string; idSiteProject: string }>, res) => {
      const datadelete = {
        idSite: req.params.idSiteProject,
      };
      const [_, code] = await req.fetchApiCode(
        `/api/overallProject/${branchConfig},${req.params.idOverallProject}`,
        datadelete,
        "DELETE",
      );
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(code));
    },
  ),
);

thesaurus.get(
  "/thesaurusGeo/:name",
  asyncHandler(async (req: Request<{ name: string }>, res) => {
    const data = await req.fetchApi(`/api/ThesaurusGeo/${req.params.name}`);
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  }),
);

thesaurus.post(
    "/addConceptThesaurus,:branch,:genre",
    asyncHandler(async (req: Request<{ branch: Branch; genre: string }>, res) => {
        const conc = await archeogrid_client.thesaurus.addMultiConcept.mutate({
            branch : req.params.branch,
            thesaurus: req.body.thesaurus,
            id_thes: parseInt(req.body.id_thes),
            type: req.params.genre,
            id_parent: parseInt(req.body.id_parent),
            name: req.body.name,
            identifier: req.body.identifier
        });

        let resu = req.body
        if (conc) {
            resu['ok'] = '1'
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(resu))
        } else {
            resu['ok'] = '0'
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(resu));
        }
    }),
);

// Vitrine exploration routes - similar to explore.ts patterns

thesaurus.get(
  "/exploreThesaurusVitrineMultiPage/:thesaurus,:idThes,:page",
  asyncHandler(async (req: Request<{ thesaurus: string; idThes: string; page: string }>, res) => {
    const { thesaurus, idThes, page } = req.params;
    const { display } = req.query;
    const branch = branchConfig;

    let lang = (req.query.lang || res.locals.lang) as string;
    if (lang?.includes("?")) {
      lang = lang.split("?")[0];
    }
    const language = ["fr", "en", "am"].includes(lang) ? (lang as "fr" | "en" | "am") : undefined;

    const [data, count] = await Promise.all([
      archeogrid_client.thesaurus.exploreThesaurusMultiItemPage.query({
        branch,
        language,
        user_id: req.user?.id || 0,
        thesaurus,
        thesaurus_id: Number.parseInt(idThes),
        page: Number.parseInt(page),
        limit: Number.MAX_SAFE_INTEGER,
      }),
      archeogrid_client.thesaurus.exploreThesaurusMultiItemNB.query({
        branch,
        language,
        thesaurus,
        thesaurus_id: Number.parseInt(idThes),
      }),
    ]);

    // Format data for vitrine display
    for (let i = 0; i < data.length; i++) {
      data[i].name = data[i].filename ? data[i].filename : "";
      data[i].id_item = data[i].id;
      if (data[i].item_type === "object") {
        data[i].idfolder = data[i].idfolder || data[i].idfolder_item || 0;
        data[i].idfile = data[i].idfile === "0" ? 0 : data[i].idfile;
      }
      data[i].has_access = (req.user && req.user.read.includes(data[i].idfolder)) || (req.user && req.user.user_status === 'admin');
    }

    // Initialize title to empty for lazy metadata loading
    for (const item of data) {
      item.title = ''; // Will be populated by client-side lazy loading
    }

    const renderEjs = display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";

    res.render(renderEjs, {
      exploreType: "thesaurus",
      user: req.user ?? { id: 0 },
      branch: branch,
      WRights: 0,
      data: data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: { page },
      virtual: false,
      selection: true,
    });
  }),
);

thesaurus.get(
  "/exploreThesaurusVitrineSimplePage/:thesaurus,:idThes,:page",
  asyncHandler(async (req: Request<{ thesaurus: string; idThes: string; page: string }>, res) => {
    const { thesaurus, idThes, page } = req.params;
    const { display } = req.query;
    const branch = branchConfig;

    let lang = (req.query.lang || res.locals.lang) as string;
    if (lang?.includes("?")) {
      lang = lang.split("?")[0];
    }
    const language = ["fr", "en", "am"].includes(lang) ? (lang as "fr" | "en" | "am") : undefined;

    const [data, count] = await Promise.all([
      archeogrid_client.thesaurus.exploreThesaurusSimpleItemPage.query({
        branch,
        language,
        thesaurus,
        thesaurus_id: Number.parseInt(idThes),
        page: Number.parseInt(page),
        limit: Number.MAX_SAFE_INTEGER,
        user_id: req.user?.id || 0,
        projectId: (req.query.projectId as string) || "0",
      }),
      archeogrid_client.thesaurus.exploreThesaurusItemNB.query({
        branch,
        language,
        thesaurus,
        thesaurus_id: Number.parseInt(idThes),
      }),
    ]);

    // Format data for vitrine display
    for (let i = 0; i < data.length; i++) {
      data[i].name = data[i].filename ? data[i].filename : "";
      data[i].id_item = data[i].id;
      if (data[i].item_type === "object") {
        data[i].idfolder = data[i].idfolder || data[i].idfolder_item || 0;
        data[i].idfile = data[i].idfile === "0" ? 0 : data[i].idfile;
      }
      data[i].has_access = (req.user && req.user.read.includes(data[i].idfolder)) || (req.user && req.user.user_status === 'admin');
    }

    // Initialize title to empty for lazy metadata loading
    for (const item of data) {
      item.title = ''; // Will be populated by client-side lazy loading
    }

    const renderEjs = display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";

    res.render(renderEjs, {
      exploreType: "thesaurus",
      user: req.user ?? { id: 0 },
      branch: branch,
      WRights: 0,
      data: data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: { page },
      virtual: false,
      selection: true,
    });
  }),
);

thesaurus.get(
  "/exploreThesaurusVitrinePactolsPage/:thesaurus,:idThes,:page",
  asyncHandler(async (req: Request<{ thesaurus: string; idThes: string; page: string }>, res) => {
    const { thesaurus, idThes, page } = req.params;
    const { display } = req.query;
    const branch = branchConfig;

    let lang = (req.query.lang || res.locals.lang) as string;
    if (lang?.includes("?")) {
      lang = lang.split("?")[0];
    }
    const language = ["fr", "en", "am"].includes(lang) ? (lang as "fr" | "en" | "am") : undefined;

    const [data, count] = await Promise.all([
      archeogrid_client.thesaurus.exploreThesaurusMultiItemPage.query({
        branch,
        language,
        user_id: req.user?.id || 0,
        thesaurus,
        thesaurus_id: Number.parseInt(idThes),
        page: Number.parseInt(page),
        limit: Number.MAX_SAFE_INTEGER,
      }),
      archeogrid_client.thesaurus.exploreThesaurusMultiItemNB.query({
        branch,
        language,
        thesaurus,
        thesaurus_id: Number.parseInt(idThes),
      }),
    ]);

    // Format data for vitrine display
    for (let i = 0; i < data.length; i++) {
      data[i].name = data[i].filename ? data[i].filename : "";
      data[i].id_item = data[i].id;
      if (data[i].item_type === "object") {
        data[i].idfolder = data[i].idfolder_item ? data[i].idfolder_item : data[i].idfolder;
        data[i].idfile = data[i].idfile === "0" ? 0 : data[i].idfile;
      }
       data[i].has_access = (req.user && req.user.read.includes(data[i].idfolder)) || (req.user && req.user.user_status === 'admin');
    }

    // Initialize title to empty for lazy metadata loading
    for (const item of data) {
      item.title = ''; // Will be populated by client-side lazy loading
    }

    const renderEjs = display === "grid" ? "explore/exploreVitrineGrid.ejs" : "explore/exploreVitrineList.ejs";

    res.render(renderEjs, {
      exploreType: "thesaurus",
      user: req.user ?? { id: 0 },
      branch: branch,
      WRights: 0,
      data: data,
      model: 'DublinCore',
      model_type: 'file',
      layout: false,
      datapost: { page },
      virtual: false,
      selection: true,
    });
  }),
);

// Add number of items routes for pagination

thesaurus.get(
  "/exploreThesaurusVitrineMultiNb/:thesaurus,:idThes",
  asyncHandler(async (req: Request<{ thesaurus: string; idThes: string }>, res) => {
    const branch = branchConfig;
    const thesaurus = req.params.thesaurus;
    const thesaurus_id = Number.parseInt(req.params.idThes);
    const language = res.locals.lang;
    const user = res.locals.user;

    const data = await archeogrid_client.thesaurus.exploreThesaurusMultiItemNB.query({
      branch,
      language,
      thesaurus,
      thesaurus_id,
    });

    if (!data) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify({ total: 0, totalCount: 0, nbRes: 0, nbResFile: 0, nbResObject: 0, nbResUnico: 0 }));
      return;
    }

    // For admin users, give them access to all items without restrictions
    if (user.user_status === 'admin') {
      const totalItems = data.total_count || 0;
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify({
        name: data.thesaurus_name,
        total: totalItems,
        totalCount: totalItems,
        nbRes: totalItems,
        nbResObject: 0, // Could be improved to get actual counts
        nbResUnico: 0,
        nbResFile: totalItems,
      }));
      return;
    }

    // Compute access counts like the regular explore endpoints for non-admin users
    const nbResFile = helpers.computeAccessItemFile(
      data.folders_info || [],
      user.read,
      user.user_status,
    );
    const nbResObject = helpers.computeAccessItemObj(
      data.folders_info || [],
      user.read,
      user.user_status,
    );
    const nbResUnico = helpers.computeAccessItemUnico(
      data.folders_info || [],
      user.read,
      user.user_status,
    );

    const nbRes = nbResFile + nbResObject + nbResUnico;

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify({
      name: data.thesaurus_name,
      total: nbRes,
      totalCount: data.total_count || nbRes, // Total before permission filtering
      nbRes: nbRes,
      nbResObject: nbResObject,
      nbResUnico: nbResUnico,
      nbResFile: nbResFile,
    }));
  }),
);

thesaurus.get(
  "/exploreThesaurusVitrineSimpleNb/:thesaurus,:idThes",
  asyncHandler(async (req: Request<{ thesaurus: string; idThes: string }>, res) => {
    const branch = branchConfig;
    const thesaurus = req.params.thesaurus;
    const thesaurus_id = Number.parseInt(req.params.idThes);
    const language = res.locals.lang;
    const user = res.locals.user;

    const data = await archeogrid_client.thesaurus.exploreThesaurusItemNB.query({
      branch,
      language,
      thesaurus,
      thesaurus_id,
    });

    if (!data) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify({ total: 0, totalCount: 0, nbRes: 0, nbResFile: 0, nbResObject: 0, nbResUnico: 0 }));
      return;
    }

    // For admin users, give them access to all items without restrictions
    if (user.user_status === 'admin') {
      const totalItems = data.total_count || 0;
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify({
        name: data.thesaurus_name,
        total: totalItems,
        totalCount: totalItems,
        nbRes: totalItems,
        nbResObject: 0, // Could be improved to get actual counts
        nbResUnico: 0,
        nbResFile: totalItems,
      }));
      return;
    }

    // Compute access counts like the regular explore endpoints for non-admin users
    const nbResFile = helpers.computeAccessItemFile(
      data.folders_info || [],
      user.read,
      user.user_status,
    );
    const nbResObject = helpers.computeAccessItemObj(
      data.folders_info || [],
      user.read,
      user.user_status,
    );
    const nbResUnico = helpers.computeAccessItemUnico(
      data.folders_info || [],
      user.read,
      user.user_status,
    );

    const nbRes = nbResFile + nbResObject + nbResUnico;

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify({
      name: data.thesaurus_name,
      total: nbRes,
      totalCount: data.total_count || nbRes, // Total before permission filtering
      nbRes: nbRes,
      nbResObject: nbResObject,
      nbResUnico: nbResUnico,
      nbResFile: nbResFile,
    }));
  }),
);

thesaurus.get(
  "/exploreThesaurusVitrinePactolsNb/:thesaurus,:idThes",
  asyncHandler(async (req: Request<{ thesaurus: string; idThes: string }>, res) => {
    const branch = branchConfig;
    const thesaurus = req.params.thesaurus;
    const thesaurus_id = Number.parseInt(req.params.idThes);
    const user = res.locals.user;

    // Use the API call since there's no unified client method for PACTOLS NB
    const queryParams = {
      thes_path: (req.query.thes_path as string) || thesaurus_id.toString(),
      projectId: (req.query.projectId as string) || "0",
      ...req.query
    };
    
    const data = await req.fetchApi(
      `/api/explorethesPactolsNB/${branch},${thesaurus},${thesaurus_id}`,
      queryParams,
    );

    if (!data || !data.folders_info) {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify({ total: 0, totalCount: 0, nbRes: 0, nbResFile: 0, nbResObject: 0, nbResUnico: 0 }));
      return;
    }

    // For admin users, give them access to all items without restrictions
    if (user.user_status === 'admin') {
      // Calculate total items from folders_info for PACTOLS
      let totalItems = 0;
      if (data.folders_info && Array.isArray(data.folders_info)) {
        totalItems = data.folders_info.reduce((sum: number, folder: any) => {
          return sum + (folder.nb_file || 0) + (folder.nb_object || 0) + (folder.nb_unico || 0);
        }, 0);
      }
      
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify({
        name: data.thesname,
        total: totalItems,
        totalCount: totalItems,
        nbRes: totalItems,
        nbResObject: 0, // Could be improved to get actual counts
        nbResUnico: 0,
        nbResFile: totalItems,
      }));
      return;
    }

    // Compute access counts like the regular explore endpoints for non-admin users
    const nbResFile = helpers.computeAccessItemFile(
      data.folders_info || [],
      user.read,
      user.user_status,
    );
    const nbResObject = helpers.computeAccessItemObj(
      data.folders_info || [],
      user.read,
      user.user_status,
    );
    const nbResUnico = helpers.computeAccessItemUnico(
      data.folders_info || [],
      user.read,
      user.user_status,
    );

    const nbRes = nbResFile + nbResObject + nbResUnico;

    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify({
      name: data.thesname,
      total: nbRes,
      totalCount: data.total_count || nbRes, // Total before permission filtering
      nbRes: nbRes,
      nbResObject: nbResObject,
      nbResUnico: nbResUnico,
      nbResFile: nbResFile,
    }));
  }),
);

export default thesaurus;
