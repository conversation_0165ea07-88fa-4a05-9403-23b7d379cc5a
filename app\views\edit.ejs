<script type='text/javascript'>
    function addFields(name, type) {
        // Container <div> where dynamic content will be placed
        let container = document.getElementById("parent_" + name);

        // Create an <input> element, set its type and name attributes
        // TODO : créer un élément au dessus ? pour avoir l'input et la pastille + pour ajouter
        let row = document.createElement("div")
        row.className = 'row'
        container.appendChild(row)
        let sm10 = document.createElement("div")
        sm10.className = 'col-sm-10'
        row.appendChild(sm10)

        let input = document.createElement(type);
        input.type = "text";
        input.className = "form-control"
        //input.name = '0#'+name+'_'+indice; // on prefix le nom de la métadonnée par 0 car le type de métadonnées est non unique
        // Et on incrémente pour avoir différents id pour le champ
        input.name = '0#' + name; // on prefix le nom de la métadonnée par 0 car le type de métadonnées est non unique
        // Mais on n'incrémente plus : on récupère tout dans un même champ => à réorganiser côté serveur
        sm10.appendChild(input);
        let sm2 = document.createElement("div")
        sm2.className = 'col d-flex justify-content-center align-items-center'
        row.appendChild(sm2)
        let minus = document.createElement("button")
        minus.setAttribute('type', 'button');
        minus.className = 'btn-vitrine-secondary'
        minus.onclick = function () {
            removeFields(this);
        };
        sm2.appendChild(minus)
        let ic = document.createElement("i")
        ic.className = 'fa fa-minus-circle'
        minus.appendChild(ic)
    }
    function removeFields(e) {
        e.parentNode.parentNode.parentNode.removeChild(e.parentNode.parentNode);
    }
</script>

<%- include('utils/title-content', { project: mainFolder, projectId: mainFolder.mainfolder, home: "home" }) %>
<!-- Main -->
<div id="main" class="container" style="padding-bottom: 32px;">
    <% if (type === 'file') { %>
    <% if (typeof image !== 'string') { %>
    <div class="text-center">
        <% if (image['file_ext'] === 'url') { %>
        <a href="<%= image['urlTarget'] %>" target="_blank">
            <img class="mx-auto"
                 src="/media/image?fid=<%= image['fid'] %>&id=<%= image['id'] %>&format=<%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"
                 alt="<%= image['filename']%>" style="width: 200px;">
        </a>
        <% } else if (image['file_ext'] === '3d'){ %>
        <a href="/viewer3d,<%= image['srcImg3d'] %>,<%= idFolder %>,<%= root%>" title="The 3D viewer" target="_blank">
            <img class="card-img mx-auto d-block"
                 src="/media/image?fid=<%= idFolder %>&id=<%= image['id']%>&format=<%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"
                 alt="<%= image['filename'] %>" style="width: 200px;">
        </a>
        <% } else { %>
        <% if (viewerFormat.indexOf(image['file_ext'].toLowerCase()) !== -1) { %>
        <a href="/visionneuse,<%= image['id'] %>-<%= image['fid'] %>,<%= root %>-v,0" target="_blank">
            <img class="mx-auto" src="/thumb/<%= image['fid']%>_<%= image['id'] %>" alt="<%= image['filename']%>">
        </a>
        <% } else {%>
        <img class="mx-auto" src="/thumb/<%= image['fid']%>_<%= image['id'] %>" alt="<%= image['filename']%>">
        <% } %>
        <% } %>
    </div>
    <% } else { %>
    <h4 class="text-center"><%=__('multipleIndexing')%>
        <a href="/projectv/<%= mainFolder.mainfolder%>?folder=<%= idFolder%>" class="fakelink" style="text-decoration: underline;"
           target="_blank" title="<%=__('visualizeItem')%>"><%= folder['name']%></a>
    </h4>
    <% } %>
    <% } else if (type === 'object') { if (item['id_file_representative']) { %>
    <div class="text-center"><a href="/visionneuseObj,<%= idItem %>,<%= item['id_file_representative'] %>-<%= idFolder %>,<%= root %>-v,0" target="_blank">
            <img class="mx-auto" src="/thumb/<%= idFolder %>_<%= item['id_file_representative'] %>"
                 alt="<%= item['name']%>">
        </a></div>
    <% } else if (idItem) { %>
    <div class="text-center"><img class="mx-auto" src="/assets/images/default_repre_image_object.png"
             alt="" style="height: 10vh;">
        <p><%=__('no2')%> <%=__('visualization')%> <%=__('for')%> <%=__('object2')%></p>
    </div>
    <% } else if (!idItem) { %><%# indexation de tout un répertoire avec plusieurs objects%>
        <div class="text-center"><p><%=__('multipleIndexing')%><a href="/projectv/<%= mainFolder.mainfolder%>?folder=<%= idFolder%>" class="fakelink" style="text-decoration: underline;"
           target="_blank" title="<%=__('visualizeItem')%>"><%= folder['name']%></a></p></div>
    <% } } else if (type === 'unico') { if (item && item.id !== 0)  { %>
    <div class="text-center">
        <a href="/visionneuseUnicoV2,<%= item.id %>,<%= idFolder %>,<%= root %>-v,0" target="_blank">
        <% if(item.type === 'poly' ){ %>
            <% let points = item.polygon.split(' ') %>
            <% for (let i in points) { let e = points[i].split(','); e[0] = parseInt(e[0]) - item.x; e[1] = parseInt(e[1]) - item.y; points[i] = e.join(',') } %>
            <% points = points.join(' ') %>
            <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                    style="max-width: 200px; max-height: 200px; object-fit: scale-down;"
                    viewBox="0 0 <%- item.width %> <%- item.height %>" id="unico-svg-<%- item.id %>">
                <mask id="svgmask-<%- item.id %>">
                    <polygon fill="#ffffff" points="<%- points %>" />
                </mask>
                <image xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="<%= `/crop/${idFolder}_${item.id_file}/${item.x},${item.y},${item.width},${item.height}` %>" alt="<%= item['name']%>"
                        width="<%- item.width %>" height="<%- item.height %>"
                        mask="url(#svgmask-<%- item.id %>)" />
            </svg>
        <% }else{ %>
            <img class="mx-auto d-block" src="<%= `/crop/${idFolder}_${item.id_file}/${item.x},${item.y},${item.width},${item.height}` %>" 
                 alt="<%= item['name']%>" style="max-width: 200px; max-height: 200px; object-fit: scale-down;">
        <% } %>
        </a>
    </div>
    <% } else if (!item){ %><div class="text-center"><p><%=__('multipleIndexing')%></p>
    <% }
    } %>

    <% if (typeof image !== 'string') { %>
    <h4 class="text-center"><%=__('edit')%> <% if (type === 'object') { %><%=__('object2')%> <% } %>
        <% if ((root === 'conservatoire3d') && (type === 'folder')) { %><a href="/3drepository/<%= idFolder %>">
            <% } else if (((root === 'pft3d') || (root === 'corpus')) && (type === 'folder')) { %>
                <% if (model === 'overall_project') { %><a href="/op/<%= idFolder %>"><%} else { %>
            <a href="/projectv/<%= idFolder %>"><% } } %><%= folder %>
                <% if  (type === 'folder') { %></a><% } %>
    </h4>
    <% }%>

    <form>
        <div class="form-group row">
            <label for="model" class="col-sm-2 col-form-label"><%=__('model')%>
                <a href="#" data-bs-toggle="modal" title="Information <%=__('model')%>" data-bs-target="#infoModel">
                    <i class="fas fa-info-circle"></i>
                </a> :
            </label>
            <div class="col-sm-3">
                <select class="form-select" id="modelSelect"
                        onchange="getmodeldescription(this.value,'<%= root %>','<%= values['id'] %>', '<%= type %>','<%= idFolder%>')">
                    <% for (let item = 0;  item < modelist.length; item++) {  %>
                    <option value="<%= modelist[item]['name'] %>"
                            <% if (modelist[item]['name'] === model) { %>selected<% } %>>
                        <%= modelist[item]['label'] %>
                    </option>
                    <% } %>
                </select>
            </div>
            <div class="col-sm-7" id="modelinfo">
                <% for (let i =  0; i < data.length; i++) { %>
                <% if ((data[i]['name'] === model) && (data[i]['metadata_type'] === type)) { %>

                <input id="infomodel" type="text" class="form-control" name="<%= data[i]['name'] %>"
                       value="<%= data[i]['description'] %>" disabled />

                <% } %>
                <% } %>
            </div>
        </div>
    </form>

    <% if (thesinfo) { %>
    <a href="/thesaurusv/<%= root %>,<%= mainFolder.mainfolder %>" target="_blank">
        <i class="fas fa-book"></i>
        <%=__('see')%> <%=__('the2')%> page <%=__('ofpluriel')%> thesauri
    </a>
    <% } %>
    <div class="modal" tabindex="-1" role="dialog" id="infoModel">
        <div class="modal-dialog modal-lg" role="document" id="modalinfo">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Information <%=__('model') %></h5>
                    <button class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">name</th>
                                <th scope="col">label</th>
                                <th scope="col">description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% for (let i =  0; i < data.length; i++) { %>
                            <% if (data[i]['metadata_type'] === type) { %>
                            <tr>
                                <th scope="row"><%= data[i]['id']%></th>
                                <td><%= data[i]['name'] %></td>
                                <td><%= data[i]['label'] %></td>
                                <td><%= data[i]['description'] %></td>
                            </tr>
                            <% } %>
                            <% } %>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-vitrine-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <p>(*) = <%=__('required') %></p>
    <div id="update">
        <form id="passport" action="" method="post" class="my-form d-flex flex-column gap-2">
            <input type="hidden" id="reqUrl" />
            <% for (let k =  0; k < metadata.length; k++) { %>
            <% if (metadata[k]['query'] === 'y') { %>
            <div class="form-group row">
                <label for="<%= metadata[k]['label']%>" class="col-sm-4 col-form-label">
                    <%= metadata[k]['label']%><% if (metadata[k]['y']) { %><sup>(*)</sup><% } %>
                    <% if (metadata[k]['description'] != '') {%>
                    <a href="#" data-bs-toggle="modal"
                       title="<%= metadata[k]['description'] %><% if (metadata[k]['list'] === 'license') { %>  (---) <%=__('click')%> <%=__('for') %> <%=__('moreinfo')%><% } %>"
                       data-bs-target="#infoModel<%= metadata[k]['id']%>">
                        <i class="fas fa-info-circle"></i>
                    </a><% } %>
                    <% if (metadata[k].optional) { %>
                    <div class="ms-2 d-inline-block">
                        <input type="checkbox" id="optional-selected#<%= metadata[k]['id'] %>"
                               class="optional-metadata-check form-check-input">
                    </div>
                    <% } %>
                </label>
                <div class="col-sm-7 d-flex flex-column gap-1 metadata-content"
                     <% if (!metadata[k]['isunique'] ) { %>id="parent_<%= metadata[k]['id'] %>" <% }%>>
                    <% if (metadata[k]['status'] === 'json') { %>

                    <% if (!metadata[k]['isunique'] ) { %>
                    <% let trouve = 0; for (let i = 0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { trouve = 1; %>
                    <% for (let c = 0; c < val[i]['value'].length; c++) { %>
                    <div class="row">
                        <div class="col-sm-10" id="sm10_<% metadata[k]['id'] %>" name="sm10">
                            <textarea class="form-control" rows="6" id="<%= metadata[k]['id'] %>"
                                      name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                                      value="<%= val[i]['value'][c] %>"
                                      <% if (metadata[k]['y']) { %>required<% } %>><%= val[i]['value'][c] %></textarea>
                        </div>
                        <div class="col d-flex justify-content-center align-items-center">
                            <% if (c === 0) { %>
                            <button type="button" class="btn-vitrine-secondary" onclick="addFields(<%= metadata[k]['id'] %>, 'textarea')">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                                </button>
                                <% } else { %>
                                <button type="button" class="btn-vitrine-secondary" onclick="removeFields(this)">
                                    <i class="fa fa-minus-circle" aria-hidden="true"></i>
                                </button>
                                <% } %>
                        </div>
                    </div>
                    <% } } } %>
                    <% if (trouve === 0) { %>
                    <div class="row">
                        <div class="col-sm-10" id="sm10_<% metadata[k]['id'] %>" name="sm10">
                            <input type="text" class="form-control" id="<%= metadata[k]['id'] %>"
                                   name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>" value=""
                                   <% if (metadata[k]['y']) { %>required<% } %>>
                        </div>
                        <div class="col d-flex justify-content-center align-items-center">
                            <button type="button" class="btn-vitrine-secondary" onclick="addFields(<%= metadata[k]['id'] %>, 'textarea')">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <% } %>
                    <% } else {  %>

                    <textarea class="form-control" rows="6"
                              name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                              id="<%= metadata[k]['id'] %>"
                              <% for (let i =  0; i < val.length; i++) { %>
                              <% if ((val) &&  (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                              value="<%= val[i]['value'] %>" <% } %>
                              <% } %>
                              <% if (metadata[k]['y']) { %>required<% } %>><% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %></textarea>
                    <textarea class="form-control" rows="6"
                              name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old"
                              id="<%= metadata[k]['id'] %>_old" style="display: none;"
                              <% for (let i =  0; i < val.length; i++) { %>
                              <% if ((val) &&  (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                              value="<%= val[i]['value'] %>" <% } %>
                              <% } %>><% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %></textarea>


                    <% } } else if  (metadata[k]['status'] === 'text') { %>
                    <% const actual_value = val.find(el => el.id_metadata === metadata[k].id)?.value ?? []; %>
                    <% if (!metadata[k]['isunique'] ) { %>
                    <% for (const [index, array_element] of actual_value.entries()) { %>
                    <div class="row">
                        <div class="col-sm-10" id="sm10_<% metadata[k]['id'] %>" name="sm10">
                            <textarea class="form-control" rows="3" id="<%= metadata[k]['id'] %>"
                                      name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                                      value="<%= array_element %>"
                                      <% if (metadata[k]['y']) { %>required<% } %>><%= array_element %></textarea>
                        </div>
                        <div class="col d-flex justify-content-center align-items-center">
                            <% if (!index) { %>
                            <button type="button" class="btn-vitrine-secondary" onclick="addFields(<%= metadata[k]['id'] %>, 'textarea')">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                            </button>
                            <% } else { %>
                            <button type="button" class="btn-vitrine-secondary" onclick="removeFields(this)">
                                <i class="fa fa-minus-circle" aria-hidden="true"></i>
                            </button>
                            <% } %>
                        </div>
                    </div>
                    <% } %>
                    <% if (!actual_value.length) { %>
                    <div class="row">
                        <div class="col-sm-10" id="sm10_<% metadata[k]['id'] %>" name="sm10">
                            <textarea class="form-control" rows="3" id="<%= metadata[k]['id'] %>"
                                      name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>" value=""></textarea>
                        </div>
                        <div class="col d-flex justify-content-center align-items-center">
                            <button type="button" class="btn-vitrine-secondary" onclick="addFields(<%= metadata[k]['id'] %>, 'textarea')">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <% } %>
                    <% } else {  %>

                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                              id="<%= metadata[k]['id'] %>"
                              <% for (let i =  0; i < val.length; i++) { %>
                              <% if ((val) &&  (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                              value="<%= val[i]['value'] %>" <% } %>
                              <% } %>
                              <% if (metadata[k]['y']) { %>required<% } %>><% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %></textarea>
                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old"
                              id="<%= metadata[k]['id'] %>_old" style="display: none;"
                              <% for (let i =  0; i < val.length; i++) { %>
                              <% if ((val) &&  (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                              value="<%= val[i]['value'] %>" <% } %>
                              <% } %>><% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %></textarea>
                    <% } } else if ((metadata[k]['status'] == 'char') || (metadata[k]['status'] == 'link')) { %>
                    <% const actual_value = val.find(el => el.id_metadata === metadata[k].id)?.value ?? []; %>
                    <% if ((metadata[k]['function']) && (metadata[k]['function'] === 'overall')) { %>
                    <select class="form-control"
                            name="<%= metadata[k]['isunique'] %>#function_<%= metadata[k]['function'] %>"
                            id="function_<%= metadata[k]['function'] %>" multiple
                            <% if (metadata[k]['y']) { %>required<% } %>>
                        <% for (let n=0; n < overallFolders.length; n++) { %>
                        <option value="<%= overallFolders[n]['id'] %>"
                                <% for (let ov = 0 ; ov < projectOverall.length; ov++) { if (overallFolders[n]['id'] === parseInt(projectOverall[ov])) {%>selected
                                <% } } %>><%= overallFolders[n]['name'] %></option>
                        <% } %>
                    </select>
                    <% } else { %>
                    <% if (!metadata[k]['isunique']) { %>
                    <% for (const [index, v] of actual_value.entries()) { %>
                    <div class="row">
                        <div class="col-sm-10" id="sm10_<% metadata[k]['id'] %>">
                            <input type="text" class="form-control" id="<%= metadata[k]['id'] %>"
                                   name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>" value="<%= v %>">
                        </div>
                        <div class="col d-flex justify-content-center align-items-center">
                            <% if (!index) { %>
                            <button type="button" class="btn-vitrine-secondary" onclick="addFields(<%= metadata[k]['id'] %>, 'input')">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                            </button>
                            <% } else { %>
                            <button type="button" class="btn-vitrine-secondary" onclick="removeFields(this)">
                                <i class="fa fa-minus-circle" aria-hidden="true"></i>
                            </button>
                            <% } %>
                        </div>
                    </div>
                    <% } %>
                    <% if (!actual_value.length) { %>
                    <div class="row">
                        <div class="col-sm-10" id="sm10_<% metadata[k]['id'] %>">
                            <input type="text" class="form-control" id="<%= metadata[k]['id'] %>"
                                   name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>" value=""
                                   <% if (metadata[k]['y']) { %>required<% } %>>
                        </div>
                        <div class="col d-flex justify-content-center align-items-center">
                            <button type="button" class="btn-vitrine-secondary" onclick="addFields(<%= metadata[k]['id'] %>, 'input')">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <% } %>
                    <% } else {  %>
                    <% if ((metadata[k]['code'] ) && (metadata[k]['code'].toLowerCase().includes('date'))) {%>
                    <input type="text" placeholder="YYYY-MM-DD" class="form-control" id="<%= metadata[k]['id'] %>"
                           name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                           <% for (let i =  0; i < val.length; i++) { %><% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                           value="<%= val[i]['value'] %>" <% } %><% } %>>
                    <% } else {%>
                    <input type="text" class="form-control" id="<%= metadata[k]['id'] %>"
                           name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                           <% for (let i =  0; i < val.length; i++) { %><% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                           value="<%= val[i]['value'] %>" <% } %><% } %> <% if (metadata[k]['y']) { %>required<% } %>>
                    <% } %>
                    <% } %>
                    <% } %>

                    <% } else if (metadata[k]['status'] == 'int' || metadata[k]['status'] == 'float') { %>
                    <input type="number" class="form-control" id="<%= metadata[k]['id'] %>"
                           name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                           <% for (let i =  0; i < val.length; i++) { %>)
                           <% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                           value="<%= val[i]['value'] %>" <% } %>
                           <% } %> <% if (metadata[k]['status'] == 'float') { %>step=".01" <% } %>
                           <% if (metadata[k]['y']) { %>required<% } %>>
                    <input type="number" class="form-control" id="<%= metadata[k]['id'] %>_old"
                           name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old" style="display: none;"
                           <% for (let i =  0; i < val.length; i++) { %>)
                           <% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                           value="<%= val[i]['value'] %>" <% } %>
                           <% } %>>
                    <% } else if (metadata[k]['status'] === 'choice') {%>
                    <% if ((metadata[k]['y']) &&  (!metadata[k]['isunique'])) { %><div
                         id="error-message#<%= metadata[k]['id'] %>" style="color: red; display: none;">
                        <%=__('mandatoryMessageCheck')%>.</div><% } %>

                    <% for(let i=0; i < list.length; i++) { %>
                    <% if (list[i]['id_list'] == metadata[k]['list']) { %>
                    <div class="form-check <% if (metadata[k]['y']) { %>changeRequired<% } %>">
                        <input type="<% if (metadata[k]['isunique']) {%>radio<%} else { %>checkbox<% }%>"
                            name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                            id="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_<%= i %>"
                            <%
                            let isChecked = false;
                            if (val && val.length > 0) {
                                const relevantVal = val.find(v => v['id_metadata'] === metadata[k]['id']);
                                if (relevantVal) {
                                    if (metadata[k]['isunique']) {
                                        if (String(relevantVal['value']).trim() === String(list[i]['name']).trim()) {
                                            isChecked = true;
                                        }
                                    } else {
                                        if (!metadata[k]['y']) {
                                            if (Array.isArray(relevantVal['value']) && relevantVal['value'].some(v => String(v).trim() === String(list[i]['name']).trim())) {
                                                isChecked = true;
                                            }
                                        }
                                    }
                                }
                            }
                            %>
                            <% if (isChecked) { %>checked<% } %>
                            value="<%= list[i]['name'] %>">&nbsp;<%= list[i]['name'] %>
                        <input type="<% if (metadata[k]['isunique']) {%>radio<%} else  { %>checkbox<% }%>"
                            name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old"
                            id="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old_<%= i %>"
                            style="display: none;"
                            <% for (let j =  0; j < val.length; j++) { %>
                                <% if ((val) && (val[j]['id_metadata'] === metadata[k]['id'])) { %>
                                    <% if (!metadata[k]['isunique']) { %>
                                        <% if (Array.isArray(val[j].value) && val[j].value.some(v => String(v).trim() === String(list[i].name).trim())) { %> checked <% } %>
                                    <% } else { %>
                                        <% if (String(val[j]['value']).trim() === String(list[i]['name']).trim()) { %> checked <% } %>
                                    <% } %>
                                <% } %>
                            <% } %> value="<%= list[i]['name'] %>">
                        <label for="<%= metadata[k]['list']%>"></label>

                    </div>
                    <% } %>
                    <% } %>
                    <% } else if (metadata[k]['status'] == 'date') { %>
                    <input type="date" class="form-control" id="<%= metadata[k]['id'] %>"
                           name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                           <% for (let i =  0; i < val.length; i++) { %>
                           <% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                           value="<%= val[i]['value'] %>" <% } %>
                           <% } %> <% if (metadata[k]['y']) { %>required<% } %>>
                    <input type="date" class="form-control" id="<%= metadata[k]['id'] %>_old"
                           name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old" style="display: none;"
                           <% for (let i =  0; i < val.length; i++) { %>
                           <% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                           value="<%= val[i]['value'] %>" <% } %>
                           <% } %>>
                    <% } else if (metadata[k]['status'] == 'doc') {%>
                    <textarea class="form-control" id="editor_<%= metadata[k]['id'] %>" name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>" <% if (metadata[k]['y']) { %>required<% } %>><%- (() => {
    let v = '';
    for (let i =  0; i < val.length; i++) {
        if (val[i]['id_metadata'] == metadata[k]['id']) {
            v = val[i]['value'];
        }
    }
    return v;
})() %></textarea>
                    <textarea class="form-control" id="editor_<%= metadata[k]['id'] %>_old"
                              name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old"
                              style="display: none;"><% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %></textarea>
                    <% } else if (metadata[k]['status'] == 'map') { %>
                    <div id="mapid_<%= metadata[k]['id']%>" style="height: 400px;"></div>
                    <input class="latlngPerso form-control" type="text" id="latlng_<%= metadata[k]['id']%>"
                           name="<%= metadata[k]['isunique'] %>#latlng--<%= metadata[k]['id'] %>"
                           value="<% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %>">
                    <input class="latlngPerso form-control" type="text" id="latlng_<%= metadata[k]['id']%>_old"
                           name="<%= metadata[k]['isunique'] %>#latlng--<%= metadata[k]['id'] %>_old"
                           value="<% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %>"
                           style="display: none;">
                    <% } else if (metadata[k]['status'] === 'thesaurus') { %>
                    <% const old_value = val.find(v => v['id_metadata'] === metadata[k]['id']) %>
                    <select class="form-control" name="<%= metadata[k]['isunique'] %>#theso--<%= metadata[k]['list'] %>#<%= metadata[k]['id'] %>" id="theso--<%= metadata[k]['list'] %>" <% if (metadata[k]['y']) { %>required<% } %>>
                        <option value="" <% if(!old_value) { %> selected <% } %>><%=__('choose') %></option>
                        <% for(const thes_val of thesaurus[metadata[k]['list']]) { %>
                            <option value="<%= thes_val['id'] %>_<%= thes_val['id_thes']%>" <% if (old_value?.value === thes_val.short_name) { %>selected<% } %>>
                                <%= thes_val.short_name %>
                            </option>
                        <% } %>
                    </select>

                    <select class="form-control d-none" name="<%= metadata[k]['isunique'] %>#theso--<%= metadata[k]['list'] %>_old#<%= metadata[k]['id'] %>" id="theso--<%= metadata[k]['list'] %>_old">
                        <% if (old_value) { %>
                            <% const old_value_thes = thesaurus[metadata[k]['list']].find(element => element.short_name === old_value.value) %>
                            <% if (old_value_thes) { %>
                                <option value="<%= old_value_thes.id %>_<%= old_value_thes.id_thes %>" selected><%= old_value_thes.short_name %></option>
                            <% } %>
                        <% } %>
                    </select>
                    <% } else if (metadata[k]['status'] == 'nomenclature') { %>
                    <% const current_value = nomenval.map(el => el.nomeninfo).find(info => info && info.id_metadata === metadata[k].id && info.model === model) %>
                    <select class="form-control" name="<%= metadata[k]['isunique'] %>#nomen--<%= metadata[k]['list'] %>#<%= metadata[k]['id'] %>"
                            id="nomen--<%= metadata[k]['list'] %>" <% if (metadata[k]['y']) { %>required<% } %>>
                        <option value=""><%=__('choose') %></option>
                        <% for (const nomenclature_element of nomenclature) { %>
                        <option value="<%= nomenclature_element['id'] %>_<%= nomenclature_element['id_thes']%>"
                                <% if (current_value?.value === nomenclature_element.short_name) { %>
                                selected
                                <% } %>>
                            <%= nomenclature_element.short_name %>
                        </option>
                        <% } %>
                    </select>
                    <select class="form-control"
                            name="<%= metadata[k]['isunique'] %>#nomen--<%= metadata[k]['list'] %>_old#<%= metadata[k]['id'] %>"
                            id="nomen--<%= metadata[k]['list'] %>_old" style="display: none;">
                        <option value=""><%=__('choose') %></option>
                        <% const current_nomenclature_element = nomenclature.find(el => current_value?.value === el.short_name) %>
                        <% if (current_nomenclature_element) { %>
                        <option value="<%= current_nomenclature_element.id %>_<%= current_nomenclature_element.id_thes %>"
                                selected><%= current_nomenclature_element.name %></option>
                        <% } %>
                    </select>

                    <% } else if (metadata[k]['status'] === 'multi') { %>
                    <%# si des tags multi sont présents, on les donne ici avec le moyen de le supprimer %>
                    <% if (multival) { for (let m = 0; m < multival.length; m++) {%>
                    <% if (multival[m]['multiinfo']['thesaurus'] === metadata[k]['list']) {  // ajouter une contrainte sur le qualifier
                       let treatment = 0;
                        if ((metadata[k]['code']) && (multival[m]['multiinfo']['qualifier'])) {
                           if (metadata[k]['code'] === multival[m]['multiinfo']['qualifier'])
                            treatment = 1 }
                        else { // pas d'info qualifier à prendre en compte mais poursuivre le traitement quand meme
                            treatment = 1 }
                    if (treatment === 1 )  {  %>
                    <div class="row flex">
                        <div class="col-sm-10">
                            <input class="form-control" type="text" value="<%= multival[m]['multiinfo']['value']%>" />
                        </div>
                        <div class="col d-flex justify-content-center align-items-center">
                            <button type="button" class="btn-vitrine-secondary" title="<%=__('delete')%>"
                                onclick="delete_thesmulti_item('<%= root %>', '<%= idItem %>', '<%= type %>', '<%= multival[m]['multiinfo']['thes_path']%>', '<%= multival[m]['multiinfo']['list']%>')">
                                <i class="fa fa-minus-circle" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <% } } } } %>
                    <% if (metadata[k]['function']) { %><%# function : on récupère 1/url d'api +(séparé par un #) 2/ le nom du thesaurus chez opentheso qui correspond à ce champs
                    exemple: https://opentheso.huma-num.fr/api/#th844
                    pour les thesaurus chez huma-num c'est https://opentheso.huma-num.fr/api/ (projet Urbaport, Stéréo )
                    pour les pactols c'est (à vérifier) : https://pactols.frantiq.fr/api/
                    pour le thesaurus frollo c'est https://frollo.notre-dame.science/api/ %>
                    <div class="input-group" id="parent_<%= metadata[k]['list'] %>">
                        <input class="form-control" placeholder="<%=__('beginEntertext') %> ..." type="text"
                               name="<%= metadata[k]['isunique'] %>#openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['code'] %>_value#<%= metadata[k]['id'] %>"
                               id="openthesomulti_function_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value">
                        <input class="form-control" type="hidden"
                               name="<%= metadata[k]['isunique'] %>#openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['code'] %>_id#<%= metadata[k]['id'] %>"
                               id="openthesomulti_function_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id" />
                        <button type="button" class="btn-vitrine-primary"
                               onclick="addkeyOpentheso('multi','<%= metadata[k]['list'] %>','<%= metadata[k]['code'] %>','openthesomulti_function_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value','openthesomulti_function_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id', '<%= metadata[k]['id'] %>', '<%= metadata[k]['function'] %>');"><%=__('save') %></button>
                    </div>


                    <% } else { %><%# si gros thes on oublie le sélect %><% for (thes in multicounts) { if ((metadata[k]['list'] === thes) && (multicounts[thes] < 15)) {%>
                    <select class="form-control" name="<%= metadata[k]['isunique'] %>#multi--<%= metadata[k]['list'] %>#<%= metadata[k]['id'] %>"
                            id="multi--<%= metadata[k]['list'] %>"
                            onchange="putChoiceInForm(this.value,'multi--<%= metadata[k]['list'] %>_t', 'multi--<%= metadata[k]['list'] %>')"
                            <% if (metadata[k]['y']) { %>required<% } %>>
                        <option value=""><%=__('browse')%></option>
                        <% for (const thesaurus_element of multi.filter(el => el.thesaurus === metadata[k].list)) { %>
                        <%# on  ajoute le path pour pouvoir vérifier si cette indexation n'existe pas dejà (polyhierarchie) %>
                        <%# si id_thes = 1 (ou si c'est 0 depuis qu'on a repris les ancien thesaurus) c'est la racine du thesaurus ,
                        c'est plus perturbant qu'autre chose de l'avoir dans la liste, surtout qu'il ne faut pas indexer avec ...%>>
                        <% if ((thesaurus_element.id_thes !== 1) && (thesaurus_element.id_thes !== 0)){ %>
                        <%# on  ajoute le qualifier pour pouvoir ajouter l'info dans l'indexation %>
                        <option value="<%= thesaurus_element.id %>_<%= thesaurus_element.id_thes %>_<%= thesaurus_element.thesaurus_path %>"
                                data_short_name="<%= thesaurus_element.short_name %>"
                                <% if (multival.map(el => el.multiinfo).some(info => info.id_metadata === metadata[k].id && info.model === model
                                        && info.id_thes_thesaurus === thesaurus_element.id_thes )) { %>
                                selected
                                <% } %>>
                            <% if (model === 'overall_project') { %>
                            <%= thesaurus_element.short_name %>
                            <% } else { %>
                            <%= thesaurus_element.name %>
                            <% } %>
                        </option>
                        <% } %>
                        <% } %>
                    </select><% } else { if (metadata[k]['list'] === thes) {%><%# autocomplete classique %>
                    <div class="input-group" id="parent_<%= metadata[k]['list'] %>">
                        <input class="form-control" placeholder="Commencer à taper..." type="text"
                               name="<%= metadata[k]['isunique'] %>#openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value#<%= metadata[k]['id'] %>"
                               id="openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value">
                        <input class="form-control" type="hidden"
                               name="<%= metadata[k]['isunique'] %>#openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['code'] %>_id#<%= metadata[k]['id'] %>"
                               id="openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id">
                                                <button type="button" class="btn-vitrine-primary"                               onclick="addkey('multi','<%= metadata[k]['list'] %>','<%= metadata[k]['code'] %>','openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['code'] %>_value', <%= metadata[k]['id'] %>);"><%=__('save') %></button>
                    </div>
                    <% } } %>
                    <% } } } else if (metadata[k]['status'] == 'multit') { %>
                    <select class="form-control" name="<%= metadata[k]['isunique'] %>#multi--<%= metadata[k]['list'] %>"
                            id="multi--<%= metadata[k]['list'] %>"
                            onchange="putChoiceInForm(this.value,'multi--<%= metadata[k]['list'] %>_t', 'multi--<%= metadata[k]['list'] %>')"
                            <% if (metadata[k]['y']) { %>required<% } %>>
                        <option value=""><%=__('choose') %></option>
                        <% for (let n=0; n < tabmulti.length; n++) { %>
                        <% if (tabmulti[n] == metadata[k]['list']) { %>
                        <% for  (ind in multi[n] ) { %>
                        <option value="<%= multi[n][ind]['id'] %>_<%= multi[n][ind]['id_thes']%>"
                                data_short_name="<%= multi[n][ind]['short_name'] %>"
                                <% for (let i =  0; i < multival.length; i++) { %>
                                <% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %>
                                <% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) {%>selected<% } %>
                                <% } %>
                                <% } %>><%= multi[n][ind]['name'] %></option>
                        <% } %>
                        <% } %>
                        <% } %>
                    </select>
                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#multi--<%= metadata[k]['list'] %>_t_id_old"
                              id="multi--<%= metadata[k]['list'] %>_t_id_old" style="display: none;"
                              //do not fix this line
                              value="<% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let i =  0; i < multival.length; i++) { %><% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %><% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) { c++; %><% if (c > 1) {%>,<% } %><%= multival[i]['multiinfo']['id_thesaurus'] %>_<%= multival[i]['multiinfo']['id_thes_thesaurus'] %><% } %><% } %><% } %><% } %><% } %><% } %>"><% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let i =  0; i < multival.length; i++) { %><% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %><% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) { c++; %><% if (c > 1 ) {%>,<% } %><%= multival[i]['multiinfo']['id_thesaurus'] %>_<%= multival[i]['multiinfo']['id_thes_thesaurus'] %><% } %><% } %><% } %><% } %><% } %><% } %></textarea>

                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#multi--<%= metadata[k]['list'] %>_t_old"
                              id="multi--<%= metadata[k]['list'] %>_t_old" style="display: none;"
                              value="<% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let i =  0; i < multival.length; i++) { %><% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %><% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) { c++; %><% if (c > 1) {%>,<% } %><%= multival[i]['multiinfo']['value'] %><% } %><% } %><% } %><% } %><% } %><% } %>"
                              disabled><% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let i =  0; i < multival.length; i++) { %><% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %><% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) { c++; %><% if (c > 1 ) {%>,<% } %><%= multival[i]['multiinfo']['value'] %><% } %><% } %><% } %><% } %><% } %><% } %></textarea>

                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#multi--<%= metadata[k]['list'] %>_t_id"
                              id="multi--<%= metadata[k]['list'] %>_t_id" style="display: none;"
                              value="<% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let i =  0; i < multival.length; i++) { %><% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %><% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) { c++; %><% if (c > 1) {%>,<% } %><%= multival[i]['multiinfo']['id_thesaurus'] %>_<%= multival[i]['multiinfo']['id_thes_thesaurus'] %><% } %><% } %><% } %><% } %><% } %><% } %>"><% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let i =  0; i < multival.length; i++) { %><% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %><% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) { c++; %><% if (c > 1 ) {%>,<% } %><%= multival[i]['multiinfo']['id_thesaurus'] %>_<%= multival[i]['multiinfo']['id_thes_thesaurus'] %><% } %><% } %><% } %><% } %><% } %><% } %></textarea>

                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#multi--<%= metadata[k]['list'] %>_t"
                              id="multi--<%= metadata[k]['list'] %>_t"
                              value="<% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let i =  0; i < multival.length; i++) { %><% if ((multival) && (multival[i]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[i]['multiinfo']['model'] === model) ) { %><% if (multival[i]['multiinfo']['value'] === multi[n][ind]['short_name']) { c++; %><% if (c > 1) {%>,<% } %><%= multival[i]['multiinfo']['value'] %><% } %><% } %><% } %><% } %><% } %><% } %>"
                              disabled><% for (let n=0; n < tabmulti.length; n++) { let c = 0; %><% if (tabmulti[n] == metadata[k]['list']) { %><% for  (ind in multi[n] ) { %><% for (let j =  0; j < multival.length; j++) { %><% if ((multival) && (multival[j]['multiinfo']['id_metadata'] == metadata[k]['id']) && (multival[j]['multiinfo']['model'] === model) ) { %><% if (multival[j]['multiinfo']['value'] == multi[n][ind]['short_name']) { c++; %><% if (c > 1 ) {%>,<% } %><%= multival[j]['multiinfo']['value'] %><% } %><% } %><% } %><% } %><% } %><% } %></textarea>

                    <% } else if ((metadata[k]['status'] == 'list') || (metadata[k]['status'] == 'choico')) { %>
                    <select <% if ((metadata[k]['function']) && (metadata[k]['function'].indexOf('hidden') !== 'hidden')) { %>hidden
                            <% } %>
                            class="form-control" name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>"
                            id="<%= metadata[k]['id'] %>"
                            <% if (metadata[k]['status'] == 'choico') { %>onchange="putChoiceInForm(this.value,'<%= metadata[k]['id'] %>_t')"
                            <% } %> <% if (metadata[k]['y']) { %>required<% } %>>
                        <option value=""><%=__('choose') %></option>
                        <% for (let n=0; n < tablist.length; n++) { %>
                        <% if (tablist[n] == metadata[k]['list']) { %>
                        <% for  (ind in listemetadata[n] ) { %>
                        <option value="<%= listemetadata[n][ind]['name'] %>"
                                <% for (let i =  0; i < val.length; i++) { %>
                                <% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                                <% if (val[i]['value'] == listemetadata[n][ind]['name']) {%>selected<% } %>
                                <% } %>
                                <% } %>><%= listemetadata[n][ind]['name'] %></option>
                        <% } %>
                        <% } %>
                        <% } %>
                    </select>
                    <% if (metadata[k]['status'] == 'list') { %>
                    <select class="form-control" name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old"
                            id="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_old" style="display: none;">
                        <option value=""></option>
                        <% for (let n=0; n < tablist.length; n++) { %>
                        <% if (tablist[n] == metadata[k]['list']) { %>
                        <% for  (ind in listemetadata[n] ) { %>
                        <option value="<%= listemetadata[n][ind]['name'] %>"
                                <% for (let i =  0; i < val.length; i++) { %>
                                <% if ((val) && (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                                <% if (val[i]['value'] == listemetadata[n][ind]['name']) {%>selected<% } %>
                                <% } %>
                                <% } %>><%= listemetadata[n][ind]['name'] %></option>
                        <% } %>
                        <% } %>
                        <% } %>
                    </select>
                    <% }%>
                    <% if (metadata[k]['status'] == 'choico') { %>
                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_t"
                              id="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_t"
                              <% for (let i =  0; i < val.length; i++) { %>
                              <% if ((val) &&  (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                              value="<%= val[i]['value'] %>" <% } %>
                              <% } %>
                              <% if (metadata[k]['y']) { %>required<% } %>><% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %></textarea>
                    <textarea class="form-control" rows="3"
                              name="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_t_old"
                              id="<%= metadata[k]['isunique'] %>#<%= metadata[k]['id'] %>_t_old" style="display: none;"
                              <% for (let i =  0; i < val.length; i++) { %>
                              <% if ((val) &&  (val[i]['id_metadata'] == metadata[k]['id'])) { %>
                              value="<%= val[i]['value'] %>" <% } %>
                              <% } %>><% for (let i =  0; i < val.length; i++) { %><% if (val[i]['id_metadata'] == metadata[k]['id']) { %><%= val[i]['value'] %><% } %><% } %></textarea>

                    <% } %>
                    <% } else if (metadata[k]['status'] === 'pactols') { %>
                    <%# si des tags pactols sont présents, on les donne ici avec le moyen de le supprimer %>
                    <% let metadata_pactols = pactolsval.filter(item => item.thespactolsinfo.id_metadata === metadata[k]['id']); %>
                    <% if (metadata_pactols) {
                        for (let m = 0; m < metadata_pactols.length; m++) {
                            if (metadata_pactols[m]['thespactolsinfo']['thesaurus'] === metadata[k]['list']) { %>
                                <div class="row flex">
                                    <div class="col-sm-10">
                                        <input class="form-control" type="text" value="<%= metadata_pactols[m]['thespactolsinfo']['value']%>" />
                                    </div>
                                <div class="col d-flex justify-content-center align-items-center">
                                    <button type="button" class="btn-vitrine-secondary" title="<%=__('delete')%>" onclick="delete_thespactols_item('<%= root %>', '<%= idItem %>', '<%= type %>', '<%= metadata_pactols[m]['thespactolsinfo']['id_thesaurus']%>', '<%= metadata_pactols[m]['thespactolsinfo']['id_thes_thesaurus']%>', '<%= metadata_pactols[m]['thespactolsinfo']['thesaurus']%>')">
                                        <i class="fa fa-minus-circle" aria-hidden="true"></i>
                                    </button>
                                </div>
                            </div>
                        <% }
                        }
                    } %>
                    <%# If the collection is define then we display all item
                     Otherwise we only show the autocomplete field %>
                    <% if(metadata[k]['function']) { %>
                        <select class="form-control"
                                name="<%= metadata[k]['isunique'] %>#thesopactols--<%= metadata[k]['list'] %>#<%= metadata[k]['id'] %>"
                                id="thesopactols--<%= metadata[k]['list'] %>"
                                onchange="putChoiceInForm(this.value,'thesopactols--<%= metadata[k]['list'] %>_t', 'thesopactols--<%= metadata[k]['list'] %>')"
                                <% if (metadata[k]['y']) { %>required<% } %>>
                            <option value=""><%=__('browse')%></option>
                            <% for (let f = 0; f < pactols.length; f++) { if (pactols[f]['filter'] === metadata[k]['function']) { %>
                            <% for (const thesaurus_element of pactols[f]['data'].filter(el => el.thesaurus === metadata[k].list)) { %>
                            <%# on  ajoute le path pour pouvoir vérifier si cette indexation n'existe pas dejà (polyhierarchie) %>
                            <%# si id_thes = 1 c'est la racine du thesaurus , c'est plus perturbant qu'autre chose de l'avoir dans la liste, surtout qu'il ne faut pas indexer avec ...%>>
                            <% if (thesaurus_element.id_thes !== 1) { %>
                            <option value="<%= thesaurus_element.id %>_<%= thesaurus_element.id_thes %>"
                                    data_short_name="<%= thesaurus_element.short_name %>"
                                    <% if (pactolsval.map(el => el.thespactolsinfo).some(info => info.id_metadata === metadata[k].id && info.model === model && info.value === thesaurus_element.short_name )) { %>
                                    selected
                                    <% } %>>
                                <% if (model === 'overall_project') { %>
                                <%= thesaurus_element.short_name %>
                                <% } else { %>
                                <%= thesaurus_element.name %>
                                <% } %>
                            </option>
                            <% } %>
                            <% } } } %>
                        </select>
                    <% } %>
                    <div class="input-group" id="parent_<%= metadata[k]['list'] %>">
                        <input class="form-control" placeholder="Commencer à taper..." type="text"
                               name="<%= metadata[k]['isunique'] %>#openthesopactols_<%= metadata[k]['list'] %>_value#<%= metadata[k]['id'] %>"
                               id="openthesopactols_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value">
                        <input class="form-control" type="hidden"
                               name="<%= metadata[k]['isunique'] %>#openthesopactols_<%= metadata[k]['list'] %>_id#<%= metadata[k]['id'] %>"
                               id="openthesopactols_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id">
                            <button type="button" class="btn-vitrine-primary" onclick="addkey('pactols','<%= metadata[k]['list'] %>','<%= metadata[k]['code'] %>','openthesopactols_<%= metadata[k]['list'] %>_value', <%= metadata[k]['id_metadata']%>);"><%=__('save') %></button>
                    </div>
                    <% } else if(metadata[k]['status'] == 'actor') { %>
                        <% let actorMetadata = val.filter(el => el.id_metadata === metadata[k].id);
                        if (actorMetadata.length === 0) {
                            actorMetadata = metadata[k];
                        }else{
                            actorMetadata = actorMetadata[0];
                        } %>
                        <%- include('./advanced_model/edit/actor.ejs', { actorMetadata, old_value: false }) %>
                        <%- include('./advanced_model/edit/actor.ejs', { actorMetadata, old_value: true }) %>
                    <% } else if(metadata[k]['status'] == 'datation') { %>
                        <% let datationMetadata = val.filter(el => el.id_metadata === metadata[k].id); %>
                        <% if (datationMetadata.length === 0) {
                            datationMetadata = metadata[k];
                        }else{
                            datationMetadata = datationMetadata[0];
                        } %>
                        <%- include('./advanced_model/edit/datation.ejs', { datationMetadata, availablePeriodo : true, old_value: false }) %>
                        <%- include('./advanced_model/edit/datation.ejs', { datationMetadata, availablePeriodo : true, old_value: true }) %>
                    <% } else if(metadata[k]['status'] == 'location') { %>
                        <% let locationMetadata = val.filter(el => el.id_metadata === metadata[k].id);
                        if (locationMetadata.length === 0) {
                            locationMetadata = metadata[k];
                        }else{
                            locationMetadata = locationMetadata[0];
                        } %>
                        <%- include('./advanced_model/edit/location.ejs', { locationMetadata, old_value: false }) %>
                        <%- include('./advanced_model/edit/location.ejs', { locationMetadata, old_value: true }) %>
                    <% } %>
                </div>
            </div>
            <% } %>
            <% } %>
            <% if (model === 'project'){ %>
            <div class="form-group row align-items-center">
                <label for="representative_image"
                       class="col-sm-4 col-form-label"><%=__('representative_image')%></label>
                <div class="checkbox col-sm-7">
                    <input type="checkbox" class="form-check-input" name="checkbox_repr_image" id="checkbox_repr_image"
                           <% if (repre_image !== 0 ) {%>checked<% } %>>
                    <input type="checkbox" class="form-check-input" name="checkbox_repr_image_old"
                           id="checkbox_repr_image_old"
                           style="display: none;" <% if (repre_image !== 0 ) {%>checked<% } %> />
                </div>
            </div>
            <% } %>

            <%# Indexation par lot, pas d'item identifié (idItem = 0)  : Ecraser ou non les valeurs ??? %>
            <% if( !idItem ) { %>
            <div class="form-group">
                <p><%=__('eraseLot') %>
                    <span id="lot-error-indicator" style="display: none; color: #ffc107; margin-left: 5px;">
                        <i class="fas fa-exclamation-circle"></i>
                    </span>
                </p>
                <div class="checkbox">
                    <input type="radio" name="lot" value="0" id="yes">
                    <label for="yes" class="col-sm-4 col-form-label"><%=__('yes')%></label>
                </div>
                <div>
                    <input type="radio" name="lot" value="1" id="no">
                    <label for="no" class="col-sm-4 col-form-label"><%=__('no')%></label>
                </div>
            </div>
            <% } %>

            <div class="form-group d-flex justify-content-center">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-vitrine-primary"><%=__('save') %></button>
                    <a href="/projects" id="cancel-link" class="btn btn-vitrine-secondary"><%=__('cancel') %></a>
                </div>
            </div>
        </form>
        <ul class="nav scroll-top-fiche">
            <li><a href="#main" title="<%=__('backtotop') %>">
                    <i class="fas fa-chevron-up fa-2x">
                    </i>
                </a>
            </li>
        </ul>
    </div>

</div>

<% for (let j =  0; j < metadata.length; j++) { %>
<div class="modal" tabindex="-1" role="dialog" id="infoModel<%= metadata[j]['id']%>">
    <div class="modal-dialog" role="document" id="modalinfo" style="height: 10vh!important;">
        <div class="modal-content" style="height: 30vh!important;">
            <div class="modal-body">
                <%= metadata[j]['description'] %>
                <% if (metadata[j]['list'] === 'license') { %><br /><a class="btn btn-info" href="/licenses"
                   target="_blank"><%=__('moreinfo')%> <%=__('about')%> <%=__('licenses')%></a><% } %>
            </div>
        </div>
    </div>
</div>
<% } %>


<script type="text/javascript" src="/assets/js/mapbefore.js"></script>

<script src="/assets/../node_modules/ckeditor/ckeditor.js"></script>
<script>
    try {
        var ckeditorIds = [
            <% for (let k = 0; k < metadata.length; k++) { 
                if (metadata[k]['status'] === 'doc') { %>
                    'editor_<%= metadata[k]['id'] %>',
            <%  } 
            } %>
        ];
        ckeditorIds.forEach(function(id) {
            var el = document.getElementById(id);
            if (el) {
                CKEDITOR.replace(id);
            }
        });
    } catch(e) {console.log(e)}
</script>
<script>
<% for (let k = 0; k < metadata.length; k++) { %>
    <% if (metadata[k]['status'] == 'map') { %>
        <% const map_metadata = val.find(m => m.id_metadata === metadata[k].id && m.value); %>
        <% if (map_metadata) { %>
                initmapGenerique(<%= map_metadata.value.split(",")[0] %>, <%= map_metadata.value.split(",")[1] %>, 'mapid_' + <%= metadata[k]['id'] %>, '<%= root%>')
                <% } else { %>
                    initmapGenerique(0, 0, 'mapid_' + <%= metadata[k]['id'] %>, '<%= root%>');
        <% } %>
    <% } %>
<% } %>
</script>
<script>
    function resolveArk(arkUrl) {
        return fetch(arkUrl, { method: "HEAD", redirect: "manual" })
            .then(response => response.headers.get("location") || arkUrl)
            .catch(error => {
                console.error("Erreur lors de la résolution de l'ARK :", error);
                return arkUrl; // Retourne l'ARK original si erreur
            });
    }
    function getParamsFromUrl(url) {
        const parsedUrl = new URL(url);
        return Object.fromEntries(parsedUrl.searchParams.entries());
    }

    function addConceptOpentheso(url, thesaurus,  id_thes, name, genre, id_thes_orig, name_orig, theso_archeo, id_metadata){
        let parentfull = '', parent ='', apiname = '';
        //https://opentheso.huma-num.fr/api/th844.848921.json ou
        //https://frollo.notre-dame.science/api/th13.60886.json
        // on appel l'api d'opentheso pour récupérer le parent / broader
        $.ajax({
            type: 'GET',
            url: url+thesaurus+'.'+id_thes+'.json',
        }).done(function (data) {
            // depuis la sortie en json on récupère
            // la première clé qui est l'identifier qu'on va mettre dans la table thesaurus d'archeoGRID
            // et le parent
            const firstKey = Object.keys(data)[0]
            const innerObject = data[firstKey];
            Object.entries(innerObject).forEach(([key, value]) => {
                if (key.indexOf('broader') !== -1) {
                    parentfull = value[0]['value']
                } else if (key.indexOf('prefLabel') !== -1) {
                    apiname = value[0]['value']
                }
            })
            // pour le cas d'un ark  :
            if (parentfull.indexOf('ark') !== -1) {
                // Cross-Origin blocked => on colle "https://cors-anywhere.herokuapp.com/" à l'url
                //const anticrossurl = 'https://cors-anywhere.herokuapp.com/'+parentfull
                //resolveArk(anticrossurl)
                resolveArk(parentfull)
                    .then(finalUrl => {
                        const params = getParamsFromUrl(finalUrl);
                        const idc = params.get("idc");
                        parent = idc ? idc : '';
                        const concept = {
                            thesaurus: theso_archeo,
                            id_thes: id_thes,
                            id_parent: parseInt(parent),
                            name: apiname,
                            identifier: firstKey,
                            url: url,
                            apitheso: thesaurus,
                            id_thes_orig: id_thes_orig,
                            name_orig: name_orig,
                            genre: genre,
                            id_metadata: id_metadata
                        }
                        $.ajax({
                            type: 'POST',
                            url: '/addConceptThesaurus,<%= root %>,' + genre,
                            data: concept,
                            dataType: 'json'
                        }).done(function(resu) {
                            if (resu['ok'] === '1') {
                                // ajout d'un nouveau concept ok
                                if (resu['id_thes'] ===  resu['id_thes_orig']) { // c'était le concept choisi pour l'indexation => on index
                                    alert('<%=__('new') %> <%=__('keyword') %> <%=__('added') %> <%=__('to2') %> thesaurus')
                                    addkeyOpentheso(genre,theso_archeo, resu['qualifier'], '', resu['identifier'], resu['id_metadata'], resu['url']+'#'+resu['thesaurus'] )
                                } else { // ce n'était pas le concept choisi au départ pour indexer, on réessaye d'ajouter le conept d'origine
                                    // on a ajouté un parent, il faut remonter pour essayer de l'ajouter à nouveau
                                    addConceptOpentheso(resu['url'], resu['apitheso'], resu['id_thes_orig'], resu['name_orig'], resu['genre'], resu['id_thes_orig'], resu['name_orig'], resu['thesaurus'], resu['id_metadata'])
                                }
                            } else if (resu['ok'] === '0') {
                                // ajout du concept impossible, on remonte à son parent pour essayer de l'ajouter au thesaurus archeogrid
                                addConceptOpentheso(resu['url'], resu['apitheso'], resu['id_parent'], '', resu['genre'], resu['id_thes_orig'], resu['name_orig'], resu['thesaurus'], resu['id_metadata'])
                            }

                        }).fail(function (jqXHR, textStatus, errorThrown) {
                            console.log("Erreur AJAX:", textStatus, errorThrown)
                            //alert('FAIL addConceptOpentheso in addConceptOpentheso')
                            return '0';
                        })
                    });
            } else {
                // parentfull: https://opentheso.huma-num.fr/?idc=848920&idt=th844
                const params = new URLSearchParams(new URL(parentfull).search)
                const idc = params.get("idc");
                parent = idc ? idc : '';
                const concept = {
                    thesaurus: theso_archeo,
                    id_thes: id_thes,
                    id_parent: parseInt(parent),
                    name: apiname,
                    identifier: firstKey,
                    url: url,
                    apitheso: thesaurus,
                    id_thes_orig: id_thes_orig,
                    name_orig: name_orig,
                    genre: genre,
                    id_metadata: id_metadata
                }
                $.ajax({
                    type: 'POST',
                    url: '/addConceptThesaurus,<%= root %>,' + genre,
                    data: concept,
                    dataType: 'json'
                }).done(function (resu) {
                    if (resu['ok'] === '1') {
                        // ajout d'un nouveau concept ok
                        if (resu['id_thes'] === resu['id_thes_orig']) { // c'était le concept choisi pour l'indexation => on index
                            alert('<%= __('new') %> <%= __('keyword') %> <%= __('added') %> <%= __('to2') %> thesaurus')
                            addkeyOpentheso(genre, theso_archeo, resu['qualifier'], '', resu['identifier'], resu['id_metadata'], resu['url'] + '#' + resu['thesaurus'])
                        } else { // ce n'était pas le concept choisi au départ pour indexer, on réessaye d'ajouter le conept d'origine
                            // on a ajouté un parent, il faut remonter pour essayer de l'ajouter à nouveau
                            addConceptOpentheso(resu['url'], resu['apitheso'], resu['id_thes_orig'], resu['name_orig'], resu['genre'], resu['id_thes_orig'], resu['name_orig'], resu['thesaurus'], resu['id_metadata'])
                        }
                    } else if (resu['ok'] === '0') {
                        // ajout du concept impossible, on remonte à son parent pour essayer de l'ajouter au thesaurus archeogrid
                        addConceptOpentheso(resu['url'], resu['apitheso'], resu['id_parent'], '', resu['genre'], resu['id_thes_orig'], resu['name_orig'], resu['thesaurus'], resu['id_metadata'])
                    }

                }).fail(function (jqXHR, textStatus, errorThrown) {
                    console.log("Erreur AJAX:", textStatus, errorThrown)
                    //alert('FAIL addConceptOpentheso in addConceptOpentheso')
                    return '0';
                })
            }

        }).fail(function () {
            alert('FAIL API opentheso on addConceptOpentheso')
            return '0';
        })
    }
    <%# préciser le genre: thesaurus pactols ou thesaurus multi pour récupérer la bonne table avec le bon element du dom %>
    function addkeyOpentheso(genre, thesaurus, qualifier, id_dom, identifier, id_metadata, metadata_function) {
        // on a utilisé l'API d'opentheso pour récupérer un id => ajout du mot-clé en 2 temps
        // 1 / est-ce que l'id est dans el thes en local ? si non : on l'ajoute
        // 2 / on ajoute l'indexation du terme
        let dataPost = {};
        let dom = genre ; //multi ou simple peuvent être opentheso
        dataPost['value'] = $(`#opentheso${dom}_function_${thesaurus}_${id_metadata}_value`).val()
        dataPost['_id_'] = $(`#opentheso${dom}_function_${thesaurus}_${id_metadata}_id`).val()
        // dans id on récupère d'opentheso : <identifier identifiant interne correspondant à l'id_thes>#<uri correspondant à la colonne identifier>

        let identifiers = $(`#opentheso${dom}_function_${thesaurus}_${id_metadata}_id`).val()
        dataPost['uri'] = identifiers.split('#')[1]
        dataPost['id_thes'] = identifiers.split('#')[0]
        dataPost['id'] = '000_'+ identifiers.split('#')[0]
        dataPost['thesaurus'] = thesaurus
        dataPost['qualifier'] = qualifier
        dataPost['id_metadata'] = id_metadata
        $.ajax({
            type: 'POST',
            url: '/checkeywordOpentheso,<%= root %>,<%= type %>,<%= idItem %>,<%= idFolder %>,' + genre,
            data: dataPost
        }).done(function (resu) {
            if (resu['ok'] == '1' ) {
                $.ajax({
                    type: 'POST',
                    url: '/addkeyword,<%= root %>,<%= type %>,<%= idItem %>,<%= idFolder %>,' + genre,
                    data: dataPost
                }).done(function (data) {
                    // si indexation multiple et que le concept est déjà indexé sur un des terme mais pas sur les autres,
                    // il ne faut pas le remonter sinon on croit à une erreur
                    if (<%= idItem %> !== 0){ //if (data[0] === '1') { data = true or false ...
                        if (data == true) {
                            // mettre à vide le champ pour taper..
                            alert('<%= __('keyword') %> <%= __('added') %>')
                            //$('#' + id_dom).val('')
                        }else {
                            alert('<%= __('keyword') %> <%= __('already') %> <%= __('indexed') %> ')
                        }
                    } else {
                        if (data['nberror']!== 0 ){
                            alert(data['nberror']+' error(s) on multiple indexing')
                        } else {
                            alert('Indexation multiple ok...'+data['success']+'<%= __('keyword') %>(s) <%= __('added') %>')
                        }
                    }
                    location.reload();
                }).fail(function () {
                    alert('ERROR')
                    location.reload();
                })
            } else { // on va essayer d'ajouter un item : depuis l'id récupéré on va chercher son parent avec l'api d'opentheso
                addConceptOpentheso(metadata_function.split('#')[0], metadata_function.split('#')[1], resu['id_thes'], resu['value'], genre, resu['id_thes'], resu['value'], resu['thesaurus'], id_metadata)
            }
        }).fail(function () {
            alert('ERROR on check concept')
        })

    }
    function addkey(genre, thesaurus, qualifier, id_dom, id_metadata) {
        let dataPost = {};
        let dom = genre === 'pactols' ? 'pactols' : genre;
        dataPost['value'] = $(`#opentheso${dom}_${thesaurus}_${id_metadata}_value`).val()
        dataPost['id'] =    $(`#opentheso${dom}_${thesaurus}_${id_metadata}_id`).val()
        dataPost['thesaurus'] = thesaurus
        dataPost['qualifier'] = qualifier
        dataPost['id_metadata'] = id_metadata

        $.ajax({
            type: 'POST',
            url: '/addkeyword,<%= root %>,<%= type %>,<%= idItem %>,<%= idFolder %>,' + genre,
            data: dataPost
        }).done(function (data) {
            // si indexation multiple et que le concept est déjà indexé sur un des terme mais pas sur les autres,
            // il ne faut pas le remonter sinon on croit à une erreur
            if (<%= idItem %> !== 0){ //if (data[0] === '1') { data = true or false ...
                if (data == true) {
                    // mettre à vide le champ pour taper..
                    alert('<%= __('keyword') %> <%= __('added') %>')
                    $('#' + id_dom).val('')
                }else {
                    alert('<%= __('keyword') %> <%= __('already') %> <%= __('indexed') %> ')
                }
            } else {
                // TODO : vérifier quelque chose ??? avec data  ?
                alert('Indexation multiple ... <%= __('keyword') %> <%= __('added') %>')
            }
            location.reload();
        }).fail(function () {
            alert('ERROR')
            location.reload();
        })
    }

    $(function () {
        <% for (let k = 0; k < metadata.length; k++) { %>
            <% if (metadata[k]['status'] === 'thesaurus') { %>
                $('#opentheso_<%= metadata[k]['list'] %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: function (request, response) {
                        $.ajax({
                            url: '/thesaurusName/<%= root %>,<%= metadata[k]['list'] %>,'+request.term,
                            dataType: "json",
                            success: function (data) {
                                response(data)
                            }
                        });
                    },
                    select: function (event, ui) {
                        $('#opentheso_<%= metadata[k]['list'] %>_id').val(ui.item.id);
                    }
                });
            <% } else if (metadata[k]['status'] === 'pactols') { %>
            <% if (metadata[k]['filter'] === undefined) { %>
                    $(`#openthesopactols_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value`).autocomplete({
                        minLength: 3,
                        autofocus: true,
                        source: function (request, response) {
                            $.ajax({
                                url: '/thesaurusPactolsName/<%= root %>,<%= metadata[k]['list'] %>,'+request.term,
                                dataType: "json",
                                success: function (data) {
                                    response(data)
                                }
                            });
                        },
                        select: function (event, ui) {
                            $('#openthesopactols_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id').val(ui.item.id);
                        }
                    });
            <% } else { %> <%# On cherche seulement sur une partie du thesaurus à partir du filtre %>
                $(`#openthesopactols_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value`).autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: function (request, response) {
                        $.ajax({
                            url: '/thesaurusPactolsName/<%= root %>,<%= metadata[k]['list'] %>,'+request.term,
                            dataType: "json",
                            success: function (data) {
                                response(data)
                            }
                        });
                    },
                    select: function (event, ui) {
                        $('#openthesopactols_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id').val(ui.item.id);
                    }
                });
            <% } %>
            <% } else if (metadata[k]['status'] === 'multi') { %>
        <% if (metadata[k]['function']) { %>
        // récupérer : le point de départ de l'url pour l'autocomplete via l'api
        // dans function on met https://opentheso.huma-num.fr/api/#th844 point de départ de l'api et nom du thesaurus chez opentheso
        // la langue => ajoutée dans la vue
        $('#openthesomulti_function_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value').autocomplete({
            minLength: 2,
            autofocus: true,
            source: function (request, response) {
                $.ajax({
                    url: '<%= metadata[k]['function'].split('#')[0]%>autocomplete?value='+request.term+'&theso=<%= metadata[k]['function'].split('#')[1] %>&lang=<%= language %>&group=&format=full',
                    dataType: "json",
                    success: function (data) {
                        response(data)
                    }
                });
            },
            select: function (event, ui) {
                $('#openthesomulti_function_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id').val(ui.item.identifier+'#'+ui.item.uri);
            }
        }).autocomplete("instance")._renderItem = function(ul, item) {
            return $("<li>")
                .append(`<div>${item.label} <span style="color: gray;">(${item.identifier})</span></div>`)
                .appendTo(ul)
        };

        <% } else { %>
                $('#openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: function (request, response) {
                        $.ajax({
                            url: '/thesaurusMultiName/<%= metadata[k]['list'] %>,'+request.term,
                            dataType: "json",
                            success: function (data) {
                                response(data)
                            }
                        });
                    },
                    select: function (event, ui) {
                        $('#openthesomulti_<%= metadata[k]['list'] %>_<%= metadata[k]['id'] %>_id').val(ui.item.id);
                    }
                });
        <% }%>
        <% }%>
    <% } %>
    })
</script>
<script>
    document.getElementById("passport").addEventListener("submit", async function (event) {
        event.preventDefault();

        const submitButton = event.target.querySelector('button[type="submit"]');
        submitButton.classList.add('loading');

        const requireCheck = document.querySelectorAll('[id^="error-message#"]');
        const errorElements = Array.from(requireCheck).map(element => element.id);

        errorElements.forEach(id => {
            document.getElementById(id).style.display = "none";
        });

        const lotErrorIndicator = document.getElementById('lot-error-indicator');
        if(lotErrorIndicator) {
            lotErrorIndicator.style.display = 'none';
        }

        const checkboxGroups = {};
        const requiredGroups = new Set();
        
        errorElements.forEach(id => {
            const metadataId = id.split('#')[1];
            requiredGroups.add(metadataId);
            checkboxGroups[metadataId] = document.querySelectorAll(`input[type="checkbox"][name$="#${metadataId}"]`);
        });
        
        let allRequiredGroupsValid = true;
        
        requiredGroups.forEach(metadataId => {
            if (checkboxGroups[metadataId].length > 0) {
                const atLeastOneCheckedInGroup = Array.from(checkboxGroups[metadataId]).some(checkbox => checkbox.checked);
                
                if (!atLeastOneCheckedInGroup) {
                    allRequiredGroupsValid = false;
                    document.getElementById(`error-message#${metadataId}`).style.display = "block";
                }
            }
        });

        let lotRadioValid = true;
        if (<%= !idItem %>) {
            const lotRadioButtons = document.querySelectorAll('input[type="radio"][name="lot"]');
            const isLotSelected = Array.from(lotRadioButtons).some(radio => radio.checked);
            if (!isLotSelected) {
                lotRadioValid = false;
                if(lotErrorIndicator) {
                     lotErrorIndicator.style.display = 'inline';
                }
            }
        }

        Array.from(document.querySelectorAll('div.mandatory-error-advanced-model.active')).forEach((prev_error) => prev_error.classList.remove('active'));
        
        let error_advanced_model = [];
        
        let datationRows = Array.from(document.querySelectorAll('.datationRow')).reduce((acc, el) => {
            const inputs = el.querySelectorAll('input');
            for(let input of inputs) {
                input.classList.remove('mandatory-error-advanced-model');
            }

            const id_metadata = el.id.split('_')[1];
            if(!acc[id_metadata]) {
                acc[id_metadata] = [];
            }
            acc[id_metadata].push(el);
            return acc;
        }, []).filter(el => el.length > 0);
        const atLeastOneCheckedDatation = datationRows.every((datationRowByMetadataId) => {
            for(let datationRow of datationRowByMetadataId) {
                let validity = datationRowIsValid(datationRow);
                if(! validity.valid) {
                    error_advanced_model.push({element: datationRow, message: validity.message});
                    return false;
                }
            }
            return true;
        });

        let locationRows = Array.from(document.querySelectorAll('.locationRow')).reduce((acc, el) => {
            const inputs = el.querySelectorAll('input');
            for(let input of inputs) {
                input.classList.remove('mandatory-error-advanced-model');
            }

            const id_metadata = el.id.split('_')[1];
            if(!acc[id_metadata]) {
                acc[id_metadata] = [];
            }
            acc[id_metadata].push(el);
            return acc;
        }, []).filter(el => el.length > 0);
        const atLeastOneCheckedLocation = locationRows.every((locationRowByMetadataId) => {
            for(let locationRow of locationRowByMetadataId) {
                let validity = locationRowIsValid(locationRow);
                if(!validity.valid) {
                    error_advanced_model.push({element: locationRow, message: validity.message});
                    return false;
                }
            }
            return true;
        });
        
        let actorRows = Array.from(document.querySelectorAll('.actorRow')).reduce((acc, el) => {
            const inputs = el.querySelectorAll('input');
            for(let input of inputs) {
                input.classList.remove('mandatory-error-advanced-model');
            }

            const id_metadata = el.id.split('_')[1];
            if(!acc[id_metadata]) {
                acc[id_metadata] = [];
            }
            acc[id_metadata].push(el);
            return acc;
        }, []).filter(el => el.length > 0);


        const actorValidityPromises = actorRows.map((actorRowByMetadataId) => { return Promise.all(actorRowByMetadataId.map(actorRow => actorRowIsValid(actorRow))) });
        const atLeastOneCheckedActor = await Promise.all(actorValidityPromises).then((actorValidityByMetadataId) => {
            let validity = true;
            for(let i = 0; i < actorValidityByMetadataId.length; i++) {
                for(let j = 0; j < actorValidityByMetadataId[i].length; j++) {
                    if(!actorValidityByMetadataId[i][j].valid) {
                        error_advanced_model.push({element: actorRows[i][j], message: actorValidityByMetadataId[i][j].message});
                        validity = false;
                    }
                }
            }
            return validity;
        });

        let scrolledTofirstError = false;
        for(let {element, message} of error_advanced_model) {
            const errorElement = element.querySelector('div.mandatory-error-advanced-model');
            const errorText = errorElement.querySelector('p');
            errorElement.classList.add('active');
            if(message !== 'default') {
                errorText.innerText = message;
            }
            if(!scrolledTofirstError) {
                scrolledTofirstError = true;
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        submitButton.classList.remove('loading');
        
        if (!allRequiredGroupsValid || !atLeastOneCheckedDatation || !atLeastOneCheckedLocation || !atLeastOneCheckedActor || !lotRadioValid) {
            return;
        }

        this.submit();
    });

    const rows = Array.from(document.querySelectorAll('form .row'));
    const optional_control_list = rows.map(el => {
        const check = el.querySelector('.optional-metadata-check');
        const content = el.querySelector('.metadata-content');

        return { check, content };
    }).filter(el => el.check && el.content);

    function update_metadata_active(control) {
        const { checked } = control.check;

        if (checked) {
            control.content.classList.remove("d-none");
        } else {
            control.content.classList.add("d-none");
        }

        const id = Number(control.check.id.split('#')[1]);

        const associated_value_elements = Array.from(
            control.check.parentElement.parentElement.parentElement.lastElementChild.querySelectorAll('input, select, textarea')
        ).filter(el => el.name);

        for (const element of associated_value_elements) {
            if (element.name.endsWith('_disabled')) {
                if (control.check.checked) {
                    element.name = element.name.split('_disabled').slice(0, -1).join('')
                }
            } else if (!control.check.checked) {
                element.name += '_disabled'
            }
        }
    }

    optional_control_list.forEach((control) => {
        control.check.addEventListener('change', () => {
            update_metadata_active(control);
        });

        const id_metadata = Number.parseInt(control.check.id.split('#')[1]);
        const metadata_item = metadata.find(m => m.id === id_metadata);
        
        let shouldBeChecked = false;

        const find_metadata_value = values.find(m => m.id_metadata ===  id_metadata);

        if (find_metadata_value) {
            if (metadata_item && metadata_item.status === 'choice' && !metadata_item.isunique && metadata_item.y) {
                shouldBeChecked = false;
            } else if (metadata_item && (metadata_item.status === 'actor' || metadata_item.status === 'datation' || metadata_item.status === 'location')) {
                const relevantValue = values.find(v => v.id_metadata === id_metadata);
                if (relevantValue && relevantValue.value && Array.isArray(relevantValue.value)) {
                    shouldBeChecked = relevantValue.value.some(item =>
                        Object.values(item).some(val =>
                            typeof val === 'string' && val.trim() !== ''
                        )
                    );
                } else {
                     shouldBeChecked = false;
                }
            } else if (metadata_item && metadata_item.status === 'multi') {
                const relevantMultival = multival.filter(item =>
                    item.multiinfo &&
                    item.multiinfo.id_metadata === id_metadata &&
                    item.multiinfo.model === model
                );
                shouldBeChecked = relevantMultival && relevantMultival.length > 0;
            } else {
                const relevantValue = values.find(v => v.id_metadata === id_metadata);
                 if (relevantValue && relevantValue.value) {
                    if (typeof relevantValue.value === 'string') {
                         shouldBeChecked = relevantValue.value.trim() !== '';
                    } else if (Array.isArray(relevantValue.value)) {
                         shouldBeChecked = relevantValue.value.some(val => typeof val === 'string' && val.trim() !== '');
                    } else {
                         shouldBeChecked = true;
                    }
                 }
            }
        }

        control.check.checked = shouldBeChecked;

        update_metadata_active(control);
    })
</script>

<!-- ADVANCED MODEL SCRIPTS -->
<script>
    function removeItem(event, item){
        event.preventDefault();
        item.remove();
    }
</script>

<!-- ACTORS SCRIPTS -->
<script>
    var activRadioInput = true;
    
    function addFieldsActor(event, metadata_id, metadata_isunique, actor_index){
        event.preventDefault();
        let nextIndex = Array.from(document.getElementsByClassName(`actorRow_${metadata_id}`))
        nextIndex = nextIndex.map(actor => actor.id.split("_")[2])
        nextIndex = nextIndex.reduce((a, b) => Math.max(a, b), 0) + 1;

        let actorTemplate = document.getElementById(`actorRow_${metadata_id}_${actor_index}`);
        activRadioInput = false;
        let newActor = actorTemplate.cloneNode(true);
        newActor.style.marginTop = "0.5rem";
        newActor.id = `actorRow_${metadata_id}_${nextIndex}`;
        newActor.setAttribute("required", "false");

        let a = newActor.querySelector("a.addActor");
        let i = a.querySelector("i");
        i.className = "fa fa-minus-circle";

        let errorElement = newActor.querySelector("div.mandatory-error-advanced-model");
        errorElement.classList.remove("active");

        let inputsText = newActor.querySelectorAll("input[type='text']");
        inputsText[0].value = "";
        inputsText[0].id = `${metadata_id}_${nextIndex}_literal`;
        inputsText[0].name = `${metadata_isunique}#actor_${metadata_id}_${nextIndex}`;
        inputsText[0].style.border = "";
        inputsText[1].value = "";
        inputsText[1].id = `${metadata_id}_${nextIndex}_identifier`;
        inputsText[1].name = `${metadata_isunique}#actor_${metadata_id}_${nextIndex}`;
        inputsText[1].style.border = "";

        let inputsRadio = newActor.querySelectorAll("input[type='radio']");
        inputsRadio[0].name = `${metadata_isunique}#actor_${metadata_id}_${nextIndex}`;
        inputsRadio[0].id =  `${metadata_id}_${nextIndex}_person-radio`;
        inputsRadio[1].name = `${metadata_isunique}#actor_${metadata_id}_${nextIndex}`;
        inputsRadio[1].id =  `actor${metadata_id}_${nextIndex}_organization-radio`;

        $(inputsText[0]).autocomplete({
            minLength: 2,
            source: function (request, response) {
                let actor_type = newActor.querySelector("input[type='radio']:checked").value;
                $.ajax({
                    url: "/actorAutoCompletion/" + encodeURIComponent(request.term) + ',<%= root %>,' + actor_type ,
                    dataType: 'json',
                    success: function(data) {
                        let result = [];
                        data.forEach(r=>{
                            result.push({label: r.name, value: r.name, uri: r.identifier})
                        });
                        response(result);
                    },
                    error: function (){
                        console.error("Error with auto-complete");
                    }
                });
            },
            select: function(event, ui) {
                $(this).next().next().val(ui.item.uri);
            }
        });
        
        let actorContainer = actorTemplate.parentNode;
        actorContainer.appendChild(newActor);
        
        a.onclick = (eventOnClick)=>{removeItem(eventOnClick, newActor)};
        activRadioInput = true;
    }

    function onchangeRadioActorType(event) {
        event.preventDefault();
        const radio = event.target
        if(!activRadioInput)
            return;

        const metadata_id = radio.name.split("#")[1].split('_')[1]
        const actor_index = radio.name.split("#")[1].split('_')[2] 
        let inputLiteral = document.getElementById( metadata_id + "_" + actor_index + "_literal")
        inputLiteral.value = ""
        let inputIdentifier = document.getElementById( metadata_id + "_" + actor_index + "_identifier")
        inputIdentifier.value = ""
    }

    $(document).ready(()=>{
        $('input.literalActorField').each(function (){
            
            let fieldset = $(this).parent().find("fieldset");
            let actor_identifier = $(this).parent().find("input.uriActorField");
            let actor_literal = this;
            let errorElement = $(this).parent().find("div.mandatory-error-advanced-model");

            $(this).autocomplete({
                minLength: 2,
                source: function (request, response) {
                    let actor_type = fieldset.find("input[type='radio']:checked").val()
                    $.ajax({
                        url: "/actorAutoCompletion/" + encodeURIComponent(request.term) + ',<%= root %>,' + actor_type ,
                        dataType: 'json',
                        success: function(data) {
                            let result = []
                            data.forEach(r=>{
                                result.push({label: r.name, value: r.name, uri: r.identifier})
                            })
                            response(result)
                        },
                        error: function (){
                            console.error("Error with auto-complete")
                        }
                    });
                },
                select: function(event, ui) {
                    actor_identifier.val(ui.item.uri);
                    actor_identifier.on('change focus', () => {
                        errorElement.find("p").text("<%= __('advanced_model.actor_modify_actor_warning') %>");
                        errorElement.addClass("active");
                    })
                },
                search: function (event, ui) {
                    errorElement.find("p").text("");
                    errorElement.removeClass("active");
                    actor_identifier.off('change focus');
                }
            });
        })
    })

    async function actorRowIsValid(actorRow){
        let actor_literal = actorRow.querySelector("input.literalActorField");
        let actor_uri = actorRow.querySelector("input.uriActorField");
        let required = actorRow.getAttribute("required") === "true";

        if(actor_literal.value === ""){
            if(actor_uri.value !== ""){
                actor_literal.classList.add('mandatory-error-advanced-model');
                return {valid: false, element: actorRow, message: "<%= __('advanced_model.actor_mandatory_error') %>"};
            }else{
                if(required){
                    actor_literal.classList.add('mandatory-error-advanced-model');
                    return {valid: false, element: actorRow, message: "<%= __('advanced_model.actor_mandatory_error') %>"};
                }
            }
        }

        if(actor_uri.value !== ""){
            let isValidURI = true;
            try {
                isValidURL = await fetch(`/isURLValid?url=${actor_uri.value}`, { method: "GET" }).then(response => response.json());
            } catch(error) {
                console.error(error);
            }
            if(!isValidURL){
                actor_uri.classList.add('mandatory-error-advanced-model');
                return {valid: false, element: actorRow, message: "<%= __('advanced_model.actor_invalidUri_error') %>"};
            }
        }
        return {valid: true, element: actorRow, message: ""};
    }
</script>

<!-- DATATION SCRIPTS -->
<script>
    class CustomDateInput extends HTMLInputElement {
        constructor() {
            super();
            this.type = 'text';
            this.addEventListener('input', this.validateDate);
        }

        validateDate() {
            const datePatternAMJ = /^-?\d{4}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/;
            const datePatternAM  = /^-?\d{4}-(0[1-9]|1[0-2])$/;
            const datePatternA   = /^-?\d{4}$/;
            if (datePatternAMJ.test(this.value) || datePatternAM.test(this.value) || datePatternA.test(this.value)) {
                this.style.border = '';
                return true;
            } else {
                return false;
            }
        }

        static get observedAttributes() {
            return ['value'];
        }

        attributeChangedCallback(name, oldValue, newValue) {
            if (oldValue !== newValue) {
                this.validateDate();
            }
        }
    }

    customElements.define("date-bc", CustomDateInput, { extends: "input" });

    function addFieldsDatation(event, metadata_id, metadata_isunique, datation_index){
        event.preventDefault();
        let nextIndex = Array.from(document.getElementsByClassName(`datationRow_${metadata_id}`))
        nextIndex = nextIndex.map(datation => datation.id.split("_")[2])
        nextIndex = nextIndex.reduce((a, b) => Math.max(a, b), 0) + 1;

        let datationTemplate = document.getElementById(`datationRow_${metadata_id}_${datation_index}`);
        let newDatation = datationTemplate.cloneNode(true);
        newDatation.style.marginTop = "0.5rem";
        newDatation.id = `datationRow_${metadata_id}_${nextIndex}`;
        newDatation.setAttribute("required", "false");

        let errorElement = newDatation.querySelector("div.mandatory-error-advanced-model");
        errorElement.classList.remove("active");

        let a = newDatation.querySelector("a.addDatation");
        let i = a.querySelector("i");
        i.className = "fa fa-minus-circle";

        let inputs = newDatation.querySelectorAll("input");
        for(input of inputs){
            input.value = "";
            let newId = input.id.split('_');
            newId[2] = nextIndex;
            input.id = newId.join('_');
            let newName = input.name.split('_');
            newName[2] = nextIndex;
            input.name = newName.join('_');
            input.classList.remove('mandatory-error-advanced-model');
        }

        let select = newDatation.querySelector("select");
        select.querySelector("option").setAttribute('selected', true);
        let newId = select.id.split("_");
        newId[2] = nextIndex;
        select.id = newId.join("_");
        let newName = select.name.split("_");
        newName[2] = nextIndex;
        select.name = newName.join("_");

        let datationContainer = datationTemplate.parentNode;
        datationContainer.appendChild(newDatation);

        a.onclick = (eventOnClick)=>{removeItem(eventOnClick, newDatation)};
    }

    function datationRowIsValid(datationRow){
        const datation_date_min = datationRow.querySelector("input.dateMinDatationField");
        const datation_date_max = datationRow.querySelector("input.dateMaxDatationField");
        const datation_date_literal = datationRow.querySelector("input.dateliteralDatationField");
        const datation_id_periodo = datationRow.querySelector("select.periodoDatationField");
        const dateMinValidity = CustomDateInput.prototype.validateDate.call(datation_date_min);
        const dateMaxValidity = CustomDateInput.prototype.validateDate.call(datation_date_max);
        let required = datationRow.getAttribute("required") === "true";

        let isEmpty = datation_date_min.value == "" && datation_date_max.value == "" && datation_date_literal.value == "" && datation_id_periodo.value == "";

        if(isEmpty){
            if(required){
                datation_date_min.classList.add('mandatory-error-advanced-model');
                return {valid: false, message: "<%= __('advanced_model.datation_mandatory_error') %>"};
            }else{
                return {valid: true, message: ""};
            }
        }else{
            if(datation_date_min.value === ""){
                datation_date_min.classList.add('mandatory-error-advanced-model');
                return {valid: false, message: "<%= __('advanced_model.datation_mandatory_error') %>"};
            }
            if(!dateMinValidity){
                datation_date_min.classList.add('mandatory-error-advanced-model');
                return {valid: false, message: "<%= __('advanced_model.datation_invalidDate_error') %>"};
            }
            if(!dateMaxValidity && datation_date_max.value !== ""){
                datation_date_max.classList.add('mandatory-error-advanced-model');
                return {valid: false, message: "<%= __('advanced_model.datation_invalidDateMax_error') %>"};
            }
            return {valid: true, message: ""};
        }
        return {valid: true, message: ""};
    }
</script>

<!-- LOCATION SCRIPT -->
<script>    

function addFieldsLocation(event, metadata_id, metadata_isunique, location_index){
    event.preventDefault();
    let nextIndex = Array.from(document.getElementsByClassName(`locationRow_${metadata_id}`))
    nextIndex = nextIndex.map(location => location.id.split("_")[2])
    nextIndex = nextIndex.reduce((a, b) => Math.max(a, b), 0) + 1;

    let locationTemplate = document.getElementById(`locationRow_${metadata_id}_${location_index}`);
    let newLocation = locationTemplate.cloneNode(true);
    newLocation.style.marginTop = "0.5rem";
    newLocation.id = `locationRow_${metadata_id}_${nextIndex}`;
    newLocation.setAttribute("required", "false");

    let errorElement = newLocation.querySelector("div.mandatory-error-advanced-model");
    errorElement.classList.remove("active");

    let a = newLocation.querySelector("a.addLocation");
    let i = a.querySelector("i");
    i.className = "fa fa-minus-circle";

    let inputs = newLocation.querySelectorAll("input");
    for(input of inputs){
        input.value = "";
        let newId = input.id.split('_');
        newId[2] = nextIndex;
        input.id = newId.join('_');
        let newName = input.name.split('_');
        newName[2] = nextIndex;
        input.name = newName.join('_');
        input.classList.remove('mandatory-error-advanced-model');
    }

    let ignoreButton = newLocation.querySelector("button.btn-ignore-mandatory-error");
    ignoreButton.addEventListener("click", toggleIgnoreMandatoryError);

    let locationContainer = locationTemplate.parentNode;
    locationContainer.appendChild(newLocation);

    a.onclick = (eventOnClick) => {removeItem(eventOnClick, newLocation)};
}

function locationRowIsValid(locationRow){
    const locationRow_id = locationRow.id.split("_")[1] + "_" + locationRow.id.split("_")[2];

    const locations_inputs = locationRow.querySelectorAll("input");
    const isEmpty = Array.from(locations_inputs).every(input => input.value === "");

    if(isEmpty){
        const required = locationRow.getAttribute("required") === "true";
        if(required){
            return {valid: false, message: "<%= __('advanced_model.location_mandatory_error') %>"};
        }
    }else{
        const geonames = locationRow.querySelector("input.geonamesLocationField");
        const geonamesValidity = !isNaN(Number(geonames.value)) || geonames.value == "";

        if(!geonamesValidity){
            geonames.classList.add('mandatory-error-advanced-model');
            return {valid: false, message: "<%= __('advanced_model.location_geonames_error') %>"};
        }
    }
    return {valid: true, message: ""};
}
</script>

<script src="/js/redirect.js"></script>

<script>
    setupRedirection({
        inputId: 'reqUrl',
        cancelBtnId: 'cancel-link',
        serverUrl: '<%= locals.reqUrl || "" %>',
        defaultUrl: 'projects',
        excludedPrefixes: ['edit']
    });
</script>
