/* Lazy Metadata Loading Styles */

/* Loading state for metadata */
.item-title.metadata-loading {
    opacity: 0.6;
    position: relative;
}

.item-title.metadata-loading::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 5px;
    border: 2px solid #ddd;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: metadata-spin 1s linear infinite;
    vertical-align: middle;
}

/* Animation for loading spinner */
@keyframes metadata-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Successfully loaded metadata */
.item-title.metadata-loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* No title found from metadata */
.item-title.metadata-no-title {
    opacity: 0.8;
    font-style: italic;
}

/* Error loading metadata */
.item-title.metadata-error {
    opacity: 0.7;
    color: #dc3545;
}

/* Smooth transitions for all metadata states */
.item-title {
    transition: opacity 0.2s ease, color 0.2s ease;
}

/* Ensure text content updates smoothly */
.vitrine-text.item-title {
    min-height: 1.2em;
    display: inline-block;
} 