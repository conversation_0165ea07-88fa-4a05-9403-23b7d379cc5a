<table class="table" id="searches-table">
    <thead>
    <tr>
        <th scope="col">#</th>
        <th scope="col"><%= __('name') %></th>
        <th scope="col"><%= __('project') %></th>
        <th scope="col"><%= __('date') %></th>
        <th scope="col"><%= __('link') %></th>
        <th scope="col"><%= __('Delete') %></th>
    </tr>
    </thead>
    <tbody>
        <% for (let s in searches) { %>
            <% const project = searches[s].project %>
            <% const search = encodeURIComponent(JSON.stringify(searches[s].search)) %>
            <% const date = new Date(searches[s].date) %>
            <tr>
                <th scope="row"><%= parseInt(s) + 1 %></th>
                <td><%= searches[s].name %></td>
                <td><%= project.name %></td>
                <td><time datetime="<%= searches[s].date %>"><%= date.getDate()+'/'+(date.getMonth()+1)+'/'+date.getFullYear()+' '+date.getHours()+':'+date.getMinutes() %></time></td>
                <td><a href="/search/<%= project.id %>?search=<%= search %>" target="_blank"> <i class="fas fa-external-link-alt"></i></a></td>
                <td><button type="button" class="btn btn-secondary erase-search" searchid="<%= searches[s].id %>"><i class="fas fa-trash-alt"></i></button></td>
            </tr>
        <% } %>
    </tbody>
</table>

<script>
    $('#searches-table .erase-search').on('click', function () {
        $.ajax({
            url: '/removeSearch/' + $(this).attr('searchid')
        })

        $(this).parent().parent().remove()
        $('th[scope="row"]').each(function (index) { // change les indices de la colomne de gauche
            $(this).text(index+1)
        })
    })
</script>