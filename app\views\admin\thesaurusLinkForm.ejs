<div>
    <h3><%= __('admin.thesaurus.link') %></h3>
    <% if (!projects.pft3d.length && !projects.corpus.length) { %>
        <h4><%= __('scribe.noAssignedProjects') %></h4>
    <% } else { %>
    <form class="form-horizontal" id="addgroup" action="/admin/thesaurusLink" method="get">
        <div class="d-flex align-items-center gap-2">
            <label for="branch" class="form-label m-0" style="min-width: 200px;"><%= __('admin.content.BranchSelection') %></label>
            <select class="form-select" id="select-branch" aria-describedby="branch" name="branch">
                <% for (const branch of branches) { %>
                    <% if (branch.branchename !== "conservatoire3d" && projects[branch.branchename].length) { %>
                        <option value="<%= branch.branchename %>"><%= branch.description %></option>
                    <% } %>
                <% } %>
            </select>
        </div>
        <div class="d-flex align-items-center justify-content-center my-3 gap-2">
            <label for="project" class="form-label m-0" style="min-width: 200px;"><%= __('admin.content.thesaurus.link.form.project') %></label>
            <select class="d-none form-select" id="select-project-pft3d" aria-describedby="project" name="project">
                <% for (const project of projects.pft3d) { %>
                    <option value="<%= project.id %>"><%= project.name %></option>
                <% } %>
            </select>
            <select class="d-none form-select" id="select-project-corpus" aria-describedby="project" name="project">
                <% for (const project of projects.corpus) { %>
                    <option value="<%= project.id %>"><%= project.name %></option>
                <% } %>
            </select>
        </div>
        <div class="d-flex align-items-center justify-content-center my-3 gap-2">
            <label for="type" class="form-label m-0" style="min-width: 200px;"><%= __('admin.content.thesaurus.link.form.type') %></label>
            <select class="form-select" id="select-type" aria-describedby="type" name="type">
                <option value="simple">simple</option>
                <option value="multi">multi</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary"><%= __('submit') %></button>
    </form>
    <% } %>
</div>

<script>
    const branch = document.getElementById('select-branch');
    const pft3d = document.getElementById('select-project-pft3d')
    const corpus = document.getElementById('select-project-corpus')

    const update_value = (value) => {
        pft3d.classList.add('d-none');
        corpus.classList.add('d-none');
        pft3d.name = '';
        corpus.name = '';
        if (value === "pft3d") {
            pft3d.classList.remove('d-none');
            pft3d.name = 'project';
        } else if (value === "corpus") {
            corpus.classList.remove('d-none');
            corpus.name = 'project';
        }
    }

    branch.addEventListener('change', (e) => {
        console.log(e.target.value);
        const { value } = e.target;
        update_value(value);
    });

    update_value(branch.value);
</script>