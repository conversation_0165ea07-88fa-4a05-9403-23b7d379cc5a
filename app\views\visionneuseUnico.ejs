<% const url = `/crop/${folderId}_${unico.id_file}/${unico.x},${unico.y},${unico.width},${unico.height}` %>
<%- include('unicoModal') %>

<% if ((context === 'p') || (context === 'd')) { %>
    <div class="row">
        <div class="col-2">
            <% if (mainFolder['mainfolder'] === '5535') { %>
                <img src="../assets/images/nd-archeogrid.png" style="height: 8em;margin-left:-3em;margin-bottom: -2em;margin-top: -1em;" >
            <% } else { %>
                <img src="../assets/images/ArcheoGRID.png" style="height: 3em;" >
            <% } %>
        </div>
        <div class="col-8  text-center">

        </div>
        <div class="col-2" id="menuDroit">
        </div>
    </div>
    <hr>
<% } %>
    <div class="container">
        <div class="text-center">
            <% if ( viewerFormat.indexOf(image['file_ext'].toLowerCase()) !== -1 ) { %>
            <a href="<%- url %>">
                <% if (unico.type === 'rect') { %>
                <img class="<% if (context === 'm') {%>card-img <% } %>mx-auto d-block" src="<%= `/crop/${folderId}_${idFile}/${unico.x},${unico.y},${unico.width},${unico.height}` %>"
                     <% if (context === 'd') {%>style="border: black 1px solid;"<% } %>>
                <% } else if (unico.type === 'poly') { %>
                <% let points = unico.polygon.split(' ') %>
                <% for (let i in points) { let e = points[i].split(','); e[0] = parseInt(e[0]) - unico.x; e[1] = parseInt(e[1]) - unico.y; points[i] = e.join(',') } %>
                <% points = points.join(' ') %>
                <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width="20vw" height="40vh" viewBox="0 0 <%- unico.width %> <%- unico.height %>" id="unico-svg-<%- unico.id %>" class="<% if (context === 'm') {%>card-img <% } %>mx-auto d-block">
                    <mask id="svgmask-<%- unico.id %>">
                        <polygon fill="#ffffff" points="<%- points %>"/>
                    </mask>
                    <image xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="<%= `/crop/${folderId}_${idFile}/${unico.x},${unico.y},${unico.width},${unico.height}` %>" width="<%- unico.width %>" height="<%- unico.height %>" mask="url(#svgmask-<%- unico.id %>)"/>
                </svg>
                <% } %>
            </a>
            <% } else { %>
            <img class="<% if (context === 'm') {%>card-img <% } %>mx-auto d-block" src="<%= `/crop/${folderId}_${idFile}/${unico.x},${unico.y},${unico.width},${unico.height}` %>">
            <% } %>

            <br>
            <p style="text-align: center"><strong>Unico : </strong><%= unico['name'] %></p>
            <br>

        </div>
        <div class="row text-center">
            <div class="col-4"><%# on affiche l'image mère ? %>

                <a href="/visionneuse,<%= image['id'] %>-<%= folderId %>,<%= branch %>-p,0"><h3><%=__('sourceFile')%></h3>
                    <img src="/thumb/<%= folderId %>_<%= idFile %>" class="img_respARCHEOGRID" style="width: 5em;"/></a>
            </div>
            <div class="col-4">
                <% if (user.id === 2000) { %>
                    <% if (mainFolder['id_metadata_model']) { %>
                        <%# TODO later %>
                        <a href="/comment,<%= branch %>,<%= folderId %>,<%= unico['id'] %>,unico" type="button" class="btn btn-secondary" title="<%= mainFolder['description'] %>"><i class="far fa-comments"></i> <%=__('note')%> / <%= mainFolder['label']%></a>
                    <% } else { %>
                        <a href="/comment,<%= branch %>,<%= folderId %>,<%= unico['id'] %>,unico" type="button" class="btn btn-secondary" title="<%=__('note')%>" ><i class="far fa-comments"></i></a>
                    <% } %>
                <% } %>
            </div>
            <div class="col-4" id="menuDroit">
                <% if ((user.id === unico.id_user) || (user.user_status === 'admin')) { %>
                    <% if (context === 'p') { %> 
                        <%# seulement si on est sur une page car les unicos sont mis à jour dans une modale et il y a un problème pour récupérer les mot-clés de thesaurus si on ouvre une modale dans un modale.. %>
                        <button type="button" class="btn btn-secondary btn-sm" unico-id="<%= unico.id %>" onclick="editUnico(<%= unico.id %>)" style="width: 48px; height: 48px;">
                            <i class="fa-lg fas fa-edit"></i>
                        </button>
                    <% } %>
                <% } %>
            </div>

            
            <% if (((user.id === unico.id_user) || (user.user_status === 'admin')) && context === 'p') { %>
                <div class="col-4">
                    <a href="#" class="btn btn-secondary"
                    title="Copy" onclick="copyMetadataItem('<%= root %>', 'object', '<%= idObj %>')"><i class="far fa-copy"></i> <%=__('copyMetadata')%></a>
                </div>
            <% }%>

            <% if (((user.id === unico.id_user) || (user.user_status === 'admin')) && context === 'p') { %>
                <div class="col-4">
                    <a href="#" class="btn btn-secondary"
                    title="Paste" onclick="pasteMetadataItem('<%= root %>', 'object', '<%= idObj %>', '<%= folderId %>', '<%= language %>')"><i class="fas fa-copy"></i> <%=__('pasteMetadata')%></a>
                </div>
            <% }%>
        </div>

        <div id="alerts" class="d-flex justify-content-center">
            <div class="alert alert-success mb-2 alert-success" role="alert" style="display: none;"><%=__('unicoDeleteSuccess')%></div>
            <div class="alert alert-danger mb-2 alert-notconnected" role="alert" style="display: none;"><%=__('disconnected')%></div>
            <div class="alert alert-danger mb-2 alert-forbidden" role="alert" style="display: none;"><%=__('forbidden')%></div>
            <div class="alert alert-danger mb-2 alert-notfound" role="alert" style="display: none;"><%=__('notFound')%></div>
            <div class="alert alert-danger mb-2 alert-error" role="alert" style="display: none;"><%=__('error')%></div>
        </div>

    </div>

    <hr>

<div style="display: none; visibility: hidden">
        <table class="table">
            <tbody>
                    <tr id-unico="<%= unico.id %>">
                        <th scope="row"><%= 1 %></th>
                        <td><%= unico.name %></td>
                        <td><%= unico.annotation %></td>
                        <td>
                            <ul style="padding-left: 0%;">
                            <% for (const thesaurus of thesaurus_data) { %>
                                <li>
                                    <div style="font-weight: bold;"><%= thesaurus.name %> :</div>
                                    <% for (const tag of thesaurus.tags) { %>
                                        <span class="pb-1 pr-1">
                                            <span class="tag p-1 my-1">
                                                <span class="tag-value pr-1" thesaurus="<%= thesaurus.thesaurus %>" status="<%= thesaurus.status %>" id="<%= tag.id %>">
                                                    <%= tag.name %>
                                                </span>
                                            </span>
                                        </span>
                                    <% } %>
                                </li>
                            <% } %>
                            </ul>
                        </td>
                        <td></td>
                        <td><a href="<%-url%>">
                            <img src="<%-url%>" style="max-width: 150px; max-height: 150px; object-fit: contain;">
                        </a></td>
                    </tr>

            </tbody>
        </table>
</div>

<div class="container">


        <%# tag %>
        <% if (image['tag'].length > 0 ) { %>
            <%= __('keywords')%> :
            <% for (var i = 0; i < image['tag'].length; i++ ) { %>
                <button class="btn"><%= image['tag'][i] %></button>
            <% } %>

        <% } %>

<p><strong><%= __('creationDate')%> <%=__('of') %> <%=__('the3')%> unico : %></strong><% const date = new Date(unico.creation_date); let mystring %>
            <% mystring = date.getFullYear()+'/'+('0'+ (date.getMonth()+1)).slice(-2) +'/'+ ('0'+ date.getDate()).slice(-2)  %>
            <%= mystring %>
</p>
<p><strong>Annotation : </strong><%=unico.annotation%>
</p>

<% for (const thesaurus of thesaurus_data) { %>
    <div>
        <span class="fw-bold"><%= thesaurus.name ?? (thesaurus.thesaurus.charAt(0).toUpperCase() + thesaurus.thesaurus.slice(1)) %> :</span>
        <% const status = thesaurus.status %>
        <div class="d-inline-flex gap-2">
        <% for (const tag of thesaurus.tags) { %>
            <span class="p-1 tag rounded d-flex gap-1 align-items-center justify-content-center">
                <a href="/concept/<%= tag.name %>?rootF=<%= mainFolder['mainfolder']%>&type=<%=status%>&idThes=<%= tag.id.split('_')[1] %>&thesaurus=<%= thesaurus.thesaurus %>" >
                    <span class="tag-value pr-1" thesaurus="<%= thesaurus.thesaurus %>" status="<%= thesaurus.status %>" id="<%= tag.id %>"><%= tag.name %></span>
                </a>
                <% if (user.id === unico.id_user || user.user_status === 'admin') { %>
                    <% if (thesaurus.status === 'pactols') { %>
                        <span class="btn btn-light btn-sm" title="<%=__('delete')%>" onclick="delete_thespactols_item('<%= branch %>', '<%= id %>', 'unico', '<%= tag['id'].split('_')[0]%>', '<%= tag['id'].split('_')[1]%>', '<%= thesaurus.thesaurus%>')">
                            <i class="far fa-trash-alt"></i>
                        </span>
                    <% } else if (thesaurus.status === 'geopactols') { %>
                        <span class="btn btn-light btn-sm" title="<%=__('delete')%>" onclick="delete_thespactolsgeo_item('<%= branch %>', '<%= id %>', 'unico', '<%= tag['id'].split('_')[0]%>')">
                            <i class="far fa-trash-alt"></i>
                        </span>
                    <% } %>
                <% } %>
            </span>
        <% } %>
        </div>
    </div>
<% } %>



    <% if (comments.length > 0) { %>
        <br><h4><%= comments.length %> <%=__('note')%><% if (comments.length > 1) {%>s<% }%></h4>
            <% for (let i = 0; i <  comments.length; i++) { %>
                <dl class="row">
                <dt class="col-sm-3"><strong><%=__('content')%></strong></dt><dd class="col-sm-9"><%- comments[i]['content'] %></dd>
                </dl><dl class="row">
                <dt class="col-sm-3"><strong><em><%=__('author')%></em></strong></dt><dd class="col-sm-9"><%= comments[i]['signature']%></dd>
                </dl><% if (comments[i]['author'].length > 0 ) {%><dl class="row"><dt class="col-sm-3"><strong><em><%=__('inNameOf')%>:</em></strong></dt><dd class="col-sm-9"><%= comments[i]['author']%></dd></dl><% } %>
            <dl class="row">
                <dt class="col-sm-3"><strong><%=__('date')%></strong></dt><dd class="col-sm-9"><%= comments[i]['date']%></dd>
                </dl><% if (comments[i]['other_date'].length > 0 ) {%><dl class="row">
                <dt class="col-sm-3"><strong><%=__('dateExplicite')%>: </strong></dt><dd class="col-sm-9"><%- (comments[i]['other_date']) %></dd></dl><% } %>
                </dl><% if (comments.length > 1) {%><hr><% } %>
            <% }%>
    <% } %>
    </div>
</div>

<% if ((context === 'p') || (context === 'd') ) { %>
</div>
<div class="loader"></div>
<% } %>