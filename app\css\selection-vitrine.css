/* Main container styles */
.selection-content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.selection-content-container .row {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
}

/* Selection header styles */
.selection-header {
    padding: 0;
    margin: 0;
}

.selection-header .dropdown-menu-end {
    right: 0;
    left: auto;
}

.selection-header .dropdown-item {
    cursor: pointer;
}

.selection-header .dropdown-toggle {
    white-space: nowrap;
}

/* Explore div styles */
#explore-div {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    height: fit-content !important;
    max-height: calc(79vh - 20px) !important;
    position: relative !important;
    top: 0 !important;
    padding-top: 0 !important;
    margin-top: 0 !important;
}

#explore-results {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    margin-bottom: 2rem !important;
}

/* Virtual folder container styling */
.virtual-folder-container {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 1rem;
    border: 1px solid #dee2e6;
    max-height: fit-content;
    overflow-y: auto;
    position: sticky;
    top: 10px;
}

/* Disabled form elements */
.disabled-select,
select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.65;
}

.disabled-button,
input[type="submit"]:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed !important;
    opacity: 0.65;
}

/* Enabled submit buttons should have pointer cursor */
input[type="submit"]:not(:disabled) {
    cursor: pointer;
}

/* Lazy loading styles */
.lazy-image {
    transition: opacity 0.3s;
    opacity: 0.5;
}

.lazy-image[src^="/thumb/"],
.lazy-image[src^="/small/"],
.lazy-image[src^="/crop/"] {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 950px) {
    .selection-content-container .row {
        flex-direction: column;
    }

    .selection-content-container .col-md-8,
    .selection-content-container .col-md-4 {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
    }

    .virtual-folder-container {
        margin-top: 1rem;
        position: static;
        max-height: none;
    }
}
