<%- include('utils/title-content', { project: mainFolder, projectId: mainFolder.mainfolder, home: "home" }) %>

<!-- Main -->
<div id="main" class="container-fluid">
    <% if (idItem !== '0') { %>
    <%# visualisation de l'item à indexer %>
    <% if ((type === 'object') && (!item['id_file_representative'])) { %>
        <div class="text-center"><img class="card-img mx-auto d-block" src="/assets/images/default_repre_image_object.png" alt="<%= idItem%>">
            <p><%=__('no2')%> <%=__('visualization')%> <%=__('for')%> <%=__('object2')%></p>
        </div>
    <% } else if((type === 'unico') ){ %>
        <div class="text-center">
            <a href="/visionneuseUnicoV2,<%= idItem %>,<%= idFolder %>,<%= root %>-v,0" target="_blank">
            <% if(item.type === 'poly' ){ %>
                <% let points = item.polygon.split(' ') %>
                <% for (let i in points) { let e = points[i].split(','); e[0] = parseInt(e[0]) - item.x; e[1] = parseInt(e[1]) - item.y; points[i] = e.join(',') } %>
                <% points = points.join(' ') %>
                <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                        style="max-width: 200px; max-height: 200px; object-fit: scale-down;"
                        viewBox="0 0 <%- item.width %> <%- item.height %>" id="unico-svg-<%- item.id %>">
                    <mask id="svgmask-<%- item.id %>">
                        <polygon fill="#ffffff" points="<%- points %>" />
                    </mask>
                    <image xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="<%= `/crop/${idFolder}_${item.id_file}/${item.x},${item.y},${item.width},${item.height}` %>" alt="<%= item['name']%>"
                            width="<%- item.width %>" height="<%- item.height %>"
                            mask="url(#svgmask-<%- item.id %>)" />
                </svg>
            <% }else{ %>
                <img class="mx-auto d-block" src="<%= `/crop/${idFolder}_${item.id_file}/${item.x},${item.y},${item.width},${item.height}` %>"
                     alt="<%= item['name']%>" style="max-width: 200px; max-height: 200px; object-fit: scale-down;">
            <% } %>
            </a>
        </div>
    <% } else { %>
        <a href="<% if ( type === 'object') { %>/visionneuseObj,<%= idItem %>,<%= item['id_file_representative'] ? item['id_file_representative'] : 0 %>-<%= item['id_folder_obj'] %>,<%= root %>-v,0<% } else { %>/visionneuse,<%= idItem %>-<%= idFolder %>,<%= root %>-v,0<% } %>" target="_blank">
            <img class="card-img mx-auto d-block" src="/small/<% if ( type === 'object') { %><%= item['id_folder_obj'] %>_<%= item['id_file_representative'] %><% } else { %><%= idFolder%>_<%= idItem %><% } %>" alt="">
        </a>
    <% } %>
        <h4 class="text-center">
            <%=__('addKeyword.title')%> <%=__('for')%>
            <% if ( type === 'object') { %>
                <%=__('object2') %>
                <a href="visionneuseObj,<%= idItem %>,<%= item['id_file_representative'] ? item['id_file_representative'] : 0 %>-<%= idFolder%>,<%= root %>-v,0"><%= item['name']%></a>
            <% } else if ( type === 'file'){ %>
                <%=__('file2') %>
                <a href="visionneuse,<%= idItem %>-<%= idFolder%>,<%= root %>-v,0"><%= item['filename'] %></a>
            <% } else if ( type === 'unico'){ %>
                <%=__('the3') %>unico
                <%= item['name'] %>
            <% } else { %>
                <%= idItem %>
            <% } %>
        </h4>
    <% } else { %>
        <h4><%=__('add')%>&nbsp;<%=__('keywords') %> <%=__('for')%> <strong><%=__('allfolderitems') %></strong></h4>
        <h4 style="text-align: center;">
            <%= item['name']%>
            <br>
            (<%= item['root_base']%>)
        </h4>
    <% } %>
</div>

<div class="selector-addingMode" style="width: 80%; margin: auto; margin-bottom: 2rem;">
    <fieldset>
        <legend><%= __('addKeyword.addingMode') %></legend>

        <label for="addingMode-scratch"><%= __('addKeyword.addFromScratch.title') %></label> 
        <input type="radio" name="addingMode" value="scratch" id="addingMode-scratch" class="addingMode" style="margin-right: 1.5rem;" checked>

        <label for="addingMode-metadata"><%= __('addKeyword.addFromMetadata.title') %></label>
        <input type="radio" name="addingMode" value="metadata" id="addingMode-metadata" class="addingMode">
    </fieldset>
</div>

<div class="form-addingMode-container" style="width: 80%; margin: auto;">
    <div id="form-addingMode-scratch" class="form-addingMode">
        <%- include('addKeyword/addFromScratch.ejs', {}) %>
    </div>

    <div id="form-addingMode-metadata" class="form-addingMode d-none">
        <%- include('addKeyword/addFromMetadata.ejs', {}) %>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const addingModeSelector = document.querySelectorAll('input.addingMode');
        for(const selector of addingModeSelector) {
            selector.addEventListener('change', () => {
                const formAddingMode = document.querySelectorAll('.form-addingMode');
                for(const form of formAddingMode) {
                    form.classList.add('d-none');
                }
                document.getElementById(`form-addingMode-${selector.value}`).classList.remove('d-none');

                if(selector.value === 'metadata') {
                    const container = document.getElementById("addingMode-metadata-container");

                    const contentCards = document.querySelectorAll('.step-addingMode-metadata .content');
                    for(const card of contentCards) {
                        card.style.width = `${container.offsetWidth / 2}px`;
                    }
                }
            });
        }
    });

    // préciser le genre: thesaurus pactols ou thesaurus multi pour récupérer la bonne table avec le bon element du dom
    // si le genre est null c'est opentheso de base (ancestralement le premier à être défini comme ça
    function addkey(genre, thesaurus, id_dom) {
        let dataPost = {};
        let dom = genre;

        console.log(id_dom)
        // TODO : pouvoir indexer avec le thesaurus normal (table <%= root %>_thesaurus)
        if (genre === 'pactols') dom = ''
        dataPost['value'] = $('#opentheso' + dom + '_' + thesaurus + '_value').val()
        dataPost['id'] = $('#opentheso' + dom + '_' + thesaurus + '_id').val()
        dataPost['thesaurus'] = thesaurus

        $.ajax({
            type: 'POST',
            url: '/addkeyword,<%= root %>,<%= type %>,<%= idItem %>,<%= idFolder %>,' + genre,
            data: dataPost
        })
        .done(function (data) {
            console.log(data)
            // si indexation multiple et que le concept est déjà indexé sur un des terme mais pas sur les autres,
            // il ne faut pas le remonter sinon on croit à une erreur
            if (<%= idItem %> !== 0) {
                if (data) {
                    if (genre === 'thesaurus') {
                        alert('<%= __('keywordUpdated') %>')
                    } else {
                        alert('<%= __('keyword') %> <%= __('added') %>')
                    }
                    // dans tous les cas, mettre à vide le champ pour taper..
                    $('#' + id_dom).val('')
                } else alert('<%= __('keyword') %> <%= __('already') %> <%= __('indexed') %> ')
            } else {
                // TODO : vérifier quelque chose ??? avec data  ?
                alert('Indexation multiple ... <%= __('keyword') %> <%= __('added') %>')
            }
        })
        .fail(function () {
            alert('ERROR')
        })
    }


    $(function () {
        <% for (let k = 0; k < thes.length; k++) { %>
            <% if (thes[k]['status'] === 'thesaurus') { %>
                $('#openthesothesaurus_<%= thes[k].thesaurus %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: (request, response) => {
                        $.ajax({
                            url: '/thesaurusSimpleName/<%= root %>,<%= thes[k].thesaurus %>,'+request.term,
                            dataType: "json",
                            success: (data) => { response(data.map(el => { return { id: el.id, label: el.value, value: el.name } })) }
                        });
                    },
                    select: (event, ui) => { $('#openthesothesaurus_<%= thes[k].thesaurus %>_id').val(ui.item.id) }
                });
            <% } else if (thes[k]['status'] === 'multi') { %>
                $('#openthesomulti_<%= thes[k].thesaurus %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: (request, response) => {
                        $.ajax({
                            url: '/thesaurusMultiName/<%= thes[k].thesaurus %>,'+request.term,
                            dataType: "json",
                            success: (data) => { response(data.map(el => { return { id: el.id, label: el.value, value: el.name } })) }
                        });
                    },
                    select: (event, ui) => { $('#openthesomulti_<%= thes[k].thesaurus %>_id').val(ui.item.id) }
                });
            <% } else if (thes[k]['status'] === 'pactols') { %>
                $('#opentheso_<%= thes[k].thesaurus %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: (request, response) => {
                        $.ajax({
                            url: '/thesaurusPactolsName/<%= thes[k].thesaurus %>,'+request.term,
                            dataType: "json",
                            success: (data) => { response(data) }
                        });
                    },
                    select: (event, ui) => {
                        $('#opentheso_<%= thes[k].thesaurus %>_id').val(ui.item.id);
                    }
                });

            <% } else if (thes[k]['status'] === 'geopactols') { %>
                $('#openthesogeopactols_<%= thes[k].thesaurus %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: (request, response) => {
                        $.ajax({
                            url: 'https://pactols.frantiq.fr/opentheso/api/autocomplete/' + request.term + '?lang=fr&theso=th17',
                            dataType: "json",
                            success: (data) => { response(data) }
                        });
                    },
                    select: (event, ui) => { $('#openthesogeopactols_<%= thes[k].thesaurus %>_id').val(ui.item.uri) }
                });

            <% } else if (thes[k]['status'] === 'chrono') { %>
                $('#openthesochrono_<%= thes[k].thesaurus %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: (request, response) => {
                        $.ajax({
                            url: '/thesaurusPactolsName/<%= root %>,<%= thes[k].thesaurus %>,'+request.term,
                            dataType: "json",
                            success: (data) => { response(data) }
                        });
                    },
                    select: (event, ui) => { $('#openthesochrono_<%= thes[k].thesaurus %>_id').val(ui.item.id) }
                });
            <% } %>
        <% } %>
    })
</script>