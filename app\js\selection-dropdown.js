function updateCheckboxState() {
    const checkbox = document.getElementById('selectPageCheckbox');
    if (!checkbox) return;

    const allItems = document.querySelectorAll('.check_item');
    const checkedItems = document.querySelectorAll('.check_item.checked');

    if (allItems.length > 0) {
        if (checkedItems.length === allItems.length) {
            checkbox.checked = true;
            checkbox.indeterminate = false;
        } else if (checkedItems.length > 0) {
            checkbox.indeterminate = true;
        } else {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        }
    } else {
        checkbox.checked = false;
        checkbox.indeterminate = false;
    }
}

// Store dropdown instance globally to prevent multiple initializations
let dropdownInstance = null;
let isInitialized = false;

function initializeSelectionDropdown() {
    const container = document.getElementById('selectionDropdownContainer');
    const checkbox = document.getElementById('selectPageCheckbox');
    const formCheckDiv = document.querySelector('.selection-dropdown .form-check');

    if (!container || !checkbox || !formCheckDiv) {
        return false;
    }

    // Check if the container is visible
    const exploreDiv = document.getElementById('explore-div');
    if (exploreDiv && window.getComputedStyle(exploreDiv).display === 'none') {
        return false;
    }

    // Prevent multiple initializations
    if (isInitialized && dropdownInstance) {
        return true;
    }

    try {
        // Dispose of existing instance if it exists
        if (dropdownInstance) {
            dropdownInstance.dispose();
        }

        // Create new Bootstrap dropdown instance
        dropdownInstance = new bootstrap.Dropdown(container);
        isInitialized = true;

        updateCheckboxState();
        setTimeout(updateCheckboxState, 500);

        // Set up mutation observer for checkbox state updates
        const observer = new MutationObserver(function(mutations) {
            let needsUpdate = false;
            for (const mutation of mutations) {
                if (mutation.type === 'attributes') {
                    if ((mutation.target.classList && mutation.target.classList.contains('check_item')) || mutation.target === checkbox) {
                        needsUpdate = true;
                        break;
                    }
                } else if (mutation.type === 'childList') {
                    const hasRelevantNodeChange = (nodes) => {
                        for (const node of nodes) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (node.matches && node.matches('.check_item')) return true;
                                if (node.id === 'explore-results' && (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)) return true;
                                if (node.querySelector && node.querySelector('.check_item')) return true;
                            }
                        }
                        return false;
                    };

                    if (hasRelevantNodeChange(mutation.addedNodes) || hasRelevantNodeChange(mutation.removedNodes)) {
                        needsUpdate = true;
                        break;
                    }
                }
            }

            if (needsUpdate) {
                updateCheckboxState();
            }
        });

        const exploreResultsContainer = document.getElementById('explore-results');
        if (exploreResultsContainer) {
            observer.observe(exploreResultsContainer, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'checked']
            });
        } else {
            console.warn("[selection-dropdown.js] #explore-results container not found for MutationObserver. Falling back to document.documentElement. This may impact performance.");
            observer.observe(document.documentElement, { // Fallback, less performant
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'checked']
            });
        }

        let wasIndeterminate = false;

        checkbox.addEventListener('mousedown', function() {
            wasIndeterminate = this.indeterminate;
        });

        // Handle click events on the checkbox
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation();
            if (wasIndeterminate) {
                e.preventDefault();
                this.checked = false;
                this.indeterminate = false;

                setTimeout(function() {
                    uncheckAll();
                    updateCheckboxState();
                }, 0);

                wasIndeterminate = false;
            } else {
                setTimeout(function() {
                    if (checkbox.checked) {
                        checkAll();
                    } else {
                        uncheckAll();
                    }
                    updateCheckboxState();
                }, 0);
            }

            const dropdownMenu = document.querySelector('.selection-dropdown-menu.show');
            if (dropdownMenu && dropdownInstance) {
                dropdownInstance.hide();
            }
        });

        formCheckDiv.addEventListener('click', function(e) {
            e.stopPropagation();

            const dropdownMenu = document.querySelector('.selection-dropdown-menu.show');
            if (dropdownMenu && dropdownInstance) {
                dropdownInstance.hide();
            }
        });

        container.addEventListener('click', function(e) {
            if (!e.target.closest('.form-check') && dropdownInstance) {
                dropdownInstance.toggle();
            }
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('#selectionDropdownContainer') &&
                !e.target.closest('.dropdown-menu')) {
                const dropdownMenu = document.querySelector('.selection-dropdown-menu.show');
                if (dropdownMenu && dropdownInstance) {
                    dropdownInstance.hide();
                }
            }
        });

        const dropdownItems = document.querySelectorAll('.selection-dropdown-menu .dropdown-item');
        dropdownItems.forEach(item => {
            item.addEventListener('click', function() {
                if (item.parentElement.id !== 'save-selection' && dropdownInstance) {
                    dropdownInstance.hide();
                }
            });
        });

        return true;

    } catch (error) {
        console.error('[selection-dropdown] Error initializing dropdown:', error);
        isInitialized = false;
        dropdownInstance = null;
        return false;
    }
}

// Initialize the selection dropdown when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSelectionDropdown();

    // Watch for explore-div to become visible and reinitialize if needed
    const exploreDiv = document.getElementById('explore-div');
    if (exploreDiv) {
        const exploreObserver = new MutationObserver(function(mutations) {
            for (const mutation of mutations) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const isNowVisible = window.getComputedStyle(exploreDiv).display !== 'none';
                    if (isNowVisible && !isInitialized) {
                        setTimeout(() => {
                            initializeSelectionDropdown();
                        }, 100); // Small delay to ensure DOM is ready
                    }
                }
            }
        });

        exploreObserver.observe(exploreDiv, {
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }
});

// Global function to reinitialize dropdown when needed
window.reinitializeSelectionDropdown = function() {
    isInitialized = false;
    if (dropdownInstance) {
        try {
            dropdownInstance.dispose();
        } catch (e) {
            console.warn('[selection-dropdown] Error disposing existing instance:', e);
        }
        dropdownInstance = null;
    }
    return initializeSelectionDropdown();
};

// Create a custom event for selection changes
document.addEventListener('selectionChanged', function() {
    updateCheckboxState();
});

// Update checkbox state after page load
$(document).ajaxComplete(function(_, __, settings) {
    if (settings.url.includes('exploreFolderVitrinePage') ||
        settings.url.includes('exploreSelectionVitrinePage') ||
        settings.url.includes('exploreUnicosPage') ||
        settings.url.includes('exploreSearchPage') ||
        settings.url.includes('exploreThesaurusPage')) {
        setTimeout(updateCheckboxState, 100);
        
        // Also reinitialize dropdown if it's not working
        setTimeout(() => {
            if (!isInitialized) {
                initializeSelectionDropdown();
            }
        }, 200);
    }
});
