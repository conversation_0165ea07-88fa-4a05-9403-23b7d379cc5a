<%- include('utils/title-content', { home: "home" }) %>

<!-- Hidden fields for JavaScript -->
<input type="hidden" id="user-id" value="<%= user ? user.id : '' %>">
<input type="hidden" id="project-id" value="<%= typeof projectId !== 'undefined' ? projectId : '' %>">
<input type="hidden" id="language" value="<%= lng || 'en' %>">

<script src="/js/searchable-dropdown.js"></script>
<script src="/js/vitrine-search.js"></script>
<script src="/js/archeogrid.js"></script>
<script src="/js/lib/imagesloaded.pkgd.min.js"></script>
<script src="/js/lib/masonry.pkgd.min.js"></script>
<script src="/js/mobile-vitrine.js"></script>
<script src="/js/selection-dropdown.js"></script>
<script src="/js/lazy-loading.js"></script>
<script src="/js/size-sliders.js"></script>
<script src="/js/folders-tree-vitrine.js"></script>
<script src="/js/thesaurus.js"></script>
<script src="/js/search.js"></script>

<link rel="stylesheet" href="/css/mobile-vitrine.css">
<link rel="stylesheet" href="/css/selection-dropdown.css">
<link rel="stylesheet" href="/css/size-sliders.css">
<link rel="stylesheet" href="/css/folders-tree-vitrine.css">
<link rel="stylesheet" href="/css/thesaurus-vitrine.css">

<div class="d-flex justify-content-between" style="height: 79vh;" id="GROS">

    <div id="menuGauche" class="hide-on-mobile d-flex flex-column justify-content-between col-3"
         style="max-height: none; overflow: visible; width: calc(25% + 20px);">

        <div id="left-top" class="d-flex flex-column gap-2">
            <!-- Navigation Links -->
            <div class="container">
                <a href="/thesaurusPactolsV/<%= root %>,<%= projectId %>" class="btn btn-outline-primary btn-sm mb-2">
                    <%=__('browsePACTOLS')%>
                </a>
            </div>

            <!-- Thesauri List -->
            <% if ((typeof info !== 'undefined' && info === 'yes') || (typeof infothesaurus !== 'undefined' && infothesaurus === 'yes')) { %>
                <div class="container">
                    <h2 class="mb-2"><%=__('browseThesauri')%></h2>
                    <ul class="list-group list-group-flush justify-content-end" style="display: inline-block;">
                        <% if (typeof thesaurus !== 'undefined') { %>
                            <% for (const t of thesaurus) { %>
                                <% if (t.visible !== '0') { %>
                                    <% if (t.thesaurus === 'Periodo') { %>
                                        <a href="#" id="<%= t.thesaurus %>" onclick="console.log('Clicking on Periodo'); getThesaurusTreePeriodo('<%= root %>');">
                                            <i class="far fa-clock fa-2x me-1" aria-hidden="true"></i><%= __('period') %>
                                            <span class="badge badge-pill ml-auto" style="color: black;"><%='('+t.nb_tot_item+')'%></span>
                                        </a>
                                    <% } else { %>
                                        <a class="list-group-item list-group-item-action MainPageThesaurus" href="#" id="<%= t.thesaurus %>"
                                           onclick="loadAndDisplayThesaurusTree('<%= root %>','<%= projectId %>', '<%= t.thesaurus %>','<%= t.id_thes %>','multi');">
                                            <% if (t.thesaurus !== 'nd_th13') { %>
                                                <% if (t.thesaurus === 'lrmh') { %>
                                                    <% if (lng === 'fr') { %>
                                                        <img src="/assets/images/lrmh.png" style="height: 2em;">Collection <%= t.name %>
                                                    <% } else { %>
                                                        <img src="/assets/images/lrmh.png" style="height: 2em;"><%= t.short_name %>collection
                                                    <% } %>
                                                <% } else { %>
                                                    <i class="fa fa-book me-1" aria-hidden="true"></i><%= t.name %>
                                                <% } %>
                                            <% } else if (t.thesaurus === 'nd_th13') { %>
                                                <% if (lng === 'fr') { %>
                                                    <i class="fa fa-university fa-2x me-1" aria-hidden="true"></i><%= t.name %> (complet)
                                                <% } else { %>
                                                    <i class="fa fa-university fa-2x me-1" aria-hidden="true"></i><%= t.label %> (complete)
                                                <% } %>
                                            <% } %>
                                            <span class="badge badge-pill text-dark"><%='('+t.nb_tot_item+')'%></span>
                                        </a>
                                    <% } %>
                                <% } %>
                            <% } %>
                        <% } %>

                        <% if (typeof thesaurusSimple !== 'undefined') { %>
                            <% for (const s of thesaurusSimple) { %>
                                <a class="list-group-item list-group-item-action MainPageThesaurus" href="#" id="<%= s.thesaurus %>"
                                   onclick="loadAndDisplayThesaurusTree('<%= root %>','<%= projectId %>', '<%= s.thesaurus %>','<%= s.id_thes %>','simple');">
                                    <% if (lng === 'fr') { %>
                                        <i class="fa fa-book me-1" aria-hidden="true"></i><%= s.name %>
                                    <% } else { %>
                                        <i class="fa fa-book me-1" aria-hidden="true"></i><%= s.label %>
                                    <% } %>
                                    <span class="badge badge-pill text-dark"><%='('+s.nb_tot_item+')'%></span>
                                </a>
                            <% } %>
                        <% } %>
                    </ul>
                </div>
            <% } %>




        </div>

        <div id="left-bot">
            <div class="row">
                <div class="col-12 d-flex align-items-center justify-content-left">
                    <button id="back-to-thesaurus-btn" onclick="hideThesaurusTree()">
                        <i class="fas fa-arrow-left"></i> <%=__('back')%>
                    </button>

                    <h4 class="mb-2" style="margin-left: 10px;"><%=__('browseThesaurus')%></h4>
                </div>
            </div>

            <!-- Searchable Dropdown for Thesaurus Navigation -->
            <div id="thesaurus-dropdown-container" class="container mb-3" style="display: none;">
                <label for="thesaurus-folder-dropdown" class="form-label"><%=__('search')%></label>
                <select id="thesaurus-folder-dropdown" class="searchable-select form-control" data-simple-mode="false">
                    <option value="search" data-type="search"><%=__('searchAllThesaurus')%></option>
                </select>
            </div>

            <div id="dynamic-thesaurus-tree" style="min-height: 50px;">
            </div>
        </div>
    </div>

    <div class="col-12 col-md-9" id="menuCentre" style="display: none;">
        <% if (locals.flash && locals.flash.ok) { %>
        <div class="m-2 alert alert-success text-capitalize">
            <%= flash.ok %>
        </div>
        <% } %>

        <%- include('explore/exploreVitrine') %>
    </div>

</div>

<!-- Modals for thesaurus trees (when needed) -->
<% if (typeof thesaurus !== 'undefined') { %>
    <% for (let d = 0; d < thesaurus.length; d++) { %>
        <% if (thesaurus[d].visible !== '0' && thesaurus[d].name && thesaurus[d].name.indexOf('[') === -1) { %>
        <div class="modal" id="modal_thesaurusTree_<%= thesaurus[d].id || thesaurus[d].id_thes %>" role="dialog">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h5 class="modal-title"><%= thesaurus[d].name || thesaurus[d].label %></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="modal-tree-<%= thesaurus[d].id || thesaurus[d].id_thes %>" style="max-height: 60vh; overflow-y: auto;">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden"><%=__('loading')%>...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <% } %>
    <% } %>
<% } %>

<!-- Scripts -->
<script src="/js/explore.js"></script>
<script src="/js/selectionVitrine.js"></script>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="thesaurus-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toastElement = document.getElementById("thesaurus-toast");
        if (toastElement && typeof bootstrap !== 'undefined') {
            bootstrap.Toast.getOrCreateInstance(toastElement);
        }

        // Initialize toast variables for selection functionality
        // Wait a bit to ensure DOM is fully loaded
        setTimeout(function() {
            const toastElement = document.getElementById("thesaurus-toast");
            if (toastElement) {
                window.toastContent = toastElement.querySelector(".toast-body");
                window.projectToast = bootstrap.Toast.getOrCreateInstance(toastElement);
            }
        }, 100);

        $('.modal').on('show.bs.modal', function (event) {
            const modal = $(this);
            const modalId = modal.attr('id');
            const thesaurusId = modalId.replace('modal_thesaurusTree_', '');

            const treeContainer = modal.find('[id^="modal-tree-"]');
            if (treeContainer.length && treeContainer.html().indexOf('spinner-border') > -1) {
                setTimeout(function() {
                    treeContainer.html('<p>Tree structure would be loaded here for thesaurus: ' + thesaurusId + '</p>');
                }, 500);
            }
        });
    });
</script>

<!-- Standalone global functions -->
<script>
    if (!window.loadAndDisplayThesaurusTree) {
        window.loadAndDisplayThesaurusTree = function(root, projectId, thesaurus, id_thes, type) {
            const menuCentre = document.getElementById('menuCentre');
            const exploreDiv = document.getElementById('explore-div');
            // Keep menuCentre hidden by default - only show when user clicks on a folder
            if (menuCentre) menuCentre.style.display = 'none';
            if (exploreDiv) exploreDiv.style.cssText = 'display: none !important;';

            setTimeout(function() {
                if (window.loadAndDisplayThesaurusTreeFull) {
                    window.loadAndDisplayThesaurusTreeFull(root, projectId, thesaurus, id_thes, type);
                }
            }, 100);
        };
    }

    if (!window.getThesaurusTreeMulti) {
        window.getThesaurusTreeMulti = function(root, projectId, thesaurus, id_thes, userId) {
            window.loadAndDisplayThesaurusTree(root, projectId, thesaurus, id_thes, 'multi');
        };
    }

    if (!window.getThesaurusTree) {
        window.getThesaurusTree = function(root, projectId, thesaurus, id_thes, userId) {
            window.loadAndDisplayThesaurusTree(root, projectId, thesaurus, id_thes, 'simple');
        };
    }

    if (!window.getThesaurusTreePeriodo) {
        window.getThesaurusTreePeriodo = function(root) {
            const menuCentre = document.getElementById('menuCentre');
            if (menuCentre) menuCentre.style.display = 'block';
            const exploreResults = document.getElementById('explore-results');
            if (exploreResults) {
                exploreResults.innerHTML = '<div class="alert alert-info">Periodo thesaurus functionality coming soon...</div>';
            }
        };
    }
</script>

<!-- Server-side variables -->
<script>
    window.SERVER_DATA = {
        USER_READ: <%- JSON.stringify((user && user.read) ? user.read : []) %>,
        USER_STATUS: '<%= (user && user.user_status) ? user.user_status : "guest" %>',
        LNG: '<%= lng || "en" %>',
        USER_WRITE: <%- JSON.stringify((user && user.write) ? user.write : []) %>,
        PROJECT_ID: '<%= typeof projectId !== "undefined" ? projectId : "" %>'
    };
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof window.createScrollToTopButton === 'function') {
            window.createScrollToTopButton();
            if (typeof window.updateScrollToTopButtonVisibility === 'function') {
                window.updateScrollToTopButtonVisibility();
                window.addEventListener('scroll', window.updateScrollToTopButtonVisibility);
            }
        }

        const resultsDiv = document.getElementById('explore-results');
        if (resultsDiv) {
            resultsDiv.innerHTML = '<div class="alert alert-info">Select a thesaurus from the left menu to explore its content.</div>';
        }

        const dynamicTreeContainer = document.getElementById('dynamic-thesaurus-tree');
        if (dynamicTreeContainer) {
            dynamicTreeContainer.innerHTML = ''; // Always start empty
        }
    });

    function showThesaurusTree() {
        const leftTop = document.getElementById('left-top');
        const leftBot = document.getElementById('left-bot');
        const backButton = document.getElementById('back-to-thesaurus-btn');
        const dynamicTree = document.getElementById('dynamic-thesaurus-tree');

        if (leftTop) leftTop.classList.add('hidden');
        if (leftBot) leftBot.classList.add('expanded');
        if (backButton) backButton.classList.add('show');
        if (dynamicTree) dynamicTree.classList.add('expanded');
    }

    function hideThesaurusTree() {
        const leftTop = document.getElementById('left-top');
        const leftBot = document.getElementById('left-bot');
        const backButton = document.getElementById('back-to-thesaurus-btn');
        const dynamicTree = document.getElementById('dynamic-thesaurus-tree');
        const menuCentre = document.getElementById('menuCentre');
        const dropdownContainer = document.getElementById('thesaurus-dropdown-container');

        if (leftTop) leftTop.classList.remove('hidden');
        if (leftBot) leftBot.classList.remove('expanded');
        if (backButton) backButton.classList.remove('show');
        if (dynamicTree) {
            dynamicTree.classList.remove('expanded');
            dynamicTree.innerHTML = '';
        }
        if (menuCentre) menuCentre.style.display = 'none';
        if (dropdownContainer) {
            dropdownContainer.style.display = 'none';
            // Clear the dropdown options except the search option
            const dropdown = document.getElementById('thesaurus-folder-dropdown');
            if (dropdown) {
                const searchOption = dropdown.querySelector('option[data-type="search"]');
                dropdown.innerHTML = '';
                if (searchOption) {
                    dropdown.appendChild(searchOption);
                }
            }
        }

        // Clear the thesaurus context
        if (window.currentThesaurusContext) {
            window.currentThesaurusContext.thesaurus = null;
            window.currentThesaurusContext.type = null;
        }
    }

    window.loadAndDisplayThesaurusTreeFull = function(root, projectId, thesaurus, id_thes, type) {
        const apiUrl = type === 'multi'
            ? `/getThesaurusTreeDataMultiVitrine/${root},${thesaurus},${id_thes}`
            : `/getThesaurusTreeDataVitrine/${root},${thesaurus},${id_thes}`;

        // Store the current thesaurus context for search and other operations
        window.currentThesaurusContext = {
            thesaurus: thesaurus,
            type: type
        };

        const menuCentre = document.getElementById('menuCentre');
        const exploreDiv = document.getElementById('explore-div');

        // Keep menuCentre hidden by default - only show when user clicks on a folder
        if (menuCentre) {
            menuCentre.style.display = 'none';
        }

        if (exploreDiv) {
            exploreDiv.style.cssText = 'display: none !important;';
        }

        showThesaurusTree();

        fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                renderThesaurusTree(data, thesaurus, type);

                // Initialize the search functionality after the thesaurus tree is rendered
                if (typeof window.initThesaurusSearch === 'function') {
                    setTimeout(function() {
                        window.initThesaurusSearch();
                    }, 200);
                }
            })
            .catch(error => {
                console.error('Error loading thesaurus tree:', error);
                const container = document.getElementById('dynamic-thesaurus-tree');
                if (container) {
                    container.innerHTML = '<div class="alert alert-danger">Error loading thesaurus tree</div>';
                }
            });
    };

    window.loadAndDisplayThesaurusTree = window.loadAndDisplayThesaurusTreeFull;

    // Thesaurus-specific exploreSearch function that keeps menuCentre visible
    window.exploreSearchThesaurus = function(id, searchParameters) {
        if (typeof cleanupPreviousContent === 'function') {
            cleanupPreviousContent();
        }

        $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
        $("#explore-div").css("display", "flex");
        // Keep menuCentre visible in thesaurus views since explore-results is inside it
        $("#menuCentre").show();

        window.explorePage = {
            url: window.exploreSearchUrl || function(page) {
                return `/exploreSearchPage/${id},${page}?display=${window.display || 'grid'}&search=${searchParameters}&limit=0`;
            },
            id: id,
            search: searchParameters,
        };

        if (typeof exploreNb === 'function') {
            exploreNb(`/exploreSearchNb/${id}?search=${searchParameters}`);
        }
    };

    // Initialize the VitrineSearch module for thesaurus when needed
    window.initThesaurusSearch = function() {
        const projectId = document.getElementById('project-id').value;
        if (projectId && typeof VitrineSearch !== 'undefined') {
            VitrineSearch.init({
                projectId: projectId,
                searchSelectId: 'thesaurus-folder-dropdown',
                folderTreeSelector: '.folders-tree',
                exploreResultsSelector: '#explore-results',
                exploreDivSelector: '#explore-div',
                menuCentreSelector: '#menuCentre',
                searchFunction: 'exploreSearchThesaurus',
                debounceDelay: 350,
                minSearchLength: 2,
                advancedSearchPath: '/search/',
                keepMenuCentreVisible: true // Keep menuCentre visible for thesaurus views
            });
        }
    };


</script>