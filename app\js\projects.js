// Initialize selection from localStorage
if (typeof storedSelectionData === 'undefined') {
  var storedSelectionData = localStorage.getItem("selection") || "[]";
}
if (typeof selection === 'undefined') {
  var selection = new Set(JSON.parse(storedSelectionData));
}
if (typeof integrate_fr === 'undefined') {
  var integrate_fr = "Intégrer les items de la sélection dans un dossier virtuel : ";
}
if (typeof integrate_en === 'undefined') {
  var integrate_en = "Integrate selected items in a virtual folder: ";
}
if (typeof link_obj_fr === 'undefined') {
  var link_obj_fr = "Lier, associer les items de la sélection à un objet : ";
}
if (typeof link_obj_en === 'undefined') {
  var link_obj_en = "Link, associate selected items to an object: ";
}
if (typeof check_fr === 'undefined') {
  var check_fr = "tout cocher";
}
if (typeof check_en === 'undefined') {
  var check_en = "Check all";
}
if (typeof uncheck_fr === 'undefined') {
  var uncheck_fr = "tout décocher";
}
if (typeof uncheck_en === 'undefined') {
  var uncheck_en = "Uncheck all";
}
if (typeof annotate_fr === 'undefined') {
  var annotate_fr = "Indexer";
}
if (typeof annotate_en === 'undefined') {
  var annotate_en = "Index";
}
if (typeof confirmRemoveSelection_fr === 'undefined') {
  var confirmRemoveSelection_fr = "Supprimer la sélection ?";
}
if (typeof confirmRemoveSelection_en === 'undefined') {
  var confirmRemoveSelection_en = "Delete Selection ?";
}
if (typeof confirmDeleteAll_fr === 'undefined') {
  var confirmDeleteAll_fr = "Supprimer tout le contenu de ce dossier virtuel ?";
}
if (typeof confirmDeleteAll_en === 'undefined') {
  var confirmDeleteAll_en = "Delete all items in this virtual folder ?";
}

if (typeof toastElement === 'undefined') {
  var toastElement = document.getElementById("project-toast");
}
if (typeof toastContent === 'undefined') {
  // Ensure toastElement is defined before trying to use it
  var toastContent = typeof toastElement !== 'undefined' && toastElement ? toastElement.querySelector(".toast-body") : undefined;
}
if (typeof projectToast === 'undefined') {
    var projectToast;
}

if (typeof bootstrap !== 'undefined' && toastElement && typeof projectToast === 'undefined') {
    projectToast = bootstrap.Toast.getOrCreateInstance(toastElement);
}

// Flag to ensure drag selection is initialized only once
if (typeof dragSelectionInitialized === 'undefined') {
    var dragSelectionInitialized = false;
}

function removeTagP(title) {
  let text = "";
  if (title) {
    if (title.indexOf("<p>") !== -1) {
      text = title.replace("<p>", "").replace("</p>", "");
    } else {
      text = title;
    }
  }
  return text;
}

function getProjectDetails(userId, folderId, lng, status, root) {
  let code = "";
  if (lng === "fr") code = "Code projet :";
  if (lng === "en") code = "Project code :";

  let html = "";
  let tag = [];
  const listModel = [];
  let dataModel = {};
  let accessRight = false;

  if (folderId !== 0 && typeof folderId !== "undefined") {
    $.ajax({
      url: "tag," + root + ",folder," + folderId,
    }).done(dataTag => {
      tag = dataTag;

      $.ajax({
        url: "accessRights/" + userId + "," + folderId + "," + root,
      }).done(dat => {
        if (dat.rights) accessRight = true;

        $.ajax({
          url: "tabloidProject," + folderId + "," + root,
        }).done(data => {
          $.ajax({
            url: "model/" + root,
          }).done(datam => {
            for (let i = 0; i < datam.length; i++) {
              if (datam[i].metadata_type === "folder") {
                listModel.push(datam[i].name);
              }
            }
            dataModel = datam;

            $("#tabloid").empty();
            if (data.metadata.length > 0) {
              if (data.metadata[0].rank === 1) {
                html = "<h3>" + data.metadata[0].value + "</h3>";
              } else {
                html = "<h3>" + data.name + "</h3>";
              }
              if (data.representativePictureId !== 0) {
                //l'image existe on la lit et on l'affiche
                html +=
                  '<div class="container-fluid" style="text-align: center">' +
                  '<img src="assets/' +
                  data.url +
                  data.folder_name +
                  '.jpg" ' +
                  'class="img-thumbnail" alt="' +
                  data.name +
                  '"></div>';
              }
              html += '<br><p style="text-align: center"><strong>' + code + "</strong> " + data.name + "</p>";
              for (let ind = 0; ind < dataModel.length; ind++) {
                if (dataModel[ind].name === "project") {
                  html += '<div class="card"><h4>';
                  // ATTENTION : il faut être sccibe pour modifier les metadonnées
                  // if (accessRight && (status !== 'guest')) {
                  if (accessRight && (status === "admin" || status === "scribe")) {
                    html +=
                      '<a href="/edit,' +
                      root +
                      ",project,folder," +
                      folderId +
                      "," +
                      folderId +
                      '" class="float-right" title="Indexer"><i class="fas fa-edit"></i></a>';
                  }
                  html += dataModel[ind].description + "</h4>";
                }
              }
              html +=
                '<table class="table table-striped">' +
                "<thead>" +
                "<tr>" +
                '<th style="width: 30%"></th>' +
                '<th style="width: 70%"></th>' +
                "</tr>" +
                "</thead>" +
                "<tbody>";
              for (let i = 1; i < data.metadata.length; i++) {
                if (data.metadata[i].value !== "" && data.metadata[i].query === "y") {
                  if (data.metadata[i].value) {
                    if (data.metadata[i].label === "Latitude" || data.metadata[i].label === "Longitude") {
                      continue;
                    }
                    if (data.metadata[i].status === "link") {
                      html += '<tr><td style="text-align: right">' + "<strong>" + data.metadata[i].label + "</strong></td>";
                      html +=
                        '<td><a href="' +
                        data.metadata[i].value +
                        '" target="_blank">' +
                        " " +
                        data.metadata[i].value +
                        "</a></td></tr>";
                    } else if (data.metadata[i].status === "map") {
                      // creer la map
                      const name = "mapDemo" + data.metadata[i].id_metadata;
                      const lat = data.metadata[i].value.split(",")[0];
                      const lng = data.metadata[i].value.split(",")[1];
                      html += '<tr><td style="text-align: right"><strong>';
                      html += data.metadata[i].label + "</strong></td>";
                      html +=
                        '<td><div id="mapDemo' +
                        data.metadata[i].id_metadata +
                        '" style="height:300px;"></div>' +
                        "<script>initMapGeoloc(" +
                        name +
                        "," +
                        lat +
                        "," +
                        lng +
                        ");</script>";
                    } else {
                      html += '<tr><td style="text-align: right"><strong>';
                      html += data.metadata[i].label + "</strong></td>";
                      html += "<td>" + data.metadata[i].value + " </td></tr>";
                    }
                  }
                }
              }
              if (tag.length > 0) {
                html += '<tr><td style="text-align: right"><strong>Tag</strong></td><td>';
                for (let t = 0; t < tag.length; t++) {
                  html += tag[t].name + " ";
                }
                html += "</td>";
              }
            } else {
              html = "<h3>" + data.name + "</h3>";
              if (data.representativePictureId !== 0) {
                html +=
                  '<div class="container" style="text-align: center">' +
                  '<img src="assets/' +
                  data.url +
                  data.folder_name +
                  '.jpg" ' +
                  //'<img src="assets/root_projects/' + data['folder_name'] + '/' +
                  //data['folder_name'] + '.jpg" class="img-thumbnail" alt="' +
                  'class="img-thumbnail" alt="' +
                  data.name +
                  '"></div>';
              }
              html += '<br><p style="text-align: center"><strong>' + code + "</strong> " + data.name + "</p>";
              for (let ind = 0; ind < dataModel.length; ind++) {
                if (dataModel[ind].name === "project") {
                  html += '<div class="card"><h4>';
                  //  ATTENTION : il faut être sccibe pour modifier les metadonnées
                  // if (accessRight && (status !== 'guest')) {
                  if (accessRight && (status === "admin" || status === "scribe")) {
                    html +=
                      '<a href="/edit,' +
                      root +
                      ",project,folder," +
                      folderId +
                      "," +
                      folderId +
                      '" class="float-right" title="Indexer">' +
                      '<i class="fas fa-edit"></i></a>';
                  }
                  html += dataModel[ind].description + "</h4></div>";
                }
              }
            }
            html += "</tbody></table></div>";
            $("#tabloid").removeClass("container-fluid");
            $("#tabloid").addClass("container");
            $("#tabloid").append(html);
          });
        });
      });
    });
  } else {
    $("#tabloid").append("");
  }

  updateMenuGauche(root, userId, folderId, lng, status);
}

function updateMenuGauche(root, userId, folderId, lng, status) {
  //si user connecté et access au projet, on remplace l'arbre des projets par l'arborescence du projet
  // si pas de user connecté on laisse l'arborescence des projets mais on mais en relief (folder open, gras)
  // le projet sélectionné
  // TODO CHANGEMENT : OUVERTURE DES dONNEES  : Si un dissier est public, même si on n'est pas connecté, on y accède !
  if (!userId) {
    //Pas connecté ou on revient la liste des projets , on liste tous les projets
    createGlobalTree(root, userId, folderId, lng, status);
  } else {
    // si le user est connecté mais qu'il n'a pas accès au projet : on ne montre pas l'arborescence
    // par exemple les users taggué conservatoire  n'ont pas  access aux projets archeovision
    $.ajax({
      url: "accessRights/" + userId + "," + folderId + "," + root,
    }).done(dat => {
      if (dat.rights) {
        // on est connecté on construit l'arborescence
        createProjectTree(root, userId, folderId, lng, status);
      } else {
        createGlobalTree(root, userId, folderId, lng, status);
      }
    });
  }
}

function createGlobalTree(root, userId, folderId, lng, status) {
  if (!folderId) {
    $(".alert-success").remove();
    $("#tabloid").removeClass("container");
    $("#tabloid").addClass("container-fluid");
  }
  let projet = "Projets 3D Collaboratifs";
  if (lng === "en") {
    projet = "3D Collaborative Projects";
  }

  $("#tabloid").html(
    '<img src="assets/images/image_' +
      root +
      '.jpg" style="max-height:99%;max-width:99%;margin-left:auto;margin-right:auto;display:block;">'
  );
  let htmlMenuGauche = '<div class="button" id="toptop"><a href="#">' + '<i class="fas fa-folder"></i> ' + projet + "</a>";

  // Est-ce qu'un scribe a le droit de creer un projet ???
  // Aujourd'hui je pense que non
  //if ((userId !== 0) && ((status === 'admin') || (status === 'scribe')) ) {
  if (userId !== 0 && status === "admin") {
    htmlMenuGauche +=
      '<button type="button" class="btn btn-sm" style="float: right;" onclick="createProject(\'' +
      root +
      "');\">New project</button></div>";
  } else {
    htmlMenuGauche += "</div>";
  }
  let fId = 0;
  if (typeof folderId !== "undefined") {
    fId = folderId;
  }

  htmlMenuGauche += "<div>";
  $.ajax({
    url: "dataProjects," + root,
  }).done(data => {
    htmlMenuGauche += '<ul id="main-ul" style="padding-left:1em!important;">';

    for (let i = 0; i < data.length; i++) {
      htmlMenuGauche += '<li><a href="#" title="' + data[i].id;

      if (data[i].id === fId) {
        htmlMenuGauche += '" style="font-weight:bold"><i class="fas fa-folder-open"></i><span ';
      } else {
        htmlMenuGauche += '"><i class="fas fa-folder"></i><span ';
      }

      htmlMenuGauche +=
        'id="libelleProjet" onclick="getProjectDetails(' +
        userId +
        "," +
        data[i].id +
        ",'" +
        lng +
        "', '" +
        status +
        "', '" +
        root +
        "' )\"> " +
        data[i].name +
        "</span>";
      if (data[i].nb_images !== 0) {
        htmlMenuGauche += " (" + data[i].nb_images + ")</a></li>";
      } else {
        htmlMenuGauche += "</a></li>";
      }
    }

    htmlMenuGauche += "</ul>";
    htmlMenuGauche += "</div>";

    $("#menuGauche").html(htmlMenuGauche);
  });
}

// l'ajout du status 0/1 pour savoir si on a les droits d'écriture ou non
// il faudrait pouvoir  donner le type de fichier pour la visionneuse : si ce n'est pas une image, on voit ce qu'on fait au click
// si fichier de type url : on va ouvrir l'url
function showPrevNextModal(id1, id2, type, folder, root, status) {
  if (type === "text") {
    $("#" + "text_" + id2)
      .find(".modal-body")
      .load("/visionneuse," + id2 + "-" + folder + "," + root + "-m," + status);
    $(".modal").modal("hide");
    $("#" + "text_" + id2).modal("show");
  } else if (type === "image") {
    $(".modal").modal("hide");
    $("#" + "image_" + id2).modal("show");
  }
}

function switchModal(type, id, folder, root, status) {
  if (type === "text") {
    $("#" + "text_" + id)
      .find(".modal-body")
      .load("/visionneuse," + id + "-" + folder + "," + root + "-m," + status);
    $(".modal").modal("hide");
    $("#" + "text_" + id).modal("show");
  } else if (type === "image") {
    $(".modal").modal("hide");
    $("#" + "image_" + id).modal("show");
  }
}

if (typeof $load === 'undefined') {
  var $load = $("body");
}

$(document).on({
  ajaxStart: () => {
    $load.addClass("loading");
  },
  ajaxStop: () => {
    $load.removeClass("loading");
  },
});

function createProject(root) {
  let projectName = "";
  let site = "";
  const name = prompt("Entrer le nom du projet :", "<Lieu_objet/scene>");
  if (name === null || name === "") {
    alert("Vous n'avez entré aucun nom. Veuillez entrer un nom complet");
  } else {
    site = prompt("Projet Archeovision ou Extérieur ? (A /E ) ");
    if (!site.match(/^(a|A|e|E)$/)) {
      alert("A ou E please ...");
      createProject(root);
    } else {
      projectName = name;
      $.ajax({
        url: "createProject," + name + "," + root + "," + site,
      }).done(data => {
        alert('Project "' + data.name + '" créé !');
        window.location.reload();
      });
    }
  }
}

function createOverallProject(root) {
  let site = "";
  const name = prompt("Entrer le nom du projet scientifique :", "Nom projet");
  if (name === null || name === "") {
    alert("Vous n'avez entré aucun nom. Veuillez entrer un nom complet");
  } else {
    site = prompt("Projet Archeovision ou Extérieur ? (A /E ) ");
    if (!site.match(/^(a|A|e|E)$/)) {
      alert("A ou E please ...");
      createProject(root);
    } else {
      $.ajax({
        url: "createOverallProject," + name + "," + root + "," + site,
      }).done(() => {
        alert('Project scientifique "' + name + '" créé !');
        window.location.reload();
      });
    }
  }
}

function searchURL(folderId) {
  let search = "";
  if ($("#mySearch").val().length > 0) {
    search += "?search=" + encodeURIComponent('{"all":[["' + $("#mySearch").val() + '"]]}');
  }
  const url = window.location.origin + "/search/" + folderId + search;
  window.location.href = url;
}

// rights : 0/1 pour droits d'écriture
function showPrevNextModalMulti(id, typeItem, typeModal, folder, branche, imgObj, rights) {
  if (typeModal === "text") {
    if (typeItem === "file")
      $("#" + "text_" + id)
        .find(".modal-body")
        .load("/visionneuse," + id + "-" + folder + "," + branche + "-m," + rights);
    else if (typeItem === "object")
      // TODO : /visionneuseObj ou /visionneuseObjet ???
      $("#" + "text_" + id)
        .find(".modal-body")
        .load("/visionneuseObj," + id + "," + imgObj + "-" + folder + "," + branche + "-m," + rights);
    else if (typeItem === "unico")
      $("#" + "text_" + id)
        .find(".modal-body")
        .load(`/visionneuseUnicoV2,${id},${folder},${branche}-m,${rights}`);
    else if (typeItem === "folder" && ((branche === "pft3d") || (branche === "corpus")))
      $("#" + "text_" + id)
        .find(".modal-body")
        .load("/projectv/" + folder + "?context=m");
    $(".modal").modal("hide");
    $("#" + "text_" + id).modal("show");
  } else if (typeModal === "image") {
    $(".modal").modal("hide");
    $("#" + "image_" + id).modal("show");
  }
}

function switchModalMulti(typeModal, typeItem, id, folder, branche, imgObj, rights) {
  if (typeModal === "text") {
    if (typeItem === "file")
      $("#" + "text_" + id)
        .find(".modal-body")
        .load("/visionneuse," + id + "-" + folder + "," + branche + "-m," + rights);
    else if (typeItem === "unico")
      $("#" + "text_" + id)
        .find(".modal-body")
        .load(`/visionneuseUnicoV2,${id},${folder},${branche}-m,${rights}`);
    else if (typeItem === "object")
      $("#" + "text_" + id)
        .find(".modal-body")
        .load("/visionneuseObj," + id + "," + imgObj + "-" + folder + "," + branche + "-m," + rights);
    else if (typeItem === "folder" && ((branche === "pft3d") || (branche === "corpus")))
      $("#" + "text_" + id)
        .find(".modal-body")
        .load("/projectv/" + folder + "?context=m");
    $(".modal").modal("hide");
    $("#" + "text_" + id).modal("show");
  } else if (typeModal === "image") {
    $(".modal").modal("hide");
    $("#" + "image_" + id).modal("show");
  }
}

function promiseSwitchModalMulti(typeModal, typeItem, id, folder, branche, imgObj, rights) {
  return new Promise((resolve) => {
    let modalSelector;
    if (typeModal === "text") {
      modalSelector = "#text_" + id;
    } else if (typeModal === "image") {
      modalSelector = "#image_" + id;
    }
    // Handler pour la résolution de la promesse
    function onShown() {
      $(modalSelector).off('shown.bs.modal', onShown);
      resolve();
    }
    $(modalSelector).on('shown.bs.modal', onShown);
    switchModalMulti(typeModal, typeItem, id, folder, branche, imgObj, rights);
  });
}

function updateItemSelection(idItem, itemType, idfolder) {
  if (window.justFinishedDragSelection) {
    return;
  }

  let type = "";

  let selected_item = "";
  if (itemType === "object") {
    type = "o";
    selected_item = "o" + idItem + "_" + idfolder;
  } else if (itemType === "file") {
    type = "i";
    selected_item = "i" + idItem + "_" + idfolder;
  } else if (itemType === "folder") {
    type = "d";
    selected_item = "d" + idItem + "_" + idfolder;
  } else if (itemType === "unico") {
    type = "u";
    selected_item = "u" + idItem + "_" + idfolder;
  }

  const el = document.getElementById("item-" + type + "-" + idItem + "_" + idfolder);
  if (!el) {
    return;
  }

  const checkbox = el.querySelector(".form-check-input");
  const arrayItem = selected_item;

  requestAnimationFrame(() => {
    if (!selection.has(arrayItem)) {
      el.classList.add("checked");
      el.classList.add("see-select");
      selection.add(arrayItem);
      if (checkbox && !checkbox.checked) {
        checkbox.checked = true;
      }
    } else {
      el.classList.remove("checked");
      el.classList.remove("see-select");
      selection.delete(arrayItem);
      if (checkbox?.checked) {
        checkbox.checked = false;
      }
    }

    localStorage.setItem("selection", JSON.stringify(Array.from(selection)));

    const nbSelectedElement = document.getElementById("nb-selected");
    if (nbSelectedElement) {
      nbSelectedElement.innerText = selection.size.toString();
    }

    const isSelectionVitrinePage = window.location.pathname.includes('/selectionV/');

    if (isSelectionVitrinePage && typeof updateButtonsState === 'function') {
      updateButtonsState(selection.size);
    }

    document.dispatchEvent(new CustomEvent('selectionChanged'));
  });
}

function allChecked(idFolder) {
  $(".check_item").prop("checked", true);
  $.ajax({
    url: "/explore," + idFolder ,
  }).done(data => {
    for (let i = 0; i < data.data_length; i++) {
      if (data[i].item_type === "file") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      } else if (data[i].item_type === "object") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      } else if (data[i].item_type === "folder") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      } else if (data[i].item_type === "unico") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      }
    }
  });
}

function allUnChecked(idFolder) {
  $(".check_item").prop("checked", false);
  $.ajax({
    url: "/explore," + idFolder ,
  }).done(data => {
    for (let i = 0; i < data.data_length; i++) {
      if (data[i].item_type === "file") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      } else if (data[i].item_type === "object") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      } else if (data[i].item_type === "folder") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      } else if (data[i].item_type === "unico") {
        updateItemSelection(data[i].id, data[i].item_type, idFolder);
      }
    }
  });
}

function registerSelection(idUser, idFolder, lng) {
  $.ajax({
    url: "/registerSelection/" + idUser + "," + idFolder,
    type: "POST",
    data: { selection: Array.from(selection) },
  }).done(data => {
    if (toastContent) {
      toastContent.innerText = data;
    }

    if (projectToast) {
      projectToast.show();
    }

    selection.clear();
    localStorage.setItem("selection", "[]");

    const nbSelectedElement = document.getElementById("nb-selected");
    if (nbSelectedElement) {
      nbSelectedElement.innerText = "0";
    }

    const checkedItems = document.querySelectorAll(".check_item.checked");
    checkedItems.forEach(item => {
      item.classList.remove("checked");
      item.classList.remove("see-select");
      const checkbox = item.querySelector(".form-check-input");
      if (checkbox?.checked) {
        checkbox.checked = false;
      }
    });

    const selectPageCheckbox = document.getElementById("selectPageCheckbox");
    if (selectPageCheckbox) {
      selectPageCheckbox.checked = false;
      selectPageCheckbox.indeterminate = false;
    }

    document.dispatchEvent(new CustomEvent('selectionChanged'));
  }).fail(error => {
    console.error("Error registering selection:", error);
  });
}

function removeSelection(idUser, idFolder, lang) {
  $.ajax({
    url: "/removeSelection/" + idUser + "," + idFolder,
  }).done(data => {
    selection.clear();
    localStorage.setItem("selection", "[]");

    if (toastContent) {
      toastContent.innerText = data;
    }

    if (projectToast) {
      projectToast.show();
    }

    const nbSelectedElement = document.getElementById("nb-selected");
    if (nbSelectedElement) {
      nbSelectedElement.innerText = "0";
    }

    const checkedItems = document.querySelectorAll(".check_item.checked");
    checkedItems.forEach(item => {
      item.classList.remove("checked");
      item.classList.remove("see-select");
      const checkbox = item.querySelector(".form-check-input");
      if (checkbox?.checked) {
        checkbox.checked = false;
      }
    });

    const selectPageCheckbox = document.getElementById("selectPageCheckbox");
    if (selectPageCheckbox) {
      selectPageCheckbox.checked = false;
      selectPageCheckbox.indeterminate = false;
    }

    document.dispatchEvent(new CustomEvent('selectionChanged'));

    const isSelectionView = window.location.href.includes('exploreSelection') ||
                           window.location.href.includes('selection');

    if (isSelectionView) {
      if (window.location.pathname.includes('/selectionV/')) {
        const projectId = window.location.pathname.split('/').pop();

        if (typeof exploreSelection === 'function') {
          $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');

          setTimeout(() => {
            exploreSelection(projectId);
          }, 500);
        } else {
          setTimeout(() => {
            window.location.href = `/projectV/${projectId}`;
          }, 1000);
        }
      } else {
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    }
  }).fail(error => {
    console.error("Error removing selection:", error);
  });
}

function getSelection(idUser, idProject) {
  $.ajax({
    url: "/getProjectSelection/" + idUser + "," + idProject,
  }).done(fav => {
    let favit = "";
    for (let f = 0; f < fav.length; f++) {
      if (fav[f].item_type === "file") {
        favit = "i" + fav[f].id;
      } else if (fav[f].item_type === "object") {
        favit = "o" + fav[f].id;
      } else if (fav[f].item_type === "folder") {
        favit = "d" + fav[f].id;
      } else if (fav[f].item_type === "unico") {
        favit = "u" + fav[f].id;
      }
      selection.add(favit);
    }

    localStorage.setItem("selection", JSON.stringify(Array.from(selection)));

    const countElement = document.getElementById("nb-selected");
    if (countElement) {
      countElement.innerText = selection.size.toString();
    }
  }).fail(error => {
    console.error("Error getting selection:", error);
  });
}

function deleteAll(idVirtualFolder, lang) {
  let txt_confirmDeleteAll = "";
  if (lang === "fr") {
    txt_confirmDeleteAll = confirmDeleteAll_fr;
  } else {
    txt_confirmDeleteAll = confirmDeleteAll_en;
  }
  if (confirm(txt_confirmDeleteAll)) {
    $.ajax({
      url: "/virtualFolderContentDelete/" + idVirtualFolder,
    }).done(data => {
      // Check if any items remain
      const totalRemaining = data.file_links + data.object_links + data.unico_links +
                           data.files + data.objects + data.unicos;
      
      if (totalRemaining > 0) {
        if (totalRemaining > 1) {
          alert(totalRemaining + " items n'ont pas pu être supprimés");
        } else {
          alert(totalRemaining + " item n'a pas pu être supprimé");
        }
      }
      window.location.reload();
    }).fail(error => {
      console.error('Error deleting virtual folder content:', error);
      alert("Une erreur est survenue lors de la suppression");
    });
  }
}

function deleteItem(idItem, itemType, idFolder) {
  $.ajax({
    url: `/virtualFolderItemDelete/${idItem},${itemType},${idFolder}`,
  }).done(() => {
    window.location.reload();
  });
}

function deleteItemReal(idItem, itemType, idFolder) {
  $.ajax({
    url: `/realFolderItemDelete/${idItem},${itemType},${idFolder}`,
  }).done(() => {
    window.location.reload();
  });
}

function createLinkProject(branche, idProject) {
  $("#idContenant").empty();

  // afficher la liste des projets dans un input
  let html = "";

  $.ajax({
    url: "/getRootProjects",
  }).done(data => {
    html +=
      '<div><form class="form-inline" id="linkoverall" action="/createLinkFolderOverall" method="post">' +
      '<input type="hidden" value="' +
      idProject +
      '" name="idProjectOverall">' +
      '<select name="idfolderToLink" id="idd">' +
      '<option value="">Choisir dans la liste</option>';
    for (let item = 0; item < data.length; item++) {
      html += '<option value="' + data[item].id + '" >' + data[item].name + "</option>";
    }
    html +=
      "</select>" +
      '<input class="btn btn-default" type="submit" value="Validate" />' +
      "</form></div>";
    $("#idContenant").append(html);
  });
}

function checkAll() {
  const items = document.querySelectorAll(".check_item:not(.checked)");
  if (items.length === 0) return;

  const itemsToAdd = [];
  const elementsToUpdate = [];

  items.forEach(item => {
    const info = item.id.split("-");
    if (info.length < 3) return;

    const item_type = info[1];
    if (!item_type) return;

    if (!(item_type === "o" || item_type === "i" || item_type === "d" || item_type === "u")) return;

    const parts = info[2].split("_");
    if (parts.length < 2) return;

    const item_id = parts[0];
    const folder_id = parts[1];

    const selected_item = item_type.charAt(0) + item_id + "_" + folder_id;

    itemsToAdd.push(selected_item);
    elementsToUpdate.push({
      element: item,
      checkbox: item.querySelector(".form-check-input")
    });
  });

  requestAnimationFrame(() => {
    elementsToUpdate.forEach(({element, checkbox}) => {
      element.classList.add("checked");
      element.classList.add("see-select");
      if (checkbox && !checkbox.checked) {
        checkbox.checked = true;
      }
    });

    itemsToAdd.forEach(item => selection.add(item));

    localStorage.setItem("selection", JSON.stringify(Array.from(selection)));

    const nbSelectedElement = document.getElementById("nb-selected");
    if (nbSelectedElement) {
      nbSelectedElement.innerText = selection.size.toString();
    }

    const isSelectionVitrinePage = window.location.pathname.includes('/selectionV/');

    if (isSelectionVitrinePage && typeof updateButtonsState === 'function') {
      updateButtonsState(selection.size);
    }

    document.dispatchEvent(new CustomEvent('selectionChanged'));
  });
}

function uncheckAll() {
  const items = document.querySelectorAll(".check_item.checked");
  if (items.length === 0) return;

  const itemsToRemove = [];
  const elementsToUpdate = [];

  items.forEach(item => {
    const info = item.id.split("-");
    if (info.length < 3) return;

    const item_type = info[1];
    if (!item_type) return;

    if (!(item_type === "o" || item_type === "i" || item_type === "d" || item_type === "u")) return;

    const parts = info[2].split("_");
    if (parts.length < 2) return;

    const item_id = parts[0];
    const folder_id = parts[1];

    const selected_item = item_type.charAt(0) + item_id + "_" + folder_id;

    itemsToRemove.push(selected_item);
    elementsToUpdate.push({
      element: item,
      checkbox: item.querySelector(".form-check-input")
    });
  });

  requestAnimationFrame(() => {
    elementsToUpdate.forEach(({element, checkbox}) => {
      element.classList.remove("checked");
      element.classList.remove("see-select");
      if (checkbox && checkbox.checked) {
        checkbox.checked = false;
      }
    });

    itemsToRemove.forEach(item => selection.delete(item));

    localStorage.setItem("selection", JSON.stringify(Array.from(selection)));

    const nbSelectedElement = document.getElementById("nb-selected");
    if (nbSelectedElement) {
      nbSelectedElement.innerText = selection.size.toString();
    }

    const isSelectionVitrinePage = window.location.pathname.includes('/selectionV/');

    if (isSelectionVitrinePage && typeof updateButtonsState === 'function') {
      updateButtonsState(selection.size);
    }

    document.dispatchEvent(new CustomEvent('selectionChanged'));
  });
}

/**
 * Drag selection functionality:
 * Allows selecting multiple items by dragging a selection rectangle
 */
function initDragSelection() {
  if (dragSelectionInitialized && !document.body.classList.contains('test-mode-allowing-reinit')) {
    return;
  }

  const selectionRect = document.getElementById('drag-selection-rectangle');
  if (!selectionRect) {
    return;
  }

  dragSelectionInitialized = true;

  let isMouseDown = false;
  let isDragging = false;
  let startX, startY;
  const dragThreshold = 5;

  let currentlyIntersectedItems = new Set();
  let allSelectedItems = new Set();

  let initialItemStates = new Map();
  let itemBoundsCache = new Map();
  let animationFrameRequestId = null;

  let startScrollTop = 0;
  let currentScrollTop = 0;

  let scrollContainer = null;
  let scrollSpeed = 0;
  let scrollDirection = 0;
  let autoScrollingActive = false;
  let scrollAnimationFrame = null;
  let lastMousePosition = { x: 0, y: 0 };

  // Find the scrollable container
  function getScrollContainer() {
    const exploreResults = document.getElementById('explore-results');
    if (!exploreResults) return null;
    if (exploreResults.scrollHeight > exploreResults.clientHeight) {
      return exploreResults;
    }
    const grid = document.getElementById('explore-grid');
    if (grid && grid.scrollHeight > grid.clientHeight) {
      return grid;
    }
    const listContainer = document.querySelector('.list-vitrine-container');
    if (listContainer && listContainer.scrollHeight > listContainer.clientHeight) {
      return listContainer;
    }
    return document.documentElement;
  }

  // Function for auto-scrolling
  function startAutoScroll() {
    if (scrollAnimationFrame) {
      cancelAnimationFrame(scrollAnimationFrame);
    }

    autoScrollingActive = true;

    function autoScroll() {
      if (!isDragging || !autoScrollingActive) {
        autoScrollingActive = false;
        return;
      }

      if (scrollContainer && scrollSpeed !== 0) {
        scrollContainer.scrollTop += scrollSpeed * scrollDirection;
        currentScrollTop = scrollContainer.scrollTop;

        if (isDragging) {
            itemBoundsCache.clear();
            document.querySelectorAll('.check_item').forEach(item => {
                itemBoundsCache.set(item.id, getElementCoordinates(item));
            });
        }

        updateSelection(lastMousePosition.x, lastMousePosition.y);
      }
      scrollAnimationFrame = requestAnimationFrame(autoScroll);
    }
    scrollAnimationFrame = requestAnimationFrame(autoScroll);
  }

  // Function to stop auto-scrolling
  function stopAutoScroll() {
    if (scrollAnimationFrame) {
      cancelAnimationFrame(scrollAnimationFrame);
      scrollAnimationFrame = null;
    }
    autoScrollingActive = false;
  }

  // Function to check if we should start auto-scrolling
  function checkForAutoScroll(mouseY) {
    if (!scrollContainer) return;

    const containerRect = scrollContainer.getBoundingClientRect();
    const thresholdHeight = Math.min(60, containerRect.height * 0.1);

    const distanceFromTop = mouseY - containerRect.top;
    const distanceFromBottom = containerRect.bottom - mouseY;

    if (distanceFromTop < thresholdHeight && scrollContainer.scrollTop > 0) {
      scrollDirection = -1;
      scrollSpeed = Math.max(1, (thresholdHeight - distanceFromTop) / 5);
      if (!autoScrollingActive) startAutoScroll();
    }
    else if (distanceFromBottom < thresholdHeight &&
             scrollContainer.scrollTop < scrollContainer.scrollHeight - scrollContainer.clientHeight) {
      scrollDirection = 1;
      scrollSpeed = Math.max(1, (thresholdHeight - distanceFromBottom) / 5);
      if (!autoScrollingActive) startAutoScroll();
    }
    else {
      stopAutoScroll();
    }
  }

  // Function to get element coordinates relative to the document
  function getElementCoordinates(element) {
    const rect = element.getBoundingClientRect();
    return {
      top: rect.top + window.pageYOffset,
      left: rect.left + window.pageXOffset,
      bottom: rect.bottom + window.pageYOffset,
      right: rect.right + window.pageXOffset,
    };
  }

  // Function to check if an element is within the selection rectangle
  function isElementInSelection(element, selectionBox) {
    let elCoords = itemBoundsCache.get(element.id);
    if (!elCoords) {
        elCoords = getElementCoordinates(element);
        itemBoundsCache.set(element.id, elCoords);
    }

    const isIntersecting = !(
      elCoords.right < selectionBox.left ||
      elCoords.left > selectionBox.right ||
      elCoords.bottom < selectionBox.top ||
      elCoords.top > selectionBox.bottom
    );

    if (!element.dataset.initialIntersection) {
      element.dataset.initialIntersection = isIntersecting.toString();
    }

    if (element.dataset.initialIntersection === 'true') {
      return true;
    }

    return isIntersecting;
  }

  // Function to toggle an item's selection state
  function toggleItemSelection(element, shouldSelect) {
    if (!element || !element.id) return;

    const isCurrentlyChecked = element.classList.contains('checked');

    if (shouldSelect === isCurrentlyChecked) return;

    const info = element.id.split("-");
    if (info.length < 3) return;

    const item_type = info[1];
    if (!item_type) return;

    let type = "";
    if (item_type === "o") type = "object";
    else if (item_type === "i") type = "file";
    else if (item_type === "d") type = "folder";
    else if (item_type === "u") type = "unico";
    else return; // Unknown type

    const parts = info[2].split("_");
    if (parts.length < 2) return;

    const item_id = parts[0];
    const folder_id = parts[1];

    updateItemSelection(item_id, type, folder_id);
  }

  // Function to finalize the selection
  function finalizeSelection() {
    currentlyIntersectedItems.clear();
    initialItemStates.clear();

    document.querySelectorAll('.check_item').forEach(item => {
      if (item.dataset.initialIntersection) {
        delete item.dataset.initialIntersection;
      }
      if (item.dataset.wasSelected) {
        delete item.dataset.wasSelected;
      }
    });

    stopAutoScroll();
  }

  // Function to update the selection rectangle and select items within it
  function updateSelection(currentX, currentY) {
    const activeSelectionRect = document.getElementById('drag-selection-rectangle');
    if (!activeSelectionRect) {
        return; // If rectangle is not in DOM, can't update it.
    }

    const viewportLeft = Math.min(startX, currentX);
    const viewportTop = Math.min(startY, currentY);
    const viewportRight = Math.max(startX, currentX);
    const viewportBottom = Math.max(startY, currentY);
    const width = viewportRight - viewportLeft;
    const height = viewportBottom - viewportTop;

    lastMousePosition = { x: currentX, y: currentY };

    activeSelectionRect.style.left = viewportLeft + 'px';
    activeSelectionRect.style.top = viewportTop + 'px';
    activeSelectionRect.style.width = width + 'px';
    activeSelectionRect.style.height = height + 'px';

    const docLeft = viewportLeft + window.pageXOffset;
    const docTop = viewportTop + window.pageYOffset;
    const docRight = viewportRight + window.pageXOffset;
    const docBottom = viewportBottom + window.pageYOffset;

    const selBox = {
      left: docLeft,
      top: docTop,
      right: docRight,
      bottom: docBottom
    };

    const items = document.querySelectorAll('.check_item');

    // Create a set of currently intersecting items IDs for this update
    const newlyIntersectedItems = new Set();

    // Check each item against the selection rectangle
    items.forEach(item => {
      if (!initialItemStates.has(item.id)) {
        initialItemStates.set(item.id, item.classList.contains('checked'));
      }

      const isIntersecting = isElementInSelection(item, selBox);

      if (isIntersecting) {
        newlyIntersectedItems.add(item.id);
        allSelectedItems.add(item.id);

        item.dataset.wasSelected = 'true';

        if (!currentlyIntersectedItems.has(item.id)) {
          toggleItemSelection(item, true);
        }
      } else {
        if (currentlyIntersectedItems.has(item.id) &&
            !initialItemStates.get(item.id) &&
            item.dataset.wasSelected !== 'true') {
          toggleItemSelection(item, false);
          allSelectedItems.delete(item.id);
        }
      }
    });

    currentlyIntersectedItems = newlyIntersectedItems;
    checkForAutoScroll(currentY);
  }

  // Start drag on mousedown
  document.addEventListener('mousedown', function(e) {
    const ignoreTags = ['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA', 'LABEL'];

    const isInteractiveElement =
      ignoreTags.includes(e.target.tagName) ||
      e.target.closest('a') !== null ||
      e.target.closest('button') !== null ||
      e.target.closest('input') !== null;

    const isCheckItem = e.target.classList.contains('check_item') ||
                        e.target.closest('.check_item') !== null;

    if (e.button === 2 && (!isInteractiveElement || isCheckItem)) {
      window.hadRightClickDrag = false;

      currentlyIntersectedItems.clear();
      allSelectedItems.clear();
      initialItemStates.clear();
      itemBoundsCache.clear();

      document.querySelectorAll('.check_item').forEach(item => {
        itemBoundsCache.set(item.id, getElementCoordinates(item));
        if (item.dataset.wasSelected) {
          delete item.dataset.wasSelected;
        }
        if (item.dataset.initialIntersection) {
          delete item.dataset.initialIntersection;
        }
      });

      isMouseDown = true;
      isDragging = false;
      window.isDraggingSelection = false;

      scrollContainer = getScrollContainer();
      startScrollTop = scrollContainer ? scrollContainer.scrollTop : 0;
      currentScrollTop = startScrollTop;

      startX = e.clientX;
      startY = e.clientY;

      const rectForMouseDown = document.getElementById('drag-selection-rectangle');
      if (rectForMouseDown) {
        rectForMouseDown.style.display = 'none';
      }
      e.preventDefault();
    }
  });

  // Update selection rectangle on mousemove
  document.addEventListener('mousemove', function(e) {
    if (!isMouseDown) return;

    if (!isDragging) {
      if (Math.abs(e.clientX - startX) < dragThreshold && Math.abs(e.clientY - startY) < dragThreshold) {
        return;
      }
      isDragging = true;
      window.isDraggingSelection = true;

      window.hadRightClickDrag = true;

      const rectForMouseMoveDisplay = document.getElementById('drag-selection-rectangle');
      if (rectForMouseMoveDisplay) {
        rectForMouseMoveDisplay.style.display = 'block';
      }
    }

    if (animationFrameRequestId) {
        cancelAnimationFrame(animationFrameRequestId);
    }
    animationFrameRequestId = requestAnimationFrame(() => {
        if (isDragging) {
          updateSelection(e.clientX, e.clientY);
        }
    });
    e.preventDefault();
  });

  // Finish selection on mouseup
  document.addEventListener('mouseup', function(e) {
    if (animationFrameRequestId) {
        cancelAnimationFrame(animationFrameRequestId);
        animationFrameRequestId = null;
    }

    if (!isMouseDown) return;

    if (e.button !== 2) return;
    const wasDragging = isDragging;
    isMouseDown = false;
    isDragging = false;
    window.isDraggingSelection = false;

    if (wasDragging) {
      const rectForMouseUp = document.getElementById('drag-selection-rectangle');
      if (rectForMouseUp) {
        rectForMouseUp.style.display = 'none';
      }
      finalizeSelection();
      e.preventDefault();
      e.stopPropagation();

      window.justFinishedDragSelection = true;

      setTimeout(() => {
        window.justFinishedDragSelection = false;
      }, 100);

      return false;
    } else {
      window.justFinishedDragSelection = false;
    }
  });

  // Handle page scroll during selection
  document.addEventListener('scroll', function() {
    if (!isDragging) return;
    if (scrollContainer) {
      currentScrollTop = scrollContainer.scrollTop;

      if (animationFrameRequestId) {
          cancelAnimationFrame(animationFrameRequestId);
      }
      animationFrameRequestId = requestAnimationFrame(() => {
          if (isDragging) {
            itemBoundsCache.clear();
            document.querySelectorAll('.check_item').forEach(item => {
                itemBoundsCache.set(item.id, getElementCoordinates(item));
            });
            updateSelection(lastMousePosition.x, lastMousePosition.y);
          }
      });
    }
  });

  // Prevent text selection during drag
  document.addEventListener('selectstart', function(e) {
    if (isDragging) {
      e.preventDefault();
    }
  });
}

/**
 * Function to unselect all items on the current page
 * This is used when clicking on the page background
 */
function unselectAllItemsOnCurrentPage() {
  // Get all checked items on the current page
  const checkedItems = document.querySelectorAll(".check_item.checked");
  if (checkedItems.length === 0) return;

  // Batch process all items
  const itemsToRemove = [];
  const elementsToUpdate = [];

  // First pass: collect all items to update
  checkedItems.forEach(item => {
    const info = item.id.split("-");
    if (info.length < 3) return;

    const item_type = info[1];
    if (!item_type) return;

    // Validate item type
    if (!(item_type === "o" || item_type === "i" || item_type === "d" || item_type === "u")) return;

    const parts = info[2].split("_");
    if (parts.length < 2) return;

    const item_id = parts[0];
    const folder_id = parts[1];

    const selected_item = item_type.charAt(0) + item_id + "_" + folder_id;

    // Add to our collections
    itemsToRemove.push(selected_item);
    elementsToUpdate.push({
      element: item,
      checkbox: item.querySelector(".form-check-input")
    });
  });

  // Second pass: batch update the DOM
  requestAnimationFrame(() => {
    elementsToUpdate.forEach(({element, checkbox}) => {
      element.classList.remove("checked");
      element.classList.remove("see-select");
      if (checkbox && checkbox.checked) {
        checkbox.checked = false;
      }
    });

    itemsToRemove.forEach(item => selection.delete(item));

    localStorage.setItem("selection", JSON.stringify(Array.from(selection)));

    const nbSelectedElement = document.getElementById("nb-selected");
    if (nbSelectedElement) {
      nbSelectedElement.innerText = selection.size.toString();
    }

    const isSelectionVitrinePage = window.location.pathname.includes('/selectionV/');

    if (isSelectionVitrinePage && typeof updateButtonsState === 'function') {
      updateButtonsState(selection.size);
    }

    document.dispatchEvent(new CustomEvent('selectionChanged'));
  });
}

// Initialize drag selection when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize global flags for tracking selection states
  window.isDraggingSelection = false;
  window.justFinishedDragSelection = false;
  window.hadRightClickDrag = false;

  initDragSelection();

  const exploreResults = document.getElementById('explore-results');
  if (exploreResults) {
    const observer = new MutationObserver(function() {
      initDragSelection();
    });

    observer.observe(exploreResults, {
      childList: true,
      subtree: true
    });
  }

  let mouseDownPos = null;
  let isMouseMoving = false;
  const moveThreshold = 5;

  document.addEventListener('mousedown', function(e) {
    if (e.button !== 0) return;

    mouseDownPos = { x: e.clientX, y: e.clientY };
    isMouseMoving = false;
  }, true);

  document.addEventListener('contextmenu', function(e) {
    if (window.hadRightClickDrag) {
      e.preventDefault();
      e.stopPropagation();

      setTimeout(() => {
        window.hadRightClickDrag = false;
      }, 300);

      return false;
    }

    if (window.isDraggingSelection || window.justFinishedDragSelection) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }

    return true;
  }, true);

  document.addEventListener('mousemove', function(e) {
    if (mouseDownPos) {
      const dx = Math.abs(e.clientX - mouseDownPos.x);
      const dy = Math.abs(e.clientY - mouseDownPos.y);

      if (dx > moveThreshold || dy > moveThreshold) {
        isMouseMoving = true;
      }
    }
  }, true);

  document.addEventListener('mouseup', function(e) {
    if (window.isDraggingSelection) {
      mouseDownPos = null;
      return;
    }

    if (e.button !== 0 || !mouseDownPos) {
      mouseDownPos = null;
      return;
    }

    if (!isMouseMoving) {
      const ignoreTags = ['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA', 'LABEL', 'IMG'];
      const ignoreClasses = [
        'check_item',
        'form-check-input',
        'vitrine-text',
        'vitrine-image',
        'vitrine-content',
        'selection-dropdown',
        'custom-nb-dropdown',
        'dropdown-toggle',
        'dropdown-menu',
        'dropdown-item',
        'custom-select-dropdown',
        'custom-select-option',
        'custom-select-container',
        'custom-select-input'
      ];

      // Check if the click is on an ignored element or its child
      const isIgnoredElement =
        ignoreTags.includes(e.target.tagName) ||
        e.target.closest('a') !== null ||
        e.target.closest('button') !== null ||
        e.target.closest('input') !== null ||
        e.target.closest('.check_item') !== null ||
        e.target.closest('.selection-dropdown') !== null ||
        e.target.closest('.custom-nb-dropdown') !== null ||
        e.target.closest('.dropdown-menu') !== null ||
        e.target.closest('.custom-select-dropdown') !== null ||
        e.target.closest('.custom-select-container') !== null ||
        e.target.closest('.custom-select-option') !== null;

      const hasIgnoredClass = Array.from(e.target.classList || []).some(cls =>
        ignoreClasses.some(ignoreCls => cls.includes(ignoreCls))
      );

      if (!isIgnoredElement && !hasIgnoredClass) {
        unselectAllItemsOnCurrentPage();
      }
    }

    mouseDownPos = null;
    isMouseMoving = false;
  }, true);
});