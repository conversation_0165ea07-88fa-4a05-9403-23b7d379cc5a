---
sidebar_position: 1
---

# Créer une table

La création d'une table est la première étape pour ajouter vos données CSV à ArcheoGRID. Cette étape créera une nouvelle table dans la base de données avec les colonnes du fichier CSV. Cette fonctionnalité est divisée en deux étapes. Si vous venez du menu principal, vous commencerez par la sélection du projet, mais si vous venez d'une autre fonctionnalité (création de modèle ou ingestion de CSV), vous commencerez par la sélection du fichier CSV avec des informations déjà renseignées.

### Sélectionner le projet

Cette étape vous permet de choisir le projet où se trouve votre fichier CSV. Vous pouvez utiliser la barre de recherche pour trouver un projet spécifique.

![](projectSelector-CT-FR.png)

> **Note**: Seuls les projets auxquels vous avez accès sont affichés.

### Sélectionner le fichier CSV

Cette étape vous permet de choisir le fichier CSV que vous souhaitez utiliser. Cela nécessite de définir le séparateur utilisé dans le fichier CSV, le nom souhaité de la table et le type d'éléments que le CSV décrit (fichier, dossier ou objet). Le nom par défaut de la table est le nom de la branche suivi du nom du fichier CSV.

![](csvFileSelector-CT-FR.png)

Le nom de la table ainsi que les noms des colonnes ne peuvent pas inclure d'espaces, ou d'autres charactères spéciaux comme ';', ':', '#' ou ','. Si l'un de ces caractères est détecté, il sera remplacé par un underscore '_'.

Si la table existait déja, même (branche, projet, csv), un message d'information vous l'indiquera. Lorsque vous validerez le formulaire, les valeurs présentes dans l'ancienne table seront supprimées et la table décrite dans le formulaire sera crée.

> **Erreur**: Un message d'erreur sera affiché si le dossier des métadonnées n'existe pas et vous serez redirigé vers le menu. Si il n'y a pas de fichier CSV dans le dossier, un message d'information sera affiché vous permettant de retourner à la sélection du projet.

### Résultat

Si la table a été créée, un message de succès sera affiché. Vous pouvez soit créer le modèle correspondant, ingérer les données du CSV dans la table ou retourner au menu.

![](success-CT-FR.png)

> **Erreur**: Un message d'erreur sera affiché si le fichier CSV n'existe pas, s'il n'y a pas d'en-tête dans le fichier ou s'il n'y a qu'une seule colonne dans le fichier.
