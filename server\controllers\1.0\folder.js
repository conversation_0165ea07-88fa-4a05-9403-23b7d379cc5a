const pgp = require("../../helpers/connexion").default;
const db = require("../../helpers/db").default;
const responseHelper = require("../../helpers/response");

// Create url endpoint for /root folders with order GET
exports.getRootFolders = async function (req, res) {
  let { root } = req.params;
  // ATTENTION : on prend tous les dossiers racines, quid des dossiers virtuels ??
  // C'est OK pour les dossiers virtuels car il n'y pas de dossier virtuel avec id_parent NULL
  // il y a des dossiers virtuels mais ils sont sous des dossiers parents
  // UPDATE mars 2023 : on ajout les overall project qui ont aussi des id_parent null
  // donc on ajout la contrainte folder_passport = 'project' car pour les overall project , le folder_passport est à 'overall'
  let query =
    `SELECT id, name, folder_name, rank, global_rank FROM ${root}_folder WHERE id_parent IS NULL` +
    ` AND folder_passport = 'project' `;

  let orderRank = " ORDER BY rank";
  let orderName = " ORDER BY name";

  if (req.query)
    if (req.query.order === "name") query += orderName;
    else query += orderRank;

  try {
    const rootfolder = await db.any(query);
    // succes
    res.status(200).sendData(rootfolder);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error", e, req, res);
  }
};

// Create url endpoint for /root folders with order PATCH
exports.patchRootFoldersOrder = async function (req, res) {
  const { root } = req.params;
  //let orderTab = req.body['0']
  let erreur = 0;
  let lerreur_last = "";

  //let newRank = orderTab.split(",")
  //for( i = 0; i < newRank.length; i++ ) {
  for (const tab in req.body) {
    let indice = parseInt(tab) + 1;
    let new_rank = req.body[tab].split("_");
    //let new_rank = newRank[i].split("_")
    let [_, fId] = new_rank;

    try {
      const rootfolder = await db.none(
        //'UPDATE '+root+'_folder SET rank = $1 WHERE id = $2', [(i+1) , fId])
        `UPDATE ${root}_folder SET rank = $1 WHERE id = $2`,
        [indice, fId]
      );
    } catch (e) {
      // error
      erreur = 1;
      lerreur_last = e;
    }
  }

  if (erreur) {
    responseHelper.sendError(500, "server_error in reorder folder", erreur, req, res);
  } else {
    try {
      const update_global_rank_to_null = await db.none(`UPDATE ${root}_folder SET global_rank = NULL`);
      try {
        const update_global_rank = await db.any(`SELECT update_${root}_folder_global_rank()`);
        res.status(200).sendData([]);
      } catch (e) {
        erreur = 1;
        console.log(e);
        responseHelper.sendError(500, "server_error in update_global_rank in reorder folder", e, req, res);
      }
    } catch (e) {
      lerreur_last = e;
      responseHelper.sendError(500, "server_error in set global_rank to null in reorder folder", e, req, res);
    }
  }
};

exports.createRootFolder = async function (req, res) {
  let { root } = req.params;
  let { body } = req;
  //let body = req.query // to test with postman easily

  try {
    const rootId = await db.any(
      `SELECT id FROM ${root}_folder WHERE nlevel(folder_path) = 1 AND name = $1`,
      body.folderName
    );
    // success
    if (rootId.length > 0) {
      res.status(201).sendData(rootId);
    } else {
      const query = pgp.as.format(
        `INSERT INTO ${root}_folder(name,id_parent,global_rank,   ` +
          `date_creation,  status,folder_passport) ` +
          `SELECT $1, NULL, trim(to_char(max(CAST(global_rank AS integer))+1, '000')) , now(), ` +
          `'public', $2  FROM ${root}_folder WHERE id_parent IS NULL RETURNING id`,
        [body.folderName, body.folderPassport]
      );

      db.one(query)
        .then((data) => {
          return db
            .result(
              `UPDATE ${root}_folder SET folder_path = text2ltree(cast(id as text)), ` +
                `rank = cast(global_rank as integer), folder_name = name  WHERE id = $1`,
              data.id
            )
            .then((result) => res.status(201).sendData(data))
            .catch((e) => {
              responseHelper.sendError(500, "server_error in UPDATE folder_path in create root folder ", e, req, res);
            });
        })
        .catch((e) => {
          responseHelper.sendError(500, "server_error in INSERT folder root", e, req, res);
        });
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in INSERT folder root", e, req, res);
  }
};

exports.getStatusFolder = async function (req, res) {
  let { root } = req.params;

  let result = [];
  let query =
    `SELECT status, CASE WHEN file_passport IS NULL THEN '0' ` +
    ` WHEN file_passport = '' THEN '0' ` +
    ` ELSE file_passport END  as model_file, ` +
    `folder_passport, id_representative_picture ` +
    `FROM ${root}_folder WHERE id = $1`;
  try {
    const objectList = await db.oneOrNone(query, req.params.folderId);
    // success
    if (objectList === null) {
      result.status = "private";
      result.model_file = "0";
      result.folder_passport = null;
      result.id_representative_picture = 0;
    } else result = objectList;
    res.status(200).sendData(result);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in GET status folder", e, req, res);
  }
};

// Create url endpoint for explore/:folderId,:root
// On ajoute une info pour un viewer éventuel pour le mettre dans les modales
// on ajoute une info pour récupérer id_nakala s'ily a une prévisu quelque part ???
// on ajoute une info check/uncheck des favorite pour pouvoir cocher les items fav
exports.getFolderItem = async function (req, res) {
  let folderId = parseInt(req.params.folderId);
  let random = false;
  if (folderId < 0) { // c'est le nouveau cas d'un exploreFolder random sur un projet => on supprime le - pour récupérer le "vrai id folder"
    if (req.params.folderId.charAt(0) === '-') {
      folderId = Number.parseInt(req.params.folderId.slice(1))
    }
    random = true;
  }
  let { root } = req.params;
  let fileTable = `${root}_file`;
  let filefolderTable = `${root}_file_folder`;
  let folderTable = `${root}_folder`;
  let favTable = `${root}_favorite_item`;

  let userId = req.query.userId
  let resu = [];
  try {
    let folderItemquery =
      `` + // file
      `  SELECT ffi.id_folder as fid, ` +
      `fi.id_folder as item_folder, ` +
      `fi.name as filename, regexp_replace(fi.name, '-([a-zA-Z])', '\\1')  COLLATE numeric  as filename_order, ` +
      `fi.id as id, ` +
      `'file' item_type, ` +
      `'' as object_name, '' as object_name_order,  ` +
      `'' as unico_name,  '' as unico_name_order,  ` +
      `0 as id_repr_image, ` +
      `fi.path as path , ` +
      `ff.name as foldername, ` +
      `fi.file_ext as extension, ` +
      `CASE WHEN get_viewer_info_gen('${root}', ${folderId}, fi.id, 'file') IS NOT NULL THEN  get_viewer_info_gen('${root}', ${folderId}, fi.id, 'file') ELSE '' END AS viewer3d,  ` +
      `CASE WHEN fi.id IS NULL THEN ''  ` +
      ` WHEN get_first_metadata_anymodel(fi.id, '${root}' , 'file', 'DublinCore') IS NOT NULL THEN get_first_metadata_anymodel(fi.id, '${root}' , 'file', 'DublinCore') ` +
      ` ELSE replace(fi.name, '.'||file_ext, '')  END as title, ` +
      `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
      `, 0 as user_link ` +
      `, fi.hash as hash_3d ` +
      `, '' as previsu  ` +
      `, 0 as x, 0 as y , 0 as width, 0 as height ` +
      `, '' as type, '' as polygon `+
      `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
      //'FROM '+fileTable+' f INNER JOIN '+filefolderTable + ' ff  ON ff.id_file = f.id ' +
      `FROM ${folderTable} ff ` +
      `INNER JOIN ${filefolderTable} ffi  ON ffi.id_folder = ff.id ` +
      `INNER JOIN ${fileTable} fi ON fi.id = ffi.id_file ` +
      `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = fi.id AND fav.item_type = 'file' AND fav.id_folder = $1 AND fav.id_user = ${userId}  ` +
      `WHERE ff.id  = $1 ` +
      //'ORDER BY fi.name ' +
      `UNION ALL ` + // object directement dans leurs répertoires
      `SELECT ff.id as fid, ` +
      ` o.id_folder as item_folder, ` +
      `'' as filename, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric  as filename_order ,  ` +
      `o.id ,` +
      `'object' as item_type , ` +
      `o.name as object_name, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric as object_name_order,  ` +
      `'' as unico_name, '' as unico_name_order, ` +
      `CASE WHEN o.id_file_representative IS NOT NULL THEN o.id_file_representative ELSE 0 END  as id_repr_image, ` +
      `CASE WHEN fi.path IS NOT NULL THEN fi.path ELSE '' END as path  , ` +
      `ff.name as foldername, ` +
      `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext ELSE '' END as extension , ` +
      ` ''  as viewer3d , ` +
      `CASE WHEN o.id IS NULL THEN ''  ` +
      ` WHEN get_first_metadata_anymodelv2(o.id, '${root}' , 'object', 'DublinCore') IS NOT NULL THEN get_first_metadata_anymodelv2(o.id, '${root}' , 'object', 'DublinCore') ` +
      ` ELSE o.name  END as title, ` +
      `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
      `, uo.id_user as user_link ` +
      `, '' as hash_3d ` +
      `, o.id_nakala  as previsu ` +
      `, 0 as x, 0 as y , 0 as width, 0 as height ` +
      `, '' as type, '' as polygon `+
      `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
      //            'FROM '+folderTable+' ff ' +
      `FROM ${root}_object o ` +
      //'INNER JOIN '+root+'_folder_object fo ON fo.id_object = o.id ' +
      `INNER JOIN ${root}_folder ff ON ff.id = o.id_folder ` + // pour les objets directement rattachés à ce folder
      `LEFT OUTER JOIN ${root}_file fi ON fi.id = o.id_file_representative ` +
      `LEFT OUTER JOIN ${root}_user_object uo ON uo.id_object = o.id ` +
      `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = o.id AND fav.item_type = 'object' AND fav.id_folder = $1 AND fav.id_user = ${userId} ` +
      `WHERE ff.id = $1 ` +
      `UNION ALL ` + // object dans un répertoire virtuel
      `SELECT vf.id as fid, ` +
      ` o.id_folder as item_folder, ` +
      `'' as filename, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric  as filename_order ,  ` +
      `o.id ,` +
      `'object' as item_type , ` +
      `o.name as object_name, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric as object_name_order,  ` +
      `'' as unico_name, '' as unico_name_order, ` +
      `CASE WHEN o.id_file_representative IS NOT NULL THEN o.id_file_representative ELSE 0 END  as id_repr_image, ` +
      `CASE WHEN fi.path IS NOT NULL THEN fi.path ELSE '' END as path  , ` +
      `vf.name as foldername, ` +
      `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext ELSE '' END as extension , ` +
      ` ''  as viewer3d , ` +
      `o.name as title, ` +
      `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
      `, uo.id_user as user_link ` +
      `, '' as hash_3d ` +
      `, o.id_nakala  as previsu ` +
      `, 0 as x, 0 as y , 0 as width, 0 as height ` +
      `, '' as type, '' as polygon `+
      `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
      //            'FROM '+folderTable+' ff ' +
      `FROM ${root}_object o ` +
      'INNER JOIN '+root+'_folder_object vfo ON vfo.id_object = o.id AND vfo.id_folder != o.id_folder ' + // on ne prend pas les objets qui sont dans leur folder de base, on les a récupérés juste au-dessus
      `INNER JOIN ${root}_folder vf ON vf.id = vfo.id_folder AND vf.folder_name IS NULL ` + // pour les objets d'un répertoire virtuel
      `LEFT OUTER JOIN ${root}_file fi ON fi.id = o.id_file_representative ` +
      `LEFT OUTER JOIN ${root}_user_object uo ON uo.id_object = o.id ` +
      `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = o.id AND fav.item_type = 'object' AND fav.id_folder = $1 AND fav.id_user = ${userId} ` +
      `WHERE vf.id = $1 ` +
      `UNION ALL ` + // unicos
      `SELECT ff.id as fid, ` +
      ` fu.id_folder as item_folder, ` +
      ` fi.name as filename, regexp_replace(fi.name, '-([a-zA-Z])', '\\1') COLLATE numeric  as filename_order  , ` +
      `u.id ,` +
      `'unico' as item_type , ` +
      `'' as object_name, '' as object_name_order, ` +
      ` u.name as unico_name, regexp_replace(u.name, '-([a-zA-Z])', '\\1') COLLATE numeric as unico_name_order , ` +
      ` fi.id  as id_repr_image, ` +
      `CASE WHEN fi.path IS NOT NULL THEN fi.path ELSE '' END as path  , ` +
      `ff.name as foldername, ` +
      `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext ELSE '' END as extension , ` +
      ` ''  as viewer3d , ` +
      `u.name as title, ` +
      `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
      `, 0 as user_link ` +
      `, '' as hash_3d ` +
      `, '' as previsu ` +
      `, u.x, u.y , u.width, u.height ` +
      `, u.type, u.polygon `+
      `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
      `FROM ${root}_unico u ` +
      `INNER JOIN ${root}_folder_unico fu ON fu.id_unico = u.id ` +
      `INNER JOIN ${root}_folder ff ON ff.id = fu.id_folder ` +
      `LEFT OUTER JOIN ${root}_file fi ON fi.id = u.id_file ` +
      `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = u.id AND fav.item_type = 'unico' AND fav.id_folder = $1 AND fav.id_user = ${userId} ` +
      `WHERE ff.id = $1 ` +
      `ORDER BY filename_order   `;

    let folderItemqueryRandom =
        `` + // file
        `  SELECT ffi.id_folder as fid, ` +
        `fi.id_folder as item_folder, ` +
        `fi.name as filename, regexp_replace(fi.name, '-([a-zA-Z])', '\\1')  COLLATE numeric  as filename_order, ` +
        `fi.id as id, ` +
        `'file' item_type, ` +
        `'' as object_name, '' as object_name_order,  ` +
        `'' as unico_name,  '' as unico_name_order,  ` +
        `0 as id_repr_image, ` +
        `fi.path as path , ` +
        `ff.name as foldername, ` +
        `fi.file_ext as extension, ` +
        `CASE WHEN get_viewer_info_gen('${root}', ${folderId}, fi.id, 'file') IS NOT NULL THEN  get_viewer_info_gen('${root}', ${folderId}, fi.id, 'file') ELSE '' END AS viewer3d,  ` +
        `CASE WHEN fi.id IS NULL THEN ''  ` +
        ` WHEN get_first_metadata_anymodel(fi.id, '${root}' , 'file', 'DublinCore') IS NOT NULL THEN get_first_metadata_anymodel(fi.id, '${root}' , 'file', 'DublinCore') ` +
        ` ELSE replace(fi.name, '.'||file_ext, '')  END as title, ` +
        `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
        `, 0 as user_link ` +
        `, fi.hash as hash_3d ` +
        `, '' as previsu  ` +
        `, 0 as x, 0 as y , 0 as width, 0 as height ` +
        `, '' as type, '' as polygon `+
        `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
        //'FROM '+fileTable+' f INNER JOIN '+filefolderTable + ' ff  ON ff.id_file = f.id ' +
        `FROM ${folderTable} ff ` +
        `INNER JOIN ${filefolderTable} ffi  ON ffi.id_folder = ff.id ` +
        `INNER JOIN ${fileTable} fi ON fi.id = ffi.id_file ` +
        `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = fi.id AND fav.item_type = 'file' AND fav.id_folder = $1 AND fav.id_user = ${userId}  ` +
        `WHERE ff.folder_path <@'$1' AND ff.status = 'public' ` +
        //'ORDER BY fi.name ' +
        `UNION ALL ` + // object directement dans leurs répertoires
        `SELECT ff.id as fid, ` +
        ` o.id_folder as item_folder, ` +
        `'' as filename, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric  as filename_order ,  ` +
        `o.id ,` +
        `'object' as item_type , ` +
        `o.name as object_name, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric as object_name_order,  ` +
        `'' as unico_name, '' as unico_name_order, ` +
        `CASE WHEN o.id_file_representative IS NOT NULL THEN o.id_file_representative ELSE 0 END  as id_repr_image, ` +
        `CASE WHEN fi.path IS NOT NULL THEN fi.path ELSE '' END as path  , ` +
        `ff.name as foldername, ` +
        `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext ELSE '' END as extension , ` +
        ` ''  as viewer3d , ` +
        `o.name as title, ` +
        `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
        `, uo.id_user as user_link ` +
        `, '' as hash_3d ` +
        `, o.id_nakala  as previsu ` +
        `, 0 as x, 0 as y , 0 as width, 0 as height ` +
        `, '' as type, '' as polygon `+
        `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
        //            'FROM '+folderTable+' ff ' +
        `FROM ${root}_object o ` +
        //'INNER JOIN '+root+'_folder_object fo ON fo.id_object = o.id ' +
        `INNER JOIN ${root}_folder ff ON ff.id = o.id_folder ` + // pour les objets directement rattachés à ce folder
        `LEFT OUTER JOIN ${root}_file fi ON fi.id = o.id_file_representative ` +
        `LEFT OUTER JOIN ${root}_user_object uo ON uo.id_object = o.id ` +
        `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = o.id AND fav.item_type = 'object' AND fav.id_folder = $1 AND fav.id_user = ${userId} ` +
        `WHERE ff.folder_path <@'$1' AND ff.status = 'public' ` +
        `UNION ALL ` + // object dans un répertoire virtuel
        `SELECT vf.id as fid, ` +
        ` o.id_folder as item_folder, ` +
        `'' as filename, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric  as filename_order ,  ` +
        `o.id ,` +
        `'object' as item_type , ` +
        `o.name as object_name, regexp_replace(o.name, '-([a-zA-Z])', '\\1') COLLATE numeric as object_name_order,  ` +
        `'' as unico_name, '' as unico_name_order, ` +
        `CASE WHEN o.id_file_representative IS NOT NULL THEN o.id_file_representative ELSE 0 END  as id_repr_image, ` +
        `CASE WHEN fi.path IS NOT NULL THEN fi.path ELSE '' END as path  , ` +
        `vf.name as foldername, ` +
        `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext ELSE '' END as extension , ` +
        ` ''  as viewer3d , ` +
        `o.name as title, ` +
        `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
        `, uo.id_user as user_link ` +
        `, '' as hash_3d ` +
        `, o.id_nakala  as previsu ` +
        `, 0 as x, 0 as y , 0 as width, 0 as height ` +
        `, '' as type, '' as polygon `+
        `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
        //            'FROM '+folderTable+' ff ' +
        `FROM ${root}_object o ` +
        'INNER JOIN '+root+'_folder_object vfo ON vfo.id_object = o.id AND vfo.id_folder != o.id_folder ' + // on ne prend pas les objets qui sont dans leur folder de base, on les a récupérés juste au-dessus
        `INNER JOIN ${root}_folder vf ON vf.id = vfo.id_folder AND vf.folder_name IS NULL ` + // pour les objets d'un répertoire virtuel
        `LEFT OUTER JOIN ${root}_file fi ON fi.id = o.id_file_representative ` +
        `LEFT OUTER JOIN ${root}_user_object uo ON uo.id_object = o.id ` +
        `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = o.id AND fav.item_type = 'object' AND fav.id_folder = $1 AND fav.id_user = ${userId} ` +
        `WHERE vf.folder_path <@'$1' AND vf.status = 'public' ` +
        `UNION ALL ` + // unicos
        `SELECT ff.id as fid, ` +
        ` fu.id_folder as item_folder, ` +
        ` fi.name as filename, regexp_replace(fi.name, '-([a-zA-Z])', '\\1') COLLATE numeric  as filename_order  , ` +
        `u.id ,` +
        `'unico' as item_type , ` +
        `'' as object_name, '' as object_name_order, ` +
        ` u.name as unico_name, regexp_replace(u.name, '-([a-zA-Z])', '\\1') COLLATE numeric as unico_name_order , ` +
        ` fi.id  as id_repr_image, ` +
        `CASE WHEN fi.path IS NOT NULL THEN fi.path ELSE '' END as path  , ` +
        `ff.name as foldername, ` +
        `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext ELSE '' END as extension , ` +
        ` ''  as viewer3d , ` +
        `u.name as title, ` +
        `CASE WHEN file_passport IS NULL THEN '0' WHEN file_passport = '' THEN '0' ELSE file_passport END as model ` +
        `, 0 as user_link ` +
        `, '' as hash_3d ` +
        `, '' as previsu ` +
        `, u.x, u.y , u.width, u.height ` +
        `, u.type, u.polygon `+
        `, CASE WHEN fav.id_item IS NOT NULL THEN 1 ELSE 0 END as check ` +
        `FROM ${root}_unico u ` +
        `INNER JOIN ${root}_folder_unico fu ON fu.id_unico = u.id ` +
        `INNER JOIN ${root}_folder ff ON ff.id = fu.id_folder ` +
        `LEFT OUTER JOIN ${root}_file fi ON fi.id = u.id_file ` +
        `LEFT OUTER JOIN ${favTable} fav ON fav.id_item = u.id AND fav.item_type = 'unico' AND fav.id_folder = $1 AND fav.id_user = ${userId} ` +
        `WHERE ff.folder_path <@ '$1' AND ff.status = 'public' ` +
        `   `;

    let folderItemqueryRandomFinal = ''

    const englobe_requete_debut = `SELECT * FROM ( `
    const englobe_requete_fin = ` ) AS combined ORDER BY RANDOM() `
    folderItemqueryRandomFinal = englobe_requete_debut +  folderItemqueryRandom + englobe_requete_fin

    if (req.query.page > 0 && req.query.limit > 0) {
      // console.log('req.query.limit: ', req.query.limit);
      // console.log('req.query.page: ', req.query.page);
      folderItemqueryRandomFinal +=  `OFFSET ${(req.query.page - 1) * req.query.limit} LIMIT ${req.query.limit}`;
      folderItemquery += `OFFSET ${(req.query.page - 1) * req.query.limit} LIMIT ${req.query.limit}`;
    }

    let UsedQuery = ""
    if (random) {
      UsedQuery = folderItemqueryRandomFinal
    } else {
      UsedQuery = folderItemquery
    }
    const folderItem = await db.any(UsedQuery, folderId);

    for (let item in folderItem) {
      if (folderItem[item].item_type) {
        resu.push(folderItem[item]);
      }
    }
    res.status(200).sendData(resu);
  } catch (e) {
    responseHelper.sendError(500, "server_error in GET explore/folderId,root - getFolderItem : ", e, req, res);
  }
};

// Create url endpoint for /folderFull/:folderId,root,lang
// get all metadata
exports.getFolderFull = async function (req, res) {
  let lang = `'${req.params.lang}'`;
  let root = `'${req.params.root}'`;
  let pId = parseInt(req.params.folderId);

  try {
    const jsonFolder = await db.any(
      `SELECT get_folderfull(${lang},${root}, id) AS folderfull ` +
        `FROM ${req.params.root}_folder WHERE id = $1 limit 1`,
      pId
    );
    res.status(200).sendData(jsonFolder);
  } catch (e) {
    responseHelper.sendError(500, "server_error in  folderfull", e, req, res);
  }
};

// Create url endpoint for /folderSimple/:folderId,root GET
exports.getFolderSimple = async function (req, res) {
  let { root } = req.params;
  let fId = parseInt(req.params.folderId);

  try {
    const simpleFolder = await db.one(
      `SELECT f.id, f.name, f.id_parent, f.folder_path, f.folder_name,` +
        `f.passport, to_char(date_creation,'DD/MM/YYYY HH24:MI:SS') as date_creation , rank, global_rank, status, visible, ` +
        `uploadable, commentable, folder_passport, file_passport, nb_images, to_char(date_update,'DD/MM/YYYY HH24:MI:SS') ,` +
        `natsort,  concat(s.${root}_url, ${root}_get_real_path(f.id)) as base ,` +
        `  ${root}_get_real_virtual_path($1) as root_base  ` +
        `FROM ${root}_folder f ` +
        `INNER JOIN ${root}_site s ON s.id = f.id_site WHERE f.id = $1 limit 1`,
      fId
    );
    res.status(200).sendData(simpleFolder);
  } catch (e) {
    responseHelper.sendError(500, "server_error", e, req, res);
  }
};

// Create url endpoint for /folderSimple/:folderId,root PATCH
exports.setFolderSimple = async function (req, res) {
  let { root } = req.params,
    erreur = 0;
  let erreur_message = "";
  let fId = parseInt(req.params.folderId);

  let newValues = req.body;

  delete newValues.rootproj;
  delete newValues.idFolder;

  for (const field in newValues) {
    try {
      const update_folder = await db.none(`UPDATE  ${root}_folder SET ${field} = $1 ` + `WHERE id = $2 `, [
        newValues[field],
        fId,
      ]);
      // TODO ? mettre à jour tous ses enfants pour l'info file_passport ?
      //  (avant, on ne mettait à jour que le dossier parent) ,
      //  ou bien on fait une fonction postgres pour aller chercher une info file_passport info
      //  chez le plus proche parent quand on veut éditer un fichier et lui coller un file_passport par défaut
      //if ( field === 'file_passport' ) {
      //    try { const update_children_folder = await db.none(
      //        'UPDATE '+root+'_folder SET '+field+' =  $1' +
      //        ' WHERE  folder_path <@ \'$2\' ', [newValues[field], fId]
      //        )
      //    }
      //    catch (e) {
      //        erreur_message += ' - '+e+' - '
      //        console.log(e)
      //        erreur = 1
      //    }
      //}
    } catch (e) {
      erreur_message += ` - ${e} - `;
      console.log(e);
      erreur = 1;
    }
  }

  if (erreur) {
    responseHelper.sendError(500, "server_error IN UPDATE simple folder", erreur_message, req, res);
  } else {
    res.status(200).sendData([]);
  }
};

// Create url endpoint for /privateFolders/:root GET
// all folders (real and virtual)
exports.getPrivateFolders = async function (req, res) {
  let { root } = req.params;

  try {
    const privateFolders = await db.any(
      `SELECT id, name, folder_name, ${root}_get_path(id) as fullname ` +
        `FROM ${root}_folder WHERE status='private'  ORDER BY 4`
    );
    res.status(200).sendData(privateFolders);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getPrivateFolders", e, req, res);
  }
};

// Create url endpoint for /privateFolders/:root  PATCH
exports.setPrivateFolders = async function (req, res) {
  let pathf;
  // Si un dossier devient private, il faut que tous ses  sous-dossiers doivent devenir privés aussi
  let { root } = req.params;
  let folders = req.body.publicfolder;
  //let folders = req.body.privatefolder
  let error = 0;
  let error_last = "";

  if (typeof folders === "object") {
    // C'est un tableau d'id folder
    for (let f = 0; f < folders.length; f++) {
      try {
        const pathf_tmp = await db.any(`SELECT folder_path FROM  ${root}_folder ` + `WHERE id = ($1)`, folders[f]);

        pathf = pathf_tmp[0].folder_path;
        try {
          const update_status_folder = await db.none(
            `UPDATE  ${root}_folder SET status='private' ` + `WHERE folder_path <@ '${pathf}' `
          );
        } catch (e) {
          error_last = e;
          error = 1;
        }
      } catch (e) {
        error_last = e;
        error = 1;
      }
    }
  } else if (typeof folders === "string") {
    try {
      const pathf_tmp = await db.any(`SELECT folder_path FROM  ${root}_folder ` + `WHERE id = ($1)`, folders);

      //console.log(pathf_tmp[0]['folder_path'])

      pathf = pathf_tmp[0].folder_path;
      try {
        const update_status_folder = await db.none(
          `UPDATE  ${root}_folder SET status='private' ` + `WHERE folder_path <@ '${pathf}' `
        );
      } catch (e) {
        error_last = e;
        error = 1;
      }
    } catch (e) {
      error_last = e;
      error = 1;
    }
  }

  if (error) {
    responseHelper.sendError(500, "server_error in setPrivateFolders", error_last, req, res);
  } else {
    res.status(200).sendData([]);
  }
};

// Create url endpoint for /publicFolders/:root GET
// all folder (real and virtual)
exports.getPublicFolders = async function (req, res) {
  let { root } = req.params;

  try {
    const publicFolders = await db.any(
      `SELECT id, name, folder_name, nb_images, ${root}_get_path(id) as fullname ` +
        `FROM ${root}_folder WHERE status='public'  ORDER BY fullname`
    );
    res.status(200).sendData(publicFolders);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getPublicFolders", e, req, res);
  }
};

// Create url endpoint for /publicFolders/:root  PATCH
exports.setPublicFolders = async function (req, res) {
  // Si un dossier devient public, il faut que tous ses parents deviennent public
  // WHERE folder_path @> le folder_path du folder en question
  let { root } = req.params;
  //let folders = req.body.publicfolder
  let folders = req.body.privatefolder;
  let error = 0;
  let error_last = "";
  let pathf = "";

  if (typeof folders == "object") {
    // C'est un tableau d'id folder
    for (let f = 0; f < folders.length; f++) {
      // Recuperer le folder_path
      try {
        const pathf_tmp = await db.any(`SELECT folder_path FROM  ${root}_folder ` + `WHERE id = ($1)`, folders[f]);
        pathf = pathf_tmp[0].folder_path;

        // Si c'est ok : on change le status pour le folder ET tous ses parents
        try {
          const update_status_folder = await db.none(
            `UPDATE  ${root}_folder SET status='public' ` + `WHERE folder_path @> '${pathf}' AND id_parent IS NOT NULL `
          );
        } catch (e) {
          error_last = e;
          error = 1;
        }
      } catch (e) {
        error_last = e;
        error = 1;
      }
    }
  } else if (typeof folders == "string") {
    try {
      const pathf_tmp = await db.any(`SELECT folder_path FROM  ${root}_folder ` + `WHERE id = ($1)`, folders);

      console.log(pathf_tmp[0].folder_path);

      pathf = pathf_tmp[0].folder_path;

      try {
        // fix bug : impossible de rendre public un dossier racine des projets...
        const update_status_folder = await db.none(
          `UPDATE  ${root}_folder SET status='public' ` +
            //'WHERE folder_path @> \''+pathf+'\'  AND id_parent IS NOT NULL ')
            `WHERE folder_path @> '${pathf}'   `
        );
      } catch (e) {
        error_last = e;
        error = 1;
      }
    } catch (e) {
      error_last = e;
      error = 1;
    }
  }

  if (error) {
    responseHelper.sendError(500, "server_error in setPublicFolders", error_last, req, res);
  } else {
    res.status(200).sendData([]);
  }
};

// Create url endpoint for /downloadableFolders/:root GET
// all folder (real and virtual)
exports.getDownloadableFolders = async function (req, res) {
  let { root } = req.params;

  try {
    const downloadableFolders = await db.any(
      `SELECT id, name, folder_name, nb_images, ${root}_get_path(id) as fullname ` +
        `FROM ${root}_folder WHERE uploadable ='true'  ORDER BY fullname`
    );
    res.status(200).sendData(downloadableFolders);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getDownloadableFolders", e, req, res);
  }
};

// Create url endpoint for /downloadableFolders/:root  PATCH
exports.setDownloadableFolders = async function (req, res) {
  let { root } = req.params;
  let folders = req.body.nondownloadablefolder;
  let error = 0;
  let error_last = "";

  //console.log(req.body)

  if (typeof folders === "object") {
    // C'est un tableau d'ids folder
    for (let f = 0; f < folders.length; f++) {
      try {
        const update_download_status_folder = await db.none(
          `UPDATE  ${root}_folder SET uploadable = 'true' ` + `WHERE id = $1 `,
          folders[f]
        );
      } catch (e) {
        error_last = e;
        error = 1;
      }
    }
  } else if (typeof folders === "string") {
    console.log("un seul folder");
    let idFolder = parseInt(folders);
    console.log(idFolder);
    try {
      const update_downloadable_status_folder = await db.none(
        `UPDATE  ${root}_folder SET uploadable = 'true' ` + `WHERE id = $1 `,
        idFolder
      );
    } catch (e) {
      error_last = e;
      error = 1;
    }
  }

  if (error) {
    responseHelper.sendError(500, "server_error in setDownloadableFolders", error_last, req, res);
  } else {
    res.status(200).sendData([]);
  }
};

// Create url endpoint for /nondownloadableFolders/:root GET
// all folder (real and virtual)
exports.getNonDownloadableFolders = async function (req, res) {
  let { root } = req.params;

  try {
    const nondownloadableFolders = await db.any(
      `SELECT id, name, folder_name, nb_images, ${root}_get_path(id) as fullname ` +
        `FROM ${root}_folder WHERE uploadable ='false'  ORDER BY fullname`
    );
    res.status(200).sendData(nondownloadableFolders);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getNonDownloadableFolders", e, req, res);
  }
};

// Create url endpoint for /downloadableFolders/:root  PATCH
exports.setNonDownloadableFolders = async function (req, res) {
  let { root } = req.params;
  let folders = req.body.downloadablefolder;
  let error = 0;
  let error_last = "";

  console.log(req.body);

  if (typeof folders === "object") {
    // C'est un tableau d'ids folder
    for (let f = 0; f < folders.length; f++) {
      try {
        const update_nondownload_status_folder = await db.none(
          `UPDATE  ${root}_folder SET uploadable = 'false' ` + `WHERE id = $1 `,
          folders[f]
        );
      } catch (e) {
        error_last = e;
        error = 1;
      }
    }
  } else if (typeof folders === "string") {
    console.log("un seul folder");
    let idFolder = parseInt(folders);
    console.log(idFolder);
    try {
      const update_nondownloadable_status_folder = await db.none(
        `UPDATE  ${root}_folder SET uploadable = 'false' ` + `WHERE id = $1 `,
        idFolder
      );
    } catch (e) {
      error_last = e;
      error = 1;
    }
  }

  if (error) {
    responseHelper.sendError(500, "server_error in setNonDownloadableFolders", error_last, req, res);
  } else {
    res.status(200).sendData([]);
  }
};

// Create url endpoint for /nbImagesFolder/:root,idFolder  GET
exports.getNbImagesFolder = async function (req, res) {
  let { root } = req.params;
  let fId = parseInt(req.params.idFolder);

  try {
    const getNbImage = await db.any(`SELECT id, nb_images FROM ${root}_folder WHERE id = $1 `, fId);
    res.status(200).sendData(getNbImage[0]);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET nb_images folder", e, req, res);
  }
};

// Create url endpoint for /nbImagesFolder/:root,idFolder  PATCH
exports.setNbImagesFolder = async function (req, res) {
  let idFolder = parseInt(req.params.idFolder);
  let rootProj = req.params.root;

  console.log(idFolder);
  db.any({
    name: "nbImages",
    text: `SELECT count(*) as nb from ${rootProj}_file_folder WHERE id_folder = $1 `,
    values: [idFolder],
  })
    .then((dataU) => {
      console.log(dataU[0].nb);
      return db
        .none({
          name: "updateNbImagesFolder",
          text: `UPDATE ${rootProj}_folder SET nb_images = $1 WHERE id = $2 `,
          values: [dataU[0].nb, idFolder],
        })
        .then(() => {
          console.log("UPDATE folder nb_images OK ");
          return res.status(200).sendData(dataU[0].nb);
        })
        .catch((e) => {
          console.log(e);
          responseHelper.sendError(500, "server_error IN SET nb_images folder", e, req, res);
        });
    })
    .catch((e) => {
      console.log(e);
      responseHelper.sendError(500, "server_error IN SET nb_images folder", e, req, res);
    });
};

// create url endpoint to retrieve id folder from its path (req.query)
exports.getFolder = async function (req, res) {
  let { root } = req.params;
  try {
    const getidFolder = await db.any(
      `SELECT id FROM ${root}_folder WHERE ${root}_get_real_path(id) = $1 `,
      req.query.folderPath
    );
    res.status(200).sendData(getidFolder[0]);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET id folder", e, req, res);
  }
};

exports.createFolder = async function (req, res) {
  //insertFolder(req.params.root,req.body.folderPath,req.body.dbParent, req.body.folderName, req.body.dateT )

  //console.log(req.body) // METHODE PUT, le BODY est en jeu
  //console.log(req.query) // NON UTILIS
  try {
    const idFolder = await db.any(
      `SELECT id FROM ${req.params.root}_folder WHERE ${req.params.root}_get_real_path(id) = $1`,
      req.body.folderPath
    );

    if (idFolder.length > 0) {
      res.status(201).sendData(idFolder[0]);
    } else {
      try {
        const idF = await db.one(
          `INSERT INTO ${req.params.root}_folder ` +
            `(id, name, folder_name, date_creation,visible, uploadable, commentable ,` +
            `id_representative_picture, nb_images, id_parent, status, folder_path, id_site) ` +
            `SELECT nextval('${req.params.root}_folder_id_seq'), $1, $1, $2, $3, $4, $3, ` +
            ` 0, 0, id, 'private' , folder_path , 1 ` +
            `FROM ${req.params.root}_folder fp WHERE ${req.params.root}_get_real_path(id) = $5 group by id  RETURNING id`,
          [req.body.folderName, req.body.dateT, "true", "false", req.body.dbParent]
        );

        db.tx((t) => {
          // creating a sequence of transaction queries:
          const q1 = t.any(`SELECT update_${req.params.root}_folder_rank()`);
          const q2 = t.any(`SELECT update_${req.params.root}_folder_global_rank()`);

          // returning a promise that determines a successful transaction:
          return t.batch([q1, q2]); // all of the queries are to be resolved;
        })
          .then((data) => {
            // success, COMMIT was executed
            return res.status(201).sendData(idF);
          })
          .catch((error) => {
            // failure, ROLLBACK was executed
            console.log(error);
            responseHelper.sendError(
              500,
              "server_error in update rank and global_rank after create folder ",
              error,
              req,
              res
            );
          });
      } catch (e) {
        responseHelper.sendError(500, "server_error IN create folder", e, req, res);
      }
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error IN create folder", e, req, res);
  }
};

// create url endpoint getIdFolder to get all folder id of one branche
// used in /project/<id> when no user are connected, user.ff must be filled with all folder id of the branche project
exports.getIdFolder = async function (req, res) {
  let { root } = req.params;
  try {
    const getidFolder = await db.any(`SELECT array_agg(id) FROM ${root}_folder  `);
    res.status(200).sendData(getidFolder[0].array_agg);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET id folder", e, req, res);
  }
};

exports.getIdPrivateFolder = async function (req, res) {
  let { root } = req.params;
  try {
    const getidPrivateFolder = await db.any(`SELECT array_agg(id) FROM ${root}_folder ` + `WHERE  status = 'private' `);
    res.status(200).sendData(getidPrivateFolder[0].array_agg);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET id private folder", e, req, res);
  }
};

exports.getIdPublicFolder = async function (req, res) {
  let { root } = req.params;
  let resu = [];
  try {
    const getidPublicFolders = await db.oneOrNone(`SELECT array_agg(id) FROM ${root}_folder ` + `WHERE  status = 'public' `);
    if (getidPublicFolders['array_agg']) resu = getidPublicFolders.array_agg
    res.status(200).sendData(resu);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET id private folder", e, req, res);
  }
};

// create url endpoint /mainFolder/:root,:idFolder,:lng GET  to get the "root" folder from an id folder
// used in edit post to redirect to the main folder (project , or depot )
// used in visionneuse to change  COMMENT button depending on project (Notre_Dame special request)
// with table _folder_metadatamodel
exports.getMainFolder = async function (req, res) {
  let { root } = req.params;
  let lng = req.params.lng === 'fr' ? 'fr' :  'en';
  let idFolder = parseInt(req.params.idFolder);

  try {
    // Dans la requete, on fait la jointure entre l'id non pas du folder mais du premier folder parent (la racine, le projet/depot)
    // Mais comme on récupère un ltree et qu'on ne peut pas caster du ltree en integer, on cast en text des 2 côtés
    const folder = await db.one(
      `SELECT subpath(fo.folder_path,0,1) as mainfolder, split_part(${root}_get_real_path(fo.id),'/',1) as folder_name,` +
        `fmm.id_metadata_model, parent_fo.id_representative_picture ` +
        `, fo.id_site ` +
        `FROM ${root}_folder  fo ` +
        `LEFT OUTER JOIN ${root}_folder_metadata_model fmm  ON  fmm.id_folder::text = subpath(fo.folder_path,0,1)::text ` +
        `LEFT OUTER JOIN ${root}_folder parent_fo ON parent_fo.id::text = subpath(fo.folder_path,0,1)::text ` +
        ` WHERE fo.id = $1`,
      idFolder
    );
    if (folder.id_metadata_model) {
      try {
        const getMainFolder = await db.one(
          `SELECT subpath(fo.folder_path,0,1) as mainfolder, split_part(${root}_get_real_path(fo.id),'/',1) as folder_name, ` +
            `fmm.id_metadata_model, mml.label, mml.description, mm.name as model_name, parent_fo.id_representative_picture  FROM ${root}_folder  fo ` +
            `LEFT OUTER JOIN ${root}_folder_metadata_model fmm  ON  fmm.id_folder::text = subpath(fo.folder_path,0,1)::text ` +
            `LEFT OUTER JOIN ${root}_metadata_model mm  ON  mm.id = fmm.id_metadata_model ` +
            `LEFT OUTER JOIN ${root}_metadata_model_label mml  ON  mml.id_metadata_model = mm.id ` +
            `LEFT OUTER JOIN ${root}_folder parent_fo ON parent_fo.id::text = subpath(fo.folder_path,0,1)::text` +
            ` WHERE fo.id = $1 AND mml.language = $2`,
          [idFolder, lng]
        );
        res.status(200).sendData(getMainFolder);
      } catch (e) {
        responseHelper.sendError(500, "server_error IN GET main id folder with model", e, req, res);
      }
    } else {
      // il n'y a pas de model associé, on renvoie null pour les autres
      folder.label = null;
      folder.description = null;
      res.status(200).sendData(folder);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET main id folder without model", e, req, res);
  }
};

// Récupérer le dossier racine depuis n'importe quel id de folder (pour récupérer le dossier maitre d'un folder)
// initialement quand on supprime le contenu d'un virtual folder et renvoyer sur la page projet mais
// plus besoin car utilisation du location.reload dans le javascript à la place
// Create url endpoint for /rootFolder/:branche,:idFolder GET
exports.getRootFolder = async function (req, res) {
  let { branche } = req.params;
  let idFolder = parseInt(req.params.idFolder);

  try {
    const root = await db.one(
      `SELECT subltree(folder_path, 0, 1) as root FROM ${branche}_folder where id = $1`,
      idFolder
    );
    res.status(200).sendData(root);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET Root folder id from one folder", e, req, res);
  }
};

// Récupérer le dossier racine depuis n'importe quel id de file (pour récupérer le dossier maitre d'un folder)
// initialement quand on supprime le contenu d'un virtual folder et renvoyer sur la page projet mais
// plus besoin car utilisation du location.reload dans le javascript à la place
// Create url endpoint for /rootFolderFromFile/:branche,:idFile GET
exports.getRootFolderFromFile = async function (req, res) {
  let { branche } = req.params;
  let idFile = parseInt(req.params.idFile);

  try {
    const root = await db.oneOrNone(
      `SELECT subltree(folder_path, 0, 1) as root FROM ${branche}_folder ` +
        `WHERE id = (SELECT id_folder FROM ${branche}_file  WHERE id = $1 ) `,
      idFile
    );
    res.status(200).sendData(root);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET Root folder id from one file", e, req, res);
  }
};

// create url endpoint for /representativeImage/:branche,:idFolder GET
exports.getRepresentativeImage = async function (req, res) {
  let idFolder = parseInt(req.params.idFolder);
  let { branche } = req.params;

  try {
    const repre = await db.one(`SELECT id_representative_image FROM ${branche}_folder ` + `WHERE id = $1`, idFolder);
    res.status(200).sendData(repre);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET repre image", e, req, res);
  }
};

// create url endpoint for /representativeImage/:branche,:idFolder PATCH
exports.patchRepresentativeImage = async function (req, res) {
  let { branche } = req.params;
  let idFolder = parseInt(req.params.idFolder);

  let idImage = parseInt(req.body.idImage)
  // Si idImage = 0 => C'est pour supprimer l'id repre image existant
  // Si idImage = 1 => C'est pour lui en mettre une
  // soit 1 si on est dans les projets d'ArcheoGRID,
  // soit on récupère un id de l'image dans la base de données dans le cas où on a fait une synch (CND3D)

  // mettre l'image dans la base (avec un synch) et donner l'id de l'image
  try {
    // Pour le conservatoire, récupérer l'id du file qui a pour id_folder le folder du dépôt
    // ET pour être sûr , ce file doit avoir pour nom (sans l'extension) le nom du folder => NON cette règle a changé pour être plus souple
    // UPDATE maintenant : pas forcément le même nom ? => on supprime la seconde partie de la requete (07/02/2022)
    const repre = await db.oneOrNone(
      `SELECT id FROM ${branche}_file WHERE id_folder = $1 `,
      //const repre = await db.oneOrNone('SELECT max(id) FROM ' + branche + '_file WHERE id_folder = $1 ' // Si jamais il y avait plusieurs images dans le dépôt
      //+ 'AND ' +
      //'replace(name, substring(name from length(name)-3), \'\') =  ( ' +
      //' SELECT folder_name FROM '+branche+'_folder where id = $1 ) '
      [idFolder]
    );
    if (repre && idImage) {
      // si idImage n'est pas égale à 0, c'est qu'on veut mettre à jour SINON, on patch en remettant à 0 la repre image
      idImage = repre.id;
    }
    try {
      const ok = await db.none(
        `UPDATE ${branche}_folder SET id_representative_picture  = $1 WHERE id = $2`,
        //                [idImage, idFolder])
        [idImage, idFolder]
      );
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error first IN PATCH repre image", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error IN second  PATCH repre image", e, req, res);
  }
};

// create url endpoint for /folderPath/:branche,:idFolder GET
exports.getFolderPath = async function (req, res) {
  let rootProj = req.params.branche;
  let idFolder = parseInt(req.params.idFolder);

  try {
    // Récupérer le path complet du folder identifié en base
    const initFolder = await db.one(
      `SELECT ${rootProj}_url as initroot, id_site as site,  concat(${rootProj}_url, ${rootProj}_get_real_path(f.id)) as path  ` +
        `FROM ${rootProj}_folder f INNER JOIN ${rootProj}_site s ON s.id = f.id_site ` +
        `WHERE f.id = $1 `,
      idFolder
    );
    //console.log(initFolder)
    res.status(200).sendData(initFolder);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN getFolderSize", e, req, res);
  }
};

//retrieve id folder from its path (req.query) on a virtual Folder
// OR ???? retrieve all virtual folder within a parent folder (project or deposit)
// create url endpoint for /virtualFolder/:branche,idParent GET
exports.getVirtualFolder = async function (req, res) {
  let idParent = parseInt(req.params.idParent);
  let { branche } = req.params;
  try {
    const getidFolder = await db.any(
      `SELECT id, name, ${branche}_get_virtual_path(id) as completename  FROM ${branche}_folder ` +
        //'WHERE '+branche+'_get_virtual_path(id) = $1 ',
        `WHERE  folder_path <@ '${idParent}' AND folder_name IS NULL ORDER BY global_rank `
    );
    res.status(200).sendData(getidFolder);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN GET id virtual folder", e, req, res);
  }
};

// create url endpoint for /virtualFolder/:branche,idParent PUT
exports.createVirtualFolder = async function (req, res) {
  let { branche } = req.params;
  const parentCandidateId = req.params.idParent;

  // Est-ce qu'un dossier (virtuel) de ce nom existe déjà dans cette arborescence ?
  try {
    let finalParentId;

    const parentCandidate = await db.oneOrNone(
      `SELECT folder_name, folder_path FROM ${branche}_folder WHERE id = $1`,
      [parentCandidateId]
    );

    if (!parentCandidate) {
      return responseHelper.sendError(404, "parent_folder_not_found", "The specified parent folder does not exist.", req, res);
    }

    if (parentCandidate.folder_name === null) {
      finalParentId = parentCandidateId;
    } else {
      const rootFolder = await db.one(
        `SELECT subpath(folder_path, 0, 1)::text AS root_id FROM ${branche}_folder WHERE id = $1`,
        [parentCandidateId]
      );
      finalParentId = parseInt(rootFolder.root_id, 10);
    }

    const idFolder = await db.oneOrNone(`SELECT id FROM ${branche}_folder WHERE name = $1 AND id_parent = $2 AND folder_name IS NULL`, [
      req.body.folderName,
      finalParentId,
    ]);

    if (idFolder) {
      // il y a déjà un folder avec ce nom, on ne fait rien
      res.status(201).sendData(idFolder);
    } else {
      try {
        // regarder le status du folder parent : s'il est privé l'enfant est privé, s'il est public, l'enfant est public
        const stat = await db.one(
          `SELECT status, id_site FROM ${branche}_folder ` + `WHERE id = $1`,
          finalParentId
        );

        try {
          // creation du folder
          // UPDATE 21/05/2021 : add nb_object = 0 pour affichage correcte dans explore
          const idF = await db.one(
            `INSERT INTO ${req.params.branche}_folder ` +
              `(id, name, folder_name, date_creation,visible, uploadable, commentable ,` +
              `id_representative_picture, nb_images, nb_objects, nb_unicos, id_parent, status, folder_path, id_site) ` +
              `SELECT nextval('${req.params.branche}_folder_id_seq'), $1, NULL, now(), $2, $3, $2, ` +
              ` 0, 0, 0, 0, id, $4 , folder_path , $5 ` +
              `FROM ${req.params.branche}_folder fp WHERE id = $6 group by id  RETURNING id`,
            [req.body.folderName, "true", "true", stat.status, stat.id_site, finalParentId]
          );

          db.tx((t) => {
            // creating a sequence of transaction queries:
            const q1 = t.any(`SELECT update_${req.params.branche}_folder_rank()`);
            const q2 = t.any(`SELECT update_${req.params.branche}_folder_global_rank()`);

            // returning a promise that determines a successful transaction:
            return t.batch([q1, q2]); // all of the queries are to be resolved;
          })
            .then((data) => {
              // success, COMMIT was executed
              // Attribution des droits au nouveau dossier virtuel pour ce user
              if (stat.status === "private") {
                if (req.body.user_status !== "admin") {
                  try {
                    const perm = db.none(
                      `INSERT INTO ${branche}_user_access (id_user, id_folder) ` + `VALUES ($1, $2)  `,
                      [req.body.user, idF.id]
                    );
                    return res.status(201).sendData(idF);
                  } catch (e) {
                    return responseHelper.sendError(
                      500,
                      "server_error in add perm on new virtual folder ",
                      e,
                      req,
                      res
                    );
                  }
                } else {
                  // user admin : pas la peine de lui attribuer des droits, il les a tous
                  return res.status(201).sendData(idF);
                }
              } else {
                // folder public rien d'autre à faire
                return res.status(201).sendData(idF);
              }
            })
            .catch((error) => {
              // failure, ROLLBACK was executed
              console.log(error);
              responseHelper.sendError(
                500,
                "server_error in update rank and global_rank after create virtual folder ",
                error,
                req,
                res
              );
            });
        } catch (e) {
          responseHelper.sendError(500, "server_error IN create VIRTUAL folder", e, req, res);
        }
      } catch (e) {
        responseHelper.sendError(500, "server_error IN GET status of Parent for the futur VIRTUAL folder", e, req, res);
      }
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error IN create the VIRTUAL folder", e, req, res);
  }
};

// create url endpoint for /virtualFolder/:branche,:idParent DELETE
exports.deleteVirtualFolder = async function (req, res) {
  let { branche } = req.params;
  let idFolder = req.params.idParent; // le idParent correspond ici au virtual folder à supprimer
  // ATTENTION : si des droits on été mis sur le dossier virtuel pour certaines personnes : il faut les supprimer également et avant
  // => construire une fonction postgres qui supprime tout avant de supprimer le dossier :

  try {
    const to_delete = await db.any(`SELECT ${branche}_delete_virtual_folder($1) `, idFolder);

    try {
      const delVirtualFolder = await db.any(
        `DELETE FROM ${branche}_folder ` + `WHERE id = $1 AND folder_name IS NULL `,
        idFolder
      );
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error IN delete virtualFolder", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error IN prepare delete virtualFolder", e, req, res);
  }
};

// create URL ENDPOINT for /virtualFolderLink/:branche,:idVirtualFolder GET
// TODO prendre en compte les différents types : object (o), file (i), folder(d)
exports.getVirtualFolderLink = async function (req, res) {
  let { branche } = req.params;
  let idFolder = parseInt(req.params.idVirtualFolder);
  let nb_tot = 0;
  let nb_object = 0;
  let nb_file = 0;
  let resu = {};

  // compter combien il y a réellement de id_file rattaché à un id_folder virtual:
  try {
    const nbItem = await db.any(
      `SELECT count(*) as nb_items, 'i' as item_type FROM ${branche}_file_folder ` +
        `WHERE id_folder = $1 ` +
        `UNION ALL ` +
        `SELECT count(*) as nb_items, 'o' as item_type  FROM ${branche}_folder_object  ` +
        `WHERE id_folder = $1 `,
      idFolder
    );
    /*
            [
              { nb_items: '0', item_type: 'i' },
              { nb_items: '3', item_type: 'o' }
            ]
         */
    for (let n in nbItem) {
      if (nbItem[n].item_type === "i") {
        nb_file = parseInt(nbItem[n].nb_items);
      } else if (nbItem[n].item_type === "o") {
        nb_object = parseInt(nbItem[n].nb_items);
      }
      nb_tot = nb_tot + parseInt(nbItem[n].nb_items);
    }
    //final = Object.assign({name: result['name']}, fina, {infoviewer3d: infoviewer3d})
    resu.nb_tot = nb_tot;
    resu.nb_file = nb_file;
    resu.nb_object = nb_object;
    res.status(200).sendData(resu);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN counting  file and object - virtualFolder", e, req, res);
  }
};

// Attach file to virtual folder
// Create URL ENDPOINT for /virtualFolderLink/:branche,:idVirtualFolder PUT
// TODO : prendre en compte le type de document (object file folder)
exports.createVirtualFolderLink = async function (req, res) {
  let { branche } = req.params;
  let idFolder = parseInt(req.body.idFolder);
  let { idUser } = req.body;
  let virtualFolder = parseInt(req.params.idVirtualFolder);
  //On récupère un id pour un virtualFolder et on y attache la sélection en cours pour le user donnée pour un
  // root folder donné (on travail par projet / depot)
  // on attache d'abord les items de type file
  try {
    let query =
      `INSERT INTO ${branche}_file_folder(id_file, id_folder) ` +
      `SELECT id_item , $1 from ${branche}_favorite_item WHERE id_user = $2 ` +
      `AND item_type = 'file' AND id_item  in (SELECT id from ${branche}_file WHERE id_folder IN (` +
      `SELECT id FROM ${branche}_folder WHERE folder_path <@ '${idFolder}')) `;

    const addVirtualFile = await db.any(query, [virtualFolder, idUser]);

    // Ensuite les objects
    try {
      const addVirtualobject = await db.any(
        `INSERT INTO ${branche}_folder_object(id_object, id_folder) ` +
          `SELECT id_item , $1 from ${branche}_favorite_item WHERE id_user = $2 ` +
          `AND item_type = 'object' AND id_item  in (SELECT o.id from ${branche}_object o ` +
          `INNER JOIN ${branche}_folder_object fo ON fo.id_object = o.id WHERE fo.id_folder IN (` +
          `SELECT id FROM ${branche}_folder WHERE folder_path <@ '${idFolder}')) `,
        [virtualFolder, idUser]
      );

      // Ensuite les unicos
      try {
        const addVirtualunicos = await db.any(
          `INSERT INTO ${branche}_folder_unico(id_unico, id_folder) ` +
            `SELECT id_item , $1 from ${branche}_favorite_item WHERE id_user = $2 ` +
            `AND item_type = 'unico' AND id_item  in (SELECT u.id from ${branche}_unico u ` +
            `INNER JOIN ${branche}_file fi ON fi.id = u.id_file WHERE fi.id_folder IN (` +
            `SELECT id FROM ${branche}_folder WHERE folder_path <@ '${idFolder}')) `,
          [virtualFolder, idUser]
        );

        res.status(201).sendData([]);
      } catch (e) {
        responseHelper.sendError(500, "server_error IN create link unico virtualFolder", e, req, res);
      }
    } catch (e) {
      responseHelper.sendError(500, "server_error IN create link object virtualFolder", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error IN create link file virtualFolder", e, req, res);
  }
};

// delete single element from virtual folder
//  Create URL ENDPOINT for /virtualFolderLink/:branche,:idUser,:idVirtualFolder  DELETE
// TODO : prendre en compte le type de lien  : file ou object (ou folder)
exports.deleteVirtualFolderLink = async function (req, res) {
  let { branche } = req.params;
  let idItem = parseInt(req.body.idItem);
  let virtualFolder = parseInt(req.params.idVirtualFolder);
  let type = req.body.itemType;
  let table = "";

  if (type === "object") table = "_folder_object";
  else if (type === "file") table = "_file_folder";
  else if (type === "unico") table = "_folder_unico";

  try {
    const delVirtual = await db.any(`DELETE FROM ${branche}${table} WHERE id_${type} = $1 AND id_folder = $2 `, [
      idItem,
      virtualFolder,
    ]);
    res.status(201).sendData([]);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN delete link item virtualFolder", e, req, res);
  }
};

// Recuperer le contenu d'un virtual folder
// Create url endpoint for /virtualFolderContent/:branche,:idFolder,:idVirtualFolder' GET
exports.getVirtualFolderContent = async function (req, res) {
  let { branche } = req.params;
  let idVirtualFolder = parseInt(req.params.idVirtualFolder);

  try {
    const getAll = await db.one(
      `SELECT array_agg(id_file) as files FROM ${branche}_file_folder WHERE id_folder = $1`,
      idVirtualFolder
    );
    res.status(200).sendData(getAll.files);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN get all content of virtualFolder", e, req, res);
  }
};

// Supprimer tout le contenu d'un virtual folder (seulement des liens symboliques entre les éléments)
// Create url endpoint for /virtualFolderContent/:branche,:idVirtualFolder' DELETE
// UPDATE du 21/05/2021 : cas des objets : on peut créer des objets dans des dossier virtuel
// Dans ce cas il faut pouvoir supprimer les objets liés symboliquement à un dossier virtuel MAIS NON les objects véritablement créé dans un virtual folder
exports.deleteVirtualFolderContent = async function (req, res) {
  let { branche } = req.params;
  let idVirtualFolder = parseInt(req.params.idVirtualFolder);

  try {
    // Use a transaction to ensure all deletions are atomic
    await db.tx(async t => {
      // 1/ First delete all references in related tables
      await t.none(`DELETE FROM ${branche}_object_object WHERE id_object_ref IN (SELECT id FROM ${branche}_object WHERE id_folder = $1)`, idVirtualFolder);
      await t.none(`DELETE FROM ${branche}_object_object WHERE id_object_min IN (SELECT id FROM ${branche}_object WHERE id_folder = $1)`, idVirtualFolder);
      await t.none(`DELETE FROM ${branche}_user_object WHERE id_object IN (SELECT id FROM ${branche}_object WHERE id_folder = $1)`, idVirtualFolder);
      await t.none(`DELETE FROM ${branche}_passport WHERE id_item IN (SELECT id FROM ${branche}_object WHERE id_folder = $1) AND item_type = 'object'`, idVirtualFolder);
      
      // 2/ Delete all links from the virtual folder
      await t.none(`DELETE FROM ${branche}_file_folder WHERE id_folder = $1`, idVirtualFolder);
      await t.none(`DELETE FROM ${branche}_folder_object WHERE id_folder = $1`, idVirtualFolder);
      await t.none(`DELETE FROM ${branche}_folder_unico WHERE id_folder = $1`, idVirtualFolder);

      // 3/ Delete unicos that are linked to files in this folder
      await t.none(`DELETE FROM ${branche}_unico WHERE id_file IN (SELECT id FROM ${branche}_file WHERE id_folder = $1)`, idVirtualFolder);

      // 4/ Delete all actual items whose id_folder is the virtual folder
      await t.none(`DELETE FROM ${branche}_file WHERE id_folder = $1`, idVirtualFolder);
      await t.none(`DELETE FROM ${branche}_object WHERE id_folder = $1`, idVirtualFolder);

      // 5/ Verify the deletion by counting remaining items
      const remainingItems = await t.one(
        `SELECT
          COALESCE((SELECT COUNT(*) FROM ${branche}_file_folder WHERE id_folder = $1), 0) as file_links,
          COALESCE((SELECT COUNT(*) FROM ${branche}_folder_object WHERE id_folder = $1), 0) as object_links,
          COALESCE((SELECT COUNT(*) FROM ${branche}_folder_unico WHERE id_folder = $1), 0) as unico_links,
          COALESCE((SELECT COUNT(*) FROM ${branche}_file WHERE id_folder = $1), 0) as files,
          COALESCE((SELECT COUNT(*) FROM ${branche}_object WHERE id_folder = $1), 0) as objects,
          COALESCE((SELECT COUNT(*) FROM ${branche}_unico u INNER JOIN ${branche}_file f ON u.id_file = f.id WHERE f.id_folder = $1), 0) as unicos`,
        idVirtualFolder
      );

      // Return the counts to verify deletion was successful
      return remainingItems;
    })
    .then(result => {
      // Convert all counts to numbers to ensure consistent type
      const response = {
        file_links: Number(result.file_links),
        object_links: Number(result.object_links),
        unico_links: Number(result.unico_links),
        files: Number(result.files),
        objects: Number(result.objects),
        unicos: Number(result.unicos)
      };
      res.status(200).sendData(response);
    })
    .catch(error => {
      console.error('Error deleting virtual folder content:', error);
      responseHelper.sendError(500, "server_error IN delete virtual folder content", error, req, res);
    });

  } catch (e) {
    console.error('Error in deleteVirtualFolderContent:', e);
    responseHelper.sendError(500, "server_error IN delete virtual folder content", e, req, res);
  }
};

// Recuperer la taille d'un virtual folder
// Suppose que les info file_size sont bien renseignées en base de données
// Create url endpoint for /virtualFolderSize/:branche,:idVirtualFolder GET
exports.getVirtualFolderSize = async function (req, res) {
  let { branche } = req.params;
  let idVirtualFolder = parseInt(req.params.idVirtualFolder);

  try {
    const getSize = await db.one(
      `SELECT sum(file_size) as size FROM ${branche}_file ` +
        `WHERE id IN (SELECT id_file FROM ${branche}_file_folder WHERE id_folder = $1) `,
      idVirtualFolder
    );
    res.status(200).sendData(getSize);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN get size of virtualFolder", e, req, res);
  }
};

// Retourne true si le folder est virtual, false sinon
// Create url endpoint for /isFolderVirtual/:branch,:idFolder GET
exports.getIsFolderVirtual = async function (req, res) {
  let { branch } = req.params;
  let idFolder = parseInt(req.params.idFolder);
  try {
    const isVirtual = await db.one(
      `SELECT CASE WHEN folder_name IS NULL THEN true ELSE false END as virtual FROM ${branch}_folder WHERE id = $1 `,
      idFolder
    );
    res.status(200).sendData(isVirtual);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN get size of virtualFolder", e, req, res);
  }
};

// Récupérer la  profondeur max d'une branche (projet / depôt) pour réorganiser les ordres des dossiers (admin)
// Create url endpoint for /maxProf/:branche,:idFolder GET
exports.getprofondeurFolder = async function (req, res) {
  let { branche } = req.params;
  try {
    const getProfondeur = await db.one(
      `SELECT max(nlevel(folder_path)) as profondeur FROM ${branche}_folder ` +
        `WHERE folder_path ~ '*.${req.params.idFolder}.*'  `
    );
    res.status(200).sendData(getProfondeur);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN get size of virtualFolder", e, req, res);
  }
};

// Changer l'ordre d'un seul folder (à faire tourner pour tous les folder concernés)
// Create url endpoint for /changeFolderOrder/:branche
exports.changeFolderOrder = async function (req, res) {
  let { branche } = req.params;
  let idFolder = parseInt(req.body.idF);
  let rank = parseInt(req.body.rank);
  try {
    const change = await db.one(`SELECT ${branche}_change_folder_order($1,$2) as change `, [idFolder, rank]);
    res.status(200).sendData(change);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN change Folder Order", e, req, res);
  }
};

// Après toutes les itérations d'un changement de rank mettre ç jour globalement la tbel des folder pour tous les global_rank qui ont été mis à null
// Create url endpoint for /updateGlobalRankFolderOrder/:branche
exports.updateGlobalRankFolderOrder = async function (req, res) {
  let { branche } = req.params;
  try {
    const gl = await db.any(`SELECT update_${branche}_folder_global_rank() as gb `);
    res.status(200).sendData(gl);
  } catch (e) {
    responseHelper.sendError(500, "server_error IN update Global Rank Folder", e, req, res);
  }
};

// get url endpoint for /foldersFromSite/:branche,:code,:value  : récupérer la liste des id des folder
// qui correspondent à des sites (à la valeur donnée pour le code voulu
// au départ pour le code siteNom puis étendu à lieuDecouverte, mais pour n'importe quel code
exports.getFoldersFromCodeValue = async function (req, res) {
  let { value } = req.params,
    { code } = req.params,
    { branche } = req.params;

  try {
    const listFolder = await db.any(
      `SELECT d.id_item, d.value[1], f.doi, f.id_representative_picture as id_file, nakala.value[1] as nakala ` +
        `FROM ${branche}_passport s ` +
        `INNER JOIN ${branche}_passport d ON d.id_item = s.id_item AND d.item_type = s.item_type  AND d.id_metadata = ` +
        ` (SELECT id FROM ${branche}_metadata WHERE code = 'nomDepot' limit 1 ) ` +
        `INNER JOIN ${branche}_folder f ON f.id = d.id_item AND d.item_type = 'folder' ` +
        `LEFT OUTER  JOIN ${branche}_passport nakala ON nakala.id_item = s.id_item AND nakala.item_type = s.item_type AND nakala.id_metadata = ` +
        ` (SELECT id FROM ${branche}_metadata WHERE code = 'nakala' ) ` +
        `WHERE s.id_metadata= (SELECT id FROM ${branche}_metadata WHERE code = $1 limit 1 ) AND s.value[1]= $2 ` +
        `AND s.item_type = 'folder' `,
      [code, decodeURI(value)]
    );
    res.status(200).sendData(listFolder);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getFolderFromSite GET", e, req, res);
  }
};

exports.patchFolderName = async function (req, res) {
  let idF = req.params.idFolder;
  let name = decodeURI(req.body.name);
  let { branche } = req.params;

  try {
    const monId = db.none(`UPDATE ${branche}_folder SET name = $1 ` + ` WHERE  id = $2 `, [name, idF]);
    res.status(200).sendData([]);
  } catch (e) {
    responseHelper.sendError(500, "server_error in patch name Folder", e, req, res);
  }
};

// Create url endpoint for /limitedPublicFolders/:root GET
// all folder (real and virtual)
// Limités aux folders des projets gérés par un scribe
exports.getLimitedPublicFolders = async function (req, res) {
  let { root } = req.params;
  let { userId } = req.query;
  let requWhere = "";
  let queryTot = "";

  db.oneOrNone({
    name: "nbFodlers",
    text: `SELECT array_agg(id_folder) as nb FROM ${root}_user_folder WHERE id_user = $1 `,
    values: [userId],
  })
    .then((resul) => {
      let data = resul.nb;
      if (data) {
        if (data.length > 1) {
          // créer pour chaque folder la condition WHERE
          for (let i = 0; i < data.length; i++) {
            if (i > 0) {
              if (i < data.length) requWhere += " OR ";
            }
            requWhere += ` id IN (SELECT id from ${root}_folder WHERE folder_path <@'${data[i]}') `;
          }
        } else if (data.length === 1)
          requWhere = ` id IN (SELECT id from ${root}_folder WHERE folder_path <@'${data[0]}' )`;

        queryTot =
          `SELECT id, name, folder_name, nb_images, ${root}_get_path(id) as fullname ` +
          `FROM ${root}_folder ` +
          `WHERE id IN (SELECT id FROM ${root}_folder WHERE status = 'public' ) AND ( ${requWhere} ) ORDER BY fullname`;

        return db
          .any({
            name: "limitPublicFolders",
            text: queryTot,
          })
          .then((data) => res.status(200).sendData(data))
          .catch((e) => {
            console.log(e);
            responseHelper.sendError(500, "server_error IN GET limited Public folders", e, req, res);
          });
      } else {
        return res.status(200).sendData("0");
      }
    })
    .catch((e) => {
      console.log(e);
      responseHelper.sendError(500, "server_error IN GET limited Public Folders", e, req, res);
    });
};

// Create url endpoint for /limitedPrivateFolders/:root GET
// all folder (real and virtual)
// Limités aux folders des projets gérés par un scribe
exports.getLimitedPrivateFolders = async function (req, res) {
  let { root } = req.params;
  let { userId } = req.query;
  let requWhere = "";
  let queryTot = "";

  db.oneOrNone({
    name: "nbFodlers",
    text: `SELECT array_agg(id_folder) as nb FROM ${root}_user_folder WHERE id_user = $1 `,
    values: [userId],
  })
    .then((resul) => {
      let data = resul.nb;
      if (data) {
        if (data.length > 1) {
          // créer pour chaque folder la condition WHERE
          for (let i = 0; i < data.length; i++) {
            if (i > 0) {
              if (i < data.length) requWhere += " OR ";
            }
            requWhere += ` id IN (SELECT id from ${root}_folder WHERE folder_path <@'${data[i]}') `;
          }
        } else if (data.length === 1)
          requWhere = ` id IN (SELECT id from ${root}_folder WHERE folder_path <@'${data[0]}' )`;
        //console.log(requWhere)

        queryTot =
          `SELECT id, name, folder_name, nb_images, ${root}_get_path(id) as fullname ` +
          `FROM ${root}_folder ` +
          `WHERE id IN (SELECT id FROM ${root}_folder WHERE status = 'private' ) AND ( ${requWhere} ) ORDER BY fullname`;

        return db
          .any({
            name: "limitPrivateFolders",
            text: queryTot,
          })
          .then((data) => {
            return res.status(200).sendData(data);
          })
          .catch((e) => {
            console.log(e);
            return responseHelper.sendError(500, "server_error IN GET limited Private folders", e, req, res);
          });
      } else {
        return res.status(200).sendData("0");
      }
    })
    .catch((e) => {
      console.log(e);
      responseHelper.sendError(500, "server_error IN GET limited Private Folders", e, req, res);
    });
};
