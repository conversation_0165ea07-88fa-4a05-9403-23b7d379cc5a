const db = require("../../helpers/db").default;
const responseHelper = require("../../helpers/response");

exports.getBranche = async function (req, res) {
  let { lng } = req.params

  let description = (lng ==='fr') ? 'description_fr' : 'description_en' ;
  try {
    const branche = await db.any(`SELECT id, branchename, ${description} as description FROM archeogrid_branche`);
    // success
    res.status(200).sendData(branche);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in branche archeogrid", e, req, res);
  }
};

// GET route for /licenseItem/:branche,:idItem,:itemType,:lng  GET
exports.getLicenseFromItem = async function (req, res) {
  let branche = req.params.branche;
  let idItem = req.params.idItem;
  let lng = req.params.lng === 'fr' ? 'fr' : 'en'; // fr ou en si autre chose que en : on met en !
  let result = [];
  let type = req.params.itemType;

  try {
    const licenseInfo = await db.oneOrNone(
      `SELECT a.id, license_name, url,
        html_img_${lng} as html_img ,
        html_text_${lng} as html_text,
        concat(html_img_${lng}, html_text_${lng}) as html
        FROM archeogrid_license a INNER JOIN
        ${branche}_thesaurus_license tl ON tl.id_license = a.id
        INNER JOIN
        ${branche}_thesaurus_item ti ON ti.thesaurus = 'license' AND ti.id_thes_thesaurus = tl.id_thes
        AND ti.item_type = '${type}'
        WHERE ti.id_item = $1
        LIMIT 1 `, idItem );
    // success
    if (licenseInfo) result = licenseInfo;
    res.status(200).sendData(result);
  } catch (e) {
    // error
    responseHelper.sendError(
      500,
      "server_error in GET license archeogrid FROM thesaurus item of any type ",
      e,
      req,
      res
    );
  }
};

// GET route for '/license/:branche,:lng' GET
exports.getLicenseGeneralInfo = async function (req, res) {
  let branche = req.params.branche;
  let lng = req.params.lng === 'fr' ? 'fr' : 'en';

  try {
    const licenseInfo = await db.any(
      " SELECT a.id, license_name, label, url, " +
        "html_img_" +
        lng +
        " as html_img , " +
        "html_text_" +
        lng +
        " as html_text, " +
        "t.identifier " +
        " FROM archeogrid_license a INNER JOIN " +
        branche +
        "_thesaurus_license tl ON tl.id_license = a.id " +
        "INNER JOIN " +
        branche +
        "_thesaurus t ON t.thesaurus = 'license' AND t.id_thes = tl.id_thes "
    );
    // success
    res.status(200).sendData(licenseInfo ?? null);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in GET all license archeogrid infos", e, req, res);
  }
};
