function updateSliderVisibility() {
    const $gridBtn = $('#grid-btn');
    const $compactSliderContainer = $('#compact-slider-container');
    const $gridSliderContainer = $('#grid-slider-container');

    if ($gridBtn.hasClass('btn-active')) {
        // Grid view is active - show grid slider, hide compact slider
        $gridSliderContainer.removeClass('d-none');
        $compactSliderContainer.addClass('d-none');
    } else {
        // List view is active - show compact slider, hide grid slider
        $compactSliderContainer.removeClass('d-none');
        $gridSliderContainer.addClass('d-none');
    }
}

let gridColumns = 7; // Default number of columns (medium setting)
let compactMode = 0; // Default to normal view (0 = normal, 1 = compact)

// Function to update grid columns based on slider value
function updateGridColumns(columns) {
    document.documentElement.style.setProperty('--grid-columns', columns);

    const scaleFactor = 7 / columns;
    document.documentElement.style.setProperty('--scale-factor', scaleFactor);

    $('body').addClass('grid-size-active');

    $('body').attr('data-columns', columns);

    saveGridColumnsPreference(columns);
}

// Function to update compact view based on slider value
function updateCompactView(mode) {
    compactMode = mode;
    const $body = $('body');
    
    if (compactMode === 1) {
        $body.addClass('compact-list-view');
    } else {
        $body.removeClass('compact-list-view');

        setTimeout(function() {
            if (typeof refreshLazyLoadingForVisibleImages === 'function') {
                refreshLazyLoadingForVisibleImages();
            } else if (typeof handleNewlyVisibleContainer === 'function') {
                const exploreResults = document.getElementById('explore-results');
                if (exploreResults) {
                    handleNewlyVisibleContainer(exploreResults);
                }
            } else if (typeof initLazyLoading === 'function') {
                initLazyLoading();
            }
        }, 100);
    }
    
    saveCompactViewPreference(compactMode);
}

function updateGridTooltip(slider) {
    const tooltip = $('#grid-slider-tooltip');
    if (!tooltip.length) return;

    const value = slider.val();
    const min = slider.attr('min');
    const max = slider.attr('max');
    const sliderWidth = slider.width();
    
    const percent = (value - min) / (max - min);
    const thumbWidth = 16;
    const offset = (percent * (sliderWidth - thumbWidth)) + (thumbWidth / 2);
    
    tooltip.text(value);
    tooltip.css('left', `${offset}px`);
}

function saveGridColumnsPreference(columns) {
    try {
        localStorage.setItem('gridColumns', columns.toString());
    } catch (e) {
        console.error('Failed to save grid columns preference:', e);
    }
}

function saveCompactViewPreference(mode) {
    try {
        localStorage.setItem('compactMode', mode.toString());
    } catch (e) {
        console.error('Failed to save compact view preference:', e);
    }
}

// Function to load grid columns preference from localStorage
function loadGridColumnsPreference() {
    try {
        const savedColumns = localStorage.getItem('gridColumns');
        if (savedColumns) {
            const columns = parseInt(savedColumns, 10);
            if (!isNaN(columns) && columns >= 4 && columns <= 10) {
                gridColumns = columns;
                return true;
            }
        }
    } catch (e) {
        console.error('Failed to load grid columns preference:', e);
    }
    return false;
}

// Function to load compact view preference from localStorage
function loadCompactViewPreference() {
    try {
        const savedMode = localStorage.getItem('compactMode');
        if (savedMode) {
            const mode = parseInt(savedMode, 10);
            if (!isNaN(mode) && (mode === 0 || mode === 1)) {
                compactMode = mode;
                return true;
            }
        }
    } catch (e) {
        console.error('Failed to load compact view preference:', e);
    }
    return false;
}

$(document).ready(function() {
    const $compactSlider = $('#compact-slider');
    const $gridSlider = $('#grid-slider');

    // Load preferences
    loadGridColumnsPreference();
    loadCompactViewPreference();

    // Initialize grid slider
    $('body').addClass('grid-size-active');
    $('body').attr('data-columns', gridColumns);
    document.documentElement.style.setProperty('--grid-columns', gridColumns);

    const scaleFactor = 7 / gridColumns;
    document.documentElement.style.setProperty('--scale-factor', scaleFactor);

    // Set grid slider value to match loaded preference
    $gridSlider.val(gridColumns);
    updateGridTooltip($gridSlider);

    // Set list size permanently to level 2
    $('body').addClass('list-size-active');
    document.documentElement.style.setProperty('--list-size-level', 2);

    // Initialize compact view
    $compactSlider.val(compactMode);
    updateCompactView(compactMode);

    // Update slider visibility based on current view mode
    updateSliderVisibility();

    // Ensure lazy loading is properly initialized after view mode is set
    setTimeout(function() {
        if (typeof refreshLazyLoadingForVisibleImages === 'function') {
            refreshLazyLoadingForVisibleImages();
        }
    }, 200);

    // Grid slider event handler
    $gridSlider.on('input', function() {
        const newColumns = parseInt($(this).val(), 10);
        updateGridColumns(newColumns);
        gridColumns = newColumns;
        updateGridTooltip($(this));
    });

    // Compact slider event handler
    $compactSlider.on('input', function() {
        const newMode = parseInt($(this).val(), 10);
        updateCompactView(newMode);
    });
});
