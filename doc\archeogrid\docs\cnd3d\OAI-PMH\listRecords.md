---
sidebar_position: 1
---

# Routes

In order to be harvested by other OAI-PMH services, the CND3D implementes multiples routes wich are formed with the access point URL **https://3d.humanities.science/oai**, the verb (defined by the OAI-PMH protocol) and possibly others parameters depending on the verb.

## Verbs

### Identify

Data warehouse information.

> **Arguments**: None

Example with **https://3d.humanities.science/oai?verb=Identify**:

```xml
    <OAI-PMH xmlns="http://www.openarchives.org/OAI/2.0/" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://www.openarchives.org/OAI/2.0/ http://www.openarchives.org/OAI/2.0/OAI-PMH.xsd">
        <responseDate>2025-04-07T11:52:51Z</responseDate>
        <request verb="Identify">https://3d.humanities.science/oai</request>
        <Identify>
            <repositoryName>Base de données Conservatoire National des Données 3D SHS - CNRS</repositoryName>
            <baseURL>https://3d.humanities.science/oai</baseURL>
            <protocolVersion>2.0</protocolVersion>
            <adminEmail><EMAIL></adminEmail>
            <earliestDatestamp>2019-10-04T00:00:00Z</earliestDatestamp>
            <deletedRecord>transient</deletedRecord>
            <granularity>YYYY-MM-DDT00:00:00Z</granularity>
            <compression>gzip</compression>
        </Identify>
    </OAI-PMH>
```
### GetRecord

Retrieves a given record from the CND3D.

> **Arguments**: identifier, metadataPrefix

Example with **https://3d.humanities.science/oai?verb=GetRecord&metadataPrefix=oai_dc&identifier=oai:3d.humanities.science:CND3D:728963_o_2023**:

```xml
    <OAI-PMH xmlns="http://www.openarchives.org/OAI/2.0/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.openarchives.org/OAI/2.0/ http://www.openarchives.org/OAI/2.0/OAI-PMH.xsd">
    <responseDate>2025-04-07T12:15:20Z</responseDate>
    <request verb="GetRecord" metadataPrefix="oai_dc" identifier="oai:3d.humanities.science:CND3D:728963_o_2023">https://3d.humanities.science/oai</request>
    <GetRecord>
        <record>
            <header>
                <identifier>oai:3d.humanities.science:CND3D:728963_o_2023</identifier>
                <datestamp>2023-02-15T16:13:20Z</datestamp>
                <setSpec>oai_dc:cnd3d_set0</setSpec>
            </header>
            <metadata>
                <oai_dc:dc xmlns:oai_dc="https://www.openarchives.org/OAI/2.0/oai_dc/"
                 xmlns:dc="http://purl.org/dc/elements/1.1/" 
                 xsi:schemaLocation="https://www.openarchives.org/OAI/2.0/oai_dc/ http://purl.org/dc/elements/1.1/">
                    <dc:title>Gavrinis, dalle de couverture P1</dc:title>
                    <dc:creator>Valentin Grimaud</dc:creator>
                    <dc:publisher>Archeovision</dc:publisher>
                    <dc:date>2020</dc:date>
                    <dc:type>3D object</dc:type>
                    <dc:format>1 fichier 3D (ply), 253 fichiers image (jpg)</dc:format>
                    <dc:identifier>https://doi.org/10.34969/CND3D/728963.o.2023</dc:identifier>
                    <dc:source>https://doi.org/10.34969/CND3D/347335.d.2023</dc:source>
                    <dc:language>fr</dc:language>
                    <dc:relation>https://3d.humanities.science/collection/561160001_Gavrinis</dc:relation>
                    <dc:coverage>47.5734,-2.89803</dc:coverage>
                    <dc:coverage>Gavrinis</dc:coverage>
                    <dc:coverage>Néolithique</dc:coverage>
                    <dc:rights>Département du Morbihan</dc:rights>
                </oai_dc:dc>
            </metadata>
        </record>
    </GetRecord>
    </OAI-PMH>
```

### ListIdentifiers

Retrieves the list of available identifiers.

> **Arguments**: from, until, metadataPrefix, set, resumptionToken

Example with **https://3d.humanities.science/oai?verb=ListIdentifiers&metadataPrefix=oai_dc**:

```xml
    <OAI-PMH xmlns="http://www.openarchives.org/OAI/2.0/" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://www.openarchives.org/OAI/2.0/ http://www.openarchives.org/OAI/2.0/OAI-PMH.xsd">
        <responseDate>2025-04-07T12:13:23Z</responseDate>
        <request verb="ListIdentifiers" metadataPrefix="oai_dc">https://3d.humanities.science/oai</request>
        <ListIdentifiers>
            <header>
                <identifier>oai:3d.humanities.science:CND3D:207944_o_2023</identifier>
                <datestamp>2023-02-15T17:24:57Z</datestamp>
                <setSpec>oai_dc:cnd3d_set0</setSpec>
            </header>
            <header>
                <identifier>oai:3d.humanities.science:CND3D:728963_o_2023</identifier>
                <datestamp>2023-02-15T17:13:20Z</datestamp>
                <setSpec>oai_dc:cnd3d_set0</setSpec>
            </header>
        </ListIdentifiers>
    </OAI-PMH>
```

### ListMetadataFormats

Requests a list of available metadata formats. Without parameters, all available formats for at least one item are returned. With the identifier parameter, only the formats available for the item in question are returned.

> **Arguments**: identifier

Example with **https://3d.humanities.science/oai?verb=ListMetadataFormats**:

```xml
    <OAI-PMH xmlns="http://www.openarchives.org/OAI/2.0/" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://www.openarchives.org/OAI/2.0/ http://www.openarchives.org/OAI/2.0/OAI-PMH.xsd">
        <responseDate>2025-04-07T12:12:17Z</responseDate>
        <request verb="ListMetadataFormats">https://3d.humanities.science/oai</request>
        <ListMetadataFormats>
            <metadataFormat>
            <metadataPrefix>oai_dc</metadataPrefix>
            <schema>https://www.openarchives.org/OAI/2.0/oai_dc.xsd</schema>
            <metadataNamespace>https://www.openarchives.org/OAI/2.0/oai_dc/</metadataNamespace>
            </metadataFormat>
        </ListMetadataFormats>
    </OAI-PMH>
```

### ListSets

Requests a list of sets available in a warehouse. The answer can be on several pages.

> **Arguments**: resumptionToken

Example with **https://3d.humanities.science/oai?verb=ListSets**:

```xml
    <OAI-PMH xmlns="http://www.openarchives.org/OAI/2.0/" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xsi:schemaLocation="http://www.openarchives.org/OAI/2.0/ http://www.openarchives.org/OAI/2.0/OAI-PMH.xsd">
        <responseDate>2025-04-07T12:12:42Z</responseDate>
        <request verb="ListSets">https://3d.humanities.science/oai</request>
        <ListSets>
            <set>
            <setSpec>oai_dc:cnd3d_set01</setSpec>
            <setName>Base de données Conservatoire National des Données 3D SHS - CNRS - CND3D</setName>
            <setDescription>Objects 3D du Conservatoire National des données 3D SHS</setDescription>
            </set>
        </ListSets>
    </OAI-PMH>
```

### ListRecords

Retrieves the list of available records.

Futher explained in the next section (ListRecords).

> **Arguments**: from, until, metadataPrefix, set, resumptionToken