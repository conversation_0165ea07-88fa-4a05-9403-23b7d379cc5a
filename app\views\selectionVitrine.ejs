<!-- Selection Vitrine styles -->
<link rel="stylesheet" href="/css/selection-vitrine.css">

<!-- Hidden fields for JavaScript -->
<input type="hidden" id="user-id" value="<%= user.id %>">
<input type="hidden" id="project-id" value="<%= projectId %>">
<input type="hidden" id="language" value="<%= lng %>">
<input type="hidden" id="items-text" value="<%=__('items')%>">
<input type="hidden" id="no-items-selected-text" value="<%=__('noItemsSelected')%>">
<input type="hidden" id="select-at-least-one-text" value="<%=__('selectAtLeastOne')%>">
<input type="hidden" id="select-only-one-text" value="<%=__('selectOnlyOne')%>">
<input type="hidden" id="no-results-text" value="<%=__('noResultsFound')%>">
<input type="hidden" id="selection-data" value='<%- JSON.stringify(locals.selectionData || []) %>'>

<%- include('utils/title-content', { home: "home" }) %>

<!-- Main content container -->
<div class="selection-content-container">
    <div class="row w-100">
        <!-- Left column: Explore results container -->
        <div class="col-md-8">
            <!-- Include explore vitrine component -->
            <div class="explore-container p-3 mb-3">
                <h4 class="explore-heading mb-3"><%=__('selectionFor')%> <%= data['name'] %></h4>
                <!-- Include explore vitrine component -->
                <%- include('explore/exploreVitrine', { selection_dropdown: false }) %>
            </div>
        </div>

        <!-- Right column: Virtual folder functionality -->
        <div class="col-md-4">
            <div class="virtual-folder-container">
                <h4 class="mb-3"><%=__('virtualFolders')%></h4>

                <!-- Create new virtual folder button -->
                <div class="px-2 mb-3">
                    <div class="button-wrapper">
                        <button class="btn btn-primary btn-sm create-vf-btn" onclick="addVirtualFolder(this, '<%= projectId %>', '<%= lng %>')">
                            <%= __('createVirtualFolder') %>
                        </button>
                    </div>
                </div>

                <%
                    const getDisplayName = (path) => {
                        if (!path) return '';
                        const parts = path.split('/');
                        if (parts.length > 1) {
                            return parts.slice(1).join('/');
                        }
                        return path;
                    };
                %>

                <% if (locals.virtualFolders) { %>
                <form action="/addSelectionToVirtualFolder/<%=user.id%>,<%=projectId%>" method="post">
                    <div class="px-2 mb-3 d-flex flex-column gap-2">
                        <div class="form-text fs-6"><%= __('txt_integrate')%></div>
                        <div class="d-flex flex-column gap-2">
                            <select name="virtualFolder" size="1" class="form-select searchable-select" data-no-bracket-filter="true" data-simple-mode="true" placeholder="<%=__('selectCollection')%>">
                                <% for (let j = 0; j < virtualFolders.length; j++) { %>
                                    <option value="<%=virtualFolders[j]['id']%>"><%= getDisplayName(virtualFolders[j]['completename']) %></option>
                                <% } %>
                            </select>
                            <div class="button-wrapper">
                                <input class="btn btn-secondary btn-sm" type="submit" value="<%=__('txt_integrate_button')%>" />
                            </div>
                        </div>
                    </div>
                </form>
                <% } %>

                <% if (locals.objectsFolders) { %>
                <form action="/addSelectionToVirtualObject/<%=user.id%>,<%=projectId%>" method="post">
                    <div class="px-2 mb-3 d-flex flex-column gap-2">
                        <div class="form-text fs-6"><%= __('txt_integrate_obj')%></div>
                        <div class="d-flex flex-column gap-2">
                            <select name="idObject" size="1" class="form-select searchable-select" data-no-bracket-filter="true" data-simple-mode="true" placeholder="<%=__('selectObject')%>">
                                <% for (let j = 0; j < objectsFolders.length; j++) { %>
                                    <option value="<%=objectsFolders[j]['id']%>"><%= getDisplayName(objectsFolders[j]['completename'] + '/' + objectsFolders[j]['name']) %></option>
                                <% } %>
                            </select>
                            <div class="button-wrapper">
                                <input class="btn btn-secondary btn-sm" type="submit" value="<%=__('txt_link_button')%>" />
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Illustrate Object Form - Always displayed but conditionally enabled via JavaScript -->
                <form action="/addSelectionToIllustrateObject/<%=user.id%>,<%=projectId%>" method="post">
                    <div class="px-2 mb-3 d-flex flex-column gap-2">
                        <input type="hidden" value="<%= locals.selectionData && locals.selectionData[0] ? locals.selectionData[0].id_item : '' %>" name="idFile">
                        <div class="form-text"><%= __('txt_illustrate_obj')%></div>
                        <div class="d-flex flex-column gap-2">
                            <select name="idObject" size="1" class="form-select searchable-select" data-no-bracket-filter="true" data-simple-mode="true" placeholder="<%=__('selectObject')%>">
                                <% for (let j = 0; j < objectsFolders.length; j++) { %>
                                    <option value="<%=objectsFolders[j]['id']%>"><%= getDisplayName(objectsFolders[j]['completename'] + '/' + objectsFolders[j]['name']) %></option>
                                <% } %>
                            </select>
                            <div class="button-wrapper">
                                <input class="btn btn-secondary btn-sm" type="submit" value="<%=__('txt_illustrate_button')%>" />
                            </div>
                        </div>
                    </div>
                </form>
                <% } %>
            </div>
        </div>
    </div>
</div>

<script>
  window.i18n = {
    exploreRandomNoResult: "<%= __('exploreRandomNoResult') %>",
    noResult: "<%= __('noResult') %>",
    errorOccured: "<%= __('errorOccured') %>",
    emptyResponse: "<%= __('emptyResponse') %>"
  };
</script>

<script src="/js/projects.js"></script>
<script src="/js/archeogrid.js"></script>
<script src="/js/explore.js"></script>
<script src="/js/searchable-dropdown.js"></script>
<script src="/js/selectionVitrine.js"></script>
<script src="/js/lazy-loading.js"></script>

<!-- Toast container for notifications -->
<div class="toast-container bottom-0 end-0 p-3">
    <div id="project-toast" class="toast align-items-center text-bg-success border-0" role="alert"
         aria-live="assertive"
         aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
    // Initialize toast for notifications
    document.addEventListener('DOMContentLoaded', function() {
        const toastElement = document.getElementById("project-toast");
        if (toastElement && typeof bootstrap !== 'undefined') {
            bootstrap.Toast.getOrCreateInstance(toastElement);
        }

        // Initialize all dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
        var dropdownList = dropdownElementList.map(function(dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });
</script>
