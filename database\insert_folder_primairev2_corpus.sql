CREATE OR REPLACE FUNCTION insert_folder_primairev2_corpus(p_id_folder INTEGER, p_id_site INTEGER)
RETURNS SETOF INTEGER
LANGUAGE plpgsql
AS $$

-- V2 : ajout du répertoire metadata par défaut pour pouvoir ingérer en masse les csv déposés dans ce répertoire
DECLARE
  v_id_parent_folder BIGINT;
  v_id_folder BIGINT;
BEGIN

  FOR v_id_folder IN SELECT DISTINCT id FROM corpus_folder WHERE id = $1  
  LOOP

      INSERT INTO corpus_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('corpus_folder_id_seq'), '3DOnline', '3DOnline', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM corpus_folder WHERE id = $1;
      INSERT INTO corpus_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('corpus_folder_id_seq'), 'Complements', 'Complements', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM corpus_folder WHERE id = $1;
      INSERT INTO corpus_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('corpus_folder_id_seq'), 'unicos', 'unicos', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM corpus_folder WHERE id = $1;
      INSERT INTO corpus_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('corpus_folder_id_seq'), 'metadata', 'metadata', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM corpus_folder WHERE id = $1;

      RETURN NEXT v_id_folder;
  END LOOP;
  RETURN;

END;
$$;

