import clsx from "clsx";
import Heading from "@theme/Heading";
import styles from "./styles.module.css";
import Translate from "@docusaurus/Translate";
import Link from "@docusaurus/Link";

type FeatureItem = {
  title: string;
  description: JSX.Element;
};

const FeatureList: FeatureItem[] = [
  {
    title: "Docusaurus",
    description: (
      <>
        <Translate>This documentation site was generated with </Translate>
        <span>Docusaurus</span>. <Translate>Learn more: </Translate>
        <Link href="https://docusaurus.io/">docusaurus.io</Link>
      </>
    ),
  },
  {
    title: "Admin",
    description: null,
  },
  {
    title: "Development",
    description: null,
  },
  {
    title: "CND3D",
    description: null,
  },
];

function Feature({ title, description }: FeatureItem) {
  return (
    <div className={clsx("col col--4")}>
      <div className="text--center padding-horiz--md">
        <Heading as="h3">{title}</Heading>
        <p>{description}</p>
      </div>
    </div>
  );
}

export default function HomepageFeatures(): JSX.Element {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className="row">
          {FeatureList.map((props, idx) => (
            <Feature key={idx} {...props} />
          ))}
        </div>
      </div>
    </section>
  );
}
