<% if ((context === 'p') || (context === 'd'))  { %>
<%- include('unicoModal') %>
<div class="row">
    <div class="col-2">
        <% if (mainFolder?.image) { %>
        <a href="/project/<%= mainFolder.mainfolder %>" title="<%=__('backprojectpage')%>">
            <img src="/assets/images/<%= mainFolder.image %>" style="height: 4em;margin-left: 0em;margin-bottom: 0em;margin-top: 0em;"></a>
        <% } else { %>
        <a href="/project/<%= mainFolder.mainfolder %>" title="<%=__('backprojectpage')%>">
            <img src="/assets/images/ArcheoGRID_<%= branch %>.png" style="height: 4em;margin-left: 0em;margin-bottom: 0em;margin-top: 0em;">
        </a>
        <% } %>
    </div>
    <div class="col-8  text-center">

    </div>
    <div class="col-2" id="menuDroit">
    </div>
</div>

<div class="container">
    <% } %>
    <div class="text-center">
        <div class="superpose"><div class="image_wrapp">
        <% if (image['file_ext'] === 'url') { %>
        <a href="<%= image['urlTarget']%>" target="_blank">
            <img class="mx-auto"
                 src="/media/image?fid=<%= folderId %>&id=<%= image['id'] %>&format=<% if (context == 'p' && image['parent_unico']==0) { %><%= image['srcImgSmall']%>&type=small&root=<%= root %>"
                 <% } else { %><%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"<% } %>
                 alt="<%= image['filename']%>" <% if (context == 'm') { %>style="width: 200px;" <% } %>>
        </a>
        <% } else if (image['file_ext'] === '3d') { %>
        <%# si le user est guest , il n'a pas accès à la 3D %>
        <% if (HDrights) { %><a href="/viewer3d,<%= image['srcImg3d'] %>,<%= folderId %>,<%= root%>"
           title="The 3D viewer" target="_blank"><% } %>
            <img class="card-img mx-auto d-block"
                 src="/media/image?fid=<%= folderId %>&id=<%= image['id']%>&format=<%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"
                 alt="<%= image['filename'] %>" style="width: 200px;">
                    <img src="/assets/images/pleinecran.png" class="image_superpose">
            <% if (HDrights) { %></a><% } %>
        <% } else if (image['file_ext'] === 'ply') { %>
        <%# on met en place le viewer 3d s'il existe %>
        <% if (viewer.length >0 ) { %>
        <% for (let i = 0; i< viewer.length; i++) { %>
        <a href="<%= viewer[i]['value']%>" title="Voir le modèle 3D" target="_blank">
            <img class="mx-auto" src="/media/image?fid=<%= folderId %>&id=<%= image['id'] %>&format=
                        <% if (context == 'p' && image['parent_unico']==0) { %>
                            <%= image['srcImgSmall']%>&type=small&root=<%= root %>"
                 <% } else { %>
                 <%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"<% } %> alt="<%= image['filename']%>"
                 <% if (context == 'm') { %>style="width: 200px;" <% } %>>
        </a>
        <% } %>
        <% } %>
        <% } else { %>
        <% if ( viewerFormat.indexOf(image['file_ext']) !== -1 ) { %>
        <a href="/viewer/<%= folderId %>_<%= image['id'] %>" target="_blank"
           title="<%=__('see') %> <%=__('file2')%> <%= image['file_ext']%>">
            <img class="mx-auto d-block"
                 src="/small/<%= folderId %>_<%= image['id']%>" alt="<%= image['filename'] %>"
                 <% if (context === 'd') {%>style="border: black 1px solid;" <% } %>>
            <img src="/assets/images/pleinecran.png" class="image_superpose"
                 style="<% if (context === 'm') {%>width:1.5rem!important;display:block;<% } else {%>display: inherit;<% } %>">
        </a>
        <% } else { %>
        <img class="mx-auto d-block"
             src="/small/<%= folderId %>_<%= image['id']%>" alt="<%= image['filename'] %>">
        <% } %>
            <% } %></div>
        </div>
        <br>
        <p style="text-align: center"><strong>Item : </strong><%= image['filename'] %></p>
        <br>
        <% if (license) { %><p style="font-size: smaller"><%- license['html'] %></p><% } %>
    </div>
    <div class="d-flex text-center justify-content-between gap-2">
        <div><%# 01/2025 Verifier qu'on peut télécharger (uploadable) %>
            <% if ((downloadable === 'true') || (user.user_status === 'admin')|| (user.user_status === 'scribe')) {%>
            <a type="button" href="/download/<%= image['fid'] %>_<%= image['id'] %>" title="<%= __('download') %>"
               class="btn btn-secondary"><%=__('download')%> <i class="fas fa-download"></i>
            </a><% } %>
        </div>
        <% if (user.id) { %>
        <div>
            <% if (mainFolder['id_metadata_model']) { %>
            <a type="button" href="/comment,<%= root%>,<%= folderId %>,<%= image['id'] %>,file"
               class="btn btn-secondary"
               title="<%= mainFolder['description'] %>">
                <i class="far fa-comments"></i> <%=__('comment')%> / <%= mainFolder['label']%>
            </a>
            <% } else { %>
            <a type="button" href="/comment,<%= root%>,<%= folderId %>,<%= image['id'] %>,file"
               class="btn btn-secondary"
               title="<%=__('comment')%>">
                <i class="far fa-comments"></i> <%=__('comment')%>
            </a>
            <% } %>
        </div>
        <% } %>
        <% if (rights) { %>
            <div>
                <a type="button" href="/edit,<%= root %>,<%= modelFile %>,file,<%= idFile %>,<%= folderId %>"
                class="btn btn-secondary"
                title="<%=__('enrichData')%>"><i class="fas fa-edit"></i> <%=__('enrichData')%>
                </a>
            </div>
        <% } %>
        
        <% if (rights) { %>
            <div>
                <a href="#" class="btn btn-secondary"
                   title="Copy" onclick="copyMetadataItem('<%= root %>', 'file', '<%= idFile %>')"><i class="far fa-copy"></i> <%=__('copyMetadata')%></a>
            </div>
        <% }%>

        <% if (rights) { %>
            <div>
                <a href="#" class="btn btn-secondary"
                   title="Paste" onclick="pasteMetadataItem('<%= root %>', 'file', '<%= idFile %>', '<%= folderId %>', '<%= lng %>')"><i class="fas fa-copy"></i> <%=__('pasteMetadata')%></a>
            </div>
        <% }%>
    </div>
    <br>

</div>
<% if ((context === 'p')  || (context === 'd')) { %></div>
<div class="container pb-5">

    <% } %>

    <%# tag %>
    <% if (image['tag'].length > 0 ) { %>
    <%= __('keywords')%> :
    <% for (var i = 0; i < image['tag'].length; i++ ) { %>
    <button class="btn btn-light"><%= image['tag'][i] %></button>
    <% } %>

    <% } %>
    <div>
        <% if (rights) { %>
        <a type="button" href="/keyword,<%= root %>,file,<%= idFile %>,<%= folderId %>" class="btn btn-secondary">
            <i class="fas fa-tag"></i> &nbsp;<%=__('addKeyword.title')%>
        </a><% } %>
        <% if (user.user_status === 'admin') { %>
        <% if (doi.doi) {
                    if (!doi.url) { %>
        <a href="#" type="button" class="btn btn-sm btn-light" onclick="validate_doi('file', <%= idFile %>)"
           style="border:2px solid #242943;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;">VALIDATE
            DOI</a>
        <% }  } else { %>
        <%# Avant de proposer à faire un DOI il faut que les conditions soient remplies  (title date creator) %>
        <% if (metadata.DublinCore) { %> <%# TODO : aller plus loin dans la vérification %>
        <a href="#" type="button" class="btn btn-sm btn-light" onclick="generate_doi('file', <%= idFile %>)"
           style="border:2px solid #242943;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;">GENERATE
            DOI</a>
        <% } } } %>
    </div>
    <br>
    <p><%= __('enterDate')%> <%=__('of2')%> <%=__('file').toLowerCase()%> <%=__('in') %> ArcheoGRID :
        <%= image['date_integration'] %></p>
    <p><% if (size !== "") { %><%= __('filesize')%> : <%= size %><% } %></p>
    <% if (doi.doi) { %><%# citation %>
    <strong><%=__('citation').charAt(0).toUpperCase() + __('citation').slice(1)%></strong> : <%= doi_info['cit'] %>
    (<%= doi_info['date_integration'].split('-')[0] %>). <%= doi_info['title']%>.<i>Archeovision</i>.
    <a href="<%= doi_info['doi'] %>"><img src="/assets/images/indexDOI.jpg"
             style="width: 1em;">&nbsp;<%= doi_info['doi'] %></a>
    <% } %>
    <br>
    <br>
    <% if (metadata) { %>
    <% if (metadata['unico'] ) { %>
    <h4>
        <a href="/getSmallImage/<%= metadata['parent_unico'] %>/<%= root %>"
           onclick="window.open(this.href, 'photo', 'status=no, resizable=yes, scrollbars=yes');return false;">
            Parent unico
        </a>
    </h4>
    <% } } %>

    <% for (let key in metadata) { %>
    <% for (let i in modelComplet) { if (modelComplet[i]['name'] === key) { %>
    <% if (Object.prototype.hasOwnProperty.call(metadata, key)) { %>

    <% if ( (metadata[key] !== null) && ((model.indexOf(key) != -1) || (key === 'nomenclature') ||  (key === 'thesaurus')  ) ) { %>
    <% if ((key === 'nomenclature') || (key === 'thesaurus')) {%>
    <table class="table table-sm">
        <tbody>
            <% for (let dat in metadata[key][0]) {%>
            <% if ({}.hasOwnProperty.call(metadata[key][0], dat)) { %>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= metadata[key][0][dat]['label'] %></td>
                <td style="width: 70%;"><%- (metadata[key][0][dat]['value']) %></td>
            </tr>
            <% } %>
            <% }%>
        </tbody>
    </table>
    <% } else { %>
    <h4><%= __('metadata') %> - <%= modelComplet[i]['label'] %></h4>
    <table class="table table-sm">
        <tbody>
            <% for (meta_key in metadata[key]) { %>
            <% if ({}.hasOwnProperty.call(metadata[key], meta_key)) { %>
            <% if (metadata[key][meta_key]['displayable'] !== 0) { %>
            <% if (metadata[key][meta_key]['status'] === 'link') {
                let link_values = Array.isArray(metadata[key][meta_key]['value']) ? metadata[key][meta_key]['value'] : [metadata[key][meta_key]['value']]; %>
                <tr>
                    <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
                        <td style="width: 70%; word-break: break-all;">
                            <% for(let val of link_values) { %>
                                <a href="<%= val %>" target="_blank">
                                    <%- ( val ) %>
                                </a>
                                <br>
                            <% } %>
                    </td>
                </tr>
            <% } else if  (metadata[key][meta_key]['status'] === 'multi') { %><%# récupérer les infos pour pointer vers les pages des concepts %>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
                <td>
                    <% let multi = 0; let d = metadata[key][meta_key]['value'].length %>
                    <% for (let t = 0; t < thes.length; t++) { %>
                        <% for (let multikey in metadata) { %>
                            <% if (metadata[multikey]) { %>
                            <%for (let i = 0; i < metadata[multikey].length; i++) {  %>
                                <% for ( let metakey in metadata[multikey][i]['multiinfo'] ) {%>
                                    <% if (((metakey === 'list') && metadata[multikey][i]['multiinfo']['query']=== 'y')
                                            && (thes[t]['list'] === metadata[multikey][i]['multiinfo'][metakey])
                                            && (thes[t]['code'] === metadata[multikey][i]['multiinfo']['qualifier'])
                                            && (metadata[multikey][i]['multiinfo']['id_metadata']=== metadata[key][meta_key]['id_metadata'])
                                    )  { multi++; %>
                                    <button class="btn btn-sm btn-hand"style="margin-top: -9px;">
                                        <a href="/concept/<%= metadata[multikey][i]['multiinfo']['value']%>?rootF=<%= mainFolder['mainfolder']%>&type=multi&idThes=<%= metadata[multikey][i]['multiinfo']['id_thes_thesaurus']%>&thesaurus=<%= metadata[multikey][i]['multiinfo']['thesaurus']%>">
                                            <%= metadata[multikey][i]['multiinfo']['value']%></a>
                                        <% if (rights) { %>
                                        <span class="btn-hand" title="<%=__('delete')%>"
                                        <% if (metadata[multikey][i]['multiinfo']['qualifier'] !== '') { %>
                                              onclick="delete_thesmulti_item_qual('<%= root %>', '<%= idFile %>', 'file', '<%= metadata[multikey][i]['multiinfo']['thes_path']%>', '<%= metadata[multikey][i]['multiinfo']['list']%>', '<%= metadata[multikey][i]['multiinfo']['qualifier']%>')">
                                            <% } else { %>
                                                onclick="delete_thesmulti_item('<%= root %>', '<%= idFile %>', 'file', '<%= metadata[multikey][i]['multiinfo']['thes_path']%>', '<%= metadata[multikey][i]['multiinfo']['list']%>')">
                                            <% } %>
                                            <i class="far fa-trash-alt"></i>
        </span><% } %>
                                    </button> <% if (d > 1) {%>,<%} %>
                                    <% d = d - 1 %>
                                    <% } %>
                    <% } } } } } %></td>
            </tr>
            <% } else if( metadata[key][meta_key]['status'] === 'actor' || metadata[key][meta_key]['status'] === 'datation' || metadata[key][meta_key]['status'] === 'location' ) { %>
                <tr>
                    <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key].label %></td>
                    <td style="width: 70%" class="advanced_model_display">
                        <%- include('displayMetadataSwitch.ejs', {metadata: metadata[key][meta_key]}) %>
                    </td>
                </tr>
            <% } else { %>
                <tr>
                <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
                <td style="width: 70%; word-break: break-all;"><%# pour une métadonnée unique on ne rajoute pas un espace à la fin%>
                    <% if (metadata[key][meta_key]['isunique'] ) { %><%- (metadata[key][meta_key]['value'][0]) %><% } else { %>
                    <% for (let val  in  metadata[key][meta_key]['value']) { %>
                    <%- (metadata[key][meta_key]['value'][val]) %><br><% } } %></td>
            </tr>
            <% } %>
            <% } %>
            <% } %>
            <% } %>
        </tbody>
    </table>
    <% } %>
    <% } %>
    <% } %>
    <% } } } %>
    <% let multi = 0; %>
    <% for (let t = 0; t < thes.length; t++)   { %>
    <% for (let key in metadata) { %>
    <% if (metadata[key]) { %>
    <% if (key === 'pactols') { let display = ''; let d = 0; %>
    <% for (let i = 0; i < metadata[key].length; i++) { %>
    <% for (let p in metadata[key][i]['thespactolsinfo']) { %>
    <% if (p === 'list') {%>
    <% if (thes[t]['list'] === metadata[key][i]['thespactolsinfo'][p]) { %>
    <% display = thes[t]['label'] %><% d++ %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% if (display !== '') { %>
    <br><%= display %> :
    <%for (let i = 0; i < metadata[key].length; i++) { %>
    <% for ( let metakey in metadata[key][i]['thespactolsinfo'] ) { %>
    <% if ((metakey === 'list') && (thes[t]['list'] === metadata[key][i]['thespactolsinfo'][metakey]))  {%>
    <button class="btn btn-sm btn-hand">
        <a href="<%= metadata[key][i]['thespactolsinfo']['identifier'] %>">
            <%= metadata[key][i]['thespactolsinfo']['value']%></a>
    </button> <% if (d > 1) {%>,<%} %>
    <% d = d - 1 %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } else if (key === 'multi') { } else { let display = ''; let d = 0; %>
    <% let surkey = key + 'info' %>
    <% for (let i = 0; i < metadata[key].length; i++) { %>
    <% for (let p in metadata[key][i][surkey]) { %>
    <% if (p === 'list') {%>
    <% if (thes[t]['list'] === metadata[key][i][surkey][p]) { %>
    <% display = thes[t]['label'] %><% d++ %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% if (display !== '') { %>
    <br><%= display %> :
    <%for (let i = 0; i < metadata[key].length; i++) { %>
    <% for ( let metakey in metadata[key][i][surkey] ) { %>
    <% if ((metakey === 'list') && (thes[t]['list'] === metadata[key][i][surkey][metakey]))  {%>
    <button class="btn btn-sm btn-hand">
        <a href="#">
            <%= metadata[key][i][surkey]['value']%></a>
    </button> <% if (d > 1) {%>,<%} %>
    <% d = d - 1 %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <%# tag de thesaurus SANS MODELE De METADONNEES RATTACHEES donc pour le moment pas possibilité de qualifier %>
    <%# deja des tag multi présents dans des modèles de métadonnées ?  multi ? s'il y en a alors on ne les affiche pas ecore... %>
    <% if (((tagThesmulti.length > 0 ) && (multi === 0 )) || ( tagPactols.length > 0 ) || ( tagMaison.length > 0 ) ) {
    let affTab = [] ; // on crée un tableau des tags déjà affichés dans les metadata
    if (metadata.multi.length ) {
        for (let tt = 0; tt < tagThesmulti.length; tt++) {
            for ( let m =0; m < metadata['multi'].length; m++ ) {
                if (tagThesmulti[tt]['thesaurus'] === metadata['multi'][m]['multiinfo']['thesaurus'] && tagThesmulti[tt]['name'] === metadata['multi'][m]['multiinfo']['value']) {
                    // C'est le même on ne l'affiche pas
                    tagThesmulti[tt]['affichage'] = 0;
                    affTab.push(tagThesmulti[tt]) ;
                }
            }
        }
    }
    if (affTab.length < tagThesmulti.length ) {
        // Créer un Set des IDs de affTab (sur le id_thesaurus)
        const idsB = new Set(affTab.map(item => item.id));
    %><hr><i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%><br>
    <% if (tagThesmulti.length > 0 ) {
    tagThesmulti.forEach(element => {
    if (!idsB.has(element.id)) { %><%# si l'id n'est pas présent dans le tableau des tags déjà affichés (affTab) mappé avec les id (idsB) alors seulement on affiche %>
    <a type="button" href="/concept/<%= element.name %>?rootF=<%= mainFolder['mainfolder']%>&type=multi&idThes=<%= element['id_thes']%>&thesaurus=<%= element['thesaurus']%>"
       class="btn btn-sm btn-light"><%= element.name %></a>
    <% if (rights) { %>
    <span class="btn-hand" title="<%=__('delete')%>"
          onclick="delete_thesmulti_item('<%= root %>', '<%= idFile %>', 'file', '<%= element['thes_path']%>','<%= element['thesaurus']%>')">
        <i class="far fa-trash-alt"></i>
        </span><% } %>
    <%      }
    });
    }
    } %>

    <% if (tagPactols.length > 0 ) { %>
        <hr><i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%> (PACTOLS)<br>
    <% for (var i = 0; i < tagPactols.length; i++ ) { %>
    <a type="button"
       href="/concept/<%= tagPactols[i].name %>?rootF=<%= mainFolder['mainfolder']%>&type=pactols&idThes=<%= tagPactols[i]['id_thes']%>&thesaurus=<%= tagPactols[i]['thesaurus']%>"
       class="btn btn-light btn-sm">
        <%= tagPactols[i].name %>
    </a>
    <% if (rights) { %>
    <span class="btn-hand" title="<%=__('delete')%>"
          onclick="delete_thespactols_item('<%= root %>', '<%= idFile %>', 'file', '<%= tagPactols[i]['id']%>', '<%= tagPactols[i]['id_thes']%>', '<%= tagPactols[i]['thesaurus']%>')">
        <i class="far fa-trash-alt"></i>
    </span><% } %>

    </a><% } %>
    <br>
    <% } %>

    <% if (tagPactolsGeo.length > 0 ) { %>
    <% for (let i = 0; i < tagPactolsGeo.length; i++ ) { %>
    <div>
        <a
        type="button"
        class="btn btn-sm btn-light"
        href="/concept/<%= tagPactolsGeo[i].name %>?rootF=<%= mainFolder['mainfolder']%>&type=pactolsgeo&idThes=<%= tagPactolsGeo[i]['id']%>">
            <%= tagPactolsGeo[i].name %>
        </a>
        <% if (rights) { %>
        <span class="btn-hand" title="<%=__('delete')%>"
              onclick="delete_thespactolsgeo_item('<%= root %>', '<%= idFile %>', 'file', '<%= tagPactolsGeo[i]['id']%>')"> <i
               class="far fa-trash-alt"></i>
        </span><% } %>
    </div>
    </a><% } %>
    <% } %>
    <% if (tagMaison.length > 0 ) { %>
    <% for (var i = 0; i < tagMaison.length; i++ ) { %>
    <div>
        <a
        type="button"
        class="btn btn-sm btn-light"
        href="/concept/<%= tagMaison[i].short_name %>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= tagMaison[i]['id_thes']%>&thesaurus=<%= tagMaison[i]['thesaurus']%>">
            <%= tagMaison[i].name %>
        </a>
        <% if (rights) { %>
        <span class="btn-hand" title="<%=__('delete')%>"
              onclick="delete_thes_item('<%= root %>', '<%= idFile %>', 'file', '<%= tagMaison[i]['thesaurus_path']%>', '<%= tagMaison[i]['thesaurus']%>')">
            <i class="far fa-trash-alt"></i>
        </span><% } %>
    </div>
    </a><% } %>
    <% } } %>
    <%# tag non alignés %>
    <% if (tags.length > 0 )  { %>
        <hr>
        <i class="fas fa-tags"></i> &nbsp;<%= __('freetags')%> (<%=__('no3') %> <%=__('alignedPlural')%>) :<p>
        <% for (let t = 0; t < tags.length; t++ ) { %>
            <div class="btn btn-sm btn-light p-2"><%= tags[t].name %></div>
            <% if (rights) { %>
            <span class="btn-hand" title="<%=__('delete')%>"
                  onclick="delete_tag_item('<%= root %>', '<%= idFile %>', 'file', '<%= tags[t]['id']%>')"> <i
                        class="far fa-trash-alt"></i>
    </span><% } %>
        <% } %></p>
    <% } %>


    <hr>
    <%# Si les iptc existent dans la base on ne va pas chercher les iptc du fichier sauf les mots clés %>
    <% if (metadata) { %>
    <% if (metadata['iptc'] ) { %>
    <% if (image['iptc_gm']) { %>
    <% if (Object.prototype.hasOwnProperty.call(image['iptc_gm'], 'Keyword[2,25]')) { %>
    <table class="table table-sm">
        <tbody>
            <td style="text-align: right;width: 30%"><strong><%= __('keywords') %></strong></td>
            <td style="width: 70%;"><%= image['iptc_gm']['Keyword[2,25]'] %></td>
        </tbody>
    </table>
    <% } %>
    <% } %>
    <% } } %>

    <% if (imageFormat.indexOf(image['file_ext'].toLowerCase()) !==  -1) { %>
    <button id="exifiptcB" type="button" class="btn-hand"
            onclick="getMoreInfoImage(<%= image['id'] %>,'<%= root %>')">Original IPTC EXIF data ?</button>
    <div id="divinfoimage_<%= image['id'] %>"></div>
    <hr>
    <% } %>
    <% if (nbObjects > 0) { %>
    <h3><%=__('object')%><% if  (nbObjects > 1) {%>s<% } %> <%=__('associated')%><% if  (nbObjects > 1) {%>s<% } %> :
        <% if  (nbObjects > 1) {%><%= nbObjects%><% } %></h3>

    <% if (nbObjects > 1) { for (let i = 0; i < objects.length; i++) { %>
    <h4><%= objects[i]['name'] %> - Project <%= objects[i]['root_dir'] %></h4>
    <% if (objects[i]['name']) { %>
    <p class="text-center"><%= __('enterDate')%> <%=__('of2')%> <%=__('object2')%> <%=__('in') %> ArcheoGRID :
        <%= objects[i]['date_integration'] %></p>
    <div class="card text-center">
        <% if (objects[i]['id']) {%>
        <a href="/viewer/<%= objects[i]['itemFolder'] %>_<%= objects[i]['id'] %>" target="_blank">
            <img class="mx-auto" src="/thumb/<%= objects[i]['itemFolder'] %>_<%= objects[i]['id'] %>"
                 alt="<%= objects[i]['filename']%>">
        </a>
        <% } else { %>
                <img class="mx-auto" src="/assets/images/default_repre_image_object.png" alt="<%= image['filename']%>"
                     style="width: 50px;">
                <p><%=__('no2')%> <%=__('visualization')%> <%=__('for')%> <%=__('object2')%></p>

        <% } %>
        <div class="card-body">
            <div class="card-text">
                <% if (objects[i]['name']) { %>
                <div class="text-center">
                    <% if (objects[i]['name'].length > 17) {%><small><%= objects[i]['name'].substring(0, 17)%>...</small>
                    <% } else { %><small><%= objects[i]['name']%></small><% } %>
                </div>
                <% } else { %><small></small><% } %>
                <div class="d-flex justify-content-between">
                    <a href="/visionneuseObj,<%= objects[i]['itemId'] %>,<%- objects[i]['id'] ? objects[i]['id'] : 0 %>-<%- objects[i]['idfolder'] ? objects[i]['idfolder'] : objects[i]['itemFolder'] %>,<%= root %>-<%= context %>,0"
                       class="mx-auto" target="_blank" title="Ouvrir dans un nouvel onglet"><i
                           class="fas fa-external-link-alt"></i></a>
                </div>
            </div>
        </div>
    </div>
    <% } %>
    <% } } else { let i = 0 %>
    <h4><%- objects['name'] %> - Project <%- objects[i]['root_dir'] %></h4>
    <% if (objects[i]['name']) { %>
    <p class="text-center"><%= __('enterDate')%> <%=__('of2')%> <%=__('object2')%> <%=__('in') %> ArcheoGRID :
        <%- objects[i]['date_integration'] %></p>
    <div class="card text-center">
        <% if (objects[i]['id']) {%>
        <a href="/viewer/<%- objects[i]['itemFolder'] %>_<%- objects[i]['id'] %>" target="_blank">
            <img class="mx-auto" src="/thumb/<%- objects[i]['itemFolder'] %>_<%- objects[i]['id'] %>"
                 alt="<%- objects[i]['filename']%>">
        </a>
        <% } else { %>
        <img class="mx-auto" src="/assets/images/default_repre_image_object.png" alt="<%= image['filename']%>"
             style="width: 50px;">
        <p><%=__('no2')%> <%=__('visualization')%> <%=__('for')%> <%=__('object2')%></p>
        <% } %>
        <div class="card-body">
            <div class="card-text">
                <% if (objects[i]['name']) { %>
                <div class="text-center">
                    <% if (objects[i]['name'].length > 17) {%><small><%- objects[i]['name'].substring(0, 17)%>...</small>
                    <% } else { %><small><%- objects[i]['name']%></small><% } %>
                </div>
                <% } else { %><small></small><% } %>
                <div class="d-flex justify-content-between">
                    <a href="/visionneuseObj,<%- objects[i]['itemId'] %>,<%- objects[i]['id'] ? objects[i]['id'] : 0 %>-<%- objects[i]['idfolder'] ? objects[i]['idfolder'] : objects[i]['itemFolder'] %>,<%= root %>-p,0"
                       class="mx-auto" target="_blank" title="Ouvrir dans un nouvel onglet"><i
                           class="fas fa-external-link-alt"></i></a>
                </div>
            </div>
        </div>
    </div>
    <% } %>
    <% } %>
    <% } %>
    <% if (comments.length > 0) { %>
    <br>
    <h4><%= comments.length %> <%=__('note')%><% if (comments.length > 1) {%>s<% }%></h4>
    <% for (let i = 0; i <  comments.length; i++) { %>
    <dl class="row">
        <dt class="col-sm-3"><strong><%=__('content')%></strong></dt>
        <dd class="col-sm-9"><%- comments[i]['content'] %></dd>
    </dl>
    <dl class="row">
        <dt class="col-sm-3"><strong><em><%=__('author')%></em></strong></dt>
        <dd class="col-sm-9"><%= comments[i]['signature']%></dd>
    </dl><% if (comments[i]['author'].length > 0 ) {%><dl class="row">
        <dt class="col-sm-3"><strong><em><%=__('inNameOf')%>:</em></strong></dt>
        <dd class="col-sm-9"><%= comments[i]['author']%></dd>
    </dl><% } %>
    <dl class="row">
        <dt class="col-sm-3"><strong>Date (auto)</strong></dt>
        <dd class="col-sm-9"><%= comments[i]['date']%></dd>
    </dl><% if (comments[i]['other_date'].length > 0 ) {%><dl class="row">
        <dt class="col-sm-3"><strong><%=__('dateExplicite')%>: </strong></dt>
        <dd class="col-sm-9"><%- (comments[i]['other_date']) %></dd>
    </dl><% } %>
    </dl><% if (comments.length > 1) {%>
    <hr><% } %>
    <% }%>







    <% } %>
    <% if (unicos.length > 0) { %>
    <div>
        <div id="alerts" class="d-flex justify-content-center">
            <div class="alert alert-success mb-2 alert-success" role="alert" style="display: none;">
                <%=__('unicoDeleteSuccess')%></div>
            <div class="alert alert-danger mb-2 alert-notconnected" role="alert" style="display: none;">
                <%=__('disconnected')%></div>
            <div class="alert alert-danger mb-2 alert-forbidden" role="alert" style="display: none;">
                <%=__('forbidden')%></div>
            <div class="alert alert-danger mb-2 alert-notfound" role="alert" style="display: none;"><%=__('notFound')%>
            </div>
            <div class="alert alert-danger mb-2 alert-error" role="alert" style="display: none;"><%=__('error')%></div>
        </div>
        <h4>Unicos</h4>
        <table class="table" id="unicos-table">
            <thead>
                <tr>
                    <th scope="col">#</th>
                    <th scope="col"><%= __('name') %></th>
                    <th scope="col"><%= __('annotation') %></th>
                    <th scope="col">Thesaurus</th>
                    <th scope="col"><%= __('date') %></th>
                    <th scope="col"><%= __('link') %></th>
                    <th scope="col"><%= __('actions') %></th>
                </tr>
            </thead>
            <tbody>
                <% for (let u in unicos) { %>
                <% const unico = unicos[u] %>
                <% const date = new Date(unico.creation_date) %>
                <% const url = `/crop/${folderId}_${unico.id_file}/${unico.x},${unico.y},${unico.width},${unico.height}` %>
                <tr id-unico="<%= unico.id %>">
                    <th scope="row"><%= parseInt(u) + 1 %></th>
                    <td><%= unico.name %></td>
                    <td><%= unico.annotation %></td>
                    <td>
                        <ul style="padding-left: 0%;">
                            <% for (let thes in unico.thesaurus) { %>
                            <% const thesaurus =  unico.thesaurus[thes] %>
                            <li>
                                <span style="font-weight: bold;"><%= thesaurus.name ?? (thesaurus.thesaurus.charAt(0).toUpperCase() + thesaurus.thesaurus.slice(1)) %> :</span>
                                <div class="d-flex gap-2">
                                    <% for (let t in thesaurus.tags) { %>
                                        <% const tag = thesaurus.tags[t] %>
                                        <span class="tag-value tag p-1" thesaurus="<%= thesaurus.thesaurus %>" status="<%= thesaurus.status %>" id="<%= tag.id %>">
                                    <%= tag.name %>
                                    <% if (rights) { %>
                                        <% if (thesaurus.status === 'pactols') { %>
                                        <span class="btn-hand" title="<%=__('delete')%>" onclick="delete_thespactols_item('<%= root %>', '<%= unico.id %>', 'unico', '<%= tag['id'].split('_')[0]%>', '<%= tag['id'].split('_')[1]%>', '<%= thesaurus.thesaurus%>')">
                                            <i class="far fa-trash-alt"></i>
                                        </span>
                                        <% } else if (thesaurus.status === 'geopactols') { %>
                                            <span class="btn-hand" title="<%=__('delete')%>" onclick="delete_thespactolsgeo_item('<%= root %>', '<%= unico.id %>', 'unico', '<%= tag['id'].split('_')[0]%>')">
                                                <i class="far fa-trash-alt"></i>
                                        </span>
                                        <% } %>
                                    <% } %>
                                </span>
                                <% } %>
                                </div>
                            </li>
                            <% } %>
                        </ul>
                    </td>
                    <td>
                        <time datetime="<%= unico.creation_date %>"><%= date.getDate()+'/'+(date.getMonth()+1)+'/'+date.getFullYear()+' '+date.getHours()+':'+date.getMinutes() %></time>
                    </td>
                    <td>
                        <a href="<%-url%>" target="_blank" style="text-align: center;">
                            <% if (unico.type === 'rect') { %>
                            <img src="<%-url%>" style="max-width: 150px; max-height: 150px; object-fit: contain;">
                            <% } else if (unico.type === 'poly') { %>
                            <% let points = unico.polygon.split(' ') %>
                            <% for (let i in points) { let e = points[i].split(','); e[0] = parseInt(e[0]) - unico.x; e[1] = parseInt(e[1]) - unico.y; points[i] = e.join(',') } %>
                            <% points = points.join(' ') %>
                            <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                                 style="max-width: 150px; max-height: 150px;"
                                 viewBox="0 0 <%- unico.width %> <%- unico.height %>" id="unico-svg-<%- unico.id %>">
                                <mask id="svgmask-<%- unico.id %>">
                                    <polygon fill="#ffffff" points="<%- points %>" />
                                </mask>
                                <image xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="<%- url %>"
                                       width="<%- unico.width %>" height="<%- unico.height %>"
                                       mask="url(#svgmask-<%- unico.id %>)" />
                            </svg>
                            <% } %>
                        </a>
                    </td>
                    <td>
                        <div style="height: 100%;">
                        <% if ((user.id === unico.id_user) || (user.user_status === 'admin')) { %>
                        <% if (context === 'p') { %>
                        <button type="button" class="btn btn-secondary mr-1" unico-id="<%=unico.id%>" onclick="editUnico(<%= unico.id %>)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <% } %>
                        <button type="button" class="btn btn-secondary erase-search" unico-id="<%=unico.id%>">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                        <% } %>
                        </div>
                    </td>
                </tr>
                <% } %>
            </tbody>
        </table>

        <script>
            $('#unicos-table .erase-search').on('click', function () {
                let element = $(this)

                $('.alert').hide()
                $.ajax({
                    url: '/deleteUnico/' + element.attr('unico-id')
                }).done(function () {
                    $('.alert-success').show().delay(2000).fadeOut(1000)
                    element.parent().parent().remove()
                    $('th[scope="row"]').each(function (index) { // change les indices de la colomne de gauche
                        $(this).text(index + 1)
                    })
                }).fail(function (jqXHR, textStatus, errorThrown) {
                    console.log('erreur', jqXHR.status)
                    if (jqXHR.status === 401) {
                        $('.alert-notconnected').show().delay(2000).fadeOut(1000)
                    } else if (jqXHR.status === 403) {
                        $('.alert-forbidden').show().delay(2000).fadeOut(1000)
                    } else if (jqXHR.status === 404) {
                        $('.alert-notfound').show().delay(2000).fadeOut(1000)
                    } else {
                        $('.alert-error').show().delay(2000).fadeOut(1000)
                    }
                })
            })
        </script>
    </div>
    <% } %>

    <% if ((context === 'p') || (context === 'd') ) { %>
</div>
<div class="loader"></div>
<script src="/js/lazy-loading.js"></script>
<% } %>