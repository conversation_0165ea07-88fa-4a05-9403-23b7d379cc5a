UPDATE corpus_metadata set code = 'dblcore_file_license' WHERE status = 'thesaurus' AND list = 'license' AND id_metadata_model = (SELECT id from corpus_metadata_model WHERE metadata_type = 'file' AND name like '%DublinCore%') ;

UPDATE corpus_metadata set code = 'dblcore_object_license' WHERE status = 'thesaurus' AND list = 'license' AND id_metadata_model = (SELECT id from corpus_metadata_model WHERE metadata_type = 'object' AND name like '%DublinCore%') ;


UPDATE corpus_metadata set code = 'dblcore_unico_license' WHERE status = 'thesaurus' AND list = 'license' AND id_metadata_model = (SELECT id from corpus_metadata_model WHERE metadata_type = 'unico' AND name like '%DublinCore%') ;



UPDATE pft3d_metadata set code = 'dblcore_unico_license' WHERE status = 'thesaurus' AND list = 'license' AND id_metadata_model = (SELECT id from pft3d_metadata_model WHERE metadata_type = 'unico' AND name like '%DublinCore%') ;

