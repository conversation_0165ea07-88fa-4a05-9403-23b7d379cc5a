<div class="container">
    <div class="well col-4 mx-auto mt-4" id="login">
        <% if (locals.flash && locals.flash.error) { %>
        <div class="alert alert-danger ">
            <%= flash.error %>
        </div>
        <% } %>

        <form method="POST" action="">
            <input type="hidden" id="reqUrl" name="reqUrl" />
            <div class="form-group mb-3">
                <label class="form-label" for="username"><%= __('user')%></label>
                <input type="text" class="form-control" id="username" name="username" placeholder="Identifiant">
            </div>
            <div class="form-group mb-3">
                <label class="form-label" for="password"><%= __('password')%></label>
                <input type="password" class="form-control" id="password" name="password" placeholder="Password">
            </div>
            <button type="submit" class="btn btn-secondary"><%= __('connecte')%></button>
        </form>
    </div>
</div>

<script src="/js/redirect.js"></script>
<script>
    setupRedirection({
        inputId: 'reqUrl',
        serverUrl: '<%= locals.reqUrl || "" %>',
        defaultUrl: 'projects',
        excludedPrefixes: ['login', 'connect']
    });
</script>
