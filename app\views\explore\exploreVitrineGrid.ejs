<% if ( data.length > 0 ) { %>
    <div id="explore-grid" class="vitrine-grid">
        <% for (let i = 0; i <  data.length; i++) { %>
        <% let item = data[i] %>
        <% let type = '' %>
        <% if (item.item_type === 'file') { %>
        <% type = 'i' %>
        <% } else if (item.item_type === 'object') { %>
        <% type = 'o' %>
        <% } else if (item.item_type === 'unico') { %>
        <% type = 'u' %>
        <% } else { %>
        <% type = 'd' %>
        <% } %>

        <%
            let href = '#';
            if (item.has_access !== false && item.idfile !== null) {
                if (item.item_type === 'file') {
                    href = `/visionneuse,${item.idfile}-${item.idfolder},${branch}-v,${WRights}`;
                } else if (item.item_type === 'object') {
                    href = `/visionneuseObj,${item.id_item},${item.idfile}-${item.idfolder},${branch}-v,${WRights}`;
                } else if (item.item_type === 'unico') {
                    href = `/visionneuseUnicoV2,${item.id_item},${item.idfolder},${branch}-v,${WRights}`;
                }
            }
        %>
        <a href="<%= href %>" class="card-link <% if (item.has_access !== false) { %>lazy-grid-item lazy-card lazy-metadata<% } %>" style="text-decoration: none; color: inherit; display: block;"
           data-index="<%= i %>"
           <% if (item.has_access !== false) { %>
               data-item-id="<%= item.id_item %>"
               data-item-type="<%= item.item_type %>"
               data-branch="<%= branch %>"
               <% if (locals.model && model !== '0') { %>data-model="<%= model %>"<% } %>
           <% } %>>
            <div id="<%= `item-${type}-${item.id_item}_${item.idfolder}` %>" class="card text-center check_item grid-vitrine-item position-relative"
                <% if ((selection && user.id)  && (user.user_status !== 'guest')) { %>
                    oncontextmenu="updateItemSelection(<%= item.id_item %>, '<%= item.item_type %>', <%= item.idfolder %>); return false;"
                <% } %>>


                <div class="explore-card-img overflow-hidden grid-vitrine-img-link" title="<%= item.name %>">
                    <% if (item.has_access === false) { %>
                        <img class="card-img mx-auto d-block vitrine-img"
                             src="/images/not_authorized.png"
                             alt="Not Authorized">
                    <% } else if ((item.item_type === 'object') && (item.previsu)) { %>
                    <img class="card-img mx-auto d-block vitrine-img lazy-image"
                        data-src="<%= item.previsu %>"
                        src="/assets/images/placeholder.png"
                        alt="<%= item.name %>">
                    <% } else if ((item.item_type === 'object') && ((item.idfile === '0') || (!item.idfile))) { %>
                    <img class="card-img mx-auto d-block vitrine-img"
                        src="/assets/images/default_repre_image_object.png"
                        alt="<%= item.name %>">
                    <% } else if (item.item_type === 'unico' && item.width && item.height) { %>
                    <% if (item.type === 'rect') { %>
                    <img class="card-img mx-auto d-block vitrine-img lazy-image"
                        data-src="<%= `/crop/${item.idfolder}_${item.idfile}/${item.x},${item.y},${item.width},${item.height}` %>"
                        src="/assets/images/placeholder.png"
                        alt="<%= item.name %>" style="max-width: 100%;">
                    <% } else if (item.type === 'poly') { %>
                    <% let points = item.polygon.split(' ') %>
                    <% for (let j in points) { let e = points[j].split(','); e[0] = parseInt(e[0]) - item.x; e[1] = parseInt(e[1]) - item.y; points[j] = e.join(',') } %>
                    <% points = points.join(' ') %>
                    <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                        class="card-img mx-auto d-block vitrine-img" viewBox="0 0 <%- item.width %> <%- item.height %>"
                        id="unico-svg-<%- item.id_item %>">
                        <mask id="svgmask-<%- item.id_item %>">
                            <polygon fill="#ffffff" points="<%- points %>" />
                        </mask>
                        <image xmlns:xlink="http://www.w3.org/1999/xlink"
                            xlink:href="<%= `/crop/${item.idfolder}_${item.idfile}/${item.x},${item.y},${item.width},${item.height}` %>"
                            width="<%- item.width %>" height="<%- item.height %>"
                            mask="url(#svgmask-<%- item.id_item %>)" style="max-width: 100%;" />
                    </svg>
                    <% } %>
                    <% } else { %>
                    <img class="card-img mx-auto d-block vitrine-img lazy-image"
                        data-src="/thumb/<%= item.idfolder %>_<%= item.idfile %>"
                        src="/assets/images/placeholder.png"
                        alt="<%= item.name %>">
                    <% } %>
                </div>

                <div class="card-body border-primary border-1 grid-vitrine-body update-item-selector">
                    <div class="card-text text-center">
                        <% if (item.has_access === false) { %>
                            <small class="grid-vitrine-item-text item-title"><%=__('not_authorized')%></small>
                        <% } else if (item.title && item.title.trim() !== '') { %>
                            <% if (item.title.length > 45) { %>
                                <small class="grid-vitrine-item-text item-title"><%= item.title.substring(0, 45) %>...</small>
                            <% } else { %>
                                <small class="grid-vitrine-item-text item-title"><%= item.title %></small>
                            <% } %>
                        <% } else { %>
                            <% if (item.name) { %>
                                <% if (item.name.length > 45) { %>
                                    <small class="grid-vitrine-item-text item-title"><%= item.name.substring(0, 45) %>...</small>
                                <% } else { %>
                                    <small class="grid-vitrine-item-text item-title"><%= item.name %></small><% } %>
                            <% } else if (item.object_name && item.item_type === 'object') { %>
                                <% if (item.object_name.length > 45) { %>
                                    <small class="grid-vitrine-item-text item-title"><%= item.object_name.substring(0, 45) %>...</small>
                                <% } else { %>
                                    <small class="grid-vitrine-item-text item-title"><%= item.object_name %></small>
                                <% } %>
                            <% } else if (item.filename && item.item_type === 'file') { %>
                                <% if (item.filename.length > 45) { %>
                                    <small class="grid-vitrine-item-text item-title"><%= item.filename.substring(0, 45) %>...</small>
                                <% } else { %>
                                    <small class="grid-vitrine-item-text item-title"><%= item.filename %></small>
                                <% } %>
                            <% } else { %>
                                <small class="grid-vitrine-item-text item-title"><%=__('no_title')%></small>
                            <% } %>
                        <% } %>
                    </div>
                </div>
            </div>
        </a>
        <% } %>
    </div>

    <!-- Add selection rectangle div -->
    <div id="drag-selection-rectangle"></div>
    <% } %>

    <script>
        $('#item-selection').hide()
        $('#index-all-btn').hide();
        $('#tag-all-btn').hide();

    <% if (locals.objectsFolders) { %>
        $("#main-ul li ul li input:checked").prop("checked", false);
    <% } %>
    </script>