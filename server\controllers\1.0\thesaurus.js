const pgp = require("../../helpers/connexion").default;
const db = require("../../helpers/db").default;
const responseHelper = require("../../helpers/response");
const {
  insertThesItem,
  updateThes,
  insertThesMultiItem,
  insertThesMultiItemWithQual,
  insertThesPactolsGeoAndItem,
  insertThesPactolsGeoItem,
  insertThesPactolsItemV2Coll,
} = require("../../helpers/db_tools");

// Create url endpoint /thesaurus/:root,:nameThesaurus to get all thesaurus item for one root, one tree
exports.getThesaurus = async function (req, res) {
  let { root } = req.params;

  let txtOpt = "";
  let txtEnd = "  ORDER BY global_rank ";
  let txtBegin =
    `SELECT id, id_thes, short_name, thesaurus_path, name, name as en_name, ${root}_get_real_thes_path_simple_short_name(id_thes, thesaurus) as fr_name  ` +
    `FROM ${root}_thesaurus WHERE thesaurus = $1 `;

  //if (root == 'conservatoire3d') txtEnd = ' AND nlevel(thesaurus_path) > 1 '+txtEnd
  // dans tous les cas
  txtEnd = ` AND nlevel(thesaurus_path) > 1 ${txtEnd}`;
  if (req.query.subset) {
    txtOpt = ` AND global_rank like '${req.query.subset}%' `;
  }

  const txtFinal = txtBegin + txtOpt + txtEnd;

  const query = pgp.as.format(txtFinal, req.params.nameThesaurus);

  db.any(query)
    .then((thes) => {
      res.status(200).sendData(thes);
    })
    .catch((e) => {
      responseHelper.sendError(500, "server_error in thesaurus", e, req, res);
    });
};

// Create url endpoint /thesaurus/:root,:nameThesaurus PUT to insert one element in thesaurus table !!!!
// (to deal with new entity pour commencer !
// thesaurus multi aussi ! ??
exports.addThesaurusElement = async function (req, res) {
  let thesaurus = req.params.nameThesaurus;
  let branche = req.params.root;
  let nomElement = req.body.name;
  let { parent } = req.body;
  let { type } = req.body;
  let { identifier } = req.body;
  let { lng } = req.body;
  let id_thes = 0;
  let erreur = 0;

  //console.log(req.body);
  if (type === "thesaurus") type = "";
  else if (type === "multi") type = "_multi";

  let lng_name = lng === 'fr' ? 'name' : 'name_'+lng

  db.any(`SELECT count(*)  FROM ${branche}_thesaurus${type} ` + `WHERE thesaurus = $1 AND ${lng_name} = $2 `, [
    thesaurus,
    nomElement,
  ])
    .then((count) => {
      if (count[0].count === "0") {
        // Récupérer le max(id_thes)
        db.one(`SELECT max(id_thes) as maxid FROM ${branche}_thesaurus${type} ` + `WHERE thesaurus = $1 `, thesaurus)
          .then((maxid) => {
            id_thes = parseInt(maxid.maxid) + 1;
            // OK : on fait un insert
            db.one(
              `INSERT INTO ${branche}_thesaurus${type}(id_thes, thesaurus, name, short_name, id_parent, date_maj) ` +
                ` VALUES ($1, $2, $3, $3, $4, now()) RETURNING id `,
              [id_thes, thesaurus, nomElement, parent],
            )
              .then((ind) => {
                db.any(
                  `UPDATE ${branche}_thesaurus${type} ` +
                    `SET thesaurus_path = ${branche}_get_thes_path${type}(id_thes, '${thesaurus}')::ltree, ` +
                    `nb_tot_item = 0, ${lng_name} = name ` +
                    ` WHERE thesaurus = $1 AND id = $2 and thesaurus_path IS NULL `,
                  [thesaurus, parseInt(ind.id)],
                )
                  .then((data) => {
                    // Mise à jour des rank et global_rank après l'insert
                    db.any(`SELECT set_rank_thes${type}('${branche}', 2, '${thesaurus}') `)
                      .then((daa) => {
                        console.log("insert and update 1 and update 2 rank global rank OK");
                        // Si on a l'info, on met à jour le champ identifier du thesaurus
                        // Pour la table des thesaurus multi, pour le thesaurus 'deposant', ce champ correspond à l'identifiant du déposant
                        // soit le id de la table entity qu'on a récupéré
                        console.log(ind);
                        db.any(
                          `UPDATE ${branche}_thesaurus${type} ` +
                            `SET identifier = $1 ` +
                            ` WHERE thesaurus = $2 AND id = $3 and identifier IS NULL `,
                          [identifier, thesaurus, parseInt(ind.id)],
                        )
                          .then((datalast) => {
                            //Mise à jour du haschild pour le parent
                            db.any(
                                `UPDATE ${branche}_thesaurus${type} ` +
                                `SET haschild = 1 ` +
                                ` WHERE thesaurus = $2 AND id_thes = $3  `,
                                [identifier, thesaurus, parseInt(parent)],
                            )
                                .then((datalast) => {
                                  //Mise à jour du haschild pour le parent
                                  // Maintenant on retourne aussi le id_thes
                                  ind.id_thes = id_thes;
                                  res.status(201).sendData(ind);
                                })
                                .catch((er) => {
                                  responseHelper.sendError(
                                      500,
                                      "server_error second update thesaurus on identifier after insert and update ",
                                      er,
                                      req,
                                      res,
                                  );
                                });

                          })
                          .catch((er) => {
                            responseHelper.sendError(
                              500,
                              "server_error second update thesaurus on haschild after update thesaurus identifier ",
                              er,
                              req,
                              res,
                            );
                          });
                      })
                      .catch((e) => {
                        responseHelper.sendError(
                          500,
                          "server_error second update thesaurus after insert and update ",
                          e,
                          req,
                          res,
                        );
                      });
                  })
                  .catch((e) => {
                    console.log("ERREUR update thesaurus after insert  ", e);
                    responseHelper.sendError(500, "server_error update thesaurus after insert", e, req, res);
                  });
              })
              .catch((e) => {
                console.log("ERREUR insert thesaurus ", e);
                responseHelper.sendError(500, "server_error update thesaurus insert", e, req, res);
              });
          })
          .catch((e) => {
            console.log("ERROR WHEN getting max id_thes");
            responseHelper.sendError(500, "server_error getting max id_thes", e, req, res);
          });
      } else {
        console.log(" already in thes, nothing to do");
        res.status(201).sendData([]);
      }
    })
    .catch((e) => {
      console.log("ERROR select thesaurus element ", e);
      responseHelper.sendError(500, "server_error", e, req, res);
    });
};


// create url endpoint /thesaurusTree/:root,:thes,:idThes
exports.getThesaurusTree = async function (req, res) {
  let { root } = req.params;
  let { lng } = req.query;
  let name = ''

  // pour les "vieux" thesaurus indexés à partir de 0 et non de 1
  let idThes = req.params.idThes;
  let oldThesaurusZero = ['rome_navone', 'LEVALLOIS_PERRET_CLEMENT_BAYARD', '2C3D_AUDRIX']

  if (root === 'pft3d' && ( oldThesaurusZero.indexOf(req.params.thes) !==-1)) {
    idThes = '0';
  }

  if (lng) {
    name = lng === 'fr' ? 'name' : 'name_'+lng
  } else {
    name = 'name'
    lng = 'fr'
  }
//${root}_get_real_thes_path_path_id_${lng}(thesaurus_path::TEXT, $1) as name
  let query = `SELECT id_thes as id, ${name} as short_name,
          ${name} as name, nb_item::int,  ` +
        `  get_children_thesaurus_${root}(id_thes, $1) as get_children,` +
        ` nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::int , thesaurus_path as path ` +
        ` FROM ${root}_thesaurus WHERE thesaurus = $1 ` +
        //` AND  nb_tot_item != 0 ` +
        ` AND thesaurus_path <@  $2::ltree ` +
        `ORDER BY global_rank  `

  try {
    const thesaurustree = await db.any( query,  [req.params.thes, idThes],
    );
    // success
    res.status(200).sendData(thesaurustree);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error", e, req, res);
  }
};

// create url endpoint /thesaurusTreeMulti/:root,:thes,:idThes
exports.getThesaurusTreeMulti = async function (req, res) {
  let { root } = req.params;

  let { lng } = req.query;
  let name = ''

  if (lng) {
    name = lng === 'fr' ? 'name' : 'name_'+lng
    lng = lng === 'fr' ? '' : lng
  } else {
    name = 'name'
    lng = ''
  }

  let query = "";
  let ORDER_BY_value = "global_rank";
  let thesaurus = req.params.thes;
  let thesaurus_path = req.params.idThes;
  // Cas particulier du thesaurusnotredame pour lequel on n'affiche que à partir de Nomenclature ??
  // Verrue  pas tenable ...
  if (thesaurus === "notredame") thesaurus_path = "1.28123.1000";

  if (thesaurus === "deposant") {
    // pas de hierarchie, on classe par nom et non par global_rank
    ORDER_BY_value = "name";
  }

  let query1 =
    ` SELECT id_thes as id, id::integer as unique_id, thesaurus_path as path,
    ${root}_get_real_thes_path_multi_unique_id${lng}(id_thes, $1) as name,  ${name} as short_name ,nb_item::integer ,  ` +
    ` haschild as get_children , ` +
    ` nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::integer  ` +
    ` ,  global_rank ` +
      ` FROM ${root}_thesaurus_multi WHERE thesaurus = $1 `;
  let query2 = " AND  nb_tot_item != 0 ";

  let query3 = "AND nlevel(thesaurus_path) > 1 ";
  let query4 = ` AND thesaurus_path <@  $2::ltree ` + `ORDER BY ${ORDER_BY_value}`;

  if (root === "conservatoire3d" && thesaurus === "deposant") {
    query = query1 + query3 + query4;
  } else {
    query =
      query1 +
      // query2 +
      // query3 +
      query4;
  }

  // TODO : RACCOURCIR la requete TROP LONGUE
  try {
    const thesaurustreeMulti = await db.any(query, [req.params.thes, thesaurus_path]);
    // success
    res.status(200).sendData(thesaurustreeMulti);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusTreeMulti", e, req, res);
  }
};

// create url endpoint /thesaurusTreePactols/:root,:thes,:idThes
exports.getThesaurusTreePactols = async function (req, res) {
  let { root } = req.params;

  let { lng } = req.query;
  let name = ''
  if (lng) {
    name = lng === 'fr' ? 'name' : 'name_'+lng
  } else {
    name = 'name'
  }

  let query = "";
  let ORDER_BY_value = "thesaurus_path";
  let thesaurus = req.params.thes;
  let thesaurus_path = req.params.idThes;


  let query1Old =
    ` SELECT id_thes as id, id::integer as unique_id, thesaurus_path as path, name,nb_item::integer ,  ` +
    //trop long, on ajoute une colonne à la table pour y mettre l'info directement et construire l'arbre plus vite
    //' CASE WHEN exists_child_thes_pactols_poly_gen(\''+root+'\', $1, id_thes) THEN 1 ELSE NULL END  as get_children,' +
    ` CASE WHEN haschild = 1 THEN haschild ELSE null END as get_children , ` +
    ` nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::integer  ` +
    ` FROM ${root}_thesaurus_pactols WHERE thesaurus = $1 `;
  let query1 =
    ` SELECT id_thes as id, id::integer as unique_id, thesaurus_path as path, 
      CASE WHEN ${name} = '' THEN name ELSE ${name} END as name, nb_item::integer ,  ` + // si un nom traduit n'existe pas on prend le nom français
    ` CASE WHEN haschild = 1 THEN haschild ELSE null END as get_children , ` +
    ` nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::integer  ` +
    ` FROM ${root}_thesaurus_pactols WHERE thesaurus = $1 `;

  let query2 = " AND  nb_tot_item != 0 ";

  let query3 = "AND nlevel(thesaurus_path) > 1 ";
  //let query4 = ' AND thesaurus_path_reloaded <@  $2::ltree ' +
  let query4 =
    " AND thesaurus_path <@  $2::ltree " +
    //'ORDER BY thesaurus_path_reloaded '
    "ORDER BY thesaurus_path ";

  let query5 = `ORDER BY ${ORDER_BY_value}`;

  if (root === "conservatoire3d" && thesaurus === "deposant") {
    query = query1 + query3 + query4;
  } else {
    query =
      query1 +
      //+ query2
      //+ query3
      query4;
    //+ query5
  }

  try {
    const thesaurustreePactols = await db.any(query, [req.params.thes, thesaurus_path]);
    // success
    res.status(200).sendData(thesaurustreePactols);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusTreeMulti Pactols", e, req, res);
  }
};

// create url endpoint /thesaurusTreePactolsPoly/:root,:thes,:idThes
// Prise en compte de la Polyhiérarchie
exports.getThesaurusTreePactolsPoly = async function (req, res) {
  let { root } = req.params;

  let query = "";
  let ORDER_BY_value = "thesaurus_path";
  let thesaurus = req.params.thes;
  let thesaurus_path = req.params.idThes;

  let query1 =
    ` SELECT id_thes as id, id::integer as unique_id, thesaurus_path as path, name,nb_item::integer ,  ` +
    //trop long, on ajoute une colonne à la table pour y mettre l'info directement et construire l'arbre plus vite
    ` CASE WHEN exists_child_thes_pactols_gen('${root}', $1, id_thes) THEN 1 ELSE NULL END  as get_children,` +
    //' haschild as get_children , '  +
    ` nlevel(thesaurus_path_reloaded) AS depth, id_parent, nb_tot_item::integer  ` +
    ` FROM ${root}_thesaurus_pactols WHERE thesaurus = $1 `;
  let query2 = " AND  nb_tot_item != 0 ";

  let query3 = "AND nlevel(thesaurus_path) > 1 ";
  let query4 = ` AND thesaurus_path <@  $2::ltree ` + `ORDER BY ${ORDER_BY_value}`;

  let query5 = `ORDER BY ${ORDER_BY_value}`;

  if (root === "conservatoire3d" && thesaurus === "deposant") {
    query = query1 + query3 + query4;
  } else {
    query =
      query1 +
      //+ query2
      //+ query3
      query4;
    //+ query5
  }

  // TODO : RACCOURCIR la requete TROP LONGUE
  try {
    const thesaurustreePactols = await db.any(query, [req.params.thes, thesaurus_path]);
    // success
    res.status(200).sendData(thesaurustreePactols);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusTreeMulti pactols poly", e, req, res);
  }
};
// create url endpoint /thesaurusPactols/:root,:thesaurus GET
// Récupérer l'id et le chemin complet avec l'arborescence
exports.getThesaurusPactols = async function (req, res) {
  let { root } = req.params;

  try {
    const thes = await db.any(
      `SELECT id, id_thes, name as short_name, ${root}_get_real_thes_path_pactols(id_thes) as name ` +
        `FROM ${root}_thesaurus_pactols WHERE thesaurus = $1  ` +
        `ORDER BY thesaurus_path `,
      req.params.thesaurus,
    );
    res.status(200).sendData(thes);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols", e, req, res);
  }
};

// create url endpoint /ThesaurusPactolsName/:root,:thesaurus,:name GET
// charger le thesaurus sur les 3 premières lettres tapées pour l'autocomplete
// former un tableau avec label et value pour l'autocomplete en rajoutant l'id pour pouvoir indexer le mot
// TODO : provoque une erreur
// error_description: error: could not write to tuplestore temporary file: Aucun espace disponible sur le périphérique
// SQL statement "WITH RECURSIVE init_path (id_thes, path) AS (
//  '  SELECT id_parent, name::TEXT FROM conservatoire3d_thesaurus_pactols WHERE id_thes = p_thes_id AND name IS NOT NULL\n' +
//  'UNION ALL\n' +
//  "  SELECT id_parent, name::TEXT||'/'||ip.path FROM conservatoire3d_thesaurus_pactols JOIN init_path AS ip USING (id_thes) WHERE ip.id_thes IS NOT NULL AND name IS NOT NULL\n" +
//  ')\n' +
//  `SELECT ''||ip.path             FROM init_path AS ip ORDER BY LENGTH(ip.path) DESC LIMIT 1"\n` +
//  'PL/pgSQL function conservatoire3d_get_real_thes_path_pactols(integer) line 10 at SQL statement',

exports.getThesaurusPactolsName = async function (req, res) {
  let name = decodeURI(req.params.name);
  let branch = req.params.root;

  // UPDATE du 05/08/2021 : correction de la fonction : impossible (perf) d'afficher le chemin complet d'un item
  // on affiche juste le name, sans le chemin complet dans l'arborescence
  // V2 : on récupère aussi la collection ? derrière le idthes
  // update 09/11/2023 : ajout de la distinction fr ou en
  let lng_name = req.query.lng === 'fr' ? 'name' : 'name_'+req.query.lng

  try {
    const theso = await db.any(
      `SELECT DISTINCT ${lng_name} AS name,
                ${lng_name} AS value,
                concat(id, '_', id_thes,'_',collection) as id,
                ${lng_name} AS label
            FROM ${branch}_thesaurus_pactols
            WHERE thesaurus = $1 AND ${lng_name} ILIKE '%${name}%'
            ORDER BY ${lng_name}`,
      req.params.thesaurus,
    );

    res.status(200).sendData(theso);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols name ", e, req, res);
  }
};

//route("/ThesaurusPactolsNameFilter/:root,:thesaurus,:filter,:name") GET
// on pose un filtre pour ne récupérer qu'une partie du thesaurus à partir d'un id_thes
exports.getThesaurusPactolsNameFilter = async function (req, res) {
  let name = decodeURI(req.params.name);
  let branch = req.params.root;

  // UPDATE du 05/08/2021 : correction de la fonction : impossible (perf) d'afficher le chemin complet d'un item
  // on affiche juste le name, sans le chemin complet dans l'arborescence
  // V2 : on récupère aussi la collection ? derrière le idthes
  // update 09/11/2023 : ajout de la distinction fr ou en
  let lng_name = req.query.lng === 'fr' ? 'name' : 'name_'+req.query.lng

  try {
    const theso = await db.any(
        `SELECT DISTINCT ${lng_name} AS name,
                ${lng_name} AS value,
                concat(id, '_', id_thes,'_',collection) as id,
                ${lng_name} AS label
            FROM ${branch}_thesaurus_pactols
            WHERE thesaurus = $1
            AND thesaurus_path <@ ( SELECT thesaurus_path FROM ${branch}_thesaurus_pactols
                    WHERE thesaurus = $1 AND id_thes = $2)::ltree
            AND ${lng_name} ILIKE '%${name}%'
            ORDER BY ${lng_name}`,
        [req.params.thesaurus,
        req.params.filter],
    );

    res.status(200).sendData(theso);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols name with filter ", e, req, res);
  }
};

// Create url endpoint /ThesaurusName/:root,:thesaurus,:name GET
exports.getThesaurusName = async function (req, res) {
  let { name } = req.params;
  let { root } = req.params;

  try {
    const theso = await db.any(
      `SELECT name as value, concat(id, '_', id_thes) as id, name as label  FROM ${root}_thesaurus ` +
        `WHERE thesaurus = $1 AND name LIKE '%${name}%'`,
      req.params.thesaurus,
    );
    res.status(200).sendData(theso);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus name ", e, req, res);
  }
};

// Récupérer un id de concept en fonction du nom complet
// Create url endpoint /ThesaurusMultiFromName/:root,:thesaurus,:name GET
exports.getThesaurusMultiFromName = async function (req, res) {
  let { name } = req.params;
  let { root } = req.params;

  // Pour le moment, on remonte tous les thesaurus trouvés
  try {
    const idthes = await db.any(
      `SELECT id_thes, thesaurus  FROM ${root}_thesaurus_multi ` + `WHERE name = '${name}'`,
      req.params.thesaurus,
    );
    res.status(200).sendData(idthes);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi id FROM name ", e, req, res);
  }
};
// get all thesaurus item indexe for pactols thesaurus
// Create url endpoint for /ThesaurusPactolsItem/:root,:type,:item GET
exports.getThesaurusPactolsItem = async function (req, res) {
  // Donner tous les mots-clés de tous les thesaurus

  let { lng } = req.query;
  let name = ''
  if (lng) {
    name = lng === 'fr' ? 'name' : 'name_'+ lng
  } else {
    name = 'name'
  }

  let { root } = req.params;
  let { type } = req.params;

  // PACTOLSV2 : on ne jointe plus sur les id_thesaurus / id mais sur les id_thes_thesaurus / id_thes
  // par sécurité, on ajoute la jointure sur le thesaurus également
  try {
    const pactagList = await db.any(
      `SELECT id, id_thes, ${name} as name, tp.thesaurus, thesaurus_path, tp.collection, tp.identifier FROM ${root}_thesaurus_pactols tp ` +
        `INNER JOIN ${root}_thesaurus_pactols_item it ON it.id_thes_thesaurus = tp.id_thes AND it.thesaurus = tp.thesaurus ` +
        `WHERE  id_item = $1 AND item_type='${type}'`,
      req.params.itemId,
    );
    // success
    res.status(200).sendData(pactagList);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusPactolsItem", e, req, res);
  }
};

// Create url endpoint for /ThesaurusPactolsItem/:root,:type,:itemId DELETE
exports.deleteThesaurusPactolsItem = async function (req, res) {
  //v2 : ON Supprime l'info id (id_thesaurus) on ne garde que l'id_thes_thesaurus (au rechargement du thesaurus , on perd les id_thesaurus,
  // seuls les id_thes_thesaurus persistent
  //console.log(req.body)
  //console.log(req.params)
  let { itemId } = req.params;

  let query =
    `DELETE FROM ${req.params.root}_thesaurus_pactols_item` +
    ` WHERE id_thes_thesaurus = $1 AND thesaurus = $2 ` +
    ` AND id_item = $3 AND item_type = $4`;

  if (itemId !== 0) {
    try {
      const delitemThes = await db.any(query, [
        req.body.id_thes_thesaurus,
        req.body.thesaurus,
        itemId,
        req.params.type,
      ]);
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error in DELETE thesaurus pactols item", e, req, res);
    }
  } else {
    responseHelper.sendError(
      500,
      "server_error",
      "not a valid id for item to delete in thesaurus pactols item",
      req,
      res,
    );
  }
};

// create url endpoint /ThesaurusMaisonItem/:root,:type,:itemId
exports.getThesaurusMaisonItem = async function (req, res) {
  // Donner tous les mots-clés de tous les thesaurus
  // TODO : créer une table qui les recense avec un vrai nom à afficher ?

  // Ne ressortir pour afficher que ceux des chrono ?
  // Pas les déposants ? => les déposants sont dans le thesaurus Multi maintenant
  // Pas les plan_classement ?
  // UPDATE 27 /09/2022 on ne traite pas les licenses de la même façon pour l'affichage dans les vues,
  // inutile de les remonter ici

  let { root } = req.params;
  let { type } = req.params;

  try {
    const pactagList = await db.any(
      `SELECT id, name, short_name, t.thesaurus, thesaurus_path, t.id_thes FROM ${root}_thesaurus t ` +
        `INNER JOIN ${root}_thesaurus_item it ON it.id_thesaurus = t.id ` +
        `WHERE id_item = $1 AND item_type='${type}' ` +
        `AND t.thesaurus != 'license' `,
      req.params.itemId,
    );
    // success
    res.status(200).sendData(pactagList);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusItem", e, req, res);
  }
};

// create url endpoint /thesaurusPactols/:root,:thesaurus PUT
// Au départ, l'indexation avec les pactols était unique : on remplace si ça existe déjà
// TODO : A revoir globalement
exports.addThesaurusPactols = async function (req, res) {
  // Ajouter une ligne dans la table _thesaurus_pactols_item
  //avec le thesaurus, l'id et l'id thesaurus, l'id item le type
  // DONE : incrémenter le nb_item (automatiquement avec un trigger de la table thesaurus_pactols_item

  // on récupère le id_thesaurus et le id_thes séparé d'un _ (voir la fonction qui récupère les info
  // update PACTOLSV2 : on ajoute l'info collection
  let id_thesaurus = req.body.id.split("_")[0];
  let id_thes_thesaurus = req.body.id.split("_")[1];
  let collection = req.body.id.split("_")[2];
  //console.log(typeof id_thes_thesaurus)
  let int_id_thes_thesaurus = parseInt(req.body.id.split("_")[1]);
  //console.log(typeof int_id_thes_thesaurus)
  // ATTENTION pour le thesaurus chrono (et les autres sujets... on peut mettre plusieurs entrées qu'on peut supprimer ensuite
  let query =
    `SELECT count(*) FROM ${req.params.root}_thesaurus_pactols_item ` +
    `WHERE thesaurus = $1 AND id_thes_thesaurus = $2 ` +
    ` AND id_item = $3 AND item_type = $4`;
  try {
    // select sur le mot clé et l'item : existe déjà ? si oui , on retourne  ok avec data, si non on retourne ok ss data
    const resu = await db.any(query, [req.params.thesaurus, id_thes_thesaurus, req.body.item, req.body.type]);
    //console.log(resu)
    if (resu[0].count === "1") {
      // console.log('déjà la')
      // RIEN FAIT
      res.status(201).sendData(["0"]);
    } else {
      //console.log('il existe pas => on fait un insert')
      //if (insertThesPactolsItem(req.params.root, id_thesaurus, int_id_thes_thesaurus, req.params['thesaurus'], req.body['item'], req.body['type'], req.body['id_user'] ) ) {
      if (
        insertThesPactolsItemV2Coll(
          req.params.root,
          id_thesaurus,
          int_id_thes_thesaurus,
          req.params.thesaurus,
          req.body.item,
          req.body.type,
          req.body.id_user,
          collection,
        )
      ) {
        res.status(201).sendData(["1"]);
      } else {
        responseHelper.sendError(500, "server_error in thesaurus pactols add item", "erreur insert", req, res);
      }
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols add item ", e, req, res);
  }
};

// Create url endpoint for /thesaurusMulti/:root,:thesaurus
// Récupérer l'id et le chemin complet pour le thesaurus multi
// polyhiérarchie : il faut également récupérer le path (déjà fait) pour s'en servir à l'indexation (l'id_thes ne suffit plus)
exports.getThesaurusMulti = async function (req, res) {
  let { root } = req.params;

  try {
    const thes = await db.any(
      `SELECT id, id_thes,thesaurus, short_name, thesaurus_path,  ${root}_get_real_thes_path_multi_unique_id(id_thes, $1) as name ` +
        `FROM ${root}_thesaurus_multi WHERE thesaurus = $1  ` +
        `ORDER BY global_rank `,
      req.params.thesaurus,
    );
    res.status(200).sendData(thes);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiChildren/:root,:thesaurus,:idParent GET
// Récupérer les enfants (l'id et le chemin complet pour le thesaurus multi d'un id parent)
// Pour le mettre à jour dans le cas où tout n'aurait pas été correctement rentré (thesaurus_multi geo CND3D)
exports.getThesaurusMultiChildren = async function (req, res) {
  let { root } = req.params;

  try {
    const thesChildren = await db.any(
      `SELECT id, id_thes,thesaurus, name, short_name, thesaurus_path, rank, global_rank ` +
        `FROM ${root}_thesaurus_multi WHERE thesaurus = $1  AND id_parent = $2`,
      [req.params.thesaurus, req.params.idParent],
    );
    res.status(200).sendData(thesChildren);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiChildren/:root,:thesaurus,:idParent DELETE
// supprimer tous les enfants (l'id et le chemin complet pour le thesaurus multi d'un id parent)
// Pour les mettre à jour dans le cas où tout n'aurait pas été correctement rentré (thesaurus_multi geo CND3D)
exports.deleteThesaurusMultiChildren = async function (req, res) {
  let { root } = req.params;

  try {
    const thesChildren = await db.any(
      `DELETE FROM ${root}_thesaurus_multi ` + `WHERE thesaurus = $1  AND id_parent = $2  RETURNING id`,
      [req.params.thesaurus, req.params.idParent],
    );
    // console.log(thesChildren)
    res.status(200).sendData(thesChildren);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi DELETE children concept ", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiChildren/:root,:thesaurus,:idParent PATCH
// UPDATE haschild tous les enfants (l'id et le chemin complet pour le thesaurus multi d'un id parent)
// Pour les mettre à jour dans le cas où tout n'aurait pas été correctement rentré (thesaurus_multi geo CND3D)
exports.patchHasChildThesaurusMultiChildren = async function (req, res) {
  let { root } = req.params;
  console.log(`id_thes pour update : ${req.params.idParent}`);

  try {
    const thesHasChild = await db.any(
      `UPDATE ${root}_thesaurus_multi  SET haschild = 1 ` +
        `WHERE thesaurus = $1   AND haschild IS NULL AND id_parent = 1 AND id_thes = $2 RETURNING id`,
      [req.params.thesaurus, req.params.idParent],
    );
    //console.log(thesHasChild)
    res.status(200).sendData(thesHasChild);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi UPDATE haschild parent  ", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiConceptItem/:root,:thesaurus,:idThes  GET
// Récupérer les items taggué avec un concept (et toutes les infos)
// Pour pouvoir les remettre - dans le cas du CND3D ou on refait une partie du thesaurus geo
exports.getThesaurusMultiConceptItem = async function (req, res) {
  let { root } = req.params;

  try {
    const conceptHasItem = await db.any(
      `SELECT * FROM ${root}_thesaurus_multi_item WHERE thesaurus = $1 ` + ` AND id_thes_thesaurus = $2`,
      [req.params.thesaurus, req.params.idThes],
    );
    res.status(200).sendData(conceptHasItem);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi GET concept item indexed FROM id_thes", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiByShortName/:root,:thesaurus
// Récupérer l'id et le chemin complet pour le thesaurus multi trié par le nom pour l'affichage sur la liste déroulante du conservatoire (entité déposantes)
// polyhiérarchie : il faut également récupérer le path (déjà fait) pour s'en servir à l'indexation (l'id_thes ne suffit plus)
// On trie par shirt name pour les thesaurus qui n'ont pas de hierarchie (deposant)
exports.getThesaurusMultiByShortName = async function (req, res) {
  let { root } = req.params;

  try {
    const thes = await db.any(
      `SELECT id, id_thes,thesaurus, short_name, thesaurus_path,  ${root}_get_real_thes_path_multi_unique_id(id_thes, $1) as name ` +
        `FROM ${root}_thesaurus_multi WHERE thesaurus = $1  ` +
        `ORDER BY short_name `,
      req.params.thesaurus,
    );
    res.status(200).sendData(thes);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi by name", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiByName/:root,:thesaurus
// Récupérer l'id et le chemin complet pour le thesaurus multi trié par le nom pour l'affichage sur la liste déroulante du conservatoire (entité déposantes)
// polyhiérarchie : il faut également récupérer le path (déjà fait) pour s'en servir à l'indexation (l'id_thes ne suffit plus)
// ON continue de trier par name ou mieu parthesaurus_path pour les thesaurus qui ont une hierarchie
exports.getThesaurusMultiByName = async function (req, res) {
  let { root } = req.params;

  try {
    const thes = await db.any(
      `SELECT id, id_thes,thesaurus, short_name, thesaurus_path,  ${root}_get_real_thes_path_multi_unique_id(id_thes, $1) as name` +
        `, identifier  ` +
        `FROM ${root}_thesaurus_multi WHERE thesaurus = $1  ` +
        `ORDER BY name `,
      req.params.thesaurus,
    );
    res.status(200).sendData(thes);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi by name", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiByGlobalRank/:root,:thesaurus
// Récupérer l'id et le chemin complet pour le thesaurus multi trié par le global rank
// pour l'affichage sur la liste déroulante du conservatoire (geo)
// polyhiérarchie : il faut également récupérer le path (déjà fait)
// et dans le path, récupérer les noms en Français dans le short_name et non le name ! sinon on s'y perd
// car une fois on affiche en français et une autre fois en anglais
exports.getThesaurusMultiByGlobalRank = async function (req, res) {
  let { root } = req.params;

  try {
    const thes = await db.any(
      `SELECT id, id_thes,thesaurus, short_name, thesaurus_path,  ${root}_get_real_thes_path_multi_unique_id_short_name(id, $1) as name ` +
        `FROM ${root}_thesaurus_multi WHERE thesaurus = $1  ` +
        `ORDER BY global_rank `,
      req.params.thesaurus,
    );
    res.status(200).sendData(thes);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi by global_rank", e, req, res);
  }
};

// Create url endpoint for /thesaurusMultiOrigin/:root,:idProject
// Récupérer l'id et le chemin complet pour le thesaurus multi
exports.getThesaurusMultiOrigin = async function (req, res) {
  // nouvelle table folder_thesaurus_multi fait le lien entre les thesaurus et un projet
  // jointure externe maintenant pour récupérer les thesaurus et leurs indexations même s'ils ne font pas parti d'un schéma)
  let { root } = req.params;
  //multilang
  let lng_name = req.query.lng === 'fr' ? 'name' : 'name_'+req.query.lng

  let query =
    `SELECT t.id , t.id_thes , ft.visible, t.${lng_name} as name, t.short_name,
      CASE WHEN l.label IS NOT NULL THEN l.label ELSE t.short_name END as label , t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer ,
      get_first_metadata( f.id, '${root}', 'folder', 'project') AS project_name
     FROM ${root}_thesaurus_multi t
     INNER JOIN ${root}_folder_thesaurus_multi ft ON ft.thesaurus = t.thesaurus AND ft.id_thes = t.id_thes
     INNER JOIN ${root}_folder f ON f.id = ft.id_folder
     LEFT OUTER JOIN ${root}_metadata m ON m.list = t.thesaurus AND m.status = 'multi'
     LEFT OUTER JOIN ${root}_metadata_label l ON l.id_metadata = m.id AND l.language  = 'en'
     WHERE t.global_rank = '001' AND ft.id_folder = $1 AND order_thes IS NOT NULL
     ORDER BY order_thes `;
  //console.log(query)
  if (root === "pft3d") {
    if (parseInt(req.params.idProject)) {
      try {
        const thesMultiOrig = await db.any(query, req.params.idProject);
        res.status(200).sendData(thesMultiOrig);
      } catch (e) {
        responseHelper.sendError(500, "server_error in thesaurus multi Origin for pft3d ", e, req, res);
      }
    } else {
      // idProject = 0 => on veut utiliser la possibilité d'ajouter des éléments de thesaurus dans l'interface admin
      try {
        const thesMultiOrig = await db.any(
          `SELECT t.id_thes as id, t.${lng_name} as name, t.short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
            `FROM ${root}_thesaurus_multi t ` +
            `WHERE nlevel(thesaurus_path) = 1  `,
        );
        res.status(200).sendData(thesMultiOrig);
      } catch (e) {
        responseHelper.sendError(500, "server_error in thesaurus multi Origin for pft3d without project", e, req, res);
      }
    }
  } else if (root === "conservatoire3d") {
    // TODO : ajouter le multi lingue pour le CND3D en aoutant les colonnes?
    try {
      const thesMultiOrig = await db.any(
        `SELECT t.id_thes as id, t.name,t.short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
          `FROM ${root}_thesaurus_multi t ` +
          `WHERE nlevel(thesaurus_path) = 1  `,
      );
      res.status(200).sendData(thesMultiOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus multi Origin", e, req, res);
    }
  } else { // les corpus
    try {
      const thesMultiOrig = await db.any(
        `SELECT t.id_thes as id, t.name,t.name as short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
          `FROM ${root}_thesaurus_multi t ` +
          `WHERE nlevel(thesaurus_path) = 1  `,
      );
      res.status(200).sendData(thesMultiOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus multi Origin", e, req, res);
    }
  }
};

// Create url endpoint for /thesaurusSimpleOrigin/:root,:idProject
// Récupérer l'id et le chemin complet pour le thesaurus Maison / simple pour afficher sur la pageprojet la possibilité de parcourir
exports.getThesaurusSimpleOrigin = async function (req, res) {
  // nouvelle table folder_thesaurus_multi fait le lien entre les thesaurus et un projet
  let { root } = req.params;
  let query =
    `SELECT t.id , t.id_thes , t.name, t.short_name, l.label , t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer , ` +
    ` get_first_metadata( f.id, '${root}', 'folder', 'project') AS project_name ` +
    `FROM ${root}_thesaurus t ` +
    `INNER JOIN ${root}_folder_thesaurus ft ON ft.thesaurus = t.thesaurus AND ft.id_thes = t.id_thes ` +
    `INNER JOIN ${root}_folder f ON f.id = ft.id_folder ` +
    `LEFT OUTER JOIN ${root}_metadata m ON m.list = t.thesaurus AND m.status = 'thesaurus' ` +
    `LEFT OUTER JOIN ${root}_metadata_label l ON l.id_metadata = m.id AND l.language  = 'en' ` +
    `WHERE t.global_rank = '001' AND ft.id_folder = $1 AND order_thes IS NOT NULL ` +
    //'WHERE nlevel(thesaurus_path) = 1  AND ft.id_folder = $1 AND order_thes IS NOT NULL ' +
    `ORDER BY order_thes `;
  //console.log(query)
  if (root === "pft3d") {
    try {
      const thesMultiOrig = await db.any(query, req.params.idProject);
      res.status(200).sendData(thesMultiOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus multi Origin", e, req, res);
    }
  } else if (root === "conservatoire3d") {
    try {
      const thesMultiOrig = await db.any(
        `SELECT t.id_thes as id, t.name,t.short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
          `FROM ${root}_thesaurus_multi t ` +
          `WHERE nlevel(thesaurus_path) = 1  `,
      );
      res.status(200).sendData(thesMultiOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus multi Origin", e, req, res);
    }
  } else {
    try {
      const thesMultiOrig = await db.any(
        `SELECT t.id_thes as id, t.name,t.short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
          `FROM ${root}_thesaurus_multi t ` +
          `WHERE nlevel(thesaurus_path) = 1  `,
      );
      res.status(200).sendData(thesMultiOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus multi Origin", e, req, res);
    }
  }
};

// Create url endpoint for /thesaurusPactolsOrigin/:root,:idProject
// Récupérer s'il y a des indexation ou non avec le thesaurus pactols ?
// Récupérer l'id et le chemin complet pour le thesaurus Pactols pour afficher sur la page projet la possibilité de le parcourir
// update : suppression de la référence a thesaurus pactols poly display pour gérer la poly hierarchie
// update : ajout de la langue pour afficher les item en anglais si besoin
exports.getThesaurusPactolsOrigin = async function (req, res) {
  // nouvelle table folder_thesaurus_pactols fait le lien entre les thesaurus et un projet
  let { lng } = req.query;
  let name = ''
  if (lng) {
    name = lng === 'fr' ? 't.name' : 't.name_'+lng
  } else {
    name = 't.name'
  }

  let { root } = req.params;
  let query =
    `SELECT t.id_thes as id, ${name} as name, ${name} as short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
    `FROM ${root}_thesaurus_pactols t ` +
    `WHERE nlevel(thesaurus_path) = 1  `;
  let queryOLD =
    `SELECT t.id , t.id_thes, t.thesaurus_path_reloaded as thesaurus_path , t.name, t.name_en as label ,` +
    `t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer  ` +
    `, nlevel(t.thesaurus_path_reloaded) ` +
    //', get_first_metadata( f.id, \'' + root + '\', \'folder\', \'project\') AS project_name ' +
    `FROM ${root}_thesaurus_pactols_poly_display t ` +
    `INNER JOIN ${root}_folder_thesaurus_pactols ft ON ft.thesaurus = t.thesaurus ` +
    //'INNER JOIN ' + root + '_folder f ON f.id = ft.id_folder ' +
    `WHERE ft.id_folder = $1 AND order_thes IS NOT NULL ` +
    ` AND nlevel(t.thesaurus_path_reloaded) = 1 ` +
    //'AND t.nb_tot_item != 0 ' +
    //' ORDER BY nlevel(t.thesaurus_path) '
    `ORDER BY order_thes `;
  if (root === "pft3d") {
    try {
      const thesPactolsOrig = await db.any(query, req.params.idProject);
      res.status(200).sendData(thesPactolsOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus Pactols Origin", e, req, res);
    }
  } else if (root === "conservatoire3d") {
    try {
      const thesPactolsOrig = await db.any(
        `SELECT t.id_thes as id, t.name,t.short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
          `FROM ${root}_thesaurus_pactols t ` +
          `WHERE nlevel(thesaurus_path) = 1  `,
      );
      res.status(200).sendData(thesPactolsOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus Pactols Origin", e, req, res);
    }
  } else {
    // pour les corpus
    try {
      const thesPactolsOrig = await db.any(
        `SELECT t.id_thes as id, t.name,t.name as short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
          `FROM ${root}_thesaurus_pactols t ` +
          `WHERE nlevel(thesaurus_path) = 1  `,
      );
      res.status(200).sendData(thesPactolsOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus Pactols Origin des corpus", e, req, res);
    }
  }
};

// Create url endpoint for /ThesaurusMultiItem/:root,:type,:item GET
exports.getThesaurusMultiItem = async function (req, res) {
  // Donner tous les mots-clés de tous les thesaurus de thesaurus multi

  let { root } = req.params;
  let { type } = req.params;
  let result = [];
  let lng_name = ''
  if (! req.query.lng) {
    lng_name = 'name'
  } else {
    lng_name = req.query.lng === 'fr' ? 'name' : 'name_' + req.query.lng
  }

  try {
    const pactagmultiList = await db.any(
      `SELECT tm.id, tm.id_thes, tm.${lng_name} as name, tm.short_name, tm.thesaurus, mm.${lng_name} as thesaurus_name, it.thes_path  ` +
        `, tm.identifier, it.qualifier ` +
        `FROM ${root}_thesaurus_multi tm ` +
        //'INNER JOIN '+root+'_thesaurus_multi_item it ON it.id_thesaurus = tm.id ' +
        // on teste une jointure non plus sur l'id absolu du tag dans lethesaurus mais par rapport à l'id du tag dans le thesaurus
        `INNER JOIN ${root}_thesaurus_multi_item it ON it.id_thes_thesaurus = tm.id_thes AND it.thesaurus = tm.thesaurus ` +
        // On jointe un seconde fois sur la table du thesaurus pour récupérer le nom du thesaurus qui est à la racine du thesaurus
        ` INNER JOIN ${root}_thesaurus_multi mm ON mm.thesaurus = tm.thesaurus  ` +
        `WHERE  id_item = $1 AND item_type='${type}'   AND nlevel(mm.thesaurus_path)=1 `,
      req.params.itemId,
    );
    // success
    result.tagthes = Object.assign(pactagmultiList);
    try {
      const list_thes = await db.any(
        `SELECT distinct name FROM ${root}_thesaurus_multi mm ` + `WHERE  nlevel(mm.thesaurus_path)=1  `,
      );
      // success
      result.thesaurusName = Object.assign(list_thes);

      let resultat = Object.assign({}, result);
      res.status(200).sendData(resultat);
    } catch (e) {
      // error
      responseHelper.sendError(500, "server_error in getThesaurusMultiItem select thesaurus", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusMultiItem", e, req, res);
  }
};

// Create url endpoint for /ThesaurusMultiItem/:root,:type,:itemId DELETE
exports.deleteThesaurusMultiItem = async function (req, res) {
  //console.log(req.body)
  //console.log(req.params)
  let { itemId } = req.params;

  // polyhierarchie: on remplage id_thesaurus (qui n'est plus valable si on recharge) par  thes_path
  let query =
    `DELETE FROM ${req.params.root}_thesaurus_multi_item` +
    ` WHERE thesaurus = $1 AND thes_path = $2 ` +
    ` AND id_item = $3 AND item_type = $4`;
  if (itemId !== 0) {
    try {
      const delitemThes = await db.any(query, [req.body.thesaurus, req.body.thes_path, itemId, req.params.type]);
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error in DELETE thesaurus multi item", e, req, res);
    }
  } else {
    responseHelper.sendError(
      500,
      "server_error",
      "not a valid id for item to delete in thesaurus multi item",
      req,
      res,
    );
  }
};

// Create url endpoint for /ThesaurusMultiItemQualifier/:root,:type,:itemId DELETE
exports.deleteThesaurusMultiItemQual = async function (req, res) {
  //console.log(req.body)
  //console.log(req.params)
  let { itemId } = req.params;

  console.log(req.body)
  // polyhierarchie: on remplage id_thesaurus (qui n'est plus valable si on recharge) par  thes_path
  // on ne supprime que les indexation sur un certain qualifier
  let query =
      `DELETE FROM ${req.params.root}_thesaurus_multi_item` +
      ` WHERE thesaurus = $1 AND thes_path = $2 ` +
      ` AND id_item = $3 AND item_type = $4 AND qualifier = $5 `;
  if (itemId !== 0) {
    try {
      const delitemThes = await db.any(query, [req.body.thesaurus, req.body.thes_path, itemId, req.params.type, req.body.qualifier]);
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error in DELETE thesaurus multi item", e, req, res);
    }
  } else {
    responseHelper.sendError(
        500,
        "server_error",
        "not a valid id for item to delete in thesaurus multi item",
        req,
        res,
    );
  }
};



// Create url endpoint for /ThesaurusMaisonItem/:root,:type,:itemId DELETE
exports.deleteThesaurusMaisonItem = async function (req, res) {
  let { itemId } = req.params;

  let query =
    `DELETE FROM ${req.params.root}_thesaurus_item` +
    ` WHERE thesaurus = $1 AND thes_path = $2 ` +
    ` AND id_item = $3 AND item_type = $4`;
  if (itemId !== 0) {
    try {
      const delitemThes = await db.any(query, [req.body.thesaurus, req.body.thes_path, itemId, req.params.type]);
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error in DELETE thesaurus maison item", e, req, res);
    }
  } else {
    responseHelper.sendError(
      500,
      "server_error",
      "not a valid id for item to delete in thesaurus multi item",
      req,
      res,
    );
  }
};

// Create url endpoint for /ThesaurusItemQualifier/:root,:type,:itemId DELETE
exports.deleteThesaurusItemQual = async function (req, res) {
  //console.log(req.body)
  //console.log(req.params)
  let { itemId } = req.params;

  // polyhierarchie: on remplage id_thesaurus (qui n'est plus valable si on recharge) par  thes_path
  // on ne supprime que les indexation sur un certain qualifier renseigné
  // on supprime sur id_thes + thesaurus (+ qual)
  let query =
      `DELETE FROM ${req.params.root}_thesaurus_item` +
      ` WHERE thesaurus = $1 AND id_thes_thesaurus = $2 ` +
      ` AND id_item = $3 AND item_type = $4 AND qualifier = $5 `;
  if (itemId !== 0) {
    try {
      const delitemThes = await db.any(query, [req.body.thesaurus, req.body.id_thes, itemId, req.params.type, req.body.qualifier]);
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error in DELETE thesaurus item", e, req, res);
    }
  } else {
    responseHelper.sendError(
        500,
        "server_error",
        "not a valid id for item to delete in thesaurus item",
        req,
        res,
    );
  }
};

// create url endpoint /thesaurusGeo/name GET
exports.getThesaurusGeo = async function (req, res) {
  let { name } = req.params;

  try {
    const geo = await db.any(`SELECT id, name, short_name FROM corpus_geo WHERE short_name LIKE '%${name}%'`);
    res.status(200).sendData(geo);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus geo", e, req, res);
  }
};

// create url endpoint /thesaurusOrigin/:root
exports.getThesaurusOrigin = async function (req, res) {
  let { root } = req.params;

  try {
    const thesaurusOrig = await db.any(
      ` SELECT id_thes as id, name, short_name,nb_item, thesaurus, nb_tot_item  ` +
        ` FROM ${root}_thesaurus WHERE nlevel(thesaurus_path) = 1 ` +
        ` ORDER BY global_rank `,
    );
    // success
    try {
      const PeriodO = await db.one(`SELECT sum(nb_item) as nb_tot_item FROM ${root}_thesaurus_periodo `);

      let periodoTOT = {};
      periodoTOT.id = 0;
      periodoTOT.name = "Periodo";
      periodoTOT.short_name = "Periodo";
      periodoTOT.thesaurus = "Periodo";
      periodoTOT.nb_item = 0;
      periodoTOT.nb_tot_item = Object.assign(PeriodO.nb_tot_item);

      thesaurusOrig.push(periodoTOT);
      res.status(200).sendData(thesaurusOrig);
    } catch (e) {
      responseHelper.sendError(500, "server_error in get thesaurus Origin Periodo", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in get thesaurus Origin", e, req, res);
  }
};

// Create url endpoint for /thesaurusOriginFolder/:root,:idFolder
// Récupérer l'id et le chemin complet pour le thesaurus
exports.getThesaurusOriginFolder = async function (req, res) {
  // nouvelle table folder_thesaurus fait le lien entre les thesaurus et un projet
  let { root } = req.params;
  let lng = req.query.params === 'fr' ? 'fr' : 'en';
  let query =
    `SELECT t.id , t.id_thes , t.name, t.short_name, l.label , t.nb_item::integer, t.thesaurus, t.nb_tot_item::integer , ` +
    ` get_first_metadata( f.id, '${root}', 'folder', 'project') AS project_name ` +
    `FROM ${root}_thesaurus t ` +
    `INNER JOIN ${root}_folder_thesaurus ft ON ft.thesaurus = t.thesaurus AND ft.id_thes = t.id_thes ` +
    `INNER JOIN ${root}_folder f ON f.id = ft.id_folder ` +
    `LEFT OUTER JOIN ${root}_metadata m ON m.list = t.thesaurus AND m.status = 'thesaurus' ` +
    `LEFT OUTER JOIN ${root}_metadata_label l ON l.id_metadata = m.id AND l.language  = '${lng}' ` +
    //'WHERE nlevel(thesaurus_path) = 1 AND ft.id_folder = $1 ' +
    `WHERE t.global_rank = '001' AND ft.id_folder = $1 AND order_thes IS NOT NULL ` +
    //'WHERE nlevel(thesaurus_path) = 1  AND ft.id_folder = $1 AND order_thes IS NOT NULL ' +
    `ORDER BY order_thes `;
  if (root === "pft3d") {
    try {
      const thesOrigFolder = await db.any(query, req.params.idFolder);
      res.status(200).sendData(thesOrigFolder);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus Origin Folder", e, req, res);
    }
  } else if (root === "conservatoire3d") {
    try {
      const thesOrigFolder = await db.any(
        `SELECT t.id_thes as id, t.name,t.short_name,  t.nb_item, t.thesaurus, t.nb_tot_item  ` +
          `FROM ${root}_thesaurus t ` +
          `WHERE nlevel(thesaurus_path) = 1  `,
      );
      res.status(200).sendData(thesOrigFolder);
    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus Origin Folder CND3D", e, req, res);
    }
  }
};

//create url endpoint for /explorethes/:root,:thes,:idThes
exports.getThesaurusItem = async function (req, res) {
  let id = req.params.idThes,
    { root } = req.params,
    model = "project",
    nakala = 0,
    codeNom = 0;
  if (root === "conservatoire3d") {
    model = "deposit";
  }

  if (req.params.thes === "periodo") {
    try {
      if (root === "conservatoire3d") model = "deposit";
      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await db.any(
        `SELECT id , code , rank from ${root}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${root}_metadata_model where name = $1) `,
        model,
      );

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code) {
          if (startQ[i].code.substring(1) === "akala") nakala = startQ[i].id;
        }
      }

      try {
        // pour les depot sans images nakala mais une thumb à la racine : il faut le path !
        let queryp =
          `SELECT i.item_type,  t.label as short_name,  ff.folder_name, ` +
          //'pnakala.value as nakala, pnom.value as depot_name ,' +
          `CASE WHEN (array_to_string(pnakala.value, '') IS NULL) THEN '' ELSE array_to_string(pnakala.value, '') END as nakala, ` +
          `CASE WHEN (array_to_string(pnom.value, '') IS NULL) THEN '' ELSE array_to_string(pnom.value, '') END as depot_name, ` +
          `ff.id as idfolder, fi.path, fi.name as filename ` +
          `, ff.doi, ff.id_representative_picture as id_file ` +
          `FROM ${root}_thesaurus_periodo t INNER JOIN ${root}_thesaurus_periodo_item i ` +
          `ON  i.id_thes_periodo = t.id_periodo ` +
          `LEFT OUTER JOIN ${root}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
          `LEFT OUTER JOIN ${root}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 ` +
          `LEFT OUTER JOIN ${root}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 ` +
          `LEFT OUTER JOIN ${root}_file fi ON fi.id = ff.id_representative_picture ` +
          `WHERE t.id = $3`;

        const thesitemp = await db.any(queryp, [nakala, codeNom, id]);
        // success
        res.status(200).sendData(thesitemp);
      } catch (e) {
        responseHelper.sendError(500, "server_error in getThesaurusItem periodo", e, req, res);
      }
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusItem periodo ultime", e, req, res);
    }
  } else {
    try {
      // Get id_metadata from nakala info => 2 sources d'infos
      // and depot name (metadata rank 1)  info
      if (root === "conservatoire3d") model = "deposit";
      const startQ = await db.any(
        `SELECT id , code , rank from ${root}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${root}_metadata_model where name = $1) `,
        model,
      );
      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code) {
          if (startQ[i].code.substring(1) === "akala") nakala = startQ[i].id;
        }
      }
      try {
        let query =
          `SELECT i.item_type,  t.short_name, ` +
          //'pnakalaf.value[1] as fnakala, pnakalao.value[1] as onakala, ' +
          `CASE WHEN (ff.folder_name IS NULL) AND (o.name IS NOT NULL) THEN o.name ` +
          `     WHEN (ff.folder_name IS NOT NULL) AND (o.name IS  NULL) THEN ff.folder_name  END as type_name, ` +
          `ff.folder_name, ` +
          `CASE WHEN (array_to_string(pnakalaf.value,'') IS NULL) AND (array_to_string(pnakalao.value, '') IS NOT NULL ) AND (o.id_nakala IS NULL)  ` +
          `   THEN  array_to_string(pnakalao.value, '') ` +
          `   WHEN (o.id_nakala IS NOT NULL)   ` +
          `   THEN  o.id_nakala ` +
          `   WHEN (array_to_string(pnakalao.value,'') IS NULL) AND (array_to_string(pnakalaf.value, '') IS NOT NULL ) AND (o.id_nakala IS NULL)  ` +
          `   THEN  array_to_string(pnakalaf.value, '') END  as nakala, ` +
          //'CASE WHEN (array_to_string(pnakalaf.value,\'\') IS NULL) THEN \'\' ELSE array_to_string(pnakalaf.value, \'\') END as nakalaf, ' +
          //'CASE WHEN (array_to_string(pnakalao.value,\'\') IS NULL) THEN \'\' ELSE array_to_string(pnakalao.value, \'\') END as nakalao, ' +
          `CASE WHEN (array_to_string(pnom.value,'') IS NULL) AND (d.fr_name IS NULL) THEN '' ` +
          `   WHEN (array_to_string(pnom.value,'') IS NULL) AND (d.fr_name IS NOT NULL) THEN  d.fr_name ` +
          `   ELSE array_to_string(pnom.value, '') END as depot_name, ` +
          `CASE WHEN (fi.id IS NULL) AND (o.id IS NULL) THEN ffo.id ` +
          `     WHEN (ff.id IS NULL) AND (o.id IS NULL) THEN fi.id ` +
          `     WHEN (ff.id IS NULL) AND (fi.id IS NULL) THEN o.id ELSE 0  END as id,` +
          `CASE WHEN (fi.path IS NULL) AND (fo.path IS NULL) THEN ffo.path ` +
          `WHEN (ffo.path IS NULL) AND (fo.path IS NULL) THEN fi.path ` +
          `WHEN (ffo.path IS NULL) AND (fi.path IS NULL) THEN fo.path ELSE '' END as path,` +
          `CASE WHEN (fi.name IS NULL) AND (fo.name IS NULL) THEN ffo.name ` +
          `WHEN (fi.name IS NULL) AND (fo.name IS NOT NULL) THEN fo.name ELSE fi.name END as filename, ` +
          `CASE WHEN (ff.id IS NULL) AND (foo.id_folder IS NOT NULL) AND ( fi.id_folder IS NULL) THEN foo.id_folder  ` +
          `   WHEN (ff.id IS NULL) AND (foo.id_folder IS NULL) AND ( fi.id_folder IS NOT  NULL)  THEN fi.id_folder ` +
          //'   WHEN (ff.id IS NULL) AND (fi.id_folder IS NULL) AND (foo.id_folder IS NOT NULL) THEN foo.id_folder ' + // CAS des folder relié aux objets
          `   ELSE ff.id END as idfolder, ` + // considéré comme point d'entrée du dépot
          `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext WHEN ffo.file_ext IS NOT NULL THEN ffo.file_ext END as extension, ` +
          ` o.name as object_name, ff.nb_objects , ` +
          `CASE WHEN o.doi IS NULL THEN  ff.doi WHEN ff.doi IS NULL THEN o.doi ELSE ff.doi END as doi, ` +
          `COALESCE(ffo.id, fi.id, fo.id) as id_file ` +
          `FROM ${root}_thesaurus t INNER JOIN ${root}_thesaurus_item i ` +
          `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus ` +
          `LEFT OUTER JOIN ${root}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
          `LEFT OUTER JOIN ${root}_file ffo ON ffo.id = ff.id_representative_picture ` +
          `LEFT OUTER JOIN ${root}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
          `LEFT OUTER JOIN ${root}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
          `LEFT OUTER JOIN ${root}_file fo ON fo.id = o.id_file_representative ` +
          `LEFT OUTER JOIN ${root}_folder_object foo ON foo.id_object = o.id ` +
          `LEFT OUTER JOIN ${root}_folder ffoo ON ffoo.id = foo.id_folder ` +
          `LEFT OUTER JOIN ${root}_folder_depot fd ON ffoo.id = fd.id_folder ` +
          `LEFT OUTER JOIN ${root}_depot d ON d.id = fd.id_depot ` +
          `LEFT OUTER JOIN ${root}_passport pnakalaf ON pnakalaf.id_item = i.id_item AND pnakalaf.id_metadata = $1 ` +
          `LEFT OUTER JOIN ${root}_passport pnakalao ON pnakalao.id_item = fd.id_folder AND pnakalao.id_metadata = $1 ` +
          `LEFT OUTER JOIN ${root}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 ` +
          `WHERE t.thesaurus = $3 AND t.id_thes = $4 ORDER BY type_name`;

        const thesitem = await db.any(query, [nakala, codeNom, req.params.thes, id]);
        // success
        res.status(200).sendData(thesitem);
      } catch (e) {
        responseHelper.sendError(500, "server_error in getThesaurusItem", e, req, res);
      }
    } catch (e) {
      // error
      responseHelper.sendError(500, "server_error in getThesaurusItem for nakala", e, req, res);
    }
  }
};

//create url endpoint for /explorethesMulti/:root,:thes,:idThes
// on utilise des fontions sql d'une part pour les infos de type 3d associé
// et d'autre part pour les infos de type ply associé
exports.exploreThesaurusMultiItem = async function (req, res) {
  let id = req.params.idThes,
    { root } = req.params,
    model = "project",
    nakala = 0,
    codeNom = 0;

  try {
    if (root === "conservatoire3d") {
      // on appel cette API pour explorer les thesaurus soit pour les folder/depot
      if (req.query)
        if (req.query.model) model = req.query.model;
        else model = "virtualObject";
      else model = "virtualObject"; // soit pour les objets cela dépend de comment/sur quoi on a posé les tags
    }
    // Get id_metadata from nakala info and depot name (metadata rank 1)  info
    const startQ = await db.any(
      `SELECT id , code , rank from ${root}_metadata WHERE id_metadata_model in ` +
        `(SELECT id from ${root}_metadata_model where name = $1) `,
      model,
    );

    //console.log(startQ)
    for (let i = 0; i < startQ.length; i++) {
      // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
      if (startQ[i].rank === 1) codeNom = startQ[i].id;
      // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
      if (startQ[i].code) {
        if (startQ[i].code.substring(1) === "akala") nakala = startQ[i].id;
      }
    }
    try {
      // DONE (20/09/2021) Ajouter un hash3D pour afficher la 3D
      // 24 01 2022 Ajouter info visible pour masquer certain depot ...
      // 13 04 2022 Ajouter info fi3dfile pour récupérer les fichiers 3D liés à un objets pour éventuellement les télécharger
      // Dans ce cas, on assume que les fichiers 3D (ply) on le même nom que le fichier illustration de l'objet par exemple (pour le dépôt GRAVETTOS ça fonctionne)
      let query =
        `SELECT i.item_type,  t.short_name,  ff.folder_name, ` +
        `CASE WHEN (array_to_string(pnakalaf.value,'') IS NULL) AND (array_to_string(pnakalao.value, '') IS NOT NULL )  AND (o.id_nakala IS NULL) ` +
        `   THEN  array_to_string(pnakalao.value, '') ` +
        `   WHEN (o.id_nakala IS NOT NULL)   ` +
        `   THEN  o.id_nakala ` +
        `   WHEN (array_to_string(pnakalao.value,'') IS NULL) AND (array_to_string(pnakalaf.value, '') IS NOT NULL ) AND (o.id_nakala IS NULL) ` +
        `   THEN  array_to_string(pnakalaf.value, '') END  as nakala, ` +
        //'CASE WHEN (array_to_string(pnakala.value,\'\') IS NULL) THEN \'\' ELSE array_to_string(pnakala.value, \'\') END as nakala, ' +
        `CASE WHEN (dfolder.fr_name IS NULL) AND (dobject.fr_name IS NOT NULL) THEN dobject.fr_name` +
        `   WHEN (dfolder.fr_name IS NOT NULL) AND (dobject.fr_name IS NULL) THEN dfolder.fr_name` +
        `   ELSE  ''  END as depot_name, ` +
        `CASE WHEN (fi.id IS NULL) AND (o.id IS NULL) THEN ffo.id ` +
        `WHEN (ff.id IS NULL) AND (o.id IS NULL) THEN fi.id ` +
        `WHEN (ff.id IS NULL) AND (fi.id IS NULL) THEN o.id ELSE 0  END as id,` +
        `CASE WHEN (fi.path IS NULL) AND (fo.path IS NULL) THEN ffo.path ` +
        `WHEN (ffo.path IS NULL) AND (fo.path IS NULL) THEN fi.path ` +
        `WHEN (ffo.path IS NULL) AND (fi.path IS NULL) THEN fo.path ELSE '' END as path,` +
        `CASE WHEN (fi.name IS NULL) AND (fo.name IS NULL) THEN ffo.name ` +
        `WHEN (fi.name IS NULL) AND (fo.name IS NOT NULL) THEN fo.name ELSE fi.name END as filename, ` +
        `CASE WHEN (fi.id_folder IS NULL) AND ( ffoo.id IS  NULL) AND (ff.id IS NOT NULL) THEN ff.id ` + // type folder
        `     WHEN (ff.id IS NULL) AND (fi.id_folder IS NULL) AND ( ffoo.id IS NOT NULL) THEN ffoo.id ` + // type object
        `     ELSE fi.id_folder END as idfolder, ` +
        `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext WHEN ffo.file_ext IS NOT NULL THEN ffo.file_ext END as extension, ` +
        ` o.name as object_name, ff.nb_objects ` +
        `, CASE WHEN (ffo.id IS NOT NULL) THEN ffo.id ` +
        `       WHEN (fi.id IS NOT NULL ) THEN fi.id ` +
        `       WHEN (fo.id IS NOT NULL ) THEN fo.id END as id_file ` + // ajout pour récupérer l'id file à afficher avec IIIF
        `, CASE WHEN o.doi IS NULL THEN  ff.doi WHEN ff.doi IS NULL THEN o.doi ELSE ff.doi END as doi  ` +
        `, CASE WHEN o.id IS NOT NULL THEN ${root}_get_hash3d(o.id, 0) ELSE NULL END as hash3d_info  ` +
        `, CASE WHEN o.id IS NOT NULL THEN ${root}_get_plyobject(o.id, 0) ELSE NULL END as ply3d_info  ` +
        `, CASE WHEN ff.id is NOT NULL THEN ff.visible ELSE 'true' END as visible ` +
        `FROM ${root}_thesaurus_multi t INNER JOIN ${root}_thesaurus_multi_item i ` +
        `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus ` +
        `LEFT OUTER JOIN ${root}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
        `LEFT OUTER JOIN ${root}_file ffo ON ffo.id = ff.id_representative_picture ` +
        `LEFT OUTER JOIN ${root}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
        `LEFT OUTER JOIN ${root}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
        `LEFT OUTER JOIN ${root}_file fo ON fo.id = o.id_file_representative ` +
        `LEFT OUTER JOIN ${root}_folder_object foo ON foo.id_object = o.id ` +
        `LEFT OUTER JOIN ${root}_folder ffoo ON ffoo.id = foo.id_folder ` +
        `LEFT OUTER JOIN ${root}_folder_depot fd ON ffoo.id = fd.id_folder ` +
        `LEFT OUTER JOIN ${root}_depot dobject ON dobject.id = fd.id_depot ` + //depot d'un objet taggue
        `LEFT OUTER JOIN ${root}_folder_depot ffd ON ffd.id_folder = ff.id ` +
        `LEFT OUTER JOIN ${root}_depot dfolder ON dfolder.id = ffd.id_depot ` + // depot d'un folder taggue
        `LEFT OUTER JOIN ${root}_passport pnakalaf ON pnakalaf.id_item = i.id_item AND pnakalaf.id_metadata = $1 ` +
        `LEFT OUTER JOIN ${root}_passport pnakalao ON pnakalao.id_item = fd.id_folder AND pnakalao.id_metadata = $1 ` +
        `LEFT OUTER JOIN ${root}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 ` +
        `WHERE t.thesaurus = $3 AND t.id_thes = $4 ` +
        `ORDER BY ff.folder_name `;

      const thesitem = await db.any(query, [nakala, codeNom, req.params.thes, id]);
      // success
      res.status(200).sendData(thesitem);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusMultiItem", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusItem for nakala", e, req, res);
  }
};

//create url endpoint for /explorethesMultiNB/:branche,:thes,:idThes
// create url endpoint for /explorethesMultiNB/:branche,:thes
// Pour la pagination , on récupère l'ensemble des folders concernés par l'item du thesaurus
// Pour le moment, seulement les items de type file sont traités
// TODO : traiter le cas où les items type taggué sont des objects
exports.exploreThesaurusMultiItemNB = async function (req, res) {
  let { branche } = req.params,
      lng = req.query.lng,
    //id = parseInt(req.params.idThes),
    path = req.query.thes_path;
  // TODO : récupérer le projectId et limiter les folder à ceux du projet
  let { projectId } = req.query;
  //{ thes_path: '28123.1000.1413.1417.1418.33027' }
  try {
    //1 on récupère tous les folders concernés et le nombre d'item
    // DONE : completer la requete pour les items de type folder ou objets
    // On revient au tag par id (et non pas par thes_path moins cohérent)
    let query =
      `select json_agg(x) as folders_info FROM (` +
      `SELECT id_folder, count(*) as nb_file, 0 as nb_object, 0 as nb_unico FROM ${branche}_file WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_multi_item WHERE thesaurus = $1 AND item_type = 'file' ` +
      ` AND id_thes_thesaurus = $2) GROUP BY id_folder ` +
      `UNION ALL ` +
      `SELECT id_folder, 0 as nb_file, count(*) as nb_object, 0 as nb_unico FROM ${branche}_object WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_multi_item WHERE thesaurus = $1 AND item_type = 'object' ` +
      ` AND id_thes_thesaurus = $2 ) ` +
      ` GROUP BY id_folder  ` +
      `UNION ALL  ` +
      `SELECT id_folder, 0 as nb_file, 0 as nb_object, count(*) as nb_unico FROM ${branche}_unico u ` +
      `INNER JOIN ${branche}_file f ON f.id = u.id_file WHERE u.id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_multi_item WHERE thesaurus = $1 AND item_type = 'unico' ` +
      ` AND id_thes_thesaurus = $2 ) ` +
      ` GROUP BY id_folder  ` +
      `) AS x`;
    //const nbthesitemfolders = await db.oneOrNone(query, [req.params.thes, path])
    const nbthesitemfolders = await db.oneOrNone(query, [req.params.thes, req.params.idThes]);

    let name = ''
    if (!lng) {
      name = 'name'
    } else {
      name = lng === 'fr' ? 'name' : 'name_'+lng
    }
    try {
      // 2 / on récupère le nom complet du concept en jeu
      // REVISION de la requete pour traiter la polyhierarchie : on interroge non pas sur le id_thes mais avec l'id
      //const thesname = await db.oneOrNone('SELECT nb_item, '+branche+'_get_real_thes_path_multi_path_id(thesaurus_path::TEXT, $1) as thesname  ' +
      //    'FROM '+branche+'_thesaurus_multi WHERE thesaurus =  $1 AND thesaurus_path = $2 ' ,  [req.params.thes, path])
      // Nouvelle revision - 06/12/2022 on regarde le id_thes (dans le path)
      // on ne prend que le premier item, tous sont sensés avoir le même name
      const thesname = await db.oneOrNone(
        `SELECT ${name} as thesname  ` + `FROM ${branche}_thesaurus_multi WHERE thesaurus =  $1 AND id_thes = $2  limit 1`,
        [req.params.thes, req.params.idThes],
      );

      // 3/ on assemble les 2
      let result = Object.assign({}, thesname, nbthesitemfolders);
      // success
      res.status(200).sendData(result);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusItemNB thesname", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusItemNB folders", e, req, res);
  }
};

//create url endpoint for /explorethesPactols/:root,:thes,:idThes
// on utilise des fontions sql d'une part pour les infos de type 3d associé
// et d'autre part pour les infos de type ply associé
exports.exploreThesaurusPactolsItem = async function (req, res) {
  let id = req.params.idThes,
    { root } = req.params,
    model = "project",
    nakala = 0,
    codeNom = 0;

  try {
    if (root === "conservatoire3d") {
      // on appel cette API pour explorer les thesaurus soit pour les folder/depot
      if (req.query)
        if (req.query.model) model = req.query.model;
        else model = "virtualObject";
      else model = "virtualObject"; // soit pour les objets cela dépend de comment/sur quoi on a posé les tags
    }
    // Get id_metadata from nakala info and depot name (metadata rank 1)  info
    const startQ = await db.any(
      `SELECT id , code , rank from ${root}_metadata WHERE id_metadata_model in ` +
        `(SELECT id from ${root}_metadata_model where name = $1) `,
      model,
    );

    for (let i = 0; i < startQ.length; i++) {
      // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
      if (startQ[i].rank === 1) codeNom = startQ[i].id;
      // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
      if (startQ[i].code) {
        if (startQ[i].code.substring(1) === "akala") nakala = startQ[i].id;
      }
    }
    try {
      // DONE (20/09/2021) Ajouter un hash3D pour afficher la 3D
      // 24 01 2022 Ajouter info visible pour masquer certain depot ...
      // 13 04 2022 Ajouter info fi3dfile pour récupérer les fichiers 3D liés à un objets pour éventuellement les télécharger
      // Dans ce cas, on assume que les fichiers 3D (ply) on le même nom que le fichier illustration de l'objet par exemple (pour le dépôt GRAVETTOS ça fonctionne)
      let query =
        `SELECT i.item_type,  t.short_name,  ff.folder_name, ` +
        `CASE WHEN (array_to_string(pnakalaf.value,'') IS NULL) AND (array_to_string(pnakalao.value, '') IS NOT NULL )  AND (o.id_nakala IS NULL) ` +
        `   THEN  array_to_string(pnakalao.value, '') ` +
        `   WHEN (o.id_nakala IS NOT NULL)   ` +
        `   THEN  o.id_nakala ` +
        `   WHEN (array_to_string(pnakalao.value,'') IS NULL) AND (array_to_string(pnakalaf.value, '') IS NOT NULL ) AND (o.id_nakala IS NULL) ` +
        `   THEN  array_to_string(pnakalaf.value, '') END  as nakala, ` +
        //'CASE WHEN (array_to_string(pnakala.value,\'\') IS NULL) THEN \'\' ELSE array_to_string(pnakala.value, \'\') END as nakala, ' +
        `CASE WHEN (dfolder.fr_name IS NULL) AND (dobject.fr_name IS NOT NULL) THEN dobject.fr_name` +
        `   WHEN (dfolder.fr_name IS NOT NULL) AND (dobject.fr_name IS NULL) THEN dfolder.fr_name` +
        `   ELSE  ''  END as depot_name, ` +
        `CASE WHEN (fi.id IS NULL) AND (o.id IS NULL) THEN ffo.id ` +
        `WHEN (ff.id IS NULL) AND (o.id IS NULL) THEN fi.id ` +
        `WHEN (ff.id IS NULL) AND (fi.id IS NULL) THEN o.id ELSE 0  END as id,` +
        `CASE WHEN (fi.path IS NULL) AND (fo.path IS NULL) THEN ffo.path ` +
        `WHEN (ffo.path IS NULL) AND (fo.path IS NULL) THEN fi.path ` +
        `WHEN (ffo.path IS NULL) AND (fi.path IS NULL) THEN fo.path ELSE '' END as path,` +
        `CASE WHEN (fi.name IS NULL) AND (fo.name IS NULL) THEN ffo.name ` +
        `WHEN (fi.name IS NULL) AND (fo.name IS NOT NULL) THEN fo.name ELSE fi.name END as filename, ` +
        `CASE WHEN (fi.id_folder IS NULL) AND ( ffoo.id IS  NULL) AND (ff.id IS NOT NULL) THEN ff.id ` + // type folder
        `     WHEN (ff.id IS NULL) AND (fi.id_folder IS NULL) AND ( ffoo.id IS NOT NULL) THEN ffoo.id ` + // type object
        `     ELSE fi.id_folder END as idfolder, ` +
        `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext WHEN ffo.file_ext IS NOT NULL THEN ffo.file_ext END as extension, ` +
        ` o.name as object_name, ff.nb_objects ` +
        `, CASE WHEN (ffo.id IS NOT NULL) THEN ffo.id ` +
        `       WHEN (fi.id IS NOT NULL ) THEN fi.id ` +
        `       WHEN (fo.id IS NOT NULL ) THEN fo.id END as id_file ` + // ajout pour récupérer l'id file à afficher avec IIIF
        `, CASE WHEN o.doi IS NULL THEN  ff.doi WHEN ff.doi IS NULL THEN o.doi ELSE ff.doi END as doi  ` +
        `, CASE WHEN o.id IS NOT NULL THEN ${root}_get_hash3d(o.id, 0) ELSE NULL END as hash3d_info  ` +
        `, CASE WHEN o.id IS NOT NULL THEN ${root}_get_plyobject(o.id, 0) ELSE NULL END as ply3d_info  ` +
        `, CASE WHEN ff.id is NOT NULL THEN ff.visible ELSE 'true' END as visible ` +
        `FROM ${root}_thesaurus_pactols t INNER JOIN ${root}_thesaurus_pactols_item i ` +
        `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus ` +
        `LEFT OUTER JOIN ${root}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
        `LEFT OUTER JOIN ${root}_file ffo ON ffo.id = ff.id_representative_picture ` +
        `LEFT OUTER JOIN ${root}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
        `LEFT OUTER JOIN ${root}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
        `LEFT OUTER JOIN ${root}_file fo ON fo.id = o.id_file_representative ` +
        `LEFT OUTER JOIN ${root}_folder_object foo ON foo.id_object = o.id ` +
        `LEFT OUTER JOIN ${root}_folder ffoo ON ffoo.id = foo.id_folder ` +
        `LEFT OUTER JOIN ${root}_folder_depot fd ON ffoo.id = fd.id_folder ` +
        `LEFT OUTER JOIN ${root}_depot dobject ON dobject.id = fd.id_depot ` + //depot d'un objet taggue
        `LEFT OUTER JOIN ${root}_folder_depot ffd ON ffd.id_folder = ff.id ` +
        `LEFT OUTER JOIN ${root}_depot dfolder ON dfolder.id = ffd.id_depot ` + // depot d'un folder taggue
        `LEFT OUTER JOIN ${root}_passport pnakalaf ON pnakalaf.id_item = i.id_item AND pnakalaf.id_metadata = $1 ` +
        `LEFT OUTER JOIN ${root}_passport pnakalao ON pnakalao.id_item = fd.id_folder AND pnakalao.id_metadata = $1 ` +
        `LEFT OUTER JOIN ${root}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 ` +
        `WHERE t.thesaurus = $3 AND t.id_thes = $4 ` +
        `ORDER BY ff.folder_name `;

      const thesitem = await db.any(query, [nakala, codeNom, req.params.thes, id]);
      // success
      res.status(200).sendData(thesitem);
    } catch (e) {
      responseHelper.sendError(500, "server_error in exploreThesaurusPactolsItem", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in exploreThesaurusPactolsItem for nakala", e, req, res);
  }
};
// create url endpoint for /explorethesPactolsNB/:branche,:thes
// Pour la pagination , on récupère l'ensemble des folders concernés par l'item du thesaurus
// Pour le moment, seulement les items de type file sont traités
// TODO : traiter le cas où les items type taggué sont des objects
exports.exploreThesaurusPactolsItemNB = async function (req, res) {
  let { branche } = req.params,
    //id = parseInt(req.params.idThes),
    path = req.query.thes_path;
  // TODO : récupérer le projectId et limiter les folder à ceux du projet
  let { projectId } = req.query;

  try {
    //1 on récupère tous les folders concernés et le nombre d'item
    // TODO : completer la requete pour les items de type folder ou objets
    let query =
      `select json_agg(x) as folders_info FROM (SELECT id_folder, count(*) as nb_file, 0 as nb_object, 0 as nb_unico FROM ${branche}_file WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_pactols_item WHERE thesaurus = $1 AND item_type = 'file' ` +
      ` AND id_thes_thesaurus = $2) GROUP BY id_folder ` +
      `UNION ALL ` +
      `SELECT id_folder, 0 as nb_file, count(*) as nb_object, 0 as nb_unico FROM ${branche}_object WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_pactols_item WHERE thesaurus = $1 AND item_type = 'object' ` +
      ` AND id_thes_thesaurus = $2 ) ` +
      ` GROUP BY id_folder  ` +
      `UNION ALL  ` +
      `SELECT id_folder, 0 as nb_file, 0 as nb_object, count(*) as nb_unico FROM ${branche}_unico u ` +
      `INNER JOIN ${branche}_file f ON f.id = u.id_file WHERE u.id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_pactols_item WHERE thesaurus = $1 AND item_type = 'unico' ` +
      ` AND id_thes_thesaurus = $2 ) ` +
      ` GROUP BY id_folder ) AS x`;
    //let query = 'select json_agg(x) as folders_info FROM (SELECT id_folder, count(*) as nb FROM '+branche+'_file WHERE id IN ' +
    //'(SELECT id_item FROM '+branche+'_thesaurus_pactols_item WHERE thesaurus = $1 AND item_type = \'file\' ' +
    //' AND id_thes_thesaurus = $2) GROUP BY id_folder ) AS x'
    const nbthesitemfolders = await db.oneOrNone(query, [req.params.thes, req.params.idThes]);

    try {
      // 2 / on récupère le nom complet du concept en jeu et le total count
      // REVISION de la requete pour traiter la polyhierarchie : on interroge non pas sur le id_thes mais avec l'id
      const thesname = await db.oneOrNone(
        `SELECT nb_item::integer, nb_item::integer as total_count, ${branche}_get_real_thes_path_pactols_path_id(thesaurus_path::TEXT, $1) as thesname  ` +
          `, ${branche}_get_title_from_project_for_doi(${projectId}) as projectName ` +
          `FROM ${branche}_thesaurus_pactols WHERE thesaurus =  $1 AND thesaurus_path = $2 `,
        [req.params.thes, path],
      );

      // 3/ on assemble les 2
      let result = Object.assign({}, thesname, nbthesitemfolders);
      // success
      res.status(200).sendData(result);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusPactolsItemNB thesname", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusPactolsItemNB folders", e, req, res);
  }
};
//create url endpoint for /explorethesMultiNBGeneral/:branche,:idThes,:thesaurus
// Pour la pagination , on récupère l'ensemble des folders concernés par l'item de tous les thesaurus
// On ne regarde pas à quel thesaurus on appartient mais on veut le concept quelque soit le thesaurus auquel il appartient
// il peut y avoir plusieurs fois le même concept
// Pour le moment, seulement les items de type file sont traités
// TODO : traiter le cas où les items type taggué sont des objects
exports.exploreThesaurusMultiItemNBGeneral = async function (req, res) {
  // Même si c'est général, on ne récupère que les thesaurus du projet (table _folder_thesaurus_multi renseignée)
  let { branche } = req.params,
      lng = req.query.lng,
    id = parseInt(req.params.idThes),
    thesaurus = req.params.thesaurus;

  // TODO : récupérer le projectId et limiter les folder à ceux du projet
  try {
    //1 on récupère tous les folders concernés et le nombre d'item
    // TODO : completer la requete pour les items de type folder ou objets
    let query =
        `select json_agg(x) as folders_info FROM (` +
        `SELECT id_folder, count(*) as nb_file, 0 as nb_object, 0 as nb_unico FROM ${branche}_file WHERE id IN ` +
        `(SELECT id_item FROM ${branche}_thesaurus_multi_item mi INNER JOIN ${branche}_folder_thesaurus_multi fm ON fm.thesaurus = mi.thesaurus
          WHERE item_type = 'file'
          AND mi.thesaurus = '${thesaurus}' ` +
        ` AND id_thes_thesaurus = $1) GROUP BY id_folder ` +
        `UNION ALL ` +
        `SELECT id_folder, 0 as nb_file, count(*) as nb_object, 0 as nb_unico FROM ${branche}_object WHERE id IN ` +
        `(SELECT id_item FROM ${branche}_thesaurus_multi_item mi INNER JOIN ${branche}_folder_thesaurus_multi fm ON fm.thesaurus = mi.thesaurus
          WHERE  item_type = 'object' AND mi.thesaurus = '${thesaurus}' ` +
        ` AND id_thes_thesaurus = $1 ) ` +
        ` GROUP BY id_folder  ` +
        `UNION ALL  ` +
        `SELECT id_folder, 0 as nb_file, 0 as nb_object, count(*) as nb_unico FROM ${branche}_unico u ` +
        `INNER JOIN ${branche}_file f ON f.id = u.id_file WHERE u.id IN ` +
        `(SELECT id_item FROM ${branche}_thesaurus_multi_item mi INNER JOIN ${branche}_folder_thesaurus_multi fm ON fm.thesaurus = mi.thesaurus
          WHERE item_type = 'unico' AND mi.thesaurus = '${thesaurus}' ` +
        ` AND id_thes_thesaurus = $1 ) ` +
        ` GROUP BY id_folder  ` +
        `) AS x`;


    let queryOLD =
      `select json_agg(x) as folders_info FROM ` +
      `(SELECT id_folder, count(*) as nb FROM ${branche}_file ` +
      ` WHERE id IN ` +
      `    (SELECT id_item FROM ${branche}_thesaurus_multi_item mi ` +
      `        INNER JOIN ${branche}_thesaurus_multi m ON m.id_thes = mi.id_thes_thesaurus AND m.thesaurus_path = mi.thes_path ` +
      `        INNER JOIN ${branche}_folder_thesaurus_multi fm ON fm.thesaurus = m.thesaurus ` +
      `        WHERE item_type = 'file' ` +
      `        AND id_thes_thesaurus = $1` +
      //'        AND thesaurus_path <@ (SELECT get_thesaurus_multi_general_enfants_path(\''+branche+'\', $1 ))::ltree[]
      `     )  ` +
      ` GROUP BY id_folder ) AS x`;
    const nbthesitemfolders = await db.oneOrNone(query, [id]);

    let name = ''
    if (!lng) {
      name = 'name'
    } else {
      name = lng === 'fr' ? 'name' : 'name_'+lng
    }
    try {
      // 2 / on récupère le nom complet du concept en jeu
      // TODO : polyhierarchie : est-ce qu'on donne toutes les hiérarchie du concept possible ?
      // avec pour chaque concept le nombre ?
      const thesname = await db.any(
        `SELECT sum(nb_item), ${name} as thesname
            FROM ${branche}_thesaurus_multi m
            INNER JOIN ${branche}_folder_thesaurus_multi fm ON fm.thesaurus = m.thesaurus
            WHERE m.id_thes = $1 AND m.thesaurus = '${thesaurus}' GROUP BY ${name} `,
        [id],
      );

      // 3/ on assemble les 2
      let result = Object.assign({}, thesname, nbthesitemfolders);
      // success
      res.status(200).sendData(result);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusItemNB thesname general ", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusItemNB folders general ", e, req, res);
  }
};

//create url endpoint for /explorethesMultiPage/:branche,:thes,:idThes POST
// on le met en POST pour récupérer les infos offset et limit dans le body ainsi que le id_user pour récupérer seulement
// les item des folders autorisés
// TODO : traiter le cas des items de types object ou folder ...
// done traiter le cas des unicos
exports.exploreThesaurusMultiItemPage = async function (req, res) {
  let id = req.params.idThes,
    { branche } = req.params,
    model = "project",
    nakala = 0,
    codeNom = 0;
  let { body } = req;
  // DONE : Mettre le id_user dans le body et envoyer la requete seulement sur les id_item autorisées par le id_user :
  //body = req.query
  let offset = (parseInt(body.page) - 1) * body.pagination;
  //console.log('offset : '+offset)
  let limit = body.pagination;
  //console.log('limit : '+limit)
  let userId = parseInt(body.userId);

  // gestion de la polyhierarchie
  let { thes_path } = req.body;
  let read = "",
    in_query = "";
  if (userId) {
    read = `SELECT get_access_gen( $1, '${branche}') as fold `;
  } else {
    read = `SELECT array_agg(id)  as fold FROM ${branche}_folder WHERE  status = 'public'  AND $1 = $1 `;
  }

  let lng = req.body.lng
  let name = ''
  if (!lng) {
    name = 'name'
  } else {
    name = lng === 'fr' ? 'name' : 'name_'+lng
  }
  // Première requete : récupèrer la liste des folders autorisés par le user :
  // TODO : ils doivent être dans le projet !!!
  // récupérer l'id projet avec la table pft3d_folder_thesaurus_multi ??? ou le passer en parametre dans le body
  try {
    const userRead = await db.oneOrNone(read, userId);

    if (userRead.fold.length === 0) in_query = "";
    else
      in_query =
        ` AND ( i.id_item IN ` +
        `( SELECT id FROM ${branche}_file WHERE id_folder in ${JSON.stringify(userRead.fold)
          .replace("[", "(")
          .replace("]", ")")}  ) OR ` +
        ` i.id_item IN ( SELECT id FROM ${branche}_object WHERE id_folder in ${JSON.stringify(userRead.fold)
          .replace("[", "(")
          .replace("]", ")")}  ) )`;

    try {
      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await db.any(
        `SELECT id , code , rank from ${branche}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${branche}_metadata_model where name = $1) `,
        model,
      );

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code === "nakala") nakala = startQ[i].id;
      }
      try {
        let debut =
          `SELECT i.item_type, t.${name} as short_name, ff.folder_name, ` +
          `COALESCE(array_to_string(pnakala.value,''), '') AS nakala, ` +
          `COALESCE(array_to_string(pnom.value,''), '') AS depot_name, ` +
          `COALESCE(ffo.id, fi.id, o.id, u.id, 0) AS id, ` +
          `COALESCE(ffo.path, fo.path, fi.path, fu.path, '') AS path, ` +
          `COALESCE(ffo.name, fo.name, fi.name, u.name, '') AS filename, ` +
          `COALESCE(ff.id, fo.id_folder, fi.id_folder, fu.id_folder, 0) AS idfolder, ` +
          `COALESCE(ff.id, o.id_folder, fi.id_folder, fu.id_folder, 0) AS idfolder_item, ` + // pour les objets sans images repr il faut quand même récupérer son folder ?
          `COALESCE(ffo.id, fo.id, fi.id, fu.id, 0) AS idfile, ` +
          `COALESCE(fi.file_ext, ffo.file_ext, fu.file_ext, '') AS extension, ` +
          `COALESCE(u.x, 0) AS x, ` +
          `COALESCE(u.y, 0) AS y, ` +
          `COALESCE(u.width, 0) AS width, ` +
          `COALESCE(u.height, 0) AS height, ` +
          `o.name as object_name, ff.nb_objects ` +
          `FROM ${branche}_thesaurus_multi t INNER JOIN ${branche}_thesaurus_multi_item i ` +
          `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus ` +
          ` INNER JOIN ${branche}_folder_thesaurus_multi fm ON fm.thesaurus = t.thesaurus ` +
          //'AND i.thes_path = t.thesaurus_path ' +
          `LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
          `LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
          `LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture ` +
          `LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
          `LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative ` +
          `LEFT OUTER JOIN ${branche}_unico u ON u.id = i.id_item AND i.item_type = 'unico' ` +
          `LEFT OUTER JOIN ${branche}_file fu ON fu.id = u.id_file ` +
          `LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 ` +
          `LEFT OUTER JOIN ${branche}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 `;

        // On ne recupère que l'id thes pour un thesaurus particulier
        // TODO polyhierarchie
        //let particulier = ' WHERE t.thesaurus_path = $3 '
        let particulier = " WHERE t.id_thes = $3 ";
        // On récupère tous les id_thes enfants de l'id_thes demandé pour tous les thesaurus (pas pris en compte pour le moment)
        //let general = ' WHERE t.thesaurus_path <@ ( SELECT get_thesaurus_multi_general_enfants_path(\''+branche+'\', $3 ) )::ltree[]  '
        let general = "WHERE t.id_thes = $3";

        let suite = " AND t.thesaurus = $4 ";
        let milieu = in_query;

        let fin = ` ORDER BY ff.folder_name OFFSET ${offset} LIMIT ${limit}`;

        let query = "",
          thesitem = {};

        if (req.params.thes === "_") {
          // general
          // polyhiérarchie : on prend tous les path d'un id_thes
          //query = debut + general + milieu + fin // A ce jour, même pour un concept général, on ne va chercher que le terme et non tous ses enfants
          // on ne récupère que les thesaurus concernés par le projet ?
          query = debut + general + milieu + fin;
          //console.log(query)
          thesitem = await db.any(query, [nakala, codeNom, id]);
        } else {
          // un thesaurus en particulier
          query = debut + particulier + suite + milieu + fin;
          //thesitem = await db.any(query, [nakala, codeNom, thes_path, req.params.thes])
          thesitem = await db.any(query, [nakala, codeNom, id, req.params.thes]);
        }

        // success
        res.status(200).sendData(thesitem);
      } catch (e) {
        responseHelper.sendError(500, "server_error in getThesaurusItemPage", e, req, res);
      }
    } catch (e) {
      // error
      responseHelper.sendError(500, "server_error in getThesaurusItemPage for nakala", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusItemPage for userRead", e, req, res);
  }
};

//create url endpoint for /explorethesPactolsPage/:branche,:thes,:idThes POST
// on le met en POST pour récupérer les infos offset et limit dans le body ainsi que le id_user pour récupérer seulement
// les item des folders autorisés
// TODO : traiter le cas des items de types object ou folder ...
exports.exploreThesaurusPactolsItemPage = async function (req, res) {
  let id = req.params.idThes,
    { branche } = req.params,
    model = "project",
    nakala = 0,
    codeNom = 0;
  let { body } = req;
  // DONE : Mettre le id_user dans le body et envoyer la requete seulement sur les id_item autorisées par le id_user :
  let userId = parseInt(body.userId);

  // Check if user is admin to bypass folder restrictions
  let isAdmin = false;
  if (userId) {
    try {
      const userStatus = await db.oneOrNone(
        `SELECT user_status FROM archeogrid_user WHERE id = $1`,
        userId
      );
      isAdmin = userStatus?.user_status === 'admin';
    } catch (e) {
      // If we can't check user status, continue with regular permission logic
      isAdmin = false;
    }
  }

  // gestion de la polyhierarchie
  let { thes_path } = req.body;
  let read = "",
    in_query = "";
  if (userId) {
    read = `SELECT get_access_gen( $1, '${branche}') as fold `;
  } else {
    read = `SELECT array_agg(id)  as fold FROM ${branche}_folder WHERE  status = 'public'  AND $1 = $1 `;
  }

  // Première requete : récupèrer la liste des folders autorisés par le user :
  // TODO : ils doivent être dans le projet !!!
  // récupérer l'id projet avec la table pft3d_folder_thesaurus_multi ??? ou le passer en parametre dans le body
  try {
    const userRead = await db.oneOrNone(read, userId);

    // For admin users, skip folder restrictions entirely
    if (isAdmin) {
      in_query = "";
    } else if (userRead.fold.length === 0) {
      in_query = "";
    } else {
      in_query =
        ` AND i.id_item IN ` +
        `( SELECT id FROM ${branche}_file WHERE id_folder in ${JSON.stringify(userRead.fold)
          .replace("[", "(")
          .replace("]", ")")} ` +
        `UNION ALL ` +
        `SELECT id FROM ${branche}_object WHERE id_folder in ${JSON.stringify(userRead.fold)
          .replace("[", "(")
          .replace("]", ")")} ` +
        ` UNION ALL ` +
        ` SELECT id FROM ${branche}_unico WHERE id_file in (SELECT id FROM ${branche}_file WHERE id_folder in ${JSON.stringify(
          userRead.fold,
        )
          .replace("[", "(")
          .replace("]", ")")}) ` +
        ` )`;
    }

    try {
      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await db.any(
        `SELECT id , code , rank from ${branche}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${branche}_metadata_model where name = $1) `,
        model,
      );

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code === "nakala") nakala = startQ[i].id;
      }
      try {
        let debut =
          `SELECT i.item_type, t.name, ff.folder_name, ` +
          `COALESCE(array_to_string(pnakala.value,''), '') AS nakala, ` +
          `COALESCE(array_to_string(pnom.value,''), '') AS depot_name, ` +
          `COALESCE(ffo.id, fi.id, o.id, u.id, 0) AS id, ` +
          `COALESCE(ffo.path, fo.path, fi.path, fu.path, '') AS path, ` +
          `COALESCE(ffo.name, fo.name, fi.name, u.name, '') AS filename, ` +
          `COALESCE(ff.id, fo.id_folder, fi.id_folder, fu.id_folder, o.id_folder, 0) AS idfolder, ` +
          `COALESCE(ffo.id, fo.id, fi.id, fu.id, 0) AS idfile, ` +
          `COALESCE(fi.file_ext, ffo.file_ext, fu.file_ext, '') AS extension, ` +
          `o.name as object_name, ff.nb_objects, ` +
          `u.x, u.y, u.width, u.height ` +
          //'FROM ' + branche + '_thesaurus_pactols_poly_display t INNER JOIN ' + branche + '_thesaurus_pactols_item i ' +
          `FROM ${branche}_thesaurus_pactols t INNER JOIN ${branche}_thesaurus_pactols_item i ` +
          `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus ` +
          //'AND i.thes_path = t.thesaurus_path ' +
          `LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
          `LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
          `LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture ` +
          `LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
          `LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative ` +
          `LEFT OUTER JOIN ${branche}_unico u ON u.id = i.id_item AND i.item_type = 'unico' ` +
          `LEFT OUTER JOIN ${branche}_file fu ON fu.id = u.id_file ` +
          `LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 ` +
          `LEFT OUTER JOIN ${branche}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 `;

        // On ne recupère que l'id thes pour un thesaurus particulier
        // For PACTOLS, we should use id_thes_thesaurus to match the count query logic
        let particulier = " WHERE t.id_thes = $3 ";
        // On récupère tous les id_thes enfants de l'id_thes demandé pour tous les thesaurus (pas pris en compte pour le moment)
        //let general = ' WHERE t.thesaurus_path <@ ( SELECT get_thesaurus_multi_general_enfants_path(\''+branche+'\', $3 ) )::ltree[]  '
        let general = "WHERE t.id_thes = $3";

        let suite = " AND t.thesaurus = $4 ";
        let milieu = in_query;

        let fin = ` ORDER BY ff.folder_name`;

        let query = "",
          thesitem = {};

        if (req.params.thes === "_") {
          // general
          // polyhiérarchie : on prend tous les path d'un id_thes
          //query = debut + general + milieu + fin // A ce jour, même pour un concept général, on ne va chercher que le terme et non tous ses enfants
          query = debut + general + milieu + fin;
          thesitem = await db.any(query, [nakala, codeNom, id]);
        } else {
          // un thesaurus en particulier - use id_thes instead of thes_path for PACTOLS
          query = debut + particulier + suite + milieu + fin;
          thesitem = await db.any(query, [nakala, codeNom, id, req.params.thes]);
        }

        // success
        res.status(200).sendData(thesitem);
      } catch (e) {
        responseHelper.sendError(500, "server_error in exploreThesaurusPactolsItemPage ", e, req, res);
      }
    } catch (e) {
      // error
      responseHelper.sendError(500, "server_error in exploreThesaurusPactolsItemPage for nakala", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in exploreThesaurusPactolsItemPage for userRead", e, req, res);
  }
};

// create url endpoint for /exploreConceptThes/:root,:name,:lang,:thestable
exports.getThesaurusItemFromName = async function (req, res) {
  let thes_table = req.params.thestable;

  let name = decodeURI(req.params.name);
  // pour les item depot, on rend le nom en francais ou en anglais
  // on sort une liste simple, sans image representative pour l'instant
  // TODO : ajouter un lien image representative pour le depot (easy, ajouter une colonne et reprendre celle des folder !)
  let { root } = req.params;
  let lang = req.params.lang === 'fr' ? 'fr' : 'en';

  // thestable :  pour le CND3D , on prends _pactols
  // pour les projets, on prend la table _multi
  // pour ... on peut prendre la table thesaurus (tout court)
  if (thes_table === "_") thes_table = "";
  else thes_table = `_${thes_table}`;

  let query_debut =
    `SELECT t.name as concept, item_type, ` +
    `CASE WHEN (ff.id IS NOT NULL)  THEN ff.id ` +
    `WHEN (fi.id IS NOT NULL)  THEN fi.id ` +
    `WHEN (o.id IS NOT NULL) THEN o.id ` +
    `WHEN (d.id IS NOT NULL) THEN d.id ` +
    `ELSE 0  END as id,` +
    `CASE WHEN (ff.id IS NOT NULL)  THEN ff.doi ` +
    `WHEN (fi.id IS NOT NULL)  THEN fi.doi ` +
    `WHEN (o.id IS NOT NULL) THEN o.doi ` +
    `WHEN (d.id IS NOT NULL) THEN '' ` +
    `ELSE ''  END as doi,` +
    `CASE WHEN (ff.name IS NOT NULL) THEN ff.name ` +
    `WHEN (fi.name IS NOT NULL) THEN fi.name ` +
    `WHEN (o.name IS NOT NULL) THEN o.name ` +
    `WHEN (d.${lang}_name IS NOT NULL) THEN d.${lang}_name ` +
    `ELSE '' END as name,` +
    `CASE WHEN (o.id_folder IS NOT NULL) THEN o.id_folder ` +
    `WHEN (ffi.id IS NOT NULL) THEN ffi.id ` +
    `ELSE 0 END as ref_object_folder, t.identifier ` +
    `, CASE WHEN (o.id_folder IS NOT NULL) THEN ffoo.name ` +
    ` WHEN (ffi.id IS NOT NULL) THEN ffi.name ` +
    ` ELSE '' END as ref_object_folder_name ` +
    `, CASE WHEN (fo.id IS NOT NULL) THEN fo.id ` +
    ` ELSE 0 END as ref_object_file_repre_id ` +
    `FROM ${root}_thesaurus${thes_table} t `;

  let query_milieu = "";
  if (thes_table.indexOf("pactols") !== -1) {
    query_milieu = `INNER JOIN ${root}_thesaurus${thes_table}_item i ` + `ON  i.id_thesaurus = t.id `;
  } else {
    query_milieu =
      `INNER JOIN ${root}_thesaurus${thes_table}_item i ` +
      `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus `;
  }
  let query_fin =
    `LEFT OUTER JOIN ${root}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
    `LEFT OUTER JOIN ${root}_depot d ON d.id = i.id_item AND i.item_type = 'depot' ` +
    `LEFT OUTER JOIN ${root}_file ffo ON ffo.id = ff.id_representative_picture ` +
    `LEFT OUTER JOIN ${root}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
    `LEFT OUTER JOIN ${root}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
    `LEFT OUTER JOIN ${root}_folder ffoo ON ffoo.id = o.id_folder ` +
    `LEFT OUTER JOIN ${root}_file fo ON fo.id = o.id_file_representative ` +
    `LEFT OUTER JOIN ${root}_folder ffi ON ffi.id = fi.id_folder ` +
    `WHERE t.name = $1 `;
  let query = query_debut + query_milieu + query_fin;
  try {
    const item = await db.any(query, name);
    res.status(200).sendData(item);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusItemfromName", e, req, res);
  }
};

// create url endpoint for /exploreConceptThesTotal/:root,:idThes,:thestable GET
// Récupérer le nombre total d'items tagués avec ce concept, quelque soit le thesaurus dans lequel il est utilisé
exports.getThesaurusNBItemFromIdthes = async function (req, res) {
  let thes_table = req.params.thestable;
  let { idThes } = req.params;
  let { root } = req.params;
  // thestable :  pour le CND3D , on prends _pactols
  // pour les projets, on prend la table _multi
  // pour ... on peut prendre la table thesaurus (tout court)
  if (thes_table === "_") thes_table = "";
  //console.log(idThes)
  // Attention : si plusieurs nom pour un concept : il ne faut pas faire sortir le name
  try {
    const nbitem = await db.oneOrNone(
      `SELECT sum(nb_tot_item) as nb_tot_item, sum(nb_item) as nb_item,` +
        //' identifier , name  ' +
        ` identifier ` +
        `FROM ${root}_thesaurus_${thes_table} t ` +
        //'WHERE t.id_thes = $1 group by id_thes, identifier, name ', idThes)
        `WHERE t.id_thes = $1 group by id_thes, identifier `,
      idThes,
    );
    res.status(200).sendData(nbitem);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusNBItemFromIdthes", e, req, res);
  }
};

// create url endpoint for /ThesaurusPactolsConcept/:root,:conceptId
exports.getThesaurusPactolsConcept = async function (req, res) {
  console.log(req.params.conceptId);
  let query = `SELECT nb_item FROM ${req.params.root}_thesaurus_pactols WHERE id_thes = $1 `;
  console.log(query);
  try {
    const nbConcept = db.any(query, req.params.conceptId);
    //'SELECT count(*) FROM '+req.params.root+'_thesaurus_pactols_item ' +
    //'SELECT nb_item FROM '+req.params.root+'_thesaurus_pactols ' +
    //'WHERE id_thes_thesausus = $1 ', req.params.conceptId)
    //'WHERE id_thes = $1 ', req.params.conceptId)
    res.status(200).sendData(nbConcept);
  } catch (e) {
    console.log("aie");
    responseHelper.sendError(500, "server_eror in getThesaurusConcept", e, req, res);
  }
};

// create url endpoint for /ThesaurusMultiConcept/:root,:thesaurus,:conceptId
// Récupérer les info de thesaurus après une récente insertion (geo CND3D)
// pour pouvoir remettre les indexation qui avaient été supprimer temporairement
// le concept remis dans le thesaurus n'a plus le meme id
exports.getThesaurusMultiConcept = async function (req, res) {
  let query = `SELECT * FROM ${req.params.root}_thesaurus_multi WHERE thesaurus =  $1 AND id_thes = $2`;

  console.log(query);
  try {
    const infoConcept = await db.oneOrNone(query, [req.params.thesaurus, req.params.conceptId]);
    console.log(infoConcept);
    res.status(200).sendData(infoConcept);
  } catch (e) {
    console.log("e");
    responseHelper.sendError(500, "server_error in getThesaurusMultiConcept", e, req, res);
  }
};

// create url endpoint for /thesaurusPerdiodO/:root
exports.getThesaurusPeriodO = async function (req, res) {
  let { root } = req.params;

  try {
    const periodo = await db.any(
      `SELECT language, id, id_periodo, label, localized_labels, start_date, stop_date, spatial_coverage_desc  ` +
        //' name as value, concat(id, \'_\', id_thes) as id, name as label  ' +
        `FROM ${root}_thesaurus_periodo  ` +
        `order by language, start_date::int `,
    );
    res.status(200).sendData(periodo);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus periodo ", e, req, res);
  }
};

// get all thesaurus Periodo item indexe for Periodo thesaurus
// Create url endpoint for /ThesaurusPeriodOItem/:root,:type,:itemId GET
exports.getThesaurusPeriodOItem = async function (req, res) {
  // Donner tous les mots-clés du thesaurus PeriodO

  let { root } = req.params;
  let { type } = req.params;

  try {
    const pactagList = await db.any(
      `SELECT id, tp.id_periodo, label FROM ${root}_thesaurus_periodo tp ` +
        `INNER JOIN ${root}_thesaurus_periodo_item it ON it.id_periodo = tp.id AND it.id_thes_periodo = tp.id_periodo ` +
        `WHERE  id_item = $1 AND item_type='${type}'`,
      req.params.itemId,
    );
    // success
    res.status(200).sendData(pactagList);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusPeriodoItem", e, req, res);
  }
};

//Create url endpoint for /:branch,:lang
exports.getThesaurusTreePeriodo = async function (req, res) {
  let language = "";
  let { branch } = req.params;

  if (req.params.lang === "fr") {
    language = "fra-latn";
  } else {
    language = "eng-latn";
  }
/*
label = (langue === 'fr' ? data[i].label :
    (data[i].localized_labels ? (
      data[i].localized_labels['eng-latn'] ? data[i].localized_labels['eng-latn'][0] : data[i].label) : data[i].label))
 */

  try {
    const thesaurusperiodo = await db.any(
      `SELECT id, label, nb_item , id as path,
        localized_labels , language ` +
        ` FROM ${branch}_thesaurus_periodo ` +
        `WHERE nb_item != 0 `+
        //AND  language = $1  ORDER BY language, start_date::int `,language
        ` ORDER BY start_date::int `
        ,
    );
    // success
    res.status(200).sendData(thesaurusperiodo);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error", e, req, res);
  }
};

// create url endpoint for /allThesaurusName/:branch,:lang
exports.getAllThesaurusName = async function (req, res) {
  let lang = req.params.lang === 'fr' ? 'fr' : 'en' ;
  let { branch } = req.params;

  try {
    const name = await db.any(
      `SELECT status, list, l.label , l.description, code ` +
        `FROM ${branch}_metadata_label l INNER JOIN ${branch}_metadata m ON m.id = l.id_metadata ` +
        `WHERE m.status  in ('thesaurus', 'multi', 'nomenclature') AND l.language = $1 `,
      lang,
    );
    res.status(200).sendData(name);
  } catch (e) {
    responseHelper.sendError(500, "server error in get all Thesaurus Name");
  }
};

// create url endpoint for /allThesaurusNameProject/:branch,:idFolder,:lang
exports.getAllThesaurusNameForProject = async function (req, res) {
  let lang = req.params.lang === 'fr' ? 'fr' : 'en';
  let { branch } = req.params;
  let { idFolder } = req.params;
  // console.log('idFolder :', idFolder)

  let queryThes =
    `SELECT status, list, l.label , l.description, code ` +
    `FROM ${branch}_metadata_label l ` +
    `INNER JOIN ${branch}_metadata m ON m.id = l.id_metadata ` +
    `INNER JOIN ${branch}_metadata_model_folder mf ON mf.id_model = m.id_metadata_model ` +
    `WHERE mf.id_folder = $1 AND m.status  in ('thesaurus', 'multi', 'nomenclature') AND l.language = $2 `;

  try {
    const name = await db.any(queryThes, [idFolder, lang]);
    res.status(200).sendData(name);
  } catch (e) {
    responseHelper.sendError(500, "server error in get all Thesaurus Name For Project :", e, req, res);
  }
};

// Create url endpoint for /orderThesaurus/:branche,:thesaurus  with order GET
exports.getOrderThesaurus = async function (req, res) {
  let { branche } = req.params;
  let { thesaurus } = req.params;

  try {
    const rootthes = await db.any(
      `SELECT id, ${branche}_get_real_thes_path(id_thes, '${thesaurus}') as name, short_name, rank, global_rank FROM ${branche}_thesaurus WHERE thesaurus = $1 ` +
        `ORDER BY global_rank `,
      req.params.thesaurus,
    );
    // succes
    res.status(200).sendData(rootthes);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in GET order Thesaurus", e, req, res);
  }
};

// Create url endpoint for /orderThesaurus/:branche,:thesaurus  with order PATCH
exports.patchThesOrder = async function (req, res) {
  const root = req.params.branche;
  const { thesaurus } = req.params;
  //let orderTab = req.body['0']
  let erreur = 0;
  let lerreur_last = "";

  for (let tab in req.body) {
    let indice = parseInt(tab) + 1;
    let new_rank = req.body[tab].split("_");
    let fId = new_rank[1];

    try {
      const rootthes = await db.none(`UPDATE ${root}_thesaurus SET rank = $1 WHERE id = $2`, [indice, fId]);
    } catch (e) {
      // error
      erreur = 1;
      lerreur_last = e;
    }
  }

  if (erreur) {
    responseHelper.sendError(500, "server_error in reorder thesaurus", erreur, req, res);
  } else {
    try {
      const update_global_rank_to_null = await db.none(
        `UPDATE ${root}_thesaurus SET global_rank = NULL WHERE thesaurus = $1`,
        thesaurus,
      );
      try {
        const update_global_rank = await db.any(`SELECT update_${root}_thesaurus_global_rank('${thesaurus}')`);
        res.status(200).sendData([]);
      } catch (e) {
        erreur = 1;
        console.log(e);
        responseHelper.sendError(500, "server_error in update_global_rank in reorder thesaurus", e, req, res);
      }
    } catch (e) {
      lerreur_last = e;
      responseHelper.sendError(500, "server_error in set global_rank to null in reorder thesaurus", e, req, res);
    }
  }
};

// create url endpoint /thesaurusMultiName/:root,:thesaurus,:name GET
// charger le thesaurus sur les 3 premières lettres tapées pour l'autocomplete
// former un tableau avec label et value pour l'autocomplete en rajoutant l'id pour pouvoir indexer le mot
exports.getThesaurusMultiName = async function (req, res) {
  let { name } = req.params;
  let { root } = req.params;
  let lng_name = req.query.lng === 'fr' ? 'name' : 'name_'+req.query.lng
  //pour pouvoir mettre des ' dans les noms des collections
  let strongName = name.replace(/singlequote/g, "''");

  let lng = req.query.lng === 'fr' ? '' : req.query.lng

  let query = ''
  if (root === 'conservatoire3d') {
    query = `SELECT m.${lng_name} as name, ${root}_get_real_thes_path_multi_unique_id${lng}(m.id_thes, $1) as value, ` +
        `concat(id, '_', m.id_thes) as id, `+
         ` ${root}_get_real_thes_path_multi_unique_id${lng}(m.id_thes, $1) as label  ` +
        `, thesaurus_path as thes_path , m.thesaurus ` +
        //'name as label  ' +
        `FROM ${root}_thesaurus_multi m INNER JOIN ${root}_search_thesaurus st ON st.thesaurus = m.thesaurus  AND st.type = 'multi'  ` +
        `WHERE m.thesaurus = $1 AND m.${lng_name} ILIKE '%${strongName}%' ` +
        `ORDER BY label `
  } else {
    query = `SELECT m.${lng_name} as name, ${root}_get_real_thes_path_multi_unique_id${lng}(m.id_thes, $1) as value, ` +
        `concat(id, '_', m.id_thes) as id, `+
         ` ${root}_get_real_thes_path_multi_unique_id${lng}(m.id_thes, $1) as label  ` +
        `, thesaurus_path as thes_path , m.thesaurus ` +
        //'name as label  ' +
        `FROM ${root}_thesaurus_multi m ` +
        // INNER JOIN ${root}_folder_thesaurus_multi fm ON fm.thesaurus = m.thesaurus ` + // on assume d'être dans le bon projet
        `WHERE m.thesaurus = $1 AND m.${lng_name} ILIKE '%${strongName}%' ` +
        `ORDER BY label `
  }

  try {
    const theso = await db.any(query, req.params.thesaurus,    );
    res.status(200).sendData(theso);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi name ", e, req, res);
  }
};

// create url endpoint /thesaurusMultiNameUser/:root,:thesaurus,:name,:idUser GET
// charger le thesaurus sur les 3 premières lettres tapées pour l'autocomplete
// former un tableau avec label et value pour l'autocomplete en rajoutant l'id pour pouvoir indexer le mot
// SEULEMENT POUR LES CONCEPTS de THESAURUS AUQUEL LE USER A DROIT (on croise avec les COLLeCTION)
exports.getThesaurusMultiNameUser = async function (req, res) {
  let { name } = req.params;
  let { root } = req.params;
  let status = req.query.userStatus;

  let lng_name = req.query.lng === 'fr' ? 'name' : 'name_'+req.query.lng

  //pour pouvoir mettre des ' dans les noms des collections
  let strongName = name.replace(/singlequote/g, "''");

  // si le user est simple user , on va rechercher ses collections
  let queryUser =
    `SELECT m.${lng_name} as name, ${root}_get_real_thes_path_multi_unique_id(m.id_thes, 'collection') as value, ` +
    `concat(m.id, '_', m.id_thes) as id, ${root}_get_real_thes_path_multi_unique_id(m.id_thes, 'collection') as label  ` +
    //'name as label  ' +
    `FROM ${root}_thesaurus_multi m ` +
    `INNER JOIN ${root}_collection c ON c.id_thes = m.id_thes AND m.thesaurus = 'collection' ` +
    `INNER JOIN ${root}_user_collection uc ON uc.id_collection  = c.id ` +
    `WHERE m.thesaurus = 'collection' AND m.${lng_name} ILIKE '%${strongName}%' AND uc.id_user = $1 ` +
    `ORDER BY label `;

  // si le user est admin, on lui propose toutes les collections
  let queryAdmin =
    `SELECT m.${lng_name} as name, ${root}_get_real_thes_path_multi_unique_id(m.id_thes, 'collection') as value, ` +
    `concat(m.id, '_', m.id_thes) as id, ${root}_get_real_thes_path_multi_unique_id(m.id_thes, 'collection') as label  ` +
    //'name as label  ' +
    `FROM ${root}_thesaurus_multi m ` +
    `WHERE m.thesaurus = 'collection' AND m.${lng_name} ILIKE '%${strongName}%' AND $1 = $1 ` +
    `ORDER BY label `;

  let query = "";
  if (status === "admin") query = queryAdmin;
  else query = queryUser;

  try {
    const theso = await db.any(query, [req.params.idUser]);
    res.status(200).sendData(theso);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi name User Collection", e, req, res);
  }
};

// create url endpoint /thesaurusMultiNameGeneral/:root,:name GET
// charger les concepts de n'importe quel thesaurus/collection de la table thesaurus multi
// qui regroupe toutes les collections/tous les thesaurus
// sur les 3 premières lettres tapées pour l'autocomplete
// former un tableau avec label et value pour l'autocomplete en rajoutant l'id
// pour pouvoir visualiser les ressources attaché à ce concept
exports.getThesaurusMultiNameGeneral = async function (req, res) {
  let { name } = req.params;
  let { root } = req.params;
  let projectId = req.query.projectId;
  let lng_name = req.query.lng === 'fr' ? 'name' : 'name_'+req.query.lng
  console.log(lng_name)
  let lng_param = req.query.lng === 'fr' ? '' : req.query.lng

  // on renvoit le thesaurus pour pouvoir chercher seulement dans ce thesaurus pour renvoyer les items indexés
  try {
    const thesogeneral = await db.any(
      `SELECT distinct t.${lng_name} as name, t.thesaurus, ` +
        `t.${lng_name} as value, ` + // ce qui se met dans le form input
        ` t.id_thes as id, ` +
          //'pft3d_get_global_thes_multi(t.id) as label   ' + // Ce qui apparait dans la liste déroulante
        `  ${root}_get_real_thes_path_multi_unique_id${lng_param}(t.id_thes, m.thesaurus) as label   ` + // Ce qui apparait dans la liste déroulante
        //`t.${lng_name} as label  ` +
        `FROM       ${root}_thesaurus_multi t ` +
        `INNER JOIN ${root}_thesaurus_multi m ON m.thesaurus= t.thesaurus ` +
        `INNER JOIN ${root}_folder_thesaurus_multi fm ON fm.thesaurus= t.thesaurus AND fm.id_folder= ${projectId} ` +
        `WHERE nlevel(m.thesaurus_path) = 1 ` +
        `AND lower(t.${lng_name}) LIKE lower('%${decodeURI(name)}%') ` +
        `ORDER BY label `,
    );
    res.status(200).sendData(thesogeneral);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi  name general ", e, req, res);
  }
};

// create url endpoint /thesaurusMulti/:root,:thesaurus PUT
// faire indexation du thesaurus multi (depuis l'indexation avec autocomplete )
exports.addThesaurusMulti = async function (req, res) {
  let { thesaurus } = req.params;
  // TODO : incrémenter le nb_item (automatiquement avec un trigger de la table thesaurus_pactols_item

  // on récupère le id_thesaurus et le id_thes séparé d'un _ (voir la fonction qui récupère les info)
  let id_thesaurus = req.body.id.split("_")[0];
  let id_thes_thesaurus = req.body.id.split("_")[1];
  //console.log(typeof id_thes_thesaurus)
  let int_id_thes_thesaurus = parseInt(req.body.id.split("_")[1]);
  //console.log(typeof int_id_thes_thesaurus)
  let resu = {};

  db.any(
    `SELECT count(*)  FROM ${req.params.root}_thesaurus_multi_item ` +
      `WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 AND id_thesaurus = $4 `,
    [thesaurus, req.body.item, req.body.type, id_thesaurus],
  )
    .then((count) => {
      if (count[0].count === "0") {
        // on ajoute le qualifier
        if (req.body.qualifier !== "") {
          // on fait un insert avec un qualifier
          if (
            insertThesMultiItemWithQual(
              req.params.root,
              id_thesaurus,
              id_thes_thesaurus,
              thesaurus,
              req.body.item,
              req.body.type,
              req.body.id_user,
              req.body.qualifier,
            )
          ) {
            console.log("insert thes multi item with qualifier ok");
            resu = ["1"];
          } else {
            // error
            console.log("error in insert thes multi item with qualifier");
          }
        } else {
          // on fait un insert simple sans qualifier
          //console.log('insert ?') // rien => on fait un insert
          if (
            insertThesMultiItem(
              req.params.root,
              id_thesaurus,
              id_thes_thesaurus,
              thesaurus,
              req.body.item,
              req.body.type,
              req.body.id_user,
            )
          ) {
            console.log("insert thes multi item ok");
            resu = ["1"];
          } else {
            // error
            console.log("error in insert thes multi item");
          }
        }
      } else {
        console.log(" already in thes multi, nothing to do");
        resu = ["2"];
      }
      res.status(201).sendData(resu);
    })
    .catch((e) => {
      console.log("ERROR select thesaurus multi item metadata value :", e);
      responseHelper.sendError(500, "server_error", e, req, res);
    });
};

// create URL endpoint / PATCH
// Mettre à jour la qualifier de la liaison thesaurus multi (exemple deposant : qualifier deposant_principal,
// exemple geo : qualifier lieu_decouverte, lieu_conservation)
exports.updateThesaurusMulti = async function (req, res) {
  let { thesaurus } = req.params;
  let { root } = req.params;
  console.log(req.query);
  console.log(req.body);

  let { idThes } = req.body;
  let { idItem } = req.body;
  let { itemType } = req.body;
  let { qualifier } = req.body;
  try {
    const rankNull = await db.any(
      `UPDATE ${root}_thesaurus_multi_item SET qualifier = $1 ` +
        `WHERE thesaurus = $2 AND id_thes_thesaurus = $3 AND id_item = $4 AND item_type = $5 `,
      [qualifier, thesaurus, idThes, idItem, itemType],
    );
    res.status(200).sendData(rankNull);
  } catch (e) {
    responseHelper.sendError(500, "server_error in PATCH thesaurus multi item with qualifier ", e, req, res);
  }
};

// create url endpoint /thesaurusMultiUpdate/:root,:thesaurus GET
exports.getThesaurusMultiToUpdate = async function (req, res) {
  try {
    const rankNull = await db.any(
      `SELECT id_thes, name, thesaurus_path ` +
        `FROM ${req.params.root}_thesaurus_multi WHERE thesaurus = $1 ` +
        ` AND rank IS NULL `,
      req.params.thesaurus,
    );
    res.status(200).sendData(rankNull);
  } catch (e) {
    responseHelper.sendError(500, "server_error in  GET thesaurus multi to update", e, req, res);
  }
};

// create url endpoint for /explorethesNB/:branche,:thes,:idThes
// Pour la pagination , on récupère l'ensemble des folders concernés par l'item du thesaurus
// Pour le moment, seulement les items de type file sont traités
// en cours : traiter le cas où les items type taggué sont des objects ou des unicos
exports.exploreThesaurusItemNB = async function (req, res) {
  let { branche } = req.params,
    path = req.query.thes_path,
      lng = req.query.lng;

  if (!lng) {
    lng = 'fr'
  }
  const idThes = req.params.idThes;
  // TODO : récupérer le projectId et limiter les folder à ceux du projet
  let { projectId } = req.query;
  //{ thes_path: '28123.1000.1413.1417.1418.33027' }
  try {
    //1 on récupère tous les folders concernés et le nombre d'item
    // TODO : completer la requete pour les items de type folder ou objets
    let query =
      `select json_agg(x) as folders_info FROM ( ` +
      `SELECT id_folder, count(*) as nb_file, 0 as nb_object, 0 as nb_unico FROM ${branche}_file WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_item WHERE thesaurus = $1 AND item_type = 'file' ` +
      ` AND id_thes_thesaurus = $2) GROUP BY id_folder ` +
      `UNION ALL ` +
      `SELECT id_folder, 0 as nb_file, count(*) as nb_object, 0 as nb_unico FROM ${branche}_object WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_item WHERE thesaurus = $1 AND item_type = 'object' ` +
      ` AND id_thes_thesaurus = $2 ) ` +
      ` GROUP BY id_folder  ` +
      `UNION ALL  ` +
      `SELECT id_folder, 0 as nb_file, 0 as nb_object, count(*) as nb_unico FROM ${branche}_unico u ` +
      `INNER JOIN ${branche}_file f ON f.id = u.id_file WHERE u.id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_item WHERE thesaurus = $1 AND item_type = 'unico' ` +
      ` AND id_thes_thesaurus = $2 ) ` +
      ` GROUP BY id_folder  ` +
      `) AS x`;
    //const nbthesitemfolders = await db.oneOrNone(query, [req.params.thes, path])
    const nbthesitemfolders = await db.oneOrNone(query, [req.params.thes, idThes]);

    try {
      // 2 / on récupère le nom complet du concept en jeu
      // REVISION de la requete pour traiter la polyhierarchie : on interroge non pas sur le id_thes mais avec l'id
      const thesname = await db.oneOrNone(
        `SELECT nb_item, ${branche}_get_real_thes_path_path_id_${lng}(thesaurus_path::TEXT, $1) as thesname  ` +
          `FROM ${branche}_thesaurus WHERE thesaurus =  $1 AND id_thes = $2 `,
        [req.params.thes, idThes],
      );

      // 3/ on assemble les 2
      let result = Object.assign({}, thesname, nbthesitemfolders);
      // success
      res.status(200).sendData(result);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusItemNB simple thesname", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusItemNB simple folders", e, req, res);
  }
};

// create url endpoint for /exploreThesaurusNB/:branche,:type,:thesaurus,:id_thes POST
// get number items of a specific thesaurus
exports.exploreThesaurusNB = async function (req, res) {
  const { branch } = req.params;
  const { type } = req.params;
  const { thesaurus } = req.params;
  const { id_thes } = req.params;

  try {
    let query = "";
    if (type === "multi") {
      query = `SELECT count(i.id_item), t.short_name AS name
            FROM ${branch}_thesaurus_multi t
            INNER JOIN ${branch}_thesaurus_multi_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus AND i.thes_path = t.thesaurus_path
            WHERE t.id_thes = ${id_thes} AND t.thesaurus = '${thesaurus}'
            GROUP BY t.short_name`;
    } else if (type === "pactols") {
      // pour les pactols, il n'y a qu'un seul thesaurus global pour FRANTIQ, pas besoin de thesaurus
      query = `SELECT count(i.id_item), t.name AS name
            FROM ${branch}_thesaurus_pactols t
            INNER JOIN ${branch}_thesaurus_pactols_item i ON i.id_thes_thesaurus = t.id_thes
            WHERE t.id_thes = ${id_thes}
            GROUP BY t.name`;
    } else if ((type === "simple" && thesaurus === "periodo") ||(type === "periodo" && thesaurus === "periodo")) {
      query = `SELECT count(i.id_item), t.label AS name
                     FROM ${branch}_thesaurus_periodo t
                              INNER JOIN ${branch}_thesaurus_periodo_item i ON i.id_thes_periodo = t.id_periodo
                     WHERE t.id = ${id_thes}
                       AND t.language = 'fra-latn'
                     GROUP BY t.label`;
    } else {
      //simple
      query = `SELECT count(i.id_item), t.short_name AS name
            FROM ${branch}_thesaurus t
            INNER JOIN ${branch}_thesaurus_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus AND i.thes_path = t.thesaurus_path
            WHERE t.id_thes = ${id_thes} AND t.thesaurus = '${thesaurus}'
            GROUP BY t.short_name`;
    }

    let count = await db.oneOrNone(query);
    if (count === null) {
      count = {
        count: 0,
        name: "",
      };
    }
    res.status(200).sendData(count);
  } catch (e) {
    responseHelper.sendError(500, "server_error in exploreThesaurusNB", e, req, res);
  }
};

//create url endpoint for /explorethesPage/:branche,:thes,:idThes POST
// on le met en POST pour récupérer les infos offset et limit dans le body ainsi que le id_user pour récupérer seulement
// les item des folders autorisés
// TODO : traiter le cas des items de types object ou folder ...
exports.exploreThesaurusItemPage = async function (req, res) {
  let id = req.params.idThes,
    { branche } = req.params,
    nakala = 0,
    nakalaObject = 0,
    codeNom = 0;
  let model = branche === "pft3d" ? "project" : "deposit";
  let { body } = req;
  let offset = (parseInt(body.page) - 1) * body.pagination;
  let limit = body.pagination;
  let userId = parseInt(body.userId);
  let { type } = req.body;

  // gestion de la polyhierarchie
  let { thes_path } = req.body;
  let read = "",
    in_query = "";
  if (userId) {
    read = `SELECT get_access_gen( $1, '${branche}') as fold `;
  } else {
    read = `SELECT array_agg(id)  as fold FROM ${branche}_folder WHERE  status = 'public'  AND $1 = $1 `;
  }

  // Première requete : récupèrer la liste des folders autorisés par le user :
  // TODO : ils doivent être dans le projet !!!
  // récupérer l'id projet avec la table pft3d_folder_thesaurus_multi ??? ou le passer en parametre dans le body
  try {
    const userRead = await db.oneOrNone(read, userId);

    if (userRead.fold.length === 0) in_query = "";
    else {
      if (branche === "conservatoire3d") {
        // on ne trie pas les infos, tout est accessible
      } else {
        in_query =
          ` AND i.id_item IN ` +
          `( SELECT id FROM ${branche}_file WHERE id_folder in ${JSON.stringify(userRead.fold)
            .replace("[", "(")
            .replace("]", ")")}  )`;
      }
    }
    try {

      let leaf;
      switch(type) {
        case "periodo":
          leaf = "thesaurus_periodo";
          break;
        case "pactols":
          leaf = "thesaurus_pactols";
          break;
        case "multi":
          leaf = "thesaurus_multi";
          break;
        default:
          leaf = "thesaurus";
          break;
      }

      // Get the metadata that implement the folder
      let all_metadata = await db.manyOrNone(`
        SELECT distinct m.id, m.code, m.rank
        FROM ${branche}_${leaf}_item tmi
        INNER JOIN ${branche}_folder_metadata_model fmm ON tmi.id_item = fmm.id_folder AND tmi.item_type = 'folder'
        INNER JOIN ${branche}_metadata m on fmm.id_metadata_model = m.id_metadata_model
        WHERE tmi.id${ type === 'periodo' ? '_periodo' : '_thes_thesaurus' } = ${id} ${ ['multi', 'simple'].includes(type) ? `AND tmi.thesaurus = '${req.params.thes}'` : '' }
        
        UNION ALL
        
        SELECT distinct m.id, m.code, m.rank
        FROM ${branche}_metadata m
        INNER JOIN ${branche}_metadata_model mm ON mm.id = m.id_metadata_model AND mm.name = '${model}'
        `
      );
      
      const codeNom = all_metadata.filter((m) => m.rank === 1)?.map((m) => m.id) ?? 0;
      const nakala = all_metadata.find((m) => m.code === "nakala")?.id ?? 0;

      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      // pour les item de type folder, l'info Nakala de leur image est présente dans la métadonnées "nakala" du model "deposit"
      //const startQ = await db.any('SELECT id , code , rank from ' + branche + '_metadata WHERE id_metadata_model in ' +
      //    '(SELECT id from ' + branche + '_metadata_model where name = $1) ', model)

      // pour les item de type object : l'info Nakala de leur image est présente dans la métadonnées "Nakala" du model "VirtualObject"
      // ou bien, et aussi maintenant dans la table object directement avec le champ id_nakala
      //const nakalaObj = await db.oneOrNone('SELECT id , code , rank from ' + branche + '_metadata WHERE code = \'Nakala\' ')
      //nakalaObject = nakalaObj['id']
      //nakalaObject = 0
      try {
        let thesitem = [];
        if (type === "multi") {
          let where = "";
          if (req.params.thes === "_") {
            where = `WHERE t.id_thes = ${id}`;
          } else {
            where = `WHERE t.thesaurus_path = '${thes_path}' AND t.thesaurus = '${req.params.thes}'`;
          }
          let query = `SELECT i.item_type,
                                        t.short_name,
                                        ff.folder_name,
                                        CASE
                                            WHEN array_to_string(pnakala.value, '') IS NOT NULL AND
                                                 i.item_type = 'folder' THEN array_to_string(pnakala.value, '')
                                            WHEN array_to_string(pnakalaObj.value, '') IS NOT NULL AND
                                                 i.item_type = 'object' THEN array_to_string(pnakalaObj.value, '')
                                            WHEN o.id_nakala IS NOT NULL AND i.item_type = 'object' THEN o.id_nakala
                                            WHEN i.item_type = 'file' THEN '' END                   AS nakala,
                                        COALESCE(array_to_string(pnom.value, ''), '')               AS depot_name,
                                        COALESCE(ffo.id, o.id, fi.id, 0)                            AS id,
                                        COALESCE(ffo.path, fo.path, fi.path, '')                    AS path,
                                        COALESCE(ffo.name, fo.name, fi.name, '')                    AS filename,
                                        COALESCE(ff.id, fo.id_folder, fi.id_folder, o.id_folder, 0) AS idfolder,
                                        COALESCE(fi.file_ext, ffo.file_ext, '')                     AS extension,
                                        o.name                                                      AS object_name,
                                        ff.nb_objects,
                                        CASE
                                          WHEN i.item_type = 'folder' THEN ff.doi
                                          WHEN i.item_type = 'object' THEN COALESCE(o.doi, ov.doi)
                                          WHEN i.item_type = 'file' THEN COALESCE(fi.doi, ff.doi)
                                        END doi,
                                        COALESCE(ffo.id, fi.id, fo.id)                              AS id_file,
                                        i.qualifier,
                                        CASE
                                          WHEN i.item_type = 'folder'
                                          THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                                            WHERE id_user = ${userId} AND id_folder = ff.id
                                            AND id_item = ff.id AND item_type = 'folder')
                                          WHEN i.item_type = 'object'
                                          THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                                            WHERE id_user = ${userId} AND id_folder = o.id_folder
                                            AND id_item = o.id AND item_type = 'object')
                                          WHEN i.item_type = 'file'
                                          THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                                            WHERE id_user = ${userId} AND id_folder = fi.id_folder
                                            AND id_item = fi.id AND item_type = 'file')
                                        END favorite
                                 FROM ${branche}_thesaurus_multi t
                                          INNER JOIN ${branche}_thesaurus_multi_item i
                                                     ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus
                                          LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                                          LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture
                                          LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                                          LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object'
                                          LEFT OUTER JOIN ${branche}_object_object oo ON oo.id_object_min = o.id
                                          LEFT OUTER JOIN ${branche}_object ov ON ov.id = oo.id_object_ref AND ov.object_type = 'virtual' AND o.object_type = 'physical'
                                          LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative
                                          LEFT OUTER JOIN ${branche}_folder foo ON foo.id = o.id_folder
                                          LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND
                                                                                         pnakala.item_type =
                                                                                         'folder' AND
                                                                                         pnakala.id_metadata = ${nakala}
                                          LEFT OUTER JOIN ${branche}_passport pnakalaObj
                                                          ON pnakalaObj.id_item = o.id AND
                                                             pnakalaObj.item_type = 'object' AND
                                                             pnakalaObj.id_metadata = ${nakalaObject}
                                          LEFT OUTER JOIN ${branche}_passport pnom
                                                          ON pnom.id_item = i.id_item AND i.item_type = 'folder' AND
                                                             pnom.id_metadata in (${codeNom.join(",")}) 
                                                             ${where}
                                 ORDER BY ff.folder_name
                                 OFFSET ${offset} LIMIT ${limit}`;
          thesitem = await db.any(query);
        } else if (type === "simple") {
          let where = "";
          //if (req.params.thes === '_') {
          where = `WHERE t.id_thes = ${id} AND t.thesaurus = '${req.params.thes}'`;
          /*} else {
                        where = `WHERE t.thesaurus_path = '${thes_path}' AND t.thesaurus = '${req.params.thes}'`
                    }*/
          let query = `SELECT i.item_type, t.short_name,  ff.folder_name,
                        CASE WHEN array_to_string(pnakala.value, '') IS NOT NULL AND i.item_type = 'folder' THEN array_to_string(pnakala.value, '')
                            WHEN array_to_string(pnakalaObj.value, '') IS NOT NULL AND i.item_type = 'object' THEN array_to_string(pnakalaObj.value, '')
                            WHEN o.id_nakala IS NOT NULL AND i.item_type = 'object' THEN o.id_nakala
                            WHEN i.item_type = 'file' THEN '' END AS nakala,
                        COALESCE(array_to_string(pnom.value, ''), '') AS depot_name,
                        COALESCE(ffo.id, o.id, fi.id, 0) AS id,
                        COALESCE(ffo.path, fo.path, fi.path, '') AS path,
                        COALESCE(ffo.name, fo.name, fi.name, '') AS filename, 
                        COALESCE(ff.id, fo.id_folder, fi.id_folder, o.id_folder, 0) AS idfolder,
                        COALESCE(fi.file_ext, ffo.file_ext, '') AS extension,
                        o.name AS object_name, 
                        ff.nb_objects, 
                        COALESCE(ff.doi, o.doi, foo.doi ) AS doi_old,
                        CASE
                            WHEN i.item_type = 'folder' THEN ff.doi
                            WHEN i.item_type = 'object' THEN COALESCE(o.doi, ov.doi)
                            WHEN i.item_type = 'file' THEN fi.doi
                        END AS doi,
                        COALESCE(ffo.id, fi.id, fo.id) AS id_file,
                        CASE
                            WHEN i.item_type = 'folder'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = ff.id
                            AND id_item = ff.id AND item_type = 'folder')
                            WHEN i.item_type = 'object'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = o.id_folder
                            AND id_item = o.id AND item_type = 'object')
                            WHEN i.item_type = 'file'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = fi.id_folder
                            AND id_item = fi.id AND item_type = 'file')
                        END favorite
                        FROM ${branche}_thesaurus t
                        INNER JOIN ${branche}_thesaurus_item i ON i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus
                        LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                        LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture
                        LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                        LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object'
                        LEFT OUTER JOIN ${branche}_object_object oo ON oo.id_object_min = o.id
                        LEFT OUTER JOIN ${branche}_object ov ON ov.id = oo.id_object_ref AND ov.object_type = 'virtual' AND o.object_type = 'physical'
                        LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative
                        LEFT OUTER JOIN ${branche}_folder foo ON foo.id = o.id_folder
                        LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.item_type = 'folder' AND pnakala.id_metadata = ${nakala}
                        LEFT OUTER JOIN ${branche}_passport pnakalaObj ON pnakalaObj.id_item = o.id AND pnakalaObj.item_type = 'object' AND pnakalaObj.id_metadata = ${nakalaObject}
                        LEFT OUTER JOIN ${branche}_passport pnom ON pnom.id_item = i.id_item AND i.item_type = 'folder' AND pnom.id_metadata in (${codeNom.join(",")})
                        ${where}
                        ORDER BY ff.folder_name
                        OFFSET ${offset} LIMIT ${limit}`;
          thesitem = await db.any(query);
        } else if (type === "pactols") {
          let query = `SELECT i.item_type, t.name AS short_name,  ff.folder_name,
                        CASE WHEN o.id_nakala is not null THEN o.id_nakala
                        ELSE COALESCE(array_to_string(pnakala.value,''), '') END AS nakala,
                        COALESCE(array_to_string(pnom.value, ''), '') AS depot_name,
                        COALESCE(ffo.id, o.id, fi.id, 0) AS id,
                        COALESCE(ffo.path, fo.path, fi.path, '') AS path,
                        COALESCE(ffo.name, fo.name, fi.name, '') AS filename,
                        COALESCE(ff.id, fo.id_folder, fi.id_folder, 0) AS idfolder,
                        COALESCE(fi.file_ext, ffo.file_ext, '') AS extension,
                        o.name AS object_name,
                        ff.nb_objects,
                        COALESCE(ff.doi, o.doi, virtual.doi) AS doi,
                        COALESCE(ffo.id, fi.id, fo.id) AS id_file,
                        CASE
                            WHEN i.item_type = 'folder'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = ff.id
                            AND id_item = ff.id AND item_type = 'folder')
                            WHEN i.item_type = 'object'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = o.id_folder
                            AND id_item = o.id AND item_type = 'object')
                            WHEN i.item_type = 'file'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = fi.id_folder
                            AND id_item = fi.id AND item_type = 'file')
                        END favorite
                        FROM ${branche}_thesaurus_pactols t
                        INNER JOIN ${branche}_thesaurus_pactols_item i ON i.id_thes_thesaurus = t.id_thes
                        LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                        LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture
                        LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                        LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object'
                        LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative
                        LEFT OUTER JOIN ${branche}_object_object oo ON oo.id_object_min = o.id
                        LEFT OUTER JOIN ${branche}_object virtual ON virtual.id = oo.id_object_ref AND virtual.object_type = 'virtual' AND o.object_type = 'physical'
                        LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = ${nakala} AND pnakala.item_type = i.item_type
                        LEFT OUTER JOIN ${branche}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata in (${codeNom.join(",")})
                        WHERE t.id_thes = ${id}
                        ORDER BY ff.folder_name
                        OFFSET ${offset} LIMIT ${limit}`;
          console.log(query)
          thesitem = await db.any(query);
        } else if (req.params.thes === "periodo") {
          let query = `SELECT i.item_type, t.label AS short_name,  ff.folder_name,
                        COALESCE(array_to_string(pnakala.value,''), '') AS nakala,
                        COALESCE(array_to_string(pnom.value, ''), '') AS depot_name,
                        COALESCE(ffo.id, o.id, fi.id, 0) AS id,
                        COALESCE(ffo.path, fo.path, fi.path, '') AS path,
                        COALESCE(ffo.name, fo.name, fi.name, '') AS filename,
                        COALESCE(ff.id, fo.id_folder, fi.id_folder, 0) AS idfolder,
                        COALESCE(fi.file_ext, ffo.file_ext, '') AS extension,
                        o.name AS object_name,
                        ff.nb_objects,
                        COALESCE(ff.doi, o.doi) AS doi, 
                        COALESCE(ffo.id, fi.id, fo.id) AS id_file,
                        CASE
                            WHEN i.item_type = 'folder'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = ff.id
                            AND id_item = ff.id AND item_type = 'folder')
                            WHEN i.item_type = 'object'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = o.id_folder
                            AND id_item = o.id AND item_type = 'object')
                            WHEN i.item_type = 'file'
                            THEN EXISTS(SELECT * FROM ${branche}_favorite_item
                            WHERE id_user = ${userId} AND id_folder = fi.id_folder
                            AND id_item = fi.id AND item_type = 'file')
                        END favorite
                        FROM ${branche}_thesaurus_periodo t
                        INNER JOIN ${branche}_thesaurus_periodo_item i ON i.id_thes_periodo = t.id_periodo
                        LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder'
                        LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture
                        LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file'
                        LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object'
                        LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative
                        LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = ${nakala} AND pnakala.item_type = i.item_type
                        LEFT OUTER JOIN ${branche}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata in (${codeNom.join(",")})
                        WHERE t.id = ${id}
                        ORDER BY ff.folder_name
                        OFFSET ${offset} LIMIT ${limit}`;
          thesitem = await db.any(query);
        } else {
          //simple ?
          let debut =
            `SELECT i.item_type,  t.short_name,  ff.folder_name, ` +
            ` COALESCE(array_to_string(pnakala.value,''), o.id_nakala,  '') AS nakala, ` +
            //'CASE WHEN (array_to_string(pnakala.value,\'\') IS NULL) THEN \'\' ' +
            //'    ELSE array_to_string(pnakala.value, \'\') END as nakala, ' +
            `COALESCE(array_to_string(pnom.value, ''), '') AS depot_name, ` +
            `CASE WHEN (fi.id IS NULL) AND (o.id IS NULL) THEN ffo.id ` +
            `WHEN (ff.id IS NULL) AND (o.id IS NULL) THEN fi.id ` +
            `WHEN (ff.id IS NULL) AND (fi.id IS NULL) THEN o.id ELSE 0  END as id,` +
            `CASE WHEN (fi.path IS NULL) AND (fo.path IS NULL) THEN ffo.path ` +
            `WHEN (ffo.path IS NULL) AND (fo.path IS NULL) THEN fi.path ` +
            `WHEN (ffo.path IS NULL) AND (fi.path IS NULL) THEN fo.path ELSE '' END as path,` +
            `COALESCE(ffo.name, fo.name, fi.name, '') AS filename, ` +
            `CASE WHEN ff.id IS NOT NULL THEN ff.id WHEN fi.id_folder IS NULL THEN fo.id_folder ELSE fi.id_folder END as idfolder, ` +
            `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext WHEN ffo.file_ext IS NOT NULL THEN ffo.file_ext END as extension, ` +
            ` o.name as object_name, ff.nb_objects, ` +
            `COALESCE(ff.doi, o.doi) AS doi, ` +
            `COALESCE(ffo.id, fi.id, fo.id) as id_file, ` +
            "CASE " +
            "WHEN i.item_type = 'folder' " +
            "THEN EXISTS(SELECT * FROM ${branche}_favorite_item " +
            "WHERE id_user = ${userId} AND id_folder = ff.id " +
            "AND id_item = ff.id AND item_type = 'folder') " +
            "WHEN i.item_type = 'object' " +
            "THEN EXISTS(SELECT * FROM ${branche}_favorite_item " +
            "WHERE id_user = ${userId} AND id_folder = o.id_folder " +
            "AND id_item = o.id AND item_type = 'object') " +
            "WHEN i.item_type = 'file' " +
            "THEN EXISTS(SELECT * FROM ${branche}_favorite_item " +
            "WHERE id_user = ${userId} AND id_folder = fi.id_folder " +
            "AND id_item = fi.id AND item_type = 'file') " +
            "END favorite " +
            `FROM ${branche}_thesaurus t INNER JOIN ${branche}_thesaurus_item i ` +
            `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus ` +
            `AND i.thes_path = t.thesaurus_path ` +
            `LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
            `LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture ` +
            `LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
            `LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
            `LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative ` +
            `LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 AND pnakala.item_type = i.item_type ` +
            `LEFT OUTER JOIN ${branche}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata in (${codeNom.join(",")}) `;

          // On ne recupère que l'id thes pour un thesaurus particulier
          // TODO polyhierarchie
          let particulier = " WHERE t.thesaurus_path = $2 ";
          // On récupère tous les id_thes enfants de l'id_thes demandé pour tous les thesaurus (pas pris en compte pour le moment)
          //let general = ' WHERE t.thesaurus_path <@ ( SELECT get_thesaurus_multi_general_enfants_path(\''+branche+'\', $3 ) )::ltree[]  '
          let general = "WHERE t.id_thes = $2";

          let suite = " AND t.thesaurus = $3 ";
          let milieu = in_query;

          let fin = ` ORDER BY ff.folder_name OFFSET ${offset} LIMIT ${limit}`;

          let query = "";

          if (req.params.thes === "_") {
            // general
            // polyhiérarchie : on prend tous les path d'un id_thes
            //query = debut + general + milieu + fin // A ce jour, même pour un concept général, on ne va chercher que le terme et non tous ses enfants
            query = debut + general + milieu + fin;
            thesitem = await db.any(query, [nakala, codeNom, id]);
          } else {
            // un thesaurus en particulier
            query = debut + particulier + suite + milieu + fin;
            thesitem = await db.any(query, [nakala, thes_path, req.params.thes]);
          }
        }

        // success
        res.status(200).sendData(thesitem);
      } catch (e) {
        responseHelper.sendError(500, "server_error in getThesaurusItemPage simple", e, req, res);
      }
    } catch (e) {
      // error
      responseHelper.sendError(500, "server_error in getThesaurusItemPage simple for nakala", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusItemPage simple for userRead", e, req, res);
  }
};

//create url endpoint for /explorethesNBGeneral/:branche,:idThes
// Pour la pagination , on récupère l'ensemble des folders concernés par l'item de tous les thesaurus
// On ne regarde pas à quel thesaurus on appartient mais on veut le concept quelque soit le thesaurus auquel il appartient
// il peut y avoir plusieurs fois le même concept
// Pour le moment, seulement les items de type file sont traités
// TODO : traiter le cas où les items type taggué sont des objects
exports.exploreThesaurusItemNBGeneral = async function (req, res) {
  let { branche } = req.params,
    id = parseInt(req.params.idThes),
    thesaurus = req.params.thesaurus;

  // TODO : récupérer le projectId et limiter les folder à ceux du projet
  try {
    //1 on récupère tous les folders concernés et le nombre d'item
    // TODO : completer la requete pour les items de type folder ou objets
    let query =
      `select json_agg(x) as folders_info FROM ` +
      `(SELECT id_folder, count(*) as nb FROM ${branche}_file ` +
      ` WHERE id IN ` +
      `    (SELECT id_item FROM ${branche}_thesaurus_item mi ` +
      `        INNER JOIN ${branche}_thesaurus m ON m.id_thes = mi.id_thes_thesaurus AND m.thesaurus_path = mi.thes_path ` +
      `        WHERE item_type = 'file' AND mi.thesaurus = '${thesaurus}  ` +
      `        AND id_thes_thesaurus = $1` +
      //'        AND thesaurus_path <@ (SELECT get_thesaurus_multi_general_enfants_path(\''+branche+'\', $1 ))::ltree[]
      `     )  ` +
      ` GROUP BY id_folder ) AS x`;
    const nbthesitemfolders = await db.oneOrNone(query, [id]);

    try {
      // 2 / on récupère le nom complet du concept en jeu
      // TODO : polyhierarchie : est-ce qu'on donne toutes les hiérarchie du concept possible ?
      // avec pour chaque concept le nombre ?
      const thesname = await db.any(
        `SELECT sum(nb_item), name as thesname  ` + `FROM ${branche}_thesaurus WHERE id_thes = $1 AND thesaurus = '${thesaurus} GROUP BY name `,
        [id],
      );

      // 3/ on assemble les 2
      let result = Object.assign({}, thesname, nbthesitemfolders);
      // success
      res.status(200).sendData(result);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusItemNB simple thesname general ", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusItemNB simple folders general ", e, req, res);
  }
};

// create url endpoint /thesaurusProject/:branche,:idProject,:lng GET
// charger tous les thesaurus impliqué dans un projet sans forcément faire partie d'un modèle de métadonnées
// pour pouvoir ajouter un tag à la volée sans être dans un modele
// TODO : Est-ce qu'on propose les Pactols à tous les projets ?
// DONE : ajouter le bilinguisme dans la requête (13/10/2022) amélioré (06/10/2023)
// Quid du projet Notre-Dame ?
exports.getAllThesaurusForProject = async function (req, res) {
  let { branche } = req.params;
  let projectId = req.params.idProject;
  // Dans les tables thesaurus, on stocke dans name le nom en français du concept, dans short_name le nom en anglais
  const multi_name = req.params.lng === "fr" ? "name" : "name_"+req.params.lng;
  const cnd3d_name = req.params.lng === "fr" ? "short_name" : "name"; // pour le cnd3d, le nom du thesaurus en français est dans champ short_name
  const cnd3d_period = req.params.lng === "fr" ? "Période" : "Period of time";
  const cnd3d_pactols = req.params.lng === "fr" ? "Mots-clés Pactols" : "Pactols Keywords";
  //const { lng } = req.params;
  const lng = req.params.lng === 'fr' ? 'fr': req.params.lng;
  let name =  req.params.lng === 'fr' ? 'name' : 'name_'+lng;


  let query = "";
  if (branche === "conservatoire3d") {
    query =
      `SELECT st.thesaurus, t.${multi_name} as name, 'multi' as status, l.description
      FROM conservatoire3d_thesaurus_multi t
      INNER JOIN conservatoire3d_search_thesaurus st ON st.thesaurus = t.thesaurus AND st.id_thes = t.id_thes
      LEFT OUTER JOIN conservatoire3d_metadata_label l ON l.label = t.name AND l.language = '${lng}'
      WHERE nlevel(thesaurus_path) = 1 AND st.visible = true AND st.type = 'multi'
      UNION ALL
      SELECT distinct t.thesaurus, t.${name} as name, 'thesaurus' as status, l.description
      FROM conservatoire3d_thesaurus t
      INNER JOIN conservatoire3d_search_thesaurus st ON st.thesaurus = t.thesaurus AND st.id_thes = t.id_thes
      LEFT OUTER JOIN conservatoire3d_metadata_label l ON l.label = t.${name} AND l.language = '${lng}'
      WHERE nlevel(thesaurus_path) = 1 AND st.visible = true AND st.type = 'thesaurus'
      UNION ALL
      SELECT 'periodo', '${cnd3d_period}' as name, 'periodo', 'periodo'
      UNION ALL
      SELECT 'pactols', '${cnd3d_pactols}', 'pactols', 'pactols' `
  } else {
    query =
      `SELECT distinct fm.thesaurus, m.${multi_name} as name, 'multi' as status,
      CASE WHEN description IS NOT NULL THEN description ELSE '' END as description
      FROM ${branche}_thesaurus_multi m
      INNER JOIN ${branche}_folder_thesaurus_multi fm ON fm.thesaurus= m.thesaurus AND fm.id_thes = m.id_thes
      LEFT OUTER JOIN ${branche}_metadata_label l ON l.label = m.name
      WHERE fm.id_folder = ${projectId} AND visible = '1'
      UNION ALL
      SELECT distinct ft.thesaurus, t.${name} as name, 'thesaurus' as status ,
      CASE WHEN description IS NOT NULL THEN description ELSE '' END as description
      FROM ${branche}_thesaurus t
      INNER JOIN ${branche}_folder_thesaurus ft ON ft.thesaurus= t.thesaurus AND ft.id_thes = t.id_thes
      LEFT OUTER JOIN ${branche}_metadata_label l ON l.label = t.name
      WHERE ft.id_folder = ${projectId} AND visible = '1'
      UNION ALL
      SELECT thesaurus, ${lng}_name as name  ,  'pactols' as status , ${lng}_description  as description
      FROM ${branche}_folder_thesaurus_pactols fp
      WHERE id_folder = ${projectId} AND thesaurus = 'sujet' AND visible = '1'
      UNION ALL
      SELECT thesaurus, ${lng}_name as name , 'geopactols' , ${lng}_description  as description
      FROM ${branche}_folder_thesaurus_pactols fp
      WHERE id_folder = ${projectId} AND thesaurus = 'lieux' AND visible = '1'
      `
  }
  // TODO: A la création d'un nouveau projet, renseigner la table _folder_thesaurus_pactols par défaut
  // TODO: dans le module admin : pouvoir cocher ou décocher les thesaurus pactols (sujet et chrono) pour gérer les mots-clefs
  try {
    const thesaurus = await db.any(query);

    res.status(200).sendData(thesaurus);
  } catch (e) {
    responseHelper.sendError(500, "server_error in Allthesaurus For Project ", e, req, res);
  }
};

//create URL end point for route /thesaurusProject/:branche,:idProject,:lng  PUT
// Ajouter une liaison entre un folder/Project et un thesaurus Frantiq/PACTOLS
// pour la branche pft3d => on lit un projet à un thesaurus pour pouvoir indexer les item du projet avec les éléments d'un des thesaurus pactols de Frantiq
exports.addThesaurusPactolsForProject = async function (req, res) {
  let { branche } = req.params;
  let { idProject } = req.params;

  let { thesaurus } = req.body;
  let { orderThes } = req.body; // order d'apparition de ce thesaurus parmi les autres du projet dans la page d'indexation
  let { idThes } = req.body; // id thes du concept premier du thesaurus
  let { visible } = req.body; // 0/1
  let { en_name } = req.body;
  let { fr_name } = req.body;
  let { en_description } = req.body;
  let { fr_description } = req.body;

  try {
    const linkthesoproject = await db.any(
      `INSERT INTO ${branche}_folder_thesaurus_pactols (id_folder, id_thes, thesaurus, order_thes, visible, en_name, fr_name, en_description, fr_description )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [idProject, idThes, thesaurus, orderThes, visible, en_name, fr_name, en_description, fr_description ],
    );

    res.status(200).sendData(linkthesoproject);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurusProject add Pactols to project ", e, req, res);
  }
};

// create url endpoint /ThesaurusSimpleName/:branch,:thesaurus,:lng,:name GET
// autocompletion thesaurus simple
// on ajoute la langue : name = anglais, short_name = français ! pour le conservatoire seulement
// pour le moment, pour le reste, on récupère toujours le "name" qui peut contenir, ou pas, la hiérarchie des mots-clés
exports.getThesaurusSimpleName = async function (req, res) {
  let { branch } = req.params;
  let { thesaurus } = req.params;
  let lng = req.params.lng === 'fr' ? 'fr' : 'en';
  let lngName =''
  let name = decodeURIComponent(req.params.name);
  if (branch === 'conservatoire3d') {
    lngName = lng === 'fr' ? 'short_name' : 'name';
  } else {
    lngName = 'name'
  }

  try {
    const thesogeneral = await db.any(
      `SELECT 
                DISTINCT t.${lngName} AS name,
                t.${lngName} AS value,
                concat(id, '_', id_thes) as id, 
                t.${lngName} AS label
            FROM ${branch}_thesaurus t
            WHERE t.thesaurus = '${thesaurus}' AND t.${lngName} ILIKE '%${name}%'
            ORDER BY label`,
    );

    res.status(200).sendData(thesogeneral);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus simple name general ", e, req, res);
  }
};

// create url endpoint /ThesaurusPeriodoName/:branch,:lang,:name GET
// autocompletion thesaurus periodo
exports.getThesaurusPeriodoName = async function (req, res) {
  let { branch } = req.params;
  let lang = req.params.lang === "fr" ? "fra-latn" : "eng-latn";
  let name = decodeURIComponent(req.params.name);

  try {
    const thesogeneral = await db.any(
      `SELECT 
                DISTINCT label AS name, 
                start_date::int AS value, 
                concat(id, '_', id_periodo) as id, 
                label AS label
            FROM ${branch}_thesaurus_periodo
            WHERE nb_item > 0 AND  language = '${lang}' AND label ILIKE '%${name}%'
            ORDER BY start_date::int`,
    );

    res.status(200).sendData(thesogeneral);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus periodo name general ", e, req, res);
  }
};

// create url endpoint /ThesaurusPactolsName/:branch,:name GET
// autocompletion thesaurus pactols
exports.getThesaurusPactolsNameSearch = async function (req, res) {
  let { branch } = req.params;
  let name = decodeURIComponent(req.params.name);

  let lng_name = req.query.lng === 'fr' ? 'name' : 'name_'+req.query.lng

  let myquery =`SELECT
                DISTINCT ${lng_name} AS name,
                thesaurus_path AS value,
                concat(id, '_', id_thes) as id,
                ${lng_name} AS label
            FROM ${branch}_thesaurus_pactols
            WHERE ${lng_name} ILIKE '%${name}%'
            ORDER BY thesaurus_path`

  try {
    const thesogeneral = await db.any( myquery , )
    ;

    res.status(200).sendData(thesogeneral);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols name general ", e, req, res);
  }
};

// create url endpoint /thesaurusSearchNames/:branch,:search GET
// récupère les noms des thesaurus de la recherche à partir des ids
exports.getThesaurusSearchNames = async function (req, res) {
  let { branch } = req.params;
  let search = decodeURIComponent(req.params.search);
  let json;

  try {
    json = JSON.parse(search);
  } catch (e) {
    json = {};
  }
  try {
    for (let type in json) {
      if (type === "multi" || type === "thesaurus") {
        let thesaurusTable = type === "multi" ? `${branch}_thesaurus_multi` : `${branch}_thesaurus`;
        for (let thesaurus in json[type]) {
          for (let ids in json[type][thesaurus]) {
            for (let id in json[type][thesaurus][ids]) {
              const names = await db.oneOrNone(
                `SELECT name FROM ${thesaurusTable} WHERE thesaurus = '${thesaurus}' AND id_thes = ${json[type][thesaurus][ids][id]}`,
              );
              json[type][thesaurus][ids][id] = { name: names.name, id: json[type][thesaurus][ids][id] };
            }
          }
        }
      }
      if (type === "periodo") {
        let thesaurusTable = `${branch}_thesaurus_periodo`;
        for (let thesaurus in json[type]) {
          for (let ids in json[type][thesaurus]) {
            for (let id in json[type][thesaurus][ids]) {
              const names = await db.oneOrNone(
                `SELECT label FROM ${thesaurusTable} WHERE id_periodo = '${json[type][thesaurus][ids][id]}'`,
              );
              json[type][thesaurus][ids][id] = { name: names.label, id: json[type][thesaurus][ids][id] };
            }
          }
        }
      }
      if (type === "pactols") {
        let thesaurusTable = `${branch}_thesaurus_pactols`;
        for (let thesaurus in json[type]) {
          for (let ids in json[type][thesaurus]) {
            for (let id in json[type][thesaurus][ids]) {
              const names = await db.oneOrNone(
                `SELECT name FROM ${thesaurusTable} WHERE id_thes = ${json[type][thesaurus][ids][id]}`,
              );
              json[type][thesaurus][ids][id] = { name: names.name, id: json[type][thesaurus][ids][id] };
            }
          }
        }
      }
      if (type === "geopactols") {
        let thesaurusTable = `${branch}_thesaurus_pactols_geo`;
        for (let thesaurus in json[type]) {
          for (let ids in json[type][thesaurus]) {
            for (let id in json[type][thesaurus][ids]) {
              const names = await db.oneOrNone(
                `SELECT name FROM ${thesaurusTable} WHERE id = ${json[type][thesaurus][ids][id]}`,
              );
              json[type][thesaurus][ids][id] = { name: names.name, id: json[type][thesaurus][ids][id] };
            }
          }
        }
      }
    }
    res.status(200).sendData(json);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusSearchNames ", e, req, res);
  }
};

// create url endpoint /thesaurusSimple/:root,:thesaurus PUT
// faire indexation du thesaurus simple (depuis l'indexation avec autocomplete )
exports.addThesaurusSimple = async function (req, res) {
  let { thesaurus } = req.params;
  // TODO : incrémenter le nb_item (automatiquement avec un trigger de la table thesaurus_pactols_item

  // on récupère le id_thesaurus et le id_thes séparé d'un _ (voir la fonction qui récupère les info)
  let id_thesaurus = req.body.id.split("_")[0];
  let id_thes_thesaurus = req.body.id.split("_")[1];
  //console.log(typeof id_thes_thesaurus)
  let int_id_thes_thesaurus = parseInt(req.body.id.split("_")[1]);
  //console.log(typeof int_id_thes_thesaurus)

  db.any(
    `SELECT count(*)  FROM ${req.params.root}_thesaurus_item ` +
      `WHERE thesaurus = $1 AND id_item = $2 AND item_type = $3 `,
    [thesaurus, req.body.item, req.body.type],
  )
    .then((count) => {
      if (count[0].count === "0") {
        // pas d'indexation pour cet item, on indexe
          insertThesItem(
            req.params.root,
            id_thesaurus,
            id_thes_thesaurus,
            thesaurus,
            req.body.item,
            req.body.type,
            req.body.id_user,
          )
        res.status(201).sendData(true);
      } else {
        // on change l'indexation existante par une autre
          updateThes(
            req.params.root,
            id_thesaurus,
            id_thes_thesaurus,
            thesaurus,
            req.body.item,
            req.body.type,
            req.body.id_user,
          )
        console.log(" already in thes simple, the indexing term has been updated for this item");
        res.status(201).sendData(true);
      }
    })
    .catch((e) => {
      console.log("ERROR select thesaurus simple item metadata value :", e);
      responseHelper.sendError(500, "server_error IN thesaurusSimple PUT when previous select ", e, req, res);
    });
};

// create url endpoint /thesaurusPactolsGeo/:root,:thesaurus GET
// Récupérer l'id et seulement le label !
exports.getThesaurusPactolsGeo = async function (req, res) {
  let { root } = req.params;

  try {
    const thes = await db.any(
      `SELECT id, name as short_name,  name ` + `FROM ${root}_thesaurus_pactols_geo ` + `ORDER BY thesaurus_path `,
      req.params.thesaurus,
    );
    res.status(200).sendData(thes);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols geo", e, req, res);
  }
};

// create url endpoint /thesaurusPactolsGeo/:root,:thesaurus PUT
// un item peut avoir plusieurs indexation => multi
exports.addThesaurusPactolsGeo = async function (req, res) {
  let identifier = req.body.id; //URI
  let name = decodeURI(req.params.name);
  let queryPactols = `SELECT id  FROM ${req.params.root}_thesaurus_pactols_geo WHERE identifier = $1 `;
  try {
    const exist = await db.oneOrNone(queryPactols, [identifier]);
    // est-ce que l'URI est déjà présente dans le thesaurus pactols geo ?
    if (exist) {
      // si oui
      // le concept existe, il suffit d'insérer l'indexation dans la table pactols_geo_item
      try {
        // Est-ce que l'item a déjà été indexé avec ce concept ?
        const existItem = await db.oneOrNone(
          `SELECT count(*) FROM ${req.params.root}_thesaurus_pactols_geo_item ` +
            `WHERE id_thesaurus = $1 AND id_item = $2 AND item_type = $3 `,
          [exist.id, req.body.item, req.body.type],
        );
        if (existItem) {
          if (existItem.count === 1) {
            // si oui on retourne 0 car déjà indexé
            res.status(201).sendData(["0"]);
          } else {
            // on retourne 1 il n'est pas indexé
            insertThesPactolsGeoItem(req.params.root, exist.id, req.body.item, req.body.type, req.body.id_user);
            res.status(201).sendData(["1"]);
          }
        } else {
          // si non on retourne 1
          if (insertThesPactolsGeoItem(req.params.root, exist.id, req.body.item, req.body.type, req.body.id_user))
            res.status(201).sendData(["1"]);
          else {
            responseHelper.sendError(500, "server_error in thesaurus pactols geo add item", "erreur insert", req, res);
          }
        }
      } catch (er) {
        responseHelper.sendError(500, "server_error in thesaurus pactols geo add item already exists");
      }
    } else {
      // Le concept n'existe pas encore dans la table thesaurus pactols geo, on l'ajoute et on ajoute l'item dans l'autre table en suivant
      insertThesPactolsGeoAndItem(req.params.root, identifier, name, req.body.item, req.body.type, req.body.id_user);
      // on retourne 1 on vient de l'indexer
      res.status(201).sendData(["1"]);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols geo add item ", e, req, res);
  }
};

// get all thesaurus item indexe for pactols geo thesaurus
// Create url endpoint for /ThesaurusPactolsGeoItem/:root,:type,:item GET
exports.getThesaurusPactolsGeoItem = async function (req, res) {
  let { root } = req.params;
  let { type } = req.params;
  let { lng } = req.query;

  let name  = '';
  if (lng) {
    name = lng === 'fr' ? 'name' : 'name_'+ lng
  } else {
    name = 'name'
  }

  try {
    const pactagList = await db.any(
      `SELECT id, ${name} as name, identifier FROM ${root}_thesaurus_pactols_geo tp ` +
        `INNER JOIN ${root}_thesaurus_pactols_geo_item it ON it.id_thesaurus = tp.id ` +
        `WHERE  id_item = $1 AND item_type='${type}'`,
      req.params.itemId,
    );
    // success
    res.status(200).sendData(pactagList);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusPactolsGeoItem", e, req, res);
  }
};

// Create url endpoint for /ThesaurusPactolsGeoItem/:root,:type,:itemId DELETE
exports.deleteThesaurusPactolsGeoItem = async function (req, res) {
  let { itemId } = req.params;
  let query =
    `DELETE FROM ${req.params.root}_thesaurus_pactols_geo_item` +
    ` WHERE id_thesaurus = $1 ` +
    ` AND id_item = $2 AND item_type = $3`;

  if (itemId !== 0) {
    try {
      const delitemThes = await db.any(query, [req.body.id_thesaurus, itemId, req.params.type]);
      res.status(200).sendData([]);
    } catch (e) {
      responseHelper.sendError(500, "server_error in DELETE thesaurus pactols geo item", e, req, res);
    }
  } else {
    responseHelper.sendError(
      500,
      "server_error",
      "not a valid id for item to delete in thesaurus pactols geo item",
      req,
      res,
    );
  }
};

//on crée une nouvelle route DÉRIVÉE DE explorethesMultiItem suite à l'ajout des infos pour les objets (CND3D) qui dupliquent les lignes
// on utilise des fontions sql d'une part pour les infos de type 3d associé
// et d'autre part pour les infos de type ply associé
//create url endpoint for /explorethesMultiItemComplet/:root,:thes,:idThes
exports.exploreThesaurusMultiItemComplet = async function (req, res) {
  let id = req.params.idThes,
    { root } = req.params,
    model = "project",
    nakala = 0,
    codeNom = 0;

  try {
    if (root === "conservatoire3d") {
      // on appel cette API pour explorer les thesaurus soit pour les folder/depot
      if (req.query)
        if (req.query.model) model = req.query.model;
        else model = "virtualObject";
      else model = "virtualObject"; // soit pour les objets cela dépend de comment/sur quoi on a posé les tags
    }
    // Get id_metadata from nakala info and depot name (metadata rank 1)  info
    const startQ = await db.any(
      `SELECT id , code , rank from ${root}_metadata WHERE id_metadata_model in ` +
        `(SELECT id from ${root}_metadata_model where name = $1) `,
      model,
    );

    //console.log(startQ)
    for (let i = 0; i < startQ.length; i++) {
      // on récupère l'id de la metadonnee de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
      if (startQ[i].rank === 1) codeNom = startQ[i].id;
      // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
      if (startQ[i].code) {
        if (startQ[i].code.substring(1) === "akala") nakala = startQ[i].id;
      }
    }
    try {
      let query =
        `SELECT i.item_type,  t.short_name,  ff.folder_name, ` +
        `CASE WHEN (array_to_string(pnakalaf.value,'') IS NULL) AND (array_to_string(pnakalao.value, '') IS NOT NULL )  AND (o.id_nakala IS NULL) ` +
        `   THEN  array_to_string(pnakalao.value, '') ` +
        `   WHEN (o.id_nakala IS NOT NULL)   ` +
        `   THEN  o.id_nakala ` +
        `   WHEN (array_to_string(pnakalao.value,'') IS NULL) AND (array_to_string(pnakalaf.value, '') IS NOT NULL ) AND (o.id_nakala IS NULL) ` +
        `   THEN  array_to_string(pnakalaf.value, '') END  as nakala, ` +
        `CASE WHEN (dfolder.fr_name IS NULL) AND (dobject.fr_name IS NOT NULL) THEN dobject.fr_name` +
        `   WHEN (dfolder.fr_name IS NOT NULL) AND (dobject.fr_name IS NULL) THEN dfolder.fr_name` +
        `   ELSE  ''  END as depot_name, ` +
        `CASE WHEN (fi.id IS NULL) AND (o.id IS NULL) THEN ffo.id ` +
        `WHEN (ff.id IS NULL) AND (o.id IS NULL) THEN fi.id ` +
        `WHEN (ff.id IS NULL) AND (fi.id IS NULL) THEN o.id ELSE 0  END as id,` +
        `CASE WHEN (fi.path IS NULL) AND (fo.path IS NULL) THEN ffo.path ` +
        `WHEN (ffo.path IS NULL) AND (fo.path IS NULL) THEN fi.path ` +
        `WHEN (ffo.path IS NULL) AND (fi.path IS NULL) THEN fo.path ELSE '' END as path,` +
        `CASE WHEN (fi.name IS NULL) AND (fo.name IS NULL) THEN ffo.name ` +
        `WHEN (fi.name IS NULL) AND (fo.name IS NOT NULL) THEN fo.name ELSE fi.name END as filename, ` +
        `CASE WHEN (fi.id_folder IS NULL) AND ( ffoo.id IS  NULL) AND (ff.id IS NOT NULL) THEN ff.id ` + // type folder
        `     WHEN (ff.id IS NULL) AND (fi.id_folder IS NULL) AND ( ffoo.id IS NOT NULL) THEN ffoo.id ` + // type object
        `     ELSE fi.id_folder END as idfolder, ` +
        `CASE WHEN fi.file_ext IS NOT NULL THEN fi.file_ext WHEN ffo.file_ext IS NOT NULL THEN ffo.file_ext END as extension, ` +
        ` o.name as object_name, ff.nb_objects ` +
        `, CASE WHEN (ffo.id IS NOT NULL) THEN ffo.id ` +
        `       WHEN (fi.id IS NOT NULL ) THEN fi.id ` +
        `       WHEN (fo.id IS NOT NULL ) THEN fo.id END as id_file ` + // ajout pour récupérer l'id file à afficher avec IIIF
        `, CASE WHEN o.doi IS NULL THEN  ff.doi WHEN ff.doi IS NULL THEN o.doi ELSE ff.doi END as doi  ` +
        `, CASE WHEN o.id IS NOT NULL THEN ${root}_get_hash3d(o.id, 0) ELSE NULL END as hash3d_info  ` +
        `, CASE WHEN o.id IS NOT NULL THEN ${root}_get_plyobject(o.id, 0) ELSE NULL END as ply3d_info  ` +
        `, CASE WHEN ff.id is NOT NULL THEN ff.visible ELSE 'true' END as visible ` +
        `FROM ${root}_thesaurus_multi t INNER JOIN ${root}_thesaurus_multi_item i ` +
        `ON  i.id_thes_thesaurus = t.id_thes AND i.thesaurus = t.thesaurus ` +
        `LEFT OUTER JOIN ${root}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
        `LEFT OUTER JOIN ${root}_file ffo ON ffo.id = ff.id_representative_picture ` +
        `LEFT OUTER JOIN ${root}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
        `LEFT OUTER JOIN ${root}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
        `LEFT OUTER JOIN ${root}_file fo ON fo.id = o.id_file_representative ` +
        `LEFT OUTER JOIN ${root}_folder_object foo ON foo.id_object = o.id ` +
        `LEFT OUTER JOIN ${root}_folder ffoo ON ffoo.id = foo.id_folder ` +
        `LEFT OUTER JOIN ${root}_folder_depot fd ON ffoo.id = fd.id_folder ` +
        `LEFT OUTER JOIN ${root}_depot dobject ON dobject.id = fd.id_depot ` + //depot d'un objet taggue
        `LEFT OUTER JOIN ${root}_folder_depot ffd ON ffd.id_folder = ff.id ` +
        `LEFT OUTER JOIN ${root}_depot dfolder ON dfolder.id = ffd.id_depot ` + // depot d'un folder taggue
        `LEFT OUTER JOIN ${root}_passport pnakalaf ON pnakalaf.id_item = i.id_item AND pnakalaf.id_metadata = $1 ` +
        `LEFT OUTER JOIN ${root}_passport pnakalao ON pnakalao.id_item = fd.id_folder AND pnakalao.id_metadata = $1 ` +
        `LEFT OUTER JOIN ${root}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 ` +
        `WHERE t.thesaurus = $3 AND t.id_thes = $4 ` +
        `ORDER BY ff.folder_name `;

      const thesitem = await db.any(query, [nakala, codeNom, req.params.thes, id]);
      // success
      res.status(200).sendData(thesitem);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusMultiItem", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusItem for nakala", e, req, res);
  }
};

// get url endpoint for /SitesFromGeonames/:branch: récupérer la liste de tous les sites présents dans une branche
// n'attraper que ceux qui ont un doi
// TODO : attraper aussi le parent ? pour être plus parlant ?
exports.getSitesFromGeonames = async function (req, res) {
  let code = req.params.codeName,
    branche = req.params.branch;

  try {
    const listSites = await db.any(`SELECT m.name, m.short_name, m.nb_tot_item, m.nb_item,
                            m.id_thes, m.thesaurus_path, mi.qualifier, p.short_name as fr_parent, p.name as en_parent, count(*)
                        FROM ${branche}_thesaurus_multi m
                        INNER JOIN ${branche}_thesaurus_multi_item mi ON mi.thesaurus = m.thesaurus AND mi.id_thes_thesaurus = m.id_thes
                        LEFT OUTER JOIN ${branche}_thesaurus_multi p ON p.thesaurus = m.thesaurus AND p.id_thes = m.id_parent
                        LEFT OUTER JOIN ${branche}_thesaurus_multi_item pi ON pi.thesaurus = p.thesaurus AND pi.id_thes_thesaurus = p.id_thes
                        WHERE m.thesaurus = 'geo' AND m.nb_item != 0 AND nlevel(m.thesaurus_path) > 1
                        GROUP BY m.name, m.short_name, m.nb_tot_item, m.nb_item,
                            m.id_thes, m.thesaurus_path, mi.qualifier, p.short_name, p.name
                        ORDER BY nb_tot_item DESC, short_name`);
    res.status(200).sendData(listSites);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getSitesFromCodeName GET", e, req, res);
  }
};

// get url endpoint for /SiteFromGeonames/:branch,:codeGeonames: récupérer la liste de tous les sites présents dans une branche
// n'attraper que ceux qui ont un doi
exports.getSiteFromGeonames = async function (req, res) {
  let code = req.params.codeGeonames,
    branche = req.params.branch;

  try {
    const listSite = await db.oneOrNone(
      `SELECT m.name, m.short_name, m.nb_tot_item, m.nb_item,
                            m.id_thes, m.thesaurus_path, m.latlng
                        FROM ${branche}_thesaurus_multi m
                        WHERE m.thesaurus = 'geo'
                        AND id_thes = $1
                        `,
      code,
    );
    res.status(200).sendData(listSite);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getSiteFromCodeName GET", e, req, res);
  }
};

// get url endpoint for /ThesItemFromMultiCodeThesaurus/:branch,:codeIdThes,:thesaurus : récupérer la liste de tous les items présents dans une branche
// dans un thesaurus multi pour un id_thes
// n'attraper que ceux qui ont un doi
exports.getThesItemFromMultiCodeThesaurus = async function (req, res) {
  let code = req.params.codeIdThes,
    branche = req.params.branch,
    { thesaurus } = req.params;

  try {
    const listItem = await db.oneOrNone(
      `SELECT m.name, m.short_name, sum(m.nb_tot_item), sum(m.nb_item),
                            m.id_thes, m.identifier
                        FROM ${branche}_thesaurus_multi m
                        WHERE m.thesaurus = $1
                        AND id_thes = $2 GROUP BY m.name, m.short_name, m.id_thes, m.identifier`,
      [thesaurus, code],
    );
    res.status(200).sendData(listItem);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesiItemFromMultiCodeThesaurus GET", e, req, res);
  }
};

// get url endpoint for /ThesItemFromPacolsCodeThesaurus/:branch,:codeIdThes,:thesaurus : récupérer la liste de tous les items présents
// dans un thesaurus des pactols pour un id_thes
exports.getThesItemFromPactolsCodeThesaurus = async function (req, res) {
  let code = req.params.codeIdThes,
    branche = req.params.branch,
    { thesaurus } = req.params;
  // evol : pour les pactols ; pas besoin de spécifier le thesaurus, l'id_thes suffit à identifier de manière unique le concept
  try {
    const listItem = await db.oneOrNone(
      `SELECT m.name, m.name as short_name, m.nb_tot_item, m.nb_item,
                            m.id_thes, m.thesaurus_path, m.identifier
                        FROM ${branche}_thesaurus_pactols m
                        WHERE id_thes = $1
                        `,
      [code],
    );
    res.status(200).sendData(listItem);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesiItemFromPactolsCodeThesaurus GET", e, req, res);
  }
};

// get url endpoint for /ThesItemFromPacolsGeoCodeThesaurus/:branch,:codeIdThes,:thesaurus : récupérer la liste de tous les items présents
// dans un thesaurus des pactols pour un id_thes
exports.getThesItemFromPactolsGeoCodeThesaurus = async function (req, res) {
  let code = req.params.codeIdThes,
    branche = req.params.branch;
  try {
    const listItem = await db.oneOrNone(
      `SELECT m.name, m.name as short_name, m.nb_tot_item, m.nb_item,
                            m.id as id_thes, '' as thesaurus_path, m.identifier
                        FROM ${branche}_thesaurus_pactols_geo m
                        WHERE id = $1
                        `,
      [code],
    );
    res.status(200).sendData(listItem);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesiItemFromPactolsGeoCodeThesaurus GET", e, req, res);
  }
};

// get url endpoint for /ThesItemFromSimpleCodeThesaurus/:branch,:codeIdThes,:thesaurus : récupérer la liste de tous les items présents dans une branche
// dans un thesaurus multi pour un id_thes
// n'attraper que ceux qui ont un doi
exports.getThesItemFromSimpleCodeThesaurus = async function (req, res) {
  let code = req.params.codeIdThes,
    branche = req.params.branch,
    { thesaurus } = req.params;

  try {
    const listItem = await db.oneOrNone(
      `SELECT m.name, m.short_name, sum(m.nb_tot_item), sum(m.nb_item),
                            m.id_thes
       --, m.thesaurus_path
                        FROM ${branche}_thesaurus m
                        WHERE m.thesaurus = $1
                        AND id_thes = $2 GROUP BY m.name, m.short_name, m.id_thes
                        `,
      [thesaurus, code],
    );
    res.status(200).sendData(listItem);
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesiItemFromSimpleCodeThesaurus GET", e, req, res);
  }
};

// create url endpoint for /explorethesPactolsGeoNB/:branche,:idThes
// Pour la pagination , on récupère l'ensemble des folders concernés par l'item du thesaurus
// Pour le moment, seulement les items de type file sont traités
// TODO : traiter le cas où les items type taggué sont des objects
exports.exploreThesaurusPactolsGeoItemNB = async function (req, res) {
  let { branche } = req.params,
    id = parseInt(req.params.idThes);

  // TODO : récupérer le projectId et limiter les folder à ceux du projet
  let { projectId } = req.query;

  try {
    //1 on récupère tous les folders concernés et le nombre d'item
    // TODO : completer la requete pour les items de type folder ou objets
    let query =
      `select json_agg(x) as folders_info FROM (SELECT id_folder, count(*) as nb_file, 0 as nb_object, 0 as nb_unico FROM ${branche}_file WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_pactols_geo_item WHERE  item_type = 'file' ` +
      ` AND id_thesaurus = $1) GROUP BY id_folder ` +
      `UNION ALL ` +
      `SELECT id_folder, 0 as nb_file, count(*) as nb_object, 0 as nb_unico FROM ${branche}_object WHERE id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_pactols_geo_item WHERE item_type = 'object' ` +
      ` AND id_thesaurus = $1 ) ` +
      ` GROUP BY id_folder  ` +
      `UNION ALL  ` +
      `SELECT id_folder, 0 as nb_file, 0 as nb_object, count(*) as nb_unico FROM ${branche}_unico u ` +
      `INNER JOIN ${branche}_file f ON f.id = u.id_file WHERE u.id IN ` +
      `(SELECT id_item FROM ${branche}_thesaurus_pactols_geo_item WHERE item_type = 'unico' ` +
      ` AND id_thesaurus = $1 ) ` +
      ` GROUP BY id_folder ) AS x`;

    const nbthesitemfolders = await db.oneOrNone(query, [req.params.idThes]);

    try {
      // 2 / on récupère le nom complet du concept en jeu

      const thesname = await db.oneOrNone(
        `SELECT nb_item, 'geo' as thesname  ` +
          `, ${branche}_get_title_from_project_for_doi(${projectId}) as projectName ` +
          `FROM ${branche}_thesaurus_pactols_geo WHERE id = $1 `,
        [req.params.idThes],
      );

      // 3/ on assemble les 2
      let result = Object.assign({}, thesname, nbthesitemfolders);
      // success
      res.status(200).sendData(result);
    } catch (e) {
      responseHelper.sendError(500, "server_error in getThesaurusPactolsGeoItemNB thesname", e, req, res);
    }
  } catch (e) {
    responseHelper.sendError(500, "server_error in getThesaurusPactolsGeoItemNB folders", e, req, res);
  }
};

//create url endpoint for /explorethesPactolsGeoPage/:branche,:idThes POST
// on le met en POST pour récupérer les infos offset et limit dans le body ainsi que le id_user pour récupérer seulement
// les item des folders autorisés
// TODO : traiter le cas des items de types object ou folder ...
exports.exploreThesaurusPactolsGeoItemPage = async function (req, res) {
  let id = req.params.idThes,
    { branche } = req.params,
    model = "project",
    nakala = 0,
    codeNom = 0;
  let { body } = req;
  // DONE : Mettre le id_user dans le body et envoyer la requete seulement sur les id_item autorisées par le id_user :
  //console.log(body)
  let offset = (parseInt(body.page) - 1) * body.pagination;
  //console.log('offset : '+offset)
  let limit = body.pagination;
  //console.log('limit : '+limit)
  let userId = parseInt(body.userId);

  let read = "",
    in_query = "";
  if (userId) {
    read = `SELECT get_access_gen( $1, '${branche}') as fold `;
  } else {
    read = `SELECT array_agg(id)  as fold FROM ${branche}_folder WHERE  status = 'public'  AND $1 = $1 `;
  }

  // Première requete : récupèrer la liste des folders autorisés par le user :
  // TODO : ils doivent être dans le projet !!!
  // récupérer l'id projet avec la table pft3d_folder_thesaurus_multi ??? ou le passer en parametre dans le body
  try {
    const userRead = await db.oneOrNone(read, userId);

    if (userRead.fold.length === 0) in_query = "";
    else
      in_query =
        ` AND i.id_item IN ` +
        `( SELECT id FROM ${branche}_file WHERE id_folder in ${JSON.stringify(userRead.fold)
          .replace("[", "(")
          .replace("]", ")")} ` +
        `UNION ALL ` +
        `SELECT id FROM ${branche}_object WHERE id_folder in ${JSON.stringify(userRead.fold)
          .replace("[", "(")
          .replace("]", ")")} ` +
        ` UNION ALL ` +
        ` SELECT id FROM ${branche}_unico WHERE id_file in (SELECT id FROM ${branche}_file WHERE id_folder in ${JSON.stringify(
          userRead.fold,
        )
          .replace("[", "(")
          .replace("]", ")")}) ` +
        ` )`;

    try {
      // Get id_metadata from nakala info and depot name (metadata rank 1)  info
      const startQ = await db.any(
        `SELECT id , code , rank from ${branche}_metadata WHERE id_metadata_model in ` +
          `(SELECT id from ${branche}_metadata_model where name = $1) `,
        model,
      );

      for (let i = 0; i < startQ.length; i++) {
        // on récupère l'id de la metadonne de rank 1 pour le nom du dépôt si c'est conservatoire, du projet si c'est projet
        if (startQ[i].rank === 1) codeNom = startQ[i].id;
        // on récupère la metadonnée de code nakala pour affichage de l'image ensuite
        if (startQ[i].code === "nakala") nakala = startQ[i].id;
      }
      try {
        let debut =
          `SELECT i.item_type, t.name, ff.folder_name, ` +
          `COALESCE(array_to_string(pnakala.value,''), '') AS nakala, ` +
          `COALESCE(array_to_string(pnom.value,''), '') AS depot_name, ` +
          `COALESCE(ffo.id, fi.id, o.id, u.id, 0) AS id, ` +
          `COALESCE(ffo.path, fo.path, fi.path, fu.path, '') AS path, ` +
          `COALESCE(ffo.name, fo.name, fi.name, u.name, '') AS filename, ` +
          `COALESCE(ff.id, fo.id_folder, fi.id_folder, fu.id_folder, o.id_folder, 0) AS idfolder, ` +
          `COALESCE(ffo.id, fo.id, fi.id, fu.id, 0) AS idfile, ` +
          `COALESCE(fi.file_ext, ffo.file_ext, fu.file_ext, '') AS extension, ` +
          `o.name as object_name, ff.nb_objects, ` +
          `u.x, u.y, u.width, u.height, ` +
          `u.type, u.polygon  `+
          `FROM ${branche}_thesaurus_pactols_geo t ` +
          `INNER JOIN ${branche}_thesaurus_pactols_geo_item i ON  i.id_thesaurus = t.id ` +
          `LEFT OUTER JOIN ${branche}_file fi ON fi.id = i.id_item AND i.item_type = 'file' ` +
          `LEFT OUTER JOIN ${branche}_folder ff ON ff.id = i.id_item AND i.item_type = 'folder' ` +
          `LEFT OUTER JOIN ${branche}_file ffo ON ffo.id = ff.id_representative_picture ` +
          `LEFT OUTER JOIN ${branche}_object o ON o.id = i.id_item AND i.item_type = 'object' ` +
          `LEFT OUTER JOIN ${branche}_file fo ON fo.id = o.id_file_representative ` +
          `LEFT OUTER JOIN ${branche}_unico u ON u.id = i.id_item AND i.item_type = 'unico' ` +
          `LEFT OUTER JOIN ${branche}_file fu ON fu.id = u.id_file ` +
          `LEFT OUTER JOIN ${branche}_passport pnakala ON pnakala.id_item = i.id_item AND pnakala.id_metadata = $1 ` +
          `LEFT OUTER JOIN ${branche}_passport pnom ON pnom.id_item = i.id_item AND pnom.id_metadata = $2 `;

        // On ne recupère que l'id thes pour un thesaurus particulier
        let general = "WHERE t.id = $3";
        let milieu = in_query;

        let fin = ` ORDER BY ff.folder_name OFFSET ${offset} LIMIT ${limit}`;

        let query = "";

        query = debut + general + milieu + fin;
        //console.log(query)
        thesitem = await db.any(query, [nakala, codeNom, id]);

        // success
        res.status(200).sendData(thesitem);
      } catch (e) {
        responseHelper.sendError(500, "server_error in exploreThesaurusPactolsGeoItemPage ", e, req, res);
      }
    } catch (e) {
      // error
      responseHelper.sendError(500, "server_error in exploreThesaurusPactolsGeoItemPage for nakala", e, req, res);
    }
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in exploreThesaurusPactolsGeoItemPage for userRead", e, req, res);
  }
};

// create url endpoint /ThesaurusPactolsGeoName/:root,:name GET
// charger le thesaurus sur les 3 premières lettres tapées pour l'autocomplete
// former un tableau avec label et value pour l'autocomplete en rajoutant l'id pour pouvoir indexer le mot
exports.getThesaurusPactolsGeoName = async function (req, res) {
  let name = decodeURI(req.params.name);
  let branch = req.params.root;

  try {
    const theso = await db.any(
      `SELECT DISTINCT name AS name,
                name AS value,
                concat(id, '_', id,'_',id) as id,
                name AS label
            FROM ${branch}_thesaurus_pactols_geo
            WHERE name ILIKE '%${name}%'
            ORDER BY name`,
    );

    res.status(200).sendData(theso);
  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus pactols geo name ", e, req, res);
  }
};

//create url endpoint for '/ThesaurusMultiConcept/:root,:thesaurus,:conceptId' DELETE
// Supprimer un concept d'un thesaurus multi récemment intégré , sans aucune indexation
// TODO : traiter le cas des items de types object ou folder ...
exports.deleteThesaurusMultiConcept = async function (req, res) {
  let { root } = req.params;

  let path = req.body.path
  let arrayPath = path.split('.')
  arrayPath.pop()
  // fix bug delete badly updated
  let newPath = arrayPath.toString().replaceAll(',', '.')

  try {
    const thesConcept = await db.any(
        `DELETE FROM ${root}_thesaurus_multi ` + `WHERE thesaurus = $1  AND id_thes = $2  RETURNING id`,
        [req.params.thesaurus, req.params.conceptId],
    );
    // mise à jour du parent s'il a perdu tous ses enfants: sa valeur de haschild passe de 1 à 0
    try {
      const hasChild = await db.oneOrNone(
          `SELECT count(*)  FROM ${root}_thesaurus_multi ` + `WHERE thesaurus = $1 AND thesaurus_path <@ $2::ltree
          AND id_thes != $3 `,
          [req.params.thesaurus, newPath, parseInt(req.body.id_parent)],
      );
      if (hasChild["count"] === "0") {
        console.log('supprimer le hasChild')
        try {
          const removeChild = await db.any(
              `UPDATE ${root}_thesaurus_multi SET haschild = NULL WHERE thesaurus = $1 AND  id_thes = $2 `,
              [req.params.thesaurus,  parseInt(req.body.id_parent)],
          );
          res.status(200).sendData(thesConcept);
        } catch (e) {
        responseHelper.sendError(500, "server_error in thesaurus multi DELETE concept ", e, req, res);
        }
      } else {
        res.status(200).sendData(thesConcept);
      }

    } catch (e) {
      responseHelper.sendError(500, "server_error in thesaurus multi DELETE concept ", e, req, res);
    }

  } catch (e) {
    responseHelper.sendError(500, "server_error in thesaurus multi DELETE concept ", e, req, res);
  }

};

// create url endpoint /thesaurusTreeMultiLng/:root,:thes,:idThes,:lng
exports.getThesaurusTreeMultiLng = async function (req, res) {
  let { root } = req.params;

  let { lng } = req.params;
  let query = "";
  let ORDER_BY_value = "global_rank";
  let thesaurus = req.params.thes;
  let thesaurus_path = req.params.idThes;
  // Cas particulier du thesaurusnotredame pour lequel on n'affiche que à partir de Nomenclature ??
  // Verrue  pas tenable ...
  if (thesaurus === "notredame") thesaurus_path = "1.28123.1000";

  if (thesaurus === "deposant") {
    // pas de hierarchie, on classe par nom et non par global_rank
    ORDER_BY_value = "name";
  }

  let name = lng === 'fr' ?  'name' : 'name_' + lng
  let query1 =
      ` SELECT id_thes as id, id::integer as unique_id, thesaurus_path as path, ${name} as name, short_name,nb_item::integer ,  ` +
      ` haschild as get_children , ` +
      ` nlevel(thesaurus_path) AS depth, id_parent, nb_tot_item::integer  ` +
      ` ,  global_rank ` +
      ` FROM ${root}_thesaurus_multi WHERE thesaurus = $1 `;
  let query2 = " AND  nb_tot_item != 0 ";

  let query3 = "AND nlevel(thesaurus_path) > 1 ";
  let query4 = ` AND thesaurus_path <@  $2::ltree ` + `ORDER BY ${ORDER_BY_value}`;

  if (root === "conservatoire3d" && thesaurus === "deposant") {
    query = query1 + query3 + query4;
  } else {
    query =
        query1 +
        // query2 +
        // query3 +
        query4;
  }

  // TODO : RACCOURCIR la requete TROP LONGUE
  try {
    const thesaurustreeMulti = await db.any(query, [req.params.thes, thesaurus_path]);
    // success
    res.status(200).sendData(thesaurustreeMulti);
  } catch (e) {
    // error
    responseHelper.sendError(500, "server_error in getThesaurusTreeMulti", e, req, res);
  }
};

// create URL endpoint / PATCH
// Mettre à jour une traduction du concept
exports.updateThesaurusTranslation = async function (req, res) {
  let { thesaurus } = req.params;
  let { root } = req.params;
  let { type } = req.params;
  let { idThes } = req.body;
  let { translation } = req.body;

  let lng = req.body.lng ? 'name_'+req.body.lng : 'ERROR'
  try {
    const rankNull = await db.any(
        `UPDATE ${root}_thesaurus_${type} SET ${lng} = $1 ` +
        `WHERE thesaurus = $2 AND id_thes = $3  `,
        [translation, thesaurus, idThes],
    );
    res.status(200).sendData(rankNull);
  } catch (e) {
    responseHelper.sendError(500, "server_error in PATCH thesaurus "+type+"  with translation ", e, req, res);
  }
};