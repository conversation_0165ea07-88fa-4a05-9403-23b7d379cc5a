/* Base styles for all screen sizes */
.containerV {
    width: 100%;
    position: relative;
    margin: 0 auto;
    padding: 15px 15px 0 15px;
}

/* Make the top row responsive */
.row:first-of-type {
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
}

.vitrine-leftcol,
.vitrine-details {
    box-sizing: border-box;
}

.vitrine-image-container,
.object-image-container {
    max-width: 100%;
    margin: 0 auto;
}

.visionneuseImg {
    max-width: 100%;
    height: auto;
    object-fit: contain;
}

/* Project image responsive styles */
.project-image {
    height: 4em;
    width: auto;
    max-width: 100%;
    object-fit: contain;
}

/* Container for project image */
.col-2 {
    min-width: fit-content;
    flex-shrink: 0;
}

.col-2 a {
    display: inline-block;
    max-width: 100%;
}

/* Medium screens */
@media (min-width: 769px) and (max-width: 1200px) {
    .project-image {
        height: 4em;
        min-height: 4em;
    }
}

/* Large screens */
@media (min-width: 1201px) {
    .project-image {
        height: 4em;
        min-height: 4em;
    }
}

img,
.card,
.btn,
.modal-body,
.modal-content {
    max-width: 100%;
    box-sizing: border-box;
}

/* Desktop layout (default) */
@media (min-width: 769px) {
    .containerV .row {
        display: flex;
        flex-wrap: nowrap;
    }

    .vitrine-leftcol {
        width: 60%;
        padding-right: 15px;
        height: calc(100vh - 3vh);
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .vitrine-leftcol {
        scrollbar-width: none;
    }

    .vitrine-details {
        width: 40%;
        padding-left: 15px;
        height: calc(100vh - 3vh);
        overflow-y: auto;
        position: sticky;
        top: 0;
    }

    /* Hide scrollbar but keep functionality */
    .vitrine-details::-webkit-scrollbar {
        display: none;
    }

    .vitrine-details {
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
}

/* Tablet layout */
@media (max-width: 992px) and (min-width: 769px) {
    .vitrine-leftcol,
    .vitrine-details {
        padding: 0 10px;
    }

    .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Mobile layout */
@media (max-width: 768px) {
    .containerV .row {
        flex-direction: column;
    }

    .vitrine-leftcol,
    .vitrine-details {
        width: 100%;
        max-width: 100%;
        padding: 0;
        height: auto;
        overflow-y: visible;
    }

    .vitrine-details {
        position: relative;
        margin-top: 20px;
    }

    .visionneuseImg {
        max-height: 60vh;
    }

    /* Project image responsive styles for mobile */
    .project-image {
        height: 3.5em;
        width: auto;
        max-width: 100%;
    }

    .col-2 {
        width: auto;
        max-width: 100%;
        margin-bottom: 10px;
    }

    .project-image-container {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    .preview-row {
        justify-content: center;
    }
    .lazy-load-card {
        width: 100px;
    }

    #modalImage {
        max-height: 80vh;
        width: auto;
    }

    .mt-3, .mt-4 {
        margin-top: 1rem !important;
    }

    .mb-3, .mb-4 {
        margin-bottom: 1rem !important;
    }
}

.modal-backdrop {
    width: 100vw;
    height: 100vh;
}

.modal-content-transparent {
    background-color: transparent;
    border: none;
}

.modal-body-zoomout {
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
}

.image-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    overflow: auto;
}

#associated-objects-container,
#associated-files-container {
    margin-top: 1.5rem;
}

.btn-toggle-section {
    background-color: transparent;
    border: none;
    padding: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-toggle-section i {
    transition: transform 0.3s ease;
    font-size: 1.5rem;
}

.vitrine-text-center {
    margin-bottom: 1rem;
}

.d-flex.justify-content-center.mt-3.mb-3 {
    flex-wrap: wrap;
    gap: 0.5rem;
}
