<form id="<%= id %>" action="<%= route_url %>" method="post">
    <div class="col-6 offset-3 d-flex flex-column">
        <label for="project" class="col-4"><%= __('admin.content.BranchSelection') %></label>
        <select class="form-select select_project" name="<%= label %>" id="<%= label %>">
            <% for (const branch of branchList) { %>
            <option value="<%= branch.branchename %>"
                    <% if ((typeof rootproj != 'undefined') && (rootproj === branch.branchename)) { %>selected<% } %>>
                <%= branch.description %>
            </option>
            <% } %>
        </select>
    </div>

    <div class="d-flex justify-content-center my-3 gap-2">
        <input class="btn btn-secondary" type="submit" value="<%= __('submit') %>" />
        <a type="button" href="<%= route_url %>" class="btn btn-outline-secondary"><%= __('cancel') %></a>
    </div>
</form>