/* Project image styles */
.project-image {
    height: 4em;
    width: auto;
    max-width: 100%;
    object-fit: contain;
    margin-left: 0;
    margin-bottom: 0;
    margin-top: 0;
}

/* Project image container styles */
.project-image-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

/* Menu right styles */
.menu-right-link {
    font-size: small;
    margin-top: 2em;
}

.object-icon {
    width: 3.3%;
}

/* Object name text alignment */
.vitrine-text-center {
    text-align: center;
}

/* Column styles */
.containerV .row {
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
}

.vitrine-leftcol {
    padding: 0;
    margin: 0;
    width: 60%;
    flex-shrink: 0;
    height: calc(100vh - 3vh);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.vitrine-details {
    height: calc(100vh - 3vh);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    position: sticky;
    top: 0;
    margin-top: 0;
    width: 40%;
    padding-left: 20px;
}

/* Column resizer styles */
.column-resizer {
    width: 1px;
    background-color: var(--bs-body-color);
    cursor: ew-resize;
    position: relative;
    margin: 0 2px;
    height: calc(100vh - 3vh);
    transition: all 0.2s ease;
    z-index: 10;
    opacity: 0.25;
}

.column-resizer::before,
.column-resizer::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 3px;
}

.column-resizer::before {
    right: 100%;
}

.column-resizer::after {
    left: 100%;
}

.column-resizer:hover {
    opacity: 1;
    transform: scaleX(3);
}

.column-resizer:active {
    opacity: 1;
    transform: scaleX(3);
}

/* Responsive styles for mobile and smaller screens */
@media (max-width: 768px) {
    .containerV .row {
        flex-direction: column;
        flex-wrap: wrap;
    }

    .vitrine-leftcol,
    .vitrine-details {
        width: 100% !important;
        max-width: 100%;
        transition: none;
    }

    .column-resizer {
        display: none;
    }

    .vitrine-details {
        height: auto;
        max-height: none;
        position: relative;
        margin-top: 20px;
        padding-left: 0;
    }

    .visionneuseImg {
        max-height: 60vh;
        max-width: 100%;
        width: auto;
        object-fit: contain;
    }
}

.vitrine-details::-webkit-scrollbar,
.vitrine-leftcol::-webkit-scrollbar {
    display: none;
}

/* Object image container styles */
.object-image-container {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 0 auto;
    padding: 0;
}

.object-image-container img {
    max-height: 70vh;
    max-width: 100%;
    object-fit: contain;
    cursor: pointer;
    width: auto;
    margin: 0 auto;
}

/* Default object image */
.default-object-image {
    width: 50px;
}

/* 3D model thumbnail styles */
.model-thumbnail {
    max-height: 150px;
}

/* Associated items card styles */
.lazy-load-card {
    width: 135px;
    margin: 0 !important;
    padding: 5px 5px 0 5px !important;
    transition: all 0.2s ease;
    border: none !important;
    border-radius: 4px !important;
    pointer-events: auto;
    height: auto !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    cursor: pointer !important;
    background-color: #f8f9fa !important;
    overflow: hidden !important;
}

.lazy-load-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25) !important;
}

.lazy-image-container {
    margin-top: 5px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lazy-image-container img,
.lazy-image {
    max-height: 80px;
    object-fit: contain;
}

/* Expandable sections styles */
.btn-toggle-section {
    background-color: transparent;
    border: none;
    padding: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-toggle-section i {
    transition: transform 0.3s ease;
    font-size: 1.5rem;
}

.count-indicator {
    font-size: 0.85rem;
    color: #666;
}

.preview-row {
    min-height: 100px;
    margin-bottom: 10px;
}

/* DOI button styles */
.doi-button {
    border: 2px solid #242943;
    border-radius: 5px;
}

/* DOI image */
.doi-image {
    width: 1em;
}

/* License text */
.license-text {
    font-size: smaller;
    color: #000000;
}

/* Multi tag button */
.btn-multi-tag {
    margin-top: -9px;
}

/* Unico table styles */
#unicos-table ul {
    padding-left: 0;
}

#unicos-table .tag-value {
    font-weight: bold;
}

/* Unico image styles */
.unico-image {
    max-width: 150px;
    max-height: 150px;
    object-fit: contain;
}

.unico-svg {
    max-width: 150px;
    max-height: 150px;
}

/* Map container styles */
.map-container {
    height: 500px !important;
    width: 100% !important;
    margin-bottom: 15px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    border: 1px solid #ddd;
}

/* Leaflet map specific styles */
.leaflet-container {
    background: #f8f8f8;
    outline: none;
}

.leaflet-control-attribution {
    font-size: 10px;
}

/* Responsive map styles */
@media (max-width: 768px) {
    .map-container {
        height: 300px;
        max-height: 350px;
    }
}

/* Thesaurus name */
.thesaurus-name {
    font-weight: bold;
}

/* Alert styles */
.d-none {
    display: none !important;
}

/* Container styles for independent scrolling */
.containerV {
    width: 100%;
    position: relative;
    margin-top: 0;
    display: flex;
    flex-direction: column;
}

.containerV .row {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
    margin-top: 20px;
}

/* Ensure all content stays within its container */
.vitrine-image-container,
.object-image-container,
.lazy-image-container,
.card,
.btn,
.modal-body,
.modal-content {
    max-width: 100%;
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* 3D Model Section Styles */
.model3d-section {
    border: solid 1px #e6f8ff;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    margin-top: 20px;
    transition: all 0.3s ease;
  }

  .model3d-section h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
  }

  .model3d-section .model-thumbnail {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    transition: transform 0.2s ease;
  }

  .model3d-section .model-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

.mt-2 {
    margin-top: 0px !important;
    margin-left: 10px !important;
}

/* Vitrine button styles */
.btn-vitrine-primary,
.btn-vitrine-secondary {
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

/* Base styles for hollow buttons */
.btn-vitrine-secondary {
    background-color: transparent;
    border: 1px solid;
}

/* Responsive styles for mobile devices */
@media (max-width: 768px) {
    .containerV .col-md-8,
    .containerV .col-md-4.vitrine-details {
        height: auto;
        max-height: none;
        overflow-y: visible;
        position: static;
    }

    .containerV .row {
        flex-wrap: wrap;
    }

    .containerV .col-md-4.vitrine-details {
        margin-top: 20px;
        width: 100%;
    }

    .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    .preview-row {
        justify-content: center;
    }
    .lazy-load-card {
        width: 100px;
    }

    #modalImage {
        max-height: 80vh;
        max-width: 100%;
        width: auto;
        object-fit: contain;
    }
}
