<script type='text/javascript'>
    let indice = 0;
    function addFields(column, type, c ){
        //add one input for type AND one input for value !
        indice = indice +1 ;
        console.log(indice)
        //add one input
        // Container <div> where dynamic content will be placed
        let container = document.getElementById("parent_"+c)

        // Create an <input> element, set its type and name attributes
        let rowtype = document.createElement("div")
        rowtype.className = 'row form-group'
        container.appendChild(rowtype)
        let labelt = document.createElement("label")
        labelt.className = 'col-sm col-form-label'
        labelt.innerHTML = 'Nom du type de l\'identifiant'
        rowtype.appendChild(labelt)
        let sm6 = document.createElement("div")
        sm6.className = 'col-sm'
        rowtype.appendChild(sm6)

        let inputt = document.createElement(type);
        inputt.type = "text";
        inputt.className = "form-control"
        inputt.placeholder = 'ROR, ISNI, ...'
        inputt.name = column+'#'+indice;
        sm6.appendChild(inputt);
        let sm2 = document.createElement("span")
        sm2.className = 'col-sm'
        rowtype.appendChild(sm2)
        let minus = document.createElement("a")
        minus.onclick = function () {
            rowtype.parentElement.removeChild(rowtype)
            rowvalue.parentElement.removeChild(rowvalue)
            this.parentElement.removeChild(this);
        };
        minus.href = "#"
        sm2.appendChild(minus)
        let ic = document.createElement("i")
        ic.className = 'fa fa-minus-circle'
        //        ic.aria-hidden = 'true'
        minus.appendChild(ic)

        let rowvalue = document.createElement("div")
        rowvalue.className = 'row form-group'
        container.appendChild(rowvalue)
        let labelv = document.createElement("label")
        labelv.className = 'col-sm-4 col-form-label'
        labelv.innerHTML = 'Valeur de l\'identifiant (URI)'
        rowvalue.appendChild(labelv)
        let sm10 = document.createElement("div")
        sm10.className = 'col-sm-6'
        rowvalue.appendChild(sm10)
        let input2 = document.createElement(type);
        input2.type = "url";
        input2.placeholder = 'URI complète : http....'
        input2.className = "form-control"
        input2.name = column+'#'+indice;
        sm10.appendChild(input2);
        //}
    }
</script>
<div id="editorganization">

    Quelle organisation voulez-vous éditer ?

    <form class="form-horizontal" id="editorganization" action="/admin/editOrganization" method="post">
        <div class="form-group row">
            <label for="model" class="col-sm-2 col-form-label"><%=__('organization')%> : </label>
                <div class="col-sm-2">
                    <select name="organizationSelect" id="organizationSelect">
                        <% for (i = 0; i < organizations.length ; i++) {  %>
                        <option value="<%= organizations[i]['id'] %>" <% if (organizations[i]['id'] === parseInt(organizationid)) { %>selected<% } %>><%= organizations[i]['name'] %></option>
                        <% } %>
                    </select>
                </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-5 col-sm-7">
                <input class="btn btn-secondary" type="submit" value="Choisir cette organisation" />
                &nbsp;<a href="/admin/editOrganization" class="btn btn-outline-secondary" ><%=__('cancel') %></a>
            </div>
        </div>
    </form>

</div>
<hr>


<% if (message === '') { %>
<div class="container" id="editorganizationid">
<% if (organization !=='') { %>
    <form class="form-horizontal" id="addorganization" action="/admin/editOrganization" method="post">
        <div class="form-group">
            <div class="col-sm-offset-5 col-sm-7">
                <input class="btn btn-secondary" type="submit" value="Valider les modifications pour cette organisation" />
                &nbsp;<a href="/admin/editOrganization" class="btn btn-outline-secondary" ><%=__('cancel') %></a>
            </div>
        </div>

    <% let c = 0; for (item in organization ) { c++; %>
        <% if (item === 'id') { %>
        <input type="hidden" value="<%= organization[item]%>" name="organizationSelect" id="organizationSelect" />
        <% } else if (item === 'id_entity') { %>
            <input type="hidden" value="<%= organization[item]%>" name="id_entity" id="id_entity" />
        <% } else if (item !== 'organization'){ %>
        <div class="form-group row">
            <label for="<%= item %>" class="col-sm-4 col-form-label">
                <% for (let i = 0; i <  col.length; i++ ) { %>
                <% if (col[i]['column_name'] === item) {%><%= col[i]['label_fr'].charAt(0).toUpperCase() +  col[i]['label_fr'].slice(1) %><% } %>
                <% } %>
            </label>
            <div class="col-sm-8" id="parent_<%= c %>">
                <% if (item === 'identifier') { if (organization[item]) { %>
                <% for (let t = 0; t < organization[item].length; t++) { %>
                    <div class="form-group row">
                    <label for="<%= organization[item][t]['type'] %>" class="col-sm-2 col-form-label">
                        <%= organization[item][t]['type'] %>
                    </label><div class="col-sm-6">
                    <input type="<% if (organization[item][t]['type'].toLowerCase() !== 'orcid') { %>url<% } else { %>text<% } %>" class="form-control" value="<%= organization[item][t]['value']%>" name="ident_<%= organization[item][t]['type'] %>" id="ident_<%= organization[item][t]['type'] %>"  />
                        </div>
                    </div>
                <% } %>

                <% } else { %>
                <%# il n'y a aucun udentifiant proposer VIAF  et de quoi ajouter un autre couple de valeur (bouton addFields... %>
                    <div class="form-group row">
                        <label for="typeVIAF" class="col-sm-2 col-form-label">
                            VIAF
                        </label><div class="col-sm-6">
                            <input type="url"  class="form-control" name="VIAF" id="VIAF" placeholder=" http://viaf.org/viaf/..." />
                        </div>
                    </div><% } %>
                    <div class="form-group row" id="parent_1">
                        <label for="identifierAutre" class="col-sm-5 col-form-label">
                            Autre type d'identifiant
                        </label>
                        <div class="col-sm-3">
                            <%# on appelle la function avec le nom de la colonne à modifier : ici item = identifier%>
                            <a href="#" id="plus" onclick="addFields('<%= item %>', 'input', <%= c %>)"><i class="fa fa-plus-circle" aria-hidden="true"></i></a>
                        </div>

                    </div>

                <% } else { %>
                <input type="text" class="form-control" value="<%= organization[item]%>" name="<%= item %>" id="<%= item %>" <% if ((item ==='name') || (item === 'country')) {%>required<% } %> />
                <% } %>
            </div>
        </div>
        <% } %>
    <% } %>

    </form>

<% } %>
</div>
<% } %>