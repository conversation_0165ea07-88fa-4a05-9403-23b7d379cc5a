<% if (typeof disableSelection === "undefined") {
    var disableSelection = false
} %>


<div id="explore-div" class="d-flex container flex-column col-0" style="display: none !important;">
    <div class="d-flex flex-column mb-2">
        <span id="nb-results"></span>
        <div class="d-flex align-items-baseline justify-content-between">
            <div id="display-menu" class="d-flex flex-wrap align-items-center gap-2">
                <% if (!disableSelection) { %>
                <div class="input-group group-borders">
                    <div class="input-group-addon">
                        <div class="input-group-text"><%=__('documentsDisplayed')%> :</div>
                    </div>

                    <select id="nb-results-page" class="btn-secondary btn-hand">
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>

                <div class="input-group group-borders">
                    <button id="grid-btn" class="btn btn-secondary" type="button" title="<%=__('displayGrid')%>"
                            onclick="switchDisplay('grid')" disabled><i class="fas fa-th-large fa-lg"></i></button>
                    <button id="list-btn" class="btn btn-secondary btn-hand" type="button"
                            title="<%=__('displayList')%>" onclick="switchDisplay('list')"><i
                           class="fas fa-list-ul fa-lg"></i></button>
                </div>

                <% if (user.id) { %>
                <div id="item-selection" class="d-flex flex-column">
                    <div>
                        <%=__('documentsSelected')%> : <span id="nb-selected">0</span>
                    </div>

                    <span>
                        <button type="button" class="fakelink btn btn-sm btn-outline-secondary" onclick="checkAll()"><%=__('checkPage')%></button>
                        <button type="button" class="fakelink btn btn-sm btn-outline-secondary" onclick="uncheckAll()"><%=__('uncheckPage')%></button>
                        <button type="button" class="fakelink btn btn-sm btn-outline-secondary" onclick="resetSelection()"><%=__('resetSelection')%></button>
                    </span>
                </div>
                <% } %>

                <div id="item-index-all">
                    <button id="index-all-btn" class="btn btn-secondary" type="button" title="<%=__('index')%>"
                            onclick=""><i
                           class="fas fa-edit"></i> <%=__('enrichData')%></button>
                    <button id="tag-all-btn" class="btn btn-secondary" type="button" title="<%=__('keyword')%>"
                            onclick="window.location =`/keyword,<%= branch %>,0,0,${explorePage.id}`;"><i
                           class="fas fa-tag"></i> <%=__('addKeyword.title')%></button>
                    <button id="delete-all-btn" class="btn btn-secondary" type="button" title="<%=__('deleteAll')%>"
                            onclick="deleteAll(explorePage.id, 'fr');"><i class="fa fa-trash ml-1"></i>
                        <%=__('deleteAll')%></button>
                </div>
                <% } %>
            </div>

            <nav class="ml-auto">
                <ul class="pagination m-0"></ul>
            </nav>
        </div>
    </div>
    <div id="explore-results" class="d-flex flex-column mb-2"></div>
</div>