<% if ( (data.length) &&  (locals.virtualFolders || locals.objectsFolders)) { %>
<hr class="mt-2 mb-1" style="width: 100%;">
<% if ((data.length) && (locals.virtualFolders)) { %>
<form action="/addSelectionToVirtualFolder/<%=user.id%>,<%=projectId%>" method="post">
    <div class="px-2 mb-3 d-flex flex-wrap align-items-center justify-content-between column-gap-4">
        <div class="form-text fs-6"><%= __('txt_integrate')%></div>
        <div class="d-flex gap-4">
            <select name="virtualFolder" size="1" class="form-select" style="max-width: 48rem;">
                <% for (let j = 0; j < virtualFolders.length; j++) { %>
                    <option value="<%=virtualFolders[j]['id']%>"><%=virtualFolders[j]['completename']%></option>
                <% } %>
            </select>
            <input class="btn btn-secondary btn-sm" type="submit" value="<%=__('txt_integrate_button')%>" />
        </div>
    </div>
</form>
<% } %>
<% if ((data.length) && (locals.objectsFolders)) { %>
<form action="/addSelectionToVirtualObject/<%=user.id%>,<%=projectId%>" method="post">
    <div class="px-2 mb-3 d-flex flex-wrap align-items-center justify-content-between column-gap-4">
        <div class="form-text fs-6"><%= __('txt_integrate_obj')%></div>
        <div class="d-flex gap-4">
            <select name="idObject" size="1" class="form-select" style="max-width: 48rem;">
                <% for (let j = 0; j < objectsFolders.length; j++) { %>
                <option value="<%=objectsFolders[j]['id']%>">
                    <%=objectsFolders[j]['completename']%>/<%=objectsFolders[j]['name']%></option>
                <% } %>
            </select>
            <input class="btn btn-secondary btn-sm" type="submit" value="<%=__('txt_link_button')%>" style="width: 4rem;" />
        </div>
    </div>
</form>
<% if ((data.length) && (data.length < 2) && (data[0]['item_type'] === 'file')) { %><%# il n'y a qu'une seule sélection de type fichier, on propose d'en faire une illustration pour un objet%>
<form action="/addSelectionToIllustrateObject/<%=user.id%>,<%=projectId%>" method="post">
    <div class="px-2 mb-3 d-flex flex-wrap align-items-center justify-content-between column-gap-4">
        <input type="hidden" value="<%= data[0]['id_item']%>" name="idFile">
        <div class="form-text"><%= __('txt_illustrate_obj')%></div>
        <div class="d-flex gap-4">
            <select name="idObject" size="1" class="form-select" style="max-width: 48rem;">
                <% for (let j = 0; j < objectsFolders.length; j++) { %>
                <option value="<%=objectsFolders[j]['id']%>">
                    <%=objectsFolders[j]['completename']%>/<%=objectsFolders[j]['name']%></option>
                <% } %>
            </select>
            <input class="btn btn-secondary btn-sm" type="submit" value="<%=__('txt_illustrate_button')%>"
                   style="width: 4rem;" />
        </div>
    </div>
</form>
<% } } %>
<hr class="mb-2 mt-1" style="width: 100%;">
<% } %>

<% if ( data.length > 0 ) { %>
<div id="explore-grid">
    <% for (let i = 0; i <  data.length; i++) { %>
    <% let item = data[i] %>
    <% let type = '' %>
    <% if (item.item_type === 'file') { %>
    <% type = 'i' %>
    <% } else if (item.item_type === 'object') { %>
    <% type = 'o' %>
    <% } else if (item.item_type === 'unico') { %>
    <% type = 'u' %>
    <% } else { %>
    <% type = 'd' %>
    <% } %>

    <div id="<%= `item-${type}-${item.id_item}_${item.idfolder}` %>" class="card col-12 text-center check_item">
        <div class="card-body <% if ( ((selection && user.id) && (user.user_status !== 'guest')) || (item.check)) { %>see-select<% } %> border-primary border-1 rounded-2 m-2 update-item-selector"
        <% if ((selection && user.id)  && (user.user_status !== 'guest')) { %>
            onClick="updateItemSelection(<%= item.id_item %>, '<%= item.item_type %>', <%= item.idfolder %>)"
        <% } %>
        >
            <div class="card-text text-center">
                <% if (branch === 'corpus') { %>
                    <% if (data[i]['title']) { %><small><%= data[i]['title']%></small>
                    <% } else { %>
                            <% if ((data[i]['name']) && (data[i].item_type !== 'object')) { %>
                                <% if (data[i]['name'].length > 35) { %>
                                    <small><%= data[i]['name'].substring(0, 35)%>...</small>
                                <% } else { %>
                                    <small><%= data[i]['name']%></small><% } %>
                            <% } else if ((data[i]['name']) && (data[i].item_type === 'object')) { %>
                                <% if (data[i]['object_name']) { if (data[i]['object_name'].length > 35) { %>
                                    <small><%= data[i]['object_name'].substring(0, 35)%>...</small>
                                <% } else { %>
                                    <small><%= data[i]['object_name']%></small><% } } else { %>
                                    <% if (data[i]['name'].length > 35) {%>
                                        <small><%= data[i]['name'].substring(0, 35)%>...</small>
                                    <% } else { %>
                                        <small><%= data[i]['name']%></small><% } }%>
                            <% } else { %>
                                <small></small>
                            <% } %>
                    <% } %>
                <% } else {%>
                    <% if ((data[i]['name']) && (data[i].item_type !== 'object')) { %>
                    <% if (data[i]['name'].length > 35) { %>
                    <small><%= data[i]['name'].substring(0, 35)%>...</small>
                    <% } else { %>
                    <small><%= data[i]['name']%></small><% } %>
                    <% } else if ((data[i]['name']) && (data[i].item_type === 'object')) { %>
                    <% if (data[i]['object_name']) { if (data[i]['object_name'].length > 35) { %>
                    <small><%= data[i]['object_name'].substring(0, 35)%>...</small>
                    <% } else { %>
                    <small><%= data[i]['object_name']%></small><% } } else { %>
                    <% if (data[i]['name'].length > 35) {%>
                    <small><%= data[i]['name'].substring(0, 35)%>...</small>
                    <% } else { %>
                    <small><%= data[i]['name']%></small><% } }%>
                    <% } else if (data[i].item_type === 'object' ) { %>
                    <small><%= data[i]['object_name']%></small>
                    <% } else { %>
                    <small></small><% } %>
                <% } %>
            </div>
        </div>

        <a href="#image_<%= data[i]['id_item'] %>" class="explore-card-img overflow-hidden" data-bs-toggle="modal" title="<%if (data[i]['item_type'] === 'object') {%><%=__('object')%> <% } %><%= data[i]['name'] %>">
            <%# si c'est un objet sans file repr, au lieu d'affcicher not found, on donne l'image d'un objet 3D (cube) par defaut %>
            <% if ((data[i]['item_type'] === 'object') && ( data[i]['previsu'])) { %>
            <img class="card-img mx-auto d-block" src="<%= data[i]['previsu'] %>" loading="lazy"
                 alt="<%= data[i]['name'] %>">
            <% } else if ((data[i]['item_type'] === 'object') && (( data[i]['idfile'] === '0' ) || (!data[i]['idfile'])) ) { %>
            <img class="card-img mx-auto d-block" src="/assets/images/default_repre_image_object.png" loading="lazy"
                 alt="<%= data[i]['name'] %>">
            <% } else if (data[i]['item_type'] === 'unico' && data[i].width && data[i].height) { %>
            <% if (data[i]['type'] === 'rect') { %>
            <img class="card-img mx-auto d-block"
                 src="<%= `/crop/${data[i].idfolder}_${data[i].idfile}/${data[i].x},${data[i].y},${data[i].width},${data[i].height}` %>"
                 loading="lazy" alt="<%= data[i]['name'] %>" style="max-width: 512px;">
            <% } else if (data[i]['type'] === 'poly') { %>
            <% let points = data[i].polygon.split(' ') %>
            <% for (let j in points) { let e = points[j].split(','); e[0] = parseInt(e[0]) - data[i].x; e[1] = parseInt(e[1]) - data[i].y; points[j] = e.join(',') } %>
            <% points = points.join(' ') %>
            <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                 class="card-img mx-auto d-block" viewBox="0 0 <%- data[i].width %> <%- data[i].height %>"
                 id="unico-svg-<%- data[i]['id_item'] %>">
                <mask id="svgmask-<%- data[i]['id_item'] %>">
                    <polygon fill="#ffffff" points="<%- points %>" />
                </mask>
                <image xmlns:xlink="http://www.w3.org/1999/xlink"
                       xlink:href="<%= `/crop/${data[i].idfolder}_${data[i].idfile}/${data[i].x},${data[i].y},${data[i].width},${data[i].height}` %>"
                       width="<%- data[i].width %>" height="<%- data[i].height %>"
                       mask="url(#svgmask-<%- data[i]['id_item'] %>)" style="max-width: 512px;" />
            </svg>
            <% } %>
            <% } else { %>
            <img class="card-img mx-auto d-block" src="/thumb/<%= data[i]['idfolder'] %>_<%= data[i]['idfile'] %>"
                 loading="lazy" alt="<%= data[i]['name'] %>">
            <% } %>
        </a>

        <div class="d-flex justify-content-evenly m-2">
            <% if (data[i].item_type === 'file') { %>
                <a href="#" ><i class="fas fa-file btn-hand" title="<%= __('file') %>" style="cursor: help;"></i></a>
            <% } else if (data[i].item_type === 'object') { %>
                    <a href="#" ><i class="fas fa-cube btn-hand" title="<%= __('object') %>" style="cursor: help;"></i></a>
            <% } else { %><a href="#" >
                    <i class="fas fa-file-image btn-hand" title="<%=data[i].item_type%>" style="cursor: help;"></i></a>
            <% } %>

            <% if (data[i]['idfile'] !== null) { %>
                <% if (user.id !== 0 ) { %>
                    <% if (data[i]['item_type'] === 'object') { %>
                        <a href="/comment,<%=branch%>,<%= data[i]['idfolder']%>,<%= data[i]['id']%>,object" title="<%= __('comment') %>">
                            <i class="far fa-comments"></i>
                        </a>
                        <a href="#" onclick="duplicateItem('<%=branch%>','object',<%= data[i]['id'] %>)">
                            <i class="fas fa-clone"></i>
                        </a>
                    <% } else { %>
                        <a href="/comment,<%=branch%>,<%= data[i]['idfolder']%>,<%= data[i]['idfile']%>,file" title="<%= __('comment') %>">
                            <i class="far fa-comments"></i>
                        </a>
                    <% } %>
                <% } %>
                <!-- <span id="<%=branch%>_<%= data[i]['idfile'] %>"></span> -->
                <% if (data[i]['item_type'] === 'file') { %>
                    <a href="/visionneuse,<%= data[i]['idfile'] %>-<%= data[i]['idfolder']%>,<%=branch%>-p,<%= WRights %>">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                <% } else if (data[i]['item_type'] === 'object') { %>
                    <a href="/visionneuseObj,<%= data[i]['id_item'] %>,<%= data[i]['idfile'] %>-<%= data[i]['idfolder']%>,<%=branch%>-p,<%= WRights %>">
                       <i class="fas fa-external-link-alt"></i>
                    </a>
                <% } else if (data[i]['item_type'] === 'unico') { %>
                    <a href="/visionneuseUnicoV2,<%= data[i]['id_item'] %>,<%= data[i]['idfolder']%>,<%=branch%>-p,<%= WRights %>">
                       <i class="fas fa-external-link-alt"></i>
                    </a>
                <% } %>
                <% if (data[i]['write']) { %>
                    <a href="/edit,<%=branch%>,<%= data[i]['model'] %>,<%= data[i]['item_type'] %>,<%= data[i]['idfile'] %>,<%= data[i]['idfolder'] %>" title="<%=__('enrichData')%>">
                       <i class="far fa-file-alt"></i>
                    </a>
                <% } %>
                <% if (exploreType === 'selection') { %>
                    <a href="javascript:" onclick="exploreFolder(<%= data[i]['fav_folder'] %>)" title="explore folder">
                        <i class="fas fa-folder"></i>
                    </a>
                <% } %>
            <% } %>

            <% if (user.id) { %>
                <% if (locals.virtual) { %>
                    <% if (item.item_type === 'object') { %>
                        <% if (((item.user_link) && (item.user_link === user.id)) || (user.user_status === 'admin')) { %>
                            <a href="javascript:" onclick="deleteItem(<%= item.id_item %>, '<%= item.item_type %>', <%= item.fid %>)" title="<%= __('Delete') %>">
                                <i class="fa fa-trash" aria-hidden="true"></i>
                            </a>
                        <% } %>
                    <% } else { %>
                        <a href="javascript:" onclick="deleteItem(<%= item.id_item %>, '<%= item.item_type %>', <%= item.fid %>)" title="<%= __('Delete') %>">
                            <i class="fa fa-trash" aria-hidden="true"></i>
                        </a>
                    <% } %>
                <% } else { %>
                    <% if (item.item_type === 'object') { %>
                        <% if (((item.user_link) && (item.user_link === user.id)) || (user.user_status === 'admin')) { %>
                            <a href="javascript:" onclick="deleteItemReal(<%= item.id_item %>, '<%= item.item_type %>', <%= item.fid %>)" title="<%= __('Delete') %>">
                                <i class="fa fa-trash" aria-hidden="true"></i>
                            </a>
                        <% } %>
                    <% } %>
                <% } %>
            <% } %>
        </div>
    </div>
    <% } %>

    <%#  mise en place de la visionneuse modale (autant de visionneuse que de contenu...) %>
    <% for (let i = 0; i < data.length; i++) { let image = 0; %>
    <div class="modal" id="image_<%= data[i]['id_item']%>" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    <h5 class="modal-title w-100"><% if (data[i]['item_type'] === 'object') {%><%=__('object')%>
                        <%= data[i]['object_name']%>
                        <% } else if (data[i]['item_type'] === 'unico') { %> Unico <%= data[i]['name']%>
                        <% } else { if (branch === 'corpus') {%><%= data[i]['title']%><% } else {%><%= data[i]['name']%><% }} %></h5>
                    <a href="javascript:"
                       onclick="switchModalMulti('text','<%= data[i]['item_type'] %>',<%= data[i]['id_item'] %>,<%=data[i]['idfolder']%>,'<%=branch%>','<%= data[i]['idfile'] %>', '<%= WRights %>')"
                       title="metadata"><i class="fas fa-file-alt fa-2x"></i></a>
                </div>
                <div class="modal-body" style="display:flex; align-items:center">
                    <div class="superpose"><div class="image_wrapp">
                <% if (data[i]['idfile'] !== 0 ) { %>
                    <% if (data[i]['extension'] === '3d') { %>
                    <a id="a-card-img-center"
                       href="/viewer3d,<%= data[i]['hash_3d']%>,<%= data[i]['item_folder'] %>,<%= branch %>"
                       target="_blank">
                        <% } else { %>
                        <a id="a-card-img-center" href="/viewer/<%= data[i]['idfolder']%>_<%= data[i]['idfile']%>"
                           target="_blank">
                            <% } %>
                            <% } %>
                            <% if ((data[i]['item_type'] === 'object') && ( data[i]['previsu'])) { %>
                            <img class="mx-auto d-block" src="<%= data[i]['previsu'] %>" loading="lazy"
                                 alt="<%= data[i]['name'] %>" style="height: 95%;">
                            <% } else if ((data[i]['item_type'] === 'object') && ( data[i]['idfile'] === 0 )) { %>
                            <img class="mx-auto d-block" src="/assets/images/default_repre_image_object.png"
                                 loading="lazy" alt="<%= data[i]['name'] %>">
                            <% } else if (data[i]['item_type'] === 'unico')  { %>
                            <% if (data[i]['type'] === 'rect') { %>
                            <img class="mx-auto d-block"
                                 src="<%= `/crop/${data[i].idfolder}_${data[i].idfile}/${data[i].x},${data[i].y},${data[i].width},${data[i].height}` %>"
                                 loading="lazy" alt="<%= data[i]['name'] %>" style="max-width: 512px;">
                            <% } else if (data[i]['type'] === 'poly') { %>
                            <% let points = data[i].polygon.split(' ') %>
                            <% for (let j in points) { let e = points[j].split(','); e[0] = parseInt(e[0]) - data[i].x; e[1] = parseInt(e[1]) - data[i].y; points[j] = e.join(',') } %>
                            <% points = points.join(' ') %>
                            <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                                 class="mx-auto d-block" width="100%" height="100%"
                                 viewBox="0 0 <%- data[i].width %> <%- data[i].height %>"
                                 id="modal-unico-svg-<%- data[i]['id_item'] %>">
                                <mask id="modal-svgmask-<%- data[i]['id_item'] %>">
                                    <polygon fill="#ffffff" points="<%- points %>" />
                                </mask>
                                <image xmlns:xlink="http://www.w3.org/1999/xlink"
                                       xlink:href="<%= `/crop/${data[i].idfolder}_${data[i].idfile}/${data[i].x},${data[i].y},${data[i].width},${data[i].height}` %>"
                                       width="<%- data[i].width %>" height="<%- data[i].height %>"
                                       mask="url(#modal-svgmask-<%- data[i]['id_item'] %>)" style="max-width: 512px;"/>
                            </svg>
                            <% } %>
                            <% } else { %>
                            <img class="mx-auto d-block" src="/small/<%= data[i]['idfolder']%>_<%= data[i]['idfile'] %>"
                                 loading="lazy" alt="<%= data[i]['name']%>">
                            <% } %>
                            <% if (data[i]['idfile'] !== 0 ) { %><img src="/assets/images/pleinecran.png" class="image_superpose"></a><% } %>


                        </div></div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <% if (i === 0) { %>
                    <button type="button" class="btn btn-primary mr-auto prev-modal"
                            onclick="loadPage(<%=parseInt(datapost['page'])-1%>, 'image')">
                        <i class="fas fa-arrow-alt-circle-left"></i></button>
                    <p><% if (data[i]['item_type'] === 'object') {%><%=__('object')%> <%= data[i]['object_name'] %><% } else { %><%= data[i]['name']%><% } %></p>
                    <button type="button" class="btn btn-primary next-modal" <% if(i + 1 < data.length) { %>
                            onclick="showPrevNextModalMulti(<%= data[i + 1]['id_item']%>,'<%= data[i+1]['item_type'] %>','image', <%= data[i+1]['idfolder']%>,'<%=branch%>',<%= parseInt(data[i+1]['idfile']) %>)" <%}%>>
                    <i class="fas fa-arrow-alt-circle-right"></i></button>
                <% } else if (i === data.length - 1) { %>
                            <button type="button" class="btn btn-primary mr-auto prev-modal" <% if(i - 1 >= 0) { %>
                            onclick="showPrevNextModalMulti(<%= data[i - 1]['id_item']%>,'<%= data[i-1]['item_type'] %>','image', <%= data[i-1]['idfolder']%>,'<% branch%>', <%= parseInt(data[i-1]['idfile']) %>)" <%}%>>
                    <i class="fas fa-arrow-alt-circle-left"></i></button>
                    <p style="margin-right: 4rem;"><% if (data[i]['item_type'] === 'object') {%><%=__('object')%>
                            <%= data[i]['object_name'] %><% } else { %><%= data[i]['name']%><% } %></p>
                        <button type="button" class="btn btn-primary next-modal"
                                onclick="loadPage(<%=parseInt(datapost['page'])+1%>, 'image');">
                            <i class="fas fa-arrow-alt-circle-right"></i></button>
                        <% } else { %>
                        <button type="button" class="btn btn-primary mr-auto prev-modal"
                                <% if(i - 1 >= 0) { %>onclick="showPrevNextModalMulti(<%= data[i - 1]['id_item']%>, '<%= data[i-1]['item_type'] %>', 'image', <%= data[i-1]['idfolder']%>,'<%=branch%>',<%= parseInt(data[i-1]['idfile']) %>)" <%}%>>
                    <i class="fas fa-arrow-alt-circle-left"></i></button>
                    <p><% if (data[i]['item_type'] === 'object') {%><%=__('object')%>
                                <%= data[i]['object_name'] %><% } else { %><%= data[i]['name'] %><% } %></p>
                            <button type="button" class="btn btn-primary next-modal"
                                    <% if(i + 1 < data.length) { %>onclick="showPrevNextModalMulti(<%= data[i + 1]['id_item']%>, '<%= data[i+1]['item_type'] %>', 'image', <%= data[i+1]['idfolder']%>,'<%=branch%>',<%= parseInt(data[i+1]['idfile']) %>)" <%}%>>
                    <i class="fas fa-arrow-alt-circle-right"></i></button>
                <% } %>
                                    </div>
                </div>
            </div>
        </div>
        <% } %>

        <%# Une 2eme modale pour le contenu metadata %>
        <% for (let i = 0; i < data.length; i++) { %>
        <div class="modal" id="text_<%= data[i]['id_item']%>" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        <h5 class="modal-title w-100"><% if (data[i]['item_type'] === 'object') {%><%=__('object')%>
                            <%= data[i]['object_name'] %>
                            <% } else if (data[i]['item_type'] === 'unico') { %>Unico <%= data[i]['name'] %>
                            <% } else { if (branch === 'corpus') {%><%= data[i]['title']%><% } else {%><%= data[i]['name']%><% }} %></h5>
                        <a href="javascript:"
                           onclick="switchModalMulti('image','<%= data[i]['item_type']%>',<%= data[i]['id_item']%>,<%= data[i]['idfolder']%>,'<%=branch%>',<%= parseInt(data[i]['id_item']) %>)"
                           title="image">
                            <i class="far fa-file-image fa-2x"></i></a>
                    </div>
                    <div class="modal-body">
                    </div>
                    <div class="modal-footer d-flex justify-content-between">
                        <% if (i === 0) { %>
                        <button type="button" class="btn btn-primary mr-auto prev-modal" onclick="loadPage(<%= parseInt(datapost['page'])- 1 %>, 'text')">
                            <i class="fas fa-arrow-alt-circle-left"></i>
                        </button>
                        <p>
                            <% if (data[i]['item_type'] === 'object') {%>
                                <%=__('object')%> <%= data[i]['object_name'] %>
                            <% } else { %>
                                <%= data[i]['name']%>
                            <% } %>
                        </p>
                        <button type="button" class="btn btn-primary next-modal"
                                <% if(i + 1 < data.length) { %>onclick="showPrevNextModalMulti(<%= data[i + 1]['id_item'] %> , '<%= data[i+1]['item_type']%>', 'text', <%= data[i+1]['idfolder']%>,'<%=branch%>',<%= parseInt(data[i+1]['idfile']) %>)"
                                <% } %>>
                            <i class="fas fa-arrow-alt-circle-right"></i></button>
                        <% } else if (i === data.length - 1) { %>
                        <button type="button" class="btn btn-primary mr-auto prev-modal"
                                <% if(i - 1 >= 0) { %>onclick="showPrevNextModalMulti(<%= data[i - 1]['id_item'] %>,'<%= data[i-1]['item_type'] %>', 'text', <%= data[i-1]['idfolder'] %>, '<%=branch%>' ,<%= parseInt(data[i-1]['idfile']) %>)"
                                <% } %>>
                            <i class="fas fa-arrow-alt-circle-left"></i></button>
                        <p style="margin-right: 4rem;"><% if (data[i]['item_type'] === 'object') {%><%=__('object')%>
                            <%= data[i]['object_name'] %><% } else { %><%= data[i]['name'] %><% } %></p>
                        <button type="button" class="btn btn-primary next-modal" onclick="loadPage(<%= parseInt(datapost['page']) + 1 %>, 'text'); ">
                            <i class="fas fa-arrow-alt-circle-right"></i></button>
                        <% } else { %>
                        <button type="button" class="btn btn-primary mr-auto prev-modal"
                                <% if(i - 1 >= 0) { %>onclick="showPrevNextModalMulti(<%= data[i - 1]['id_item'] %>,'<%= data[i-1]['item_type']%>', 'text', <%= data[i-1]['idfolder']%>, '<%=branch%>',<%= parseInt(data[i-1]['idfile']) %>)"
                                <% } %>>
                            <i class="fas fa-arrow-alt-circle-left"></i></button>
                        <p><% if (data[i]['item_type'] === 'object') {%><%=__('object')%> <%= data[i]['object_name'] %><% } else { %><%= data[i]['name'] %><% } %></p>
                        <button type="button" class="btn btn-primary next-modal"
                                <% if(i + 1 < data.length) { %>onclick="showPrevNextModalMulti(<%= data[i + 1]['id_item'] %>,'<%= data[i+1]['item_type'] %>', 'text',<%= data[i+1]['idfolder'] %>,'<%=branch%>',<%= parseInt(data[i+1]['idfile']) %>)"
                                <% } %>>
                            <i class="fas fa-arrow-alt-circle-right"></i></button>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
        <% } %>
    </div>
    <% } %>

<script>
    let index_all = function(){
        window.location =`/edit,<%= branch %>,<%= model%>,<%= model_type %>,0,${explorePage.id}`;
    }
<% if (selection && WRights === true) { %> $('#item-selection').show() <% } else { %> $('#item-selection').hide() <% } %>
<% if (selection && (WRights === true)) { %>
    $('#index-all-btn').show();
    $('#index-all-btn').off("click").on("click", index_all);
    $('#tag-all-btn').show();
<% } else { %>
    $('#index-all-btn').hide();
    $('#tag-all-btn').hide();
<% } %>
<% if (locals.virtual === true) { %>
    <% if (typeof user !== undefined) { %>
        <% if (user.id) { %> $('#delete-all-btn').show() <% } else { %> $('#delete-all-btn').hide() <% } %>
    <% } %>
<% } else { %> $('#delete-all-btn').hide() <% } %>

<% if (locals.objectsFolders) { %>
    //on remet à zero l'exploration de l'arborescence si on est dans la page de gestion de la selection
    $("#main-ul li ul li input:checked").prop("checked", false);
<% } %>

<% if ((exploreType) && (exploreType === 'selection')) { %>
    checkAll();
<% } %>
</script>