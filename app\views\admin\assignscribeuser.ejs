<div id="main_permuser">
    <% if (userid === 0) { %>
    <h3>Pour quel utilisateur voulez-vous attribuer des projets?</h3>
    <form id="permuser" action="/admin/assignScribeUser" method="post">
        <div class="col-6 offset-3 d-flex">
            <label for="model" class="col-4"><%=__('user')%> : </label>
            <select class="form-select" name="userSelect" id="userSelect">
                <% for (i = 0; i < users.length ; i++) {  %>
                <option value="<%= users[i]['id'] %>" <% if (users[i]['id'] === userid) { %>selected<% } %>>
                    <%= users[i]['username'] %></option>
                <% } %>
            </select>
        </div>
        <div class="col-6 offset-3 d-flex my-2">
            <label for="model" class="col-4">
                <%= __('admin.content.BranchSelection') %>
            </label>
            <select class="form-select select_project" name="rootSelect" id="rootSelect">
                <% for (const branch of branchList) { %>
                <option value="<%= branch.branchename %>"
                        <% if ((typeof rootproj != 'undefined') && (rootproj === branch.branchename)) { %>selected<% } %>>
                    <%= branch.description %>
                </option>
                <% } %>
            </select>
        </div>
        <% if (method !=='POST') { %>
        <div class="d-flex justify-content-center my-3 gap-2">
            <input class="btn btn-secondary" type="submit" value="<%=__('submit') %>" />
            <a type="button" href="/admin/permUser" class="btn btn-outline-secondary"><%=__('cancel') %></a>
        </div>
        <% } %>
    </form>
    <% } else { %>
    <h3>Utilisateur concerné pour cette attribution de projet : <%= userScribe.username %>
        <%if (userScribe.signature !== '') { %>(<%= userScribe.signature %>)<% }%>
    </h3>
    <% if (assignedProjects.length) { %>
    <div class="container-fluid">
        <h3>Projets déjà attribués à cet utilisateur :</h3>
        <ul>
            <% for (let i = 0; i < assignedProjects.length; i++) { %>
            <li><%= assignedProjects[i]['folder_name'] %></li>
            <% } %>
        </ul>
        <% }%>
        <% } %>
        <% if (method ==='POST') { %>
        <form class="" id="permuser" action="/admin/assignScribeUser" method="post">
            Choisir un projet
            <div class="input-group">
                <input class="form-control" placeholder="Commencer à taper..." type="text" name="project_value"
                       id="project_value">
                <input class="form-control" type="hidden" name="project_id" id="project_id">
                <input class="btn btn-secondary btn-hand" value="<%=__('save') %>" onclick="addkey('project_value');" />
            </div>
        </form>

        <hr class="m-5">

        <h3>Pour information :</h3>
        <div>
            <h4>
                Liste des projets existants
            </h4>
            <input type="hidden" value="<%= userid %>" name="userSelect" id="userSelect" />
            <input type="hidden" value="<%= root %>" name="rootSelect" id="rootSelect" />
            <ul class="list-group overflow-auto" style="max-height: 320px;" name="assignFolder" multiple>
                <% for (let i = 0; i < projects.length ; i++) {  %>
                <li value="<%= projects[i].id %>" class="list-group-item">
                    <%= projects[i].name %> (<%= projects[i].folder_name %>)
                </li>
                <% }%>
            </ul>
        </div>
    </div>
    <% } %>
</div>

<script>
    function addkey(id_dom) {
        let dataPost = {};
        dataPost['value'] = $('#project_value').val()
        dataPost['id'] = $('#project_id').val()

        $.ajax({
            type: 'POST',
            url: '/addProjectToScribe,<%= root %>,<%= userid %>',
            data: dataPost
        })
            .done(function (data) {
                console.log(data)
                if (data !== '0') {
                    // mettre à vide le champ pour pouvoir taper à nouveau
                    alert('OK, projet <%= __('assigned') %> , ' + data + ' <%=__('folders')%>  <%=__('concerned_plur')%>')
                    $('#' + id_dom).val('')
                    location.reload()
                }
                else {
                    alert('Projet <%= __('already') %> <%= __('assigned') %> ')
                    $('#' + id_dom).val('')
                }
            })
            .fail(function () {
                alert('ERROR')
                location.reload();
            })

    }




    $(function () {

        $('#project_value').autocomplete({
            minLength: 3,
            autofocus: true,
            source: function (request, response) {
                $.ajax({
                    url: '/projectsRootName/<%= root %>/' + request.term,
                    dataType: "json",
                    success: function (data) {
                        response(data)
                    }
                });
            },
            select: function (event, ui) {
                //event.preventDefault();
                $('#project_id').val(ui.item.id);
            }
        });

    })




</script>