const projectId = window.location.pathname.substring(window.location.pathname.lastIndexOf("/") + 1);

// Fonctions
function switchSearch() {
  // De simple à avancée
  if ($("#advanced-search").is(".hidden-search")) {
    const search = $("#simple-search-bar input").val();
    emptySearch();
    addTag($("#all-search-bar-list input:first"), search);
  }
  // D'avancée à simple
  else {
    const search = $("#all-search-bar-list .tag-value:first").text();
    $("#simple-search-bar input").val(search);
    emptyTags();
  }

  $("#advanced-search").toggleClass("hidden-search");
  $("#simple-search").toggleClass("hidden-search");
  newSearch();
}

function addSearchBar(e) {
  const bar = e.parent();
  const newBar = e.parent().clone();

  newBar.find("input").val("");
  newBar.find(".tag-list span").remove();

  newBar.insertAfter(bar);
  loadAutocomplete();
}

function removeSearchBar(e) {
  const bar = e.parent();
  const list = bar.parent();

  if (list.children().length > 1) {
    bar.remove();
  } else {
    // Si c'est la dernière barre, on efface son contenu
    bar.find(".tag-list span").remove();
    bar.find("input:first").val("");
  }
  $("#search-btn").click();
}

function emptySearch() {
  $("#advanced-search input").val("");
  emptyTags();
  emptyTree();
  $("#item-type option:first").prop("selected", true);
  newSearch();
}

function searchBarsToJSON() {
  const parameters = {};

  // Recherche simple
  if (!$("#simple-search").is(".hidden-search")) {
    // Get search value from searchable dropdown custom input
    const searchSelectElement = document.getElementById('searchSelect');
    const customInputContainer = searchSelectElement ? searchSelectElement.parentElement.querySelector('.custom-select-container') : null;
    const customInput = customInputContainer ? customInputContainer.querySelector('.custom-select-input') : null;

    let simpleSearchValue = '';
    if (customInput) {
      simpleSearchValue = customInput.value || '';
    } else {
      // Fallback to original input if searchable dropdown not initialized
      simpleSearchValue = $("#simple-search input").val() || '';
    }

    // Check for folder selection from searchable dropdown
    const selectedOption = searchSelectElement ? searchSelectElement.options[searchSelectElement.selectedIndex] : null;
    if (selectedOption && (selectedOption.dataset.type === 'folder' || selectedOption.dataset.type === 'thesaurus-folder')) {
      parameters.folder = selectedOption.value;
    }

    // Also check for folder selection from folders tree (for compatibility)
    if (!parameters.folder) {
      const selectedFolderElement = document.querySelector('.folders-tree li.folder-selected');
      if (selectedFolderElement && selectedFolderElement.getAttribute('folderId')) {
        parameters.folder = selectedFolderElement.getAttribute('folderId');
      }
    }

    // Check for pending folder ID (for automatic folder selection)
    if (!parameters.folder && window.pendingFolderId) {
      parameters.folder = window.pendingFolderId;
    }

    // Additional check: try to extract folder from prefix in search input if not already detected
    if (!parameters.folder && simpleSearchValue.includes(': ')) {
      const prefixMatch = simpleSearchValue.match(/^(.+?):\s*(.*)$/);
      if (prefixMatch && prefixMatch[1] && prefixMatch[1].trim().length > 0) {
        const folderName = prefixMatch[1].trim();
        // Try to find matching folder option by name
        const matchingOption = searchSelectElement ? Array.from(searchSelectElement.options).find(option => {
          if (option.dataset.type !== 'folder' && option.dataset.type !== 'thesaurus-folder') return false;
          const optionText = option.textContent.trim();
          // Remove brackets and numbers from option text for comparison
          const cleanOptionText = optionText.replace(/\s*\[[^\]]*\]\s*$/, '').replace(/\s*\([^)]*\)\s*$/, '').trim();
          return cleanOptionText === folderName;
        }) : null;

        if (matchingOption) {
          parameters.folder = matchingOption.value;
        }
      }
    }

    // Extract search term from folder prefix format (e.g., "FolderName: search term" -> "search term")
    // Only include search text if there's actual content after the prefix
    if (simpleSearchValue.includes(': ')) {
      const prefixMatch = simpleSearchValue.match(/^.*?:\s*(.*)$/);
      if (prefixMatch && prefixMatch[1] && prefixMatch[1].trim().length > 0) {
        simpleSearchValue = prefixMatch[1].trim();
      } else {
        // Only prefix, no actual search content
        simpleSearchValue = '';
      }
    }

    if (simpleSearchValue && simpleSearchValue.length > 1) {
      // Filter out search terms that look like folder names with brackets (fallback protection)
      const folderNamePattern = /^.+\s+\[\d+\]$/;
      if (!folderNamePattern.test(simpleSearchValue.trim())) {
        // If the search value is wrapped in double quotes, preserve them as real quotes
        if (simpleSearchValue.startsWith('"') && simpleSearchValue.endsWith('"')) {
          parameters.all = [[simpleSearchValue]];
        } else {
          parameters.all = [[simpleSearchValue]];
        }
      }
    }
  }

  // Recherche avancée
  else {
    // FOLDER
    if ($(".folder-selected").attr("folderId")) {
      parameters.folder = $(".folder-selected").attr("folderId");
    }

    // Check for pending folder ID (for automatic folder selection)
    if (!parameters.folder && window.pendingFolderId) {
      parameters.folder = window.pendingFolderId;
    }

    // Also check for folder selection from advanced search dropdown
    if (!parameters.folder) {
      const searchSelectAdvancedElement = document.getElementById('searchSelectAdvanced');
      if (searchSelectAdvancedElement) {
        const selectedOption = searchSelectAdvancedElement.options[searchSelectAdvancedElement.selectedIndex];
        if (selectedOption && (selectedOption.dataset.type === 'folder' || selectedOption.dataset.type === 'thesaurus-folder')) {
          parameters.folder = selectedOption.value;
        }
      }
    }

    // EXTENSION
    if ("#item-type" && $("#item-type").val() !== "allFiles") {
      parameters.type = $("#item-type").val();
    }

    // CHAMPS MULTIPLES
    const all = [];
    $("#all-search-bar-list .tag-list").each(function (i) {
      if ($(this).children().length > 1) {
        const tags = [];
        $(this)
          .find(".tag-value")
          .each(function () {
            tags.push($(this).attr("value"));
          });
        if (tags.length > 0) {
          all.push(tags);
        }
      }
    });

    if (all.length > 0) {
      parameters.all = all;
    }

    // THESAURUS
    $("#thesaurus-search .tag-list").each(function (i) {
      if ($(this).children().length > 1) {
        const status = $(this).parents("[status]").attr("status");
        const thesaurus = $(this).parents("[thesaurus]").attr("thesaurus");
        const tags = [];

        $(this)
          .find(".tag-value")
          .each(function () {
            tags.push(parseInt($(this).attr("value")));
          });

        if (tags.length > 0) {
          if (!parameters[status]) parameters[status] = {};
          if (!parameters[status][thesaurus]) parameters[status][thesaurus] = [];
          parameters[status][thesaurus].push(tags);
        }
      }
    });

    // PASSPORT
    const passportSearch = {};
    $("#passport-search .tag-list").each(function (i) {
      if ($(this).children().length > 1) {
        const id = $(this).parents("[id-metadata]").attr("id-metadata");
        const tags = [];
        $(this)
          .find(".tag-value")
          .each(function () {
            tags.push($(this).attr("value"));
          });
        if (tags.length > 0) {
          passportSearch[id] = tags;
        }
      }
    });

    if (Object.keys(passportSearch).length > 0) {
      parameters.passport = passportSearch;
    }

    // ADVANCED MODEL
    const advancedModelSearch = {};
    let actors = {};
    $("#passport-search .tag-list-actor").each(function (i) {
      const childs = $(this).children().first().children();
      if (childs.length > 1) {
        const id = $(this).parents("[id-metadata]").attr("id-metadata");
        const type_inputs = $(this).find(".input-actor-type-element input[type=checkbox]")
        let types = [];
        if(type_inputs.eq(0).is(':checked')){
          types.push("person")
        }
        if(type_inputs.eq(1).is(':checked')){
          types.push("organization")
        }

        const names = $(this).find(".input-actor-name-element .tag-value").map(function () {
          return $(this).attr("value")
        }).get();
        const identifiers  = $(this).find(".input-actor-identifier-element  .tag-value").map(function () {
          return $(this).attr("value");
        }).get();

        if(types.length < 2 || names.length > 0 || identifiers.length > 0){
          actors[id] = {types, names, identifiers};
        }
      }
    });
    if (Object.keys(actors).length > 0) {
      advancedModelSearch.actors = actors;
    }

    if(Object.keys(datation_tags).length > 0){
      advancedModelSearch.datations = datation_tags;
      console.log("HERE 1")
    }

    if(Object.keys(advancedModelSearch).length > 0){
      parameters.advancedModel = advancedModelSearch;
      console.log("HERE 2")
    }
  }

  let encodedSearch = encodeURIComponent(JSON.stringify(parameters));
  // Recherche trop complexe => URL trop longue
  if (encodedSearch.length > 1900) {
    // TODO : Prévenir utilisateur. Alert ?
    encodedSearch = encodeURIComponent("{}"); // transforme en recherche vide
  }

  return encodedSearch;
}

// Change l'url avant de lancer la recherche si la recherche est différente
// Permet de de naviguer via les pages précédentes et suivantes du navigateur sans casser l'historique
// TODO : Clarifier les deux fonctions newSearch et search
function newSearch() {
  const newSearchParameters = searchBarsToJSON();

  // Vérifie s'il s'agit d'une nouvelle url et donc d'une nouvelle recherche
  // if (location.pathname + '?search=' + searchParameters !== location.pathname + location.search) {
  // Empêche l'apparition de «?search=%7B%7D» dans l'url si recherche vide
  const newUrl =
    newSearchParameters === encodeURIComponent("{}")
      ? window.location.pathname
      : window.location.pathname + "?search=" + newSearchParameters;

  window.history.pushState({ search: newSearchParameters }, "", newUrl);
  selection.clear();
  $("#nb-selected").html(0);
  search(newSearchParameters);
}

function search(searchParameters = null) {
  const finalSearchParameters = searchParameters || searchBarsToJSON();

  if (finalSearchParameters === encodeURIComponent("{}") && searchParameters === null) {
    const selectedFolderElement = document.querySelector('.folders-tree li.folder-selected');
    if (!selectedFolderElement || !selectedFolderElement.getAttribute('folderId')) {
      const exploreResults = $("#explore-results");
      const hasSpinner = exploreResults.find(".spinner-border").length > 0;
      const hasAlerts = exploreResults.find(".alert").length > 0;
      const hasContentElements = exploreResults.find(".card, .result-item, .vitrine-item, .explore-item, [data-id]").length > 0;

      const hasResults = hasSpinner || hasContentElements || (hasAlerts && exploreResults.find(".alert-info").length > 0);

      if (!hasResults) {
        $("#explore-div").hide();
        $("#menuCentre").show();
      }
    }
    return;
  }

  page = 1;
  exploreSearch(projectId, finalSearchParameters);
}

function saveSearch(name) {
  searchParameters = searchBarsToJSON();
  $(".alert").hide();
  $.ajax({
    url: "/saveSearch/" + projectId + "," + encodeURIComponent(name) + "," + searchParameters,
  })
    .done(() => {
      $(".save-success").show().delay(4000).fadeOut(1000);
    })
    .fail(jqXHR => {
      if (jqXHR.status === 401) {
        $(".save-notconnected").show().delay(4000).fadeOut(1000);
      } else {
        $(".save-error").show().delay(4000).fadeOut(1000);
      }
    });
}

function loadAutocomplete() {
  $("#thesaurus-search input").each(function () {
    const status = $(this).parents(".search-bar-list").attr("status");
    const thesaurus = $(this).parents(".search-bar-list").attr("thesaurus");
    let url = "";

    if (status === "thesaurus") {
      url = "/thesaurusSimpleName";
    } else if (status === "pactols") {
      url = "/thesaurusPactolsName";
    } else if (status === "geopactols") {
      url = "/thesaurusPactolsGeoName";
    } else {
      url = "/thesaurusMultiName";
    }
    url += "/" + thesaurus + ",";

    $(this).autocomplete({
      minLength: 2,
      autofocus: true,
      source: (request, response) => {
        $.ajax({
          url: url + encodeURIComponent(request.term),
          dataType: "json",
          success: data => {
            response(data);
          },
        });
      },
      select: function (event, ui) {
        event.preventDefault();
        addTag($(this), ui.item.name, ui.item.id.slice(ui.item.id.indexOf("_") + 1));
        $(this).val("");
        newSearch();
      },
    });
  });
}

// Events
$(() => {
  loadAutocomplete();
  search();

  $("#simple-search-bar input").on('input', function() {
    let val = $(this).val();
    // Try to get the prefix from the selected folder in the tree
    let prefix = '';
    const selectedFolder = document.querySelector('.folders-tree li.folder-selected .folder-name');
    if (selectedFolder) {
      const folderName = (selectedFolder.getAttribute('data-full-name') || selectedFolder.textContent || '').trim();
      if (folderName) {
        prefix = folderName + ': ';
      }
    }
    if (prefix && val.startsWith(prefix)) {
      val = val.substring(prefix.length);
    }
    if (val.length > 1) {
      newSearch();
    }
  });
});

$(document).on("click", ".lang", function () {
  window.location.replace(
    window.location.origin +
      "/" +
      $(this).text() +
      ",search?redirect=" +
      encodeURIComponent(window.location.pathname + window.location.search)
  );
});

window.onpopstate = (event) => {
  if (event.state && event.state.search) {
    search(event.state.search);
  } else {
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    if (searchParam) {
      search(searchParam);
    } else {
      search();
    }
  }
};
