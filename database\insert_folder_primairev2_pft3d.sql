CREATE OR REPLACE FUNCTION insert_folder_primairev2_pft3d(p_id_folder INTEGER, p_id_site INTEGER)
RETURNS SETOF INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  v_id_parent_folder BIGINT;
  v_id_folder BIGINT;
BEGIN

  FOR v_id_folder IN SELECT DISTINCT id FROM pft3d_folder WHERE id = $1  
  LOOP

      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Archivage3D', 'Archivage3D', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Epars', 'Epars', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Iconographie', 'Iconographie', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'InSitu', 'InSitu', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Interpretations', 'Interpretations', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Nomenclature', 'Nomenclature', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Releves', 'Releves', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Restitutions', 'Restitutions', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), '3DOnline', '3DOnline', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Textes', 'Textes', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'Complements', 'Complements', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'unicos', 'unicos', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'unitxt', 'unitxt', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;
      INSERT INTO pft3d_folder(id, name, folder_name, date_creation,visible, uploadable, commentable ,id_representative_picture, nb_images, id_parent, status, folder_path, id_site) SELECT nextval('pft3d_folder_id_seq'), 'metadata', 'metadata', now(), 'true', 'false', 'false', 0, 0, $1, 'private', ($1::text)::ltree, p_id_site FROM pft3d_folder WHERE id = $1;

      RETURN NEXT v_id_folder;
  END LOOP;
  RETURN;

END;
$$;

