<script>
    function initMapGeoloc(name, lat, lng) {

        let mymap = L.map(name, { scrollWheelZoom: false }).setView([lat, lng], 4)
        L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>' +
                ' contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
            maxZoom: 18,
        }).addTo(mymap)
        let marker = L.marker([lat, lng]).addTo(mymap)

        //return marker

    }
    setTimeout(function () {
        window.dispatchEvent(new Event('resize'));
    }, 100);
</script>
<!-- Main -->

<div class="container-fluid" id="mainbandeau">

    <div class="row">
        <div class="col-md-3">
        </div>
        <div class="col-md-6">
            <p><a href="/projectv/9772"><img class="img_respARCHEOGRID" src="/assets/images/bandeau3dicons.jpg" /></a>
            </p>
        </div>
        <div class="col-md-3" id="menuDroit">
            <a href="/fr/3diconsdoc,<%= idItem %>"><button type="button" class="btn btn-sm">fr</button></a>
            <a href="/en/3diconsdoc,<%= idItem %>"><button type="button" class="btn btn-sm">en</button></a>
        </div>
    </div>
</div>

<div class="container">

    <div class="centre"><a href="/hhdd/<%= idItem %>" target="_blank">
            <img src="/small/<%= folderId %>_<%= idItem %>" alt="<%= name %>" title="<%= name %>">
        </a>
    </div>
    <br>
    <ul class="nav nav-tabs">
        <li class="nav-item nav3dicons"><a class="nav-link nav3dicons active" href="#heritage"
               data-toggle="tab"><%=__('heritageAsset')%></a></li>
        <li class="nav-item nav3dicons"><a class="nav-link nav3dicons" href="#location"
               data-toggle="tab"><%=__('location')%></a></li>
        <% if (youTube !== '') { %><li class="nav-item nav3dicons"><a class="nav-link nav3dicons" href="#video"
               data-toggle="tab">Video</a></li><% } %>
        <li class="nav-item nav3dicons"><a class="nav-link nav3dicons" href="#images" data-toggle="tab">Images</a></li>
    </ul>
    <br>
    <!-- Tab panes -->
    <div class="tab-content">

        <%# HERITAGE %>
        <div class="tab-pane fade show active" id="heritage">
            <h3><%=__('heritageAsset')%>: <%= heritageAsset['appellation']['name']['_']%></h3>
            <% if (heritageAsset['description']) { %><%= heritageAsset['description']['_'] %><br><% } %>
            <% if (heritageAsset['characters']['heritageAssetType']) { %>
            <strong><%=__('heritageAssetType')%>:</strong>
            <%= heritageAsset['characters']['heritageAssetType']['_'] %><br><% } %>
            <% if ((heritageAsset['characters']['temporal']['timeSpan']['startDate']) &&
                    (heritageAsset['characters']['temporal']['timeSpan']['endDate']) &&
                    (heritageAsset['characters']['temporal']['periodName']) ) { %>
            <strong><%=__('period')%></strong>: from
            <%= heritageAsset['characters']['temporal']['timeSpan']['startDate'] %> to
            <%= heritageAsset['characters']['temporal']['timeSpan']['endDate'] %>
            - <%= heritageAsset['characters']['temporal']['periodName']['_'] %>
            <% } %>
            <% if (heritageAsset['spatial']['locationSet']['namedLocation'] ) { %>
            <br><br><strong><%=__('location')%></strong>:
            <%= heritageAsset['spatial']['locationSet']['namedLocation']['_']%>
            <% } %>
            <br />
            <% if (resource.length > 0) { for (let r in resource) { %>
            <% if (parseInt(resource[r]['recordInformation']['id'].replace('https://www.archeogrid.fr/3diconsview3d/', '')) === parseInt(idItem)) { %>
            <br /><br />
            <h3><%=__('digitalResource')%>: <%= resource[r]['appellation']['name']['_']%></h3><br />
            <% if (resource[r]['description']) { %>
            <strong><%=__('3dmodeldescription')%></strong>: <%= resource[r]['description']['_']%>
            <% } %>

            <% if (resource[r]['rights']) {%>
            <br><br><strong><%=__('rights')%></strong>:<ul>
                <li><% if (resource[r]['rights']['accessRights']) { %>
                    <%= resource[r]['rights']['accessRights']['_']%><% } else
                                if (resource[r]['rights']['copyrightCreditLine']) {%>
                    <strong><%=__('copyrights')%>:</strong>
                    <%= resource[r]['rights']['copyrightCreditLine']['_'] %><% } %>
                </li>
                <li>
                    <strong><%=__('EuropeanaRights')%>:</strong> <%= resource[r]['rights']['europeanaRights']%>
                </li>
            </ul>

            <% } %>
            <% } %>
            <% } } else { %>
            <br /><br />
            <h3><%=__('digitalResource')%>: <%= resource['appellation']['name']['_']%></h3><br />
            <% if (resource['description']) { %>
            <br><br><strong><%=__('3dmodeldescription')%></strong>: <%= resource['description']['_']%>
            <% } %>

            <% if (resource['rights']) {%>
            <br><br><strong>Rights</strong>:<ul>
                <li><% if (resource['rights']['accessRights']) { %>
                    <%= resource['rights']['accessRights']['_']%><% } else
                            if (resource['rights']['copyrightCreditLine']) {%>
                    all rights reserved <strong><%= resource['rights']['copyrightCreditLine']['_'] %></strong><% } %>
                </li>
                <li>
                    <%= resource['rights']['europeanaRights']%>
                </li>
            </ul>

            <% } %>
            <% } %>

            <% if ((carare.hasOwnProperty('activity')) &&
                 (carare['activity']['recordInformation'].hasOwnProperty('creation')) ) { %>
            <br><strong>Creation date</strong>: <%= carare['activity']['recordInformation']['creation']['date']%>
            <% } else if (heritageAsset.hasOwnProperty('recordInformation')) {
                if (heritageAsset['recordInformation']['creation']['date']) { %>
            <br><strong>Creation date</strong>: <%= heritageAsset['recordInformation']['creation']['date']%>
            <% } %>
            <% } %>

        </div>
        <%# Location %>
        <%# mettre la classe active pour que la carte se charge correctement dès le début %>
        <div class="tab-pane fade active" id="location">
            <div id="mapCarareDoc" style="height:300px;"></div>
        </div>

        <%# VIDEO %>
        <% if (youTube !== '') { %><div class="tab-pane fade" id="video"><br>
            <a href="https://www.youtube.com/watch?<%= youTube %>" target="_blank">
                <i class="fa fa-video fa-2x" aria-hidden="true"></i> Video available
            </a>
        </div>
        <% } %>

        <%# IMAGES %>
        <div class="tab-pane fade" id="images"><br>
            <% if (!imagesTab.length) { %>No image available<%} else { %>
            <div class="listTabloid">
                <% for (let i = 0; i <  5; i++) { %><%# ATTENTION, trop d'images à lire, on reduit à 5 au lieu de  imagesTab.length A MODIFIER %>
                <div class="card text-center">
                    <a href="/3diconsdoc/<%= imagesTab[i]['id']%>" title="<%= imagesTab[i]['filename']%>">
                        <img class="card-img mx-auto d-block" src="/thumb/<<%= folderIf %>_<%= imagesTab[i]['id'] %>"
                             style="margin-top: 5px;">
                    </a>
                </div>
                <% } %>
                <% } %>
            </div>
        </div>
    </div>

    <script>
<% if (collectionInfo['coverage']['spatial']['geometry']['quickpoint']) {
            let name = 'mapCarareDoc'
            let lat = collectionInfo['coverage']['spatial']['geometry']['quickpoint']['x']
            let lng = collectionInfo['coverage']['spatial']['geometry']['quickpoint']['y'] %>
                initMapGeoloc('<%= name %>', '<%= lat %>', '<%= lng %>');
<% } %>


    </script>




</div>