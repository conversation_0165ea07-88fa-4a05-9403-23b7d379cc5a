CREATE OR REPLACE FUNCTION get_first_metadata_anymodel( i_id_item bigint, i_root text, i_type archeogrid_passport_item_type, i_model text)
RETURNS text
LANGUAGE plpgsql
AS $BODY$
DECLARE

    query TEXT;
    value TEXT;
    model TEXT;

BEGIN
    value := NULL;

    -- 1. Prioritize the i_model parameter if it's provided and not empty
    IF i_model IS NOT NULL AND i_model <> '' THEN
        query := format(
            'SELECT p.value[1] FROM %I_passport p ' ||
            'JOIN %I_metadata m ON p.id_metadata = m.id ' ||
            'JOIN %I_metadata_model mm ON m.id_metadata_model = mm.id ' ||
            'WHERE p.id_item = %L AND p.item_type = %L AND mm.name = %L ' ||
            'ORDER BY m.rank ASC LIMIT 1',
            i_root, i_root, i_root,
            i_id_item, i_type, i_model
        );
        EXECUTE query INTO value;
        
        -- If we get a result, return it
        IF value IS NOT NULL AND value <> '' THEN
            RETURN value;
        END IF;
    END IF;

    -- 2. Fallback to file_passport logic if i_model was not provided or yielded no result
    query := format('SELECT CASE WHEN f.file_passport IS NULL THEN ''DublinCore'' WHEN f.file_passport = '''' THEN ''DublinCore'' ELSE f.file_passport END as model FROM %I_folder f JOIN %I i ON f.id = i.id_folder WHERE i.id = %L',
        i_root, i_root || '_' || i_type, i_id_item
    );
    
    EXECUTE query INTO model;

    IF model IS NOT NULL AND model <> '' THEN
        query := format(
            'SELECT p.value[1] FROM %I_passport p ' ||
            'JOIN %I_metadata m ON p.id_metadata = m.id ' ||
            'JOIN %I_metadata_model mm ON m.id_metadata_model = mm.id ' ||
            'WHERE p.id_item = %L AND p.item_type = %L AND mm.name = %L ' ||
            'ORDER BY m.rank ASC LIMIT 1',
            i_root, i_root, i_root,
            i_id_item, i_type, model
        );

        EXECUTE query INTO value;
        RETURN value;
    END IF;
    
    -- 3. Final fallback if all else fails
    RETURN '';

END
$BODY$;

