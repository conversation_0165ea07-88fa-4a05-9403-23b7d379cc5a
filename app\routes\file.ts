import { Router, type NextFunction, type Request, type Response } from "express";
import async<PERSON>and<PERSON> from "express-async-handler";
import fs from "node:fs";
import http from "node:http";
import https from "node:https";
import path from "node:path";

import config from "../tools/config";
import { acceptedCantaloupeFormat, audioFormat, branchConfig, cantaloupeServer, videoFormat, cantaloupeFormatIcon, readAndParse3DFile } from "../tools/globals";
import { archeogrid_client } from "../tools/request";

const serverHost = config.server_host;
const serverPort = config.server_port;

const __dirname = process.cwd();

const file = Router();

const accessFileChecker = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = res.locals.user.id;
  // id => <idFolder>_<idFile>
  const idFolder = req.params.id.split("_")[0];
  const idFile = req.params.id.split("_")[1];
  const file = await req.fetchApi(`/api/userAccessFile/${userId},${idFile},${idFolder},${branchConfig}`);
  req.file = file;
  next();
});

/**
 *  _____ _ _
 * |  ___(_) | ___
 * | |_  | | |/ _ \
 * |  _| | | |  __/
 * |_|   |_|_|\___|
 */
async function getCantaloupeImage(req: Request, res: Response, url: string) {
  const user = req.user ?? { username: "", password: "" };
  const credentials = `${user.username}:${user.password}`;
  const buff = Buffer.from(credentials);
  const b64Credentials = buff.toString("base64");

  const fetchProtocol = url.startsWith("https") ? https : http;
  fetchProtocol
    .get(url, { headers: { Authorization: `Basic ${b64Credentials}` } }, (response) => {
      if (
        response.statusCode ===
        200 /*&& ( response.headers['content-length'] > 0 || response.headers['transfer-encoding'] === 'chunked' )*/
      ) {
        res.writeHead(response.statusCode, {
          "Content-Type": response.headers["content-type"],
        });
        response.pipe(res);
      } else if (response.statusCode === 401 || response.statusCode === 403) {
        res.sendFile("/images/cantaloupe/not-authorized.png", {
          root: path.join(__dirname, "public"),
        });
      } else if (response.statusCode === 404) {
        res.sendFile("/images/cantaloupe/not-found.png", {
          root: path.join(__dirname, "public"),
        });
      } else {
        res.sendFile("/images/cantaloupe/file-error.png", {
          root: path.join(__dirname, "public"),
        });
      }
    })
    .on("error", (e) => {
      console.error(`Error /getCantaloupeImage/${req.params.id}:  ${e.message}, ${e}`);
      res.sendFile("/images/cantaloupe/server-error.png", {
        root: path.join(__dirname, "public"),
      });
    });
}

async function getServerImage(req: Request, res: Response, url: string, fallbackUrl: string, isRetry = false) {
  const imageRoot = path.join(__dirname, "public");

  // url => thumb/<branch>/<user_id>/<folder_id>/<file_id>
  const file_id = url.split("/")[4];
  const file_info = await archeogrid_client.files.getFile.query({ branch: branchConfig, file_id: Number.parseInt(file_id) });

  // Special handling for XML and 3D files - use a default image
  if (file_info.file_ext === "xml") {
    console.log(`Using default image for ${url} (XML or 3D file)`);
    res.sendFile("/images/cantaloupe/not-found.png", { root: imageRoot });
    return;
  }

  // Special handling for 3D files - use the thumbnail define in the .3d file
  if(file_info.file_ext === "3d"){
    try {
      const data3d = await readAndParse3DFile(file_info.path);
      if(data3d == null){
        res.sendFile("/images/cantaloupe/unknown.png", { root: imageRoot });
        return;
      }
      if(data3d.type === "3DHOP"){
        const thumbnail_local_path = data3d.data["3dhop"].thumbnail;
        if(thumbnail_local_path){
          const start_path = file_info.path.split(path.sep).slice(0, -1);
          const thumbnail = path.join(...start_path, thumbnail_local_path);
          if(fs.existsSync(thumbnail)){
            const thumbnailStream = fs.createReadStream(thumbnail);
            res.writeHead(200, { "Content-Type": "image/" + thumbnail_local_path.split(".").pop() });
            thumbnailStream.pipe(res);
            return;
          }else{
            console.log(`The thumbnail of ${file_info.name} does not exist`, thumbnail);
          }
        }else{
          console.log(`Attribute 'thumbnail' not found in the .3d file ${file_info.name}`);
        }
      }else {
        const thumbnail_local_path = data3d.data.potree.thumbnail;
        if(thumbnail_local_path){
          const start_path = file_info.path.split(path.sep).slice(0, -1);
          const thumbnail = path.join(...start_path, thumbnail_local_path);
          if(fs.existsSync(thumbnail)){
            const thumbnailStream = fs.createReadStream(thumbnail);
            res.writeHead(200, { "Content-Type": "image/" + thumbnail_local_path.split(".").pop() });
            thumbnailStream.pipe(res);
            return;
          }else{
            console.log(`The thumbnail of ${file_info.name} does not exist`, thumbnail);
          }
        }else{
          console.log(`Attribute 'thumbnail' not found in the .3d file ${file_info.name}`);
        }
      }

      // Si on arrive ici, c'est qu'on n'a pas trouvé de thumbnail -> On utilise le thumbnail par defaut
      console.log(`Thumbnail not found for 3D file ${file_info.name} -> Default thumbnail used`);
      res.sendFile("/images/default3DThumbnail.jpg", { root: imageRoot });
      return;
    }catch(err){
      console.error(`Error reading 3D file ${file_info.path}: ${err}`);
      res.sendFile("/images/cantaloupe/file-error.png", { root: imageRoot });
      return;
    }
  }

  // Cas particulier pour les fichiers pdf -> On utilise Cantaloupe
  if (file_info.file_ext === "pdf" ) {
    console.log(`Using Cantaloupe server to display ${file_info.name} because it's a '${file_info.file_ext}'.`);
    await getCantaloupeImage(req, res, fallbackUrl);
    return;
  }

  // Si on dispose d'une icone pour le format, on l'utilise sans passser par Cantaloupe
  if(cantaloupeFormatIcon.includes(file_info.file_ext)) {
    res.sendFile(`/images/cantaloupe/${file_info.file_ext}.png`, {
      root: path.join(__dirname, "public"),
    });
    return;
  }

  const pipeImage = async (response: http.IncomingMessage) => {
    if (response.statusCode === 200 || response.statusCode === 302) {
      res.contentType(response.headers["content-type"] ?? "webp");
      response.pipe(res);
    } else {
      console.warn(`Fetch server image returned an error (${response.statusMessage}), NEVER AGAIN fallback to cantaloupe server !!!`);
      // Check if we're already in a retry to prevent infinite loops
      //if (isRetry) {
      //  console.error(`Both primary and fallback image sources failed for ${url}`);
      //  res.sendFile("/images/cantaloupe/not-found.png", { root: imageRoot });
      //  return;
      //}
      // if any error fallback to cantaloupe if image middleware does not support the requested image (500 error code)
      //await getCantaloupeImage(req, res, fallbackUrl);
      if (response.statusCode === 404) {
        res.sendFile("/images/cantaloupe/not-found.png", {
          root: path.join(__dirname, "public"),
        });
      } else if (response.statusCode === 403) {
        res.sendFile("/images/cantaloupe/not-authorized.png", {
          root: path.join(__dirname, "public"),
        });
      } else {
        // problème non identifié
        res.sendFile("/images/cantaloupe/file-error.png", {
          root: path.join(__dirname, "public"),
        });
      }

    }
  };

  const getImage = http.get(`http://${serverHost}:${serverPort}/image/${url}`, pipeImage);

  getImage.on("error", (e) => {
    console.error(`Error get ${url}: ${e.message}, ${e}`);
    if (isRetry) {
      console.error(`Both primary and fallback image sources failed for ${url}`);
      res.sendFile("/images/cantaloupe/not-found.png", { root: imageRoot });
    } else {
      res.sendFile("/images/cantaloupe/server-error.png", { root: imageRoot });
    }
  });
}

file.get(
  "/iiif/*",
  asyncHandler(async (req, res) => {
    const url = `${cantaloupeServer}/iiif/${req.params[0]}`;
    await getCantaloupeImage(req, res, url);
  }),
);

file.get(
  "/thumb/:id",
  asyncHandler(async (req, res) => {
    const [folder_id, file_id] = req.params.id.split("_");
    const prefix = branchConfig === 'corpus' ? 'o' : 'p';
    await getServerImage(
      req,
      res,
      `thumb/${branchConfig}/${req.user?.id ?? 0}/${folder_id}/${file_id}`,
      `${cantaloupeServer}/iiif/3/${prefix}${req.params.id}/full/^!255,255/0/default.jpg`,
      false // Not a retry
    );
  }),
);

file.get(
  "/small/:id",
  asyncHandler(async (req, res) => {
    const [folder_id, file_id] = req.params.id.split("_");
    const prefix = branchConfig === 'corpus' ? 'o' : 'p';
    await getServerImage(
      req,
      res,
      `small/${branchConfig}/${req.user?.id ?? 0}/${folder_id}/${file_id}`,
      `${cantaloupeServer}/iiif/3/${prefix}${req.params.id}/full/^!512,640/0/default.jpg`,
      false // Not a retry
    );
  }),
);

file.get(
  "/hhdd/:id",
  asyncHandler(async (req, res) => {
    const [folder_id, file_id] = req.params.id.split("_");
    const prefix= branchConfig === 'corpus' ? 'o' : 'p';
    await getServerImage(
      req,
      res,
      `hhdd/${branchConfig}/${req.user?.id ?? 0}/${folder_id}/${file_id}`,
      `${cantaloupeServer}/iiif/3/${prefix}${req.params.id}/full/^!1280,1280/0/default.jpg`,
      false // Not a retry
    );
  }),
);

file.get(
  "/crop/:id/:x,:y,:w,:h",
  asyncHandler(async (req: Request<{ id: string; x: string; y: string; w: string; h: string }>, res) => {
    const { x, y, w, h } = req.params;
    const [folder_id, file_id] = req.params.id.split("_");
    const prefix= branchConfig === 'corpus' ? 'o' : 'p';
    await getServerImage(
      req,
      res,
      `/crop/${branchConfig}/${req.user?.id ?? 0}/${folder_id}/${file_id}/${x}/${y}/${w}/${h}`,
      `${cantaloupeServer}/iiif/3/${prefix}${req.params.id}/${x},${y},${w},${h}/^!512,640/0/default.jpg`,
      false // Not a retry
    );
  }),
);

file.get(
  "/viewer/:id",
  accessFileChecker,
  asyncHandler(async (req, res) => {
    if (!req.file) throw new Error("File is undefined!");
    // on ajoute l'info id folder dans l'id : <idFolder>_<ifFile>
    const id = req.params.id;
    const file_id = Number.parseInt(req.params.id.split("_")[1]);
    const viewer = "viewerCrop";
    const language = res.locals.lang ?? "fr";

    // Get branch from query parameter, fallback to branchConfig if not provided
    const requestedBranch = req.query.branch as string || branchConfig;
    const prefix = requestedBranch === 'corpus' ? 'o' : 'p';
    if (req.file.rights === -1) {
      res.sendFile("/images/cantaloupe/file-error.png", {
        root: path.join(__dirname, "public"),
      });
    } else if (req.file.rights === 0) {
      res.sendFile("/images/cantaloupe/not-authorized.png", {
        root: path.join(__dirname, "public"),
      });
    } else {
      if (acceptedCantaloupeFormat.includes(req.file.file_ext.toLowerCase())) {
        const user = res.locals.user;
        const url = `${cantaloupeServer}/iiif/3/${prefix}${id}/info.json`;

        const credentials = `${user.username}:${user.password}`;
        const buff = Buffer.from(credentials);
        const b64Credentials = buff.toString("base64");

        const branch = requestedBranch;

        const data = {};

        const fetchProtocol = url.startsWith("https") ? https : http;
        fetchProtocol
          .get(url, { headers: { Authorization: `Basic ${b64Credentials}` } }, (response) => {
            if (response.statusCode === 200) {
              response.setEncoding("utf8");
              let data = "";
              response.on("data", (chunk) => {
                data += chunk;
              });
              response.on("end", async () => {
                const parsedData = JSON.parse(data);
                parsedData.id = parsedData.id
                  .replace(`${cantaloupeServer}`, "") // TODO : Passer le serveur cantaloupe en HTTPS
                  .replace("http://", "https://");

                const project_id = await archeogrid_client.projects.getProjectId.query({
                  branch,
                  file_id,
                });
                if (!project_id) throw new Error("Project not found!");

                const thesaurus_info = await archeogrid_client.thesaurus.getAllThesaurusForProject.query({
                  branch,
                  language,
                  project_id,
                });
                const unicos = await archeogrid_client.unico.getFileUnicos.query({
                  branch,
                  language,
                  project_id,
                  file_id,
                });

                // Set the branch variable for proper CSS loading
                res.locals.branch = requestedBranch;

                res.render(viewer, {
                  type: "image",
                  source: parsedData,
                  id: id,
                  headers: { Authorization: `Basic ${b64Credentials}` },
                  thesaurus_info,
                  unicos,
                  branch: requestedBranch,
                });
              });
            } else {
              // Set the branch variable for proper CSS loading
              res.locals.branch = requestedBranch;

              res.render(viewer, {
                type: "image",
                source: {},
                id: 0,
                thesaurus_info: [],
                unicos: [],
                branch: requestedBranch,
              });
            }
          })
          .on("error", () => {
            // impossible de se connecter à Cantaloupe
            // Set the branch variable for proper CSS loading
            res.locals.branch = requestedBranch;

            res.render(viewer, {
              type: "image",
              source: data,
              branch: requestedBranch
            });
          })
          .end();
      } else if (videoFormat.includes(req.file.file_ext.toLowerCase())) {
        // Set the branch variable for proper CSS loading
        res.locals.branch = requestedBranch;

        res.render(viewer, {
          type: "video",
          name: req.file.name,
          id: id,
          branch: requestedBranch,
        });
      } else if (audioFormat.includes(req.file.file_ext.toLowerCase())) {
        // Set the branch variable for proper CSS loading
        res.locals.branch = requestedBranch;

        res.render(viewer, {
          type: "audio",
          name: req.file.name,
          id: id,
          branch: requestedBranch,
        });
      } else if (req.file.file_ext.toLowerCase() === "pdf") {
        // Set the branch variable for proper CSS loading
        res.locals.branch = requestedBranch;

        res.render(viewer, {
          type: "pdf",
          name: req.file.name,
          id: id,
          branch: requestedBranch,
        });
      } else if (req.file.file_ext.toLowerCase() === "json") {
        // Set the branch variable for proper CSS loading
        res.locals.branch = requestedBranch;

        res.render(viewer, {
          type: "json",
          name: req.file.name,
          id: id,
          branch: requestedBranch,
        });
      } else if (req.file.file_ext.toLowerCase() === "xml") {
        const fullpath = path.join(__dirname, req.file.path);
        try {
          const content = fs.readFileSync(fullpath);
          res.setHeader("Content-Type", "text/xml");
          res.send(content);
        } catch (err) {
          res.sendFile("/images/cantaloupe/file-error.png", {
            root: path.join(__dirname, "public"),
          });
        }
      } else {
        // Set the branch variable for proper CSS loading
        res.locals.branch = requestedBranch;

        res.render(viewer, {
          type: "none",
          name: req.file.name,
          id: id,
          branch: requestedBranch,
        });
      }
    }
  }),
);

file.get("/json/:id", accessFileChecker, (req, res) => {
  if (!req.file) throw new Error("File is undefined!");
  if (req.file.rights === 1) {
    if (req.file.file_ext.toLowerCase() === "json") {
      const fullpath = path.join(__dirname, req.file.path);
      try {
        const content = fs.readFileSync(fullpath, "utf-8");
        const dataj = JSON.parse(content);
        res.setHeader("Content-Type", "application/json");
        res.send(JSON.stringify(dataj));
      } catch (err) {
        res.sendFile("/images/cantaloupe/file-error.png", {
          root: path.join(__dirname, "public"),
        });
      }
    }
  }
});

file.get("/pdf/:id", accessFileChecker, (req, res) => {
  if (!req.file) throw new Error("File is undefined!");
  if (req.file.rights === 1) {
    if (req.file.file_ext.toLowerCase() === "pdf") {
      res.sendFile(path.join(__dirname, req.file.path), (err) => {
        if (err) {
          console.error(err);
          if (!res.headersSent) {
            res.sendStatus(404);
          }
        }
      });
    } else {
      res.sendFile("/images/cantaloupe/file-error.png", {
        root: path.join(__dirname, "public"),
      });
    }
  } else if (req.file.rights === 0) {
    res.sendFile("/images/cantaloupe/not-authorized.png", {
      root: path.join(__dirname, "public"),
    });
  } else {
    res.sendFile("/images/cantaloupe/file-error.png", { root: path.join(__dirname, "public") });
  }
});

file
  .get("/video/:id?", accessFileChecker, (req, res) => {
    if (!req.file) throw new Error("File is undefined!");
    if (req.file.rights === 1) {
      try {
        const re = /(?:\.([^.]+))?$/;
        // Explanation :
        /*
          (?:   # begin non-capturing group
          \.    # a dot
          (     # begin capturing group (captures the actual extension)
          [^.]+ # anything except a dot, multiple times
          )     # end capturing group
          )?    # end non-capturing group, make it optional
          $     # anchor to the end of the string
        */

        let fullpath = path.join(__dirname, req.file.path);
        const matches = re.exec(fullpath);
        // Si format non compatible avec navigateur, essaie de chercher l'alternative mp4
        if (matches && matches[1] !== "mp4" && matches[1] !== "webm") {
          fullpath = fullpath.replace(matches[1], "mp4");
        }
        const stat = fs.statSync(fullpath);
        const fileSize = stat.size;
        const range = req.headers.range;

        if (range) {
          const parts = range.replace(/bytes=/, "").split("-");
          const start = Number.parseInt(parts[0], 10);
          const end = parts[1] ? Number.parseInt(parts[1], 10) : fileSize - 1;
          const chunksize = end - start + 1;
          const file = fs.createReadStream(fullpath, { start, end });
          const head = {
            "Content-Range": `bytes ${start}-${end}/${fileSize}`,
            "Accept-Ranges": "bytes",
            "Content-Length": chunksize,
            "Content-Type": "video/webm",
          };
          res.writeHead(206, head);
          file.pipe(res);
        } else {
          const head = {
            "Content-Length": fileSize,
            "Content-Type": "video/webm",
          };
          res.writeHead(200, head);
          fs.createReadStream(fullpath).pipe(res);
        }
      } catch (err) {
        res.sendStatus(404);
      }
    } else if (req.file.rights === 0) {
      res.sendFile("/images/cantaloupe/not-authorized.png", {
        root: path.join(__dirname, "public"),
      });
    } else {
      res.sendFile("/images/cantaloupe/file-error.png", {
        root: path.join(__dirname, "public"),
      });
    }
  })

  .get(
    "/download/:id?",
    accessFileChecker,
    asyncHandler(async (req, res) => {
      if (!req.file) throw new Error("File is undefined!");
      // Get branch from query parameter, fallback to branchConfig if not provided
      const requestedBranch = req.query.branch as string || branchConfig;
      const prefix = requestedBranch === 'corpus' ? 'o' : 'p';
      if (req.file.rights === -1) {
        res.sendStatus(404);
      } else if (req.file.rights === 0) {
        res.sendStatus(401);
      } else {
        if (acceptedCantaloupeFormat.includes(req.file.file_ext.toLowerCase())) {
          const size = req.user && req.user.user_status !== "guest" ? "max" : "pct:50";

          if (acceptedCantaloupeFormat.includes(req.file.file_ext.toLowerCase())) {
            const user = res.locals.user;

            const url = `${cantaloupeServer}/iiif/3/${prefix}${req.params.id}/full/${size}/0/default.jpg`;

            const credentials = `${user.username}:${user.password}`;
            const buff = Buffer.from(credentials);
            const b64Credentials = buff.toString("base64");

            const fetchProtocol = url.startsWith("https") ? https : http;
            fetchProtocol
              .get(url, { headers: { Authorization: `Basic ${b64Credentials}` } }, (response) => {
                if (response.statusCode === 200) {
                  res.writeHead(response.statusCode, {
                    "Content-Type": response.headers["content-type"],
                    "Content-disposition": `attachment; filename=${req.file?.name}`,
                  });
                  response.pipe(res);
                } else {
                  res.sendStatus(500);
                }
              })
              .on("error", (e) => {
                console.error(`download ${req.params.id} file error: ${e.message}`, e);
              });
          }
        } else {
          res.download(path.join(__dirname, req.file.path), req.file.name, (err) => {
            if (err) {
              console.error(`download error with file ${req.params.id}\n${err}`);
              if (!res.headersSent) {
                res.sendStatus(404);
              }
            }
          });
        }
      }
    }),
  );

export default file;
