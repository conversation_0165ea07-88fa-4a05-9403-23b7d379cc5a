/**
 * Vitrine Search Module
 *
 * Reusable search functionality for vitrine views (projectVitrine, thesaurusVitrine, etc.)
 * Handles search execution, folder-based search, debouncing, and UI management.
 *
 * Usage:
 * 1. Include this script in your view
 * 2. Call VitrineSearch.init(config) to initialize
 * 3. The module will handle all search interactions automatically
 */

(function() {
    'use strict';

    // Default configuration
    const DEFAULT_CONFIG = {
        projectId: null,
        branch: null,
        model: null,
        modelType: null,
        searchSelectId: 'searchSelect',
        folderTreeSelector: '.folders-tree',
        exploreResultsSelector: '#explore-results',
        exploreDivSelector: '#explore-div',
        menuCentreSelector: '#menuCentre',
        searchFunction: 'exploreSearch', // or 'exploreSearchThesaurus'
        debounceDelay: 350,
        minSearchLength: 2,
        advancedSearchPath: '/search/',
        keepMenuCentreVisible: false // for thesaurus views
    };

    let config = {};
    let customSearchInputGlobalRef = null;

    /**
     * Initialize the vitrine search functionality
     * @param {Object} userConfig - Configuration options
     */
    function init(userConfig = {}) {
        config = { ...DEFAULT_CONFIG, ...userConfig };

        if (!config.projectId) {
            console.error('[VitrineSearch] projectId is required');
            return;
        }

        setupSearchListeners();
        setupGlobalFunctions();
    }

    /**
     * Execute search with the given search term
     * @param {string} searchTerm - The search term to execute
     */
    function executeSearch(searchTerm) {
        let trimmedSearchTerm = searchTerm.trim();

        // Handle folder prefix extraction
        const selectedFolder = $(config.folderTreeSelector + ' li.folder-selected .folder-name');
        let prefix = '';
        if (selectedFolder.length > 0) {
            const folderName = (selectedFolder.attr('data-full-name') || selectedFolder.text() || '').trim();
            if (folderName) {
                prefix = folderName + ': ';
                if (trimmedSearchTerm.includes(prefix)) {
                    trimmedSearchTerm = trimmedSearchTerm.replace(prefix, '').trim();
                }
            }
        }

        const isEffectivelyEmpty = trimmedSearchTerm.length === 0 ||
                                   (prefix && searchTerm.trim() === prefix.trim()) ||
                                   (prefix && searchTerm.trim() === prefix);

        const isCompletelyEmpty = searchTerm.trim() === '';

        // Handle empty search
        if (isCompletelyEmpty) {
            $(config.exploreDivSelector).hide();
            $(config.menuCentreSelector).show();
            return;
        }

        // Handle effectively empty search (only prefix)
        if (isEffectivelyEmpty) {
            const selectedFolderLi = $(config.folderTreeSelector + ' li.folder-selected');
            if (selectedFolderLi.length > 0 && selectedFolderLi.attr('folderId')) {
                // Handle thesaurus-specific folder exploration
                if (config.searchFunction === 'exploreSearchThesaurus' && typeof exploreThesaurusItems === 'function') {
                    const folderId = selectedFolderLi.attr('folderId');
                    const currentThesaurus = window.currentThesaurusContext?.thesaurus;
                    const currentType = window.currentThesaurusContext?.type;
                    if (currentThesaurus && currentType) {
                        exploreThesaurusItems(currentThesaurus, folderId, currentType);
                    }
                } else if (typeof exploreFolderVitrine === 'function') {
                    exploreFolderVitrine(selectedFolderLi.attr('folderId'));
                }
            } else {
                $(config.menuCentreSelector).show();
                $(config.exploreDivSelector).hide();
            }
            return;
        }

        // Execute search if term is long enough
        if (trimmedSearchTerm.length >= config.minSearchLength) {
            const searchFunctionName = config.searchFunction;
            const searchFunction = window[searchFunctionName];

            if (typeof searchFunction !== 'function') {
                console.error(`[VitrineSearch] Search function '${searchFunctionName}' not found`);
                return;
            }

            // Prepare search parameters
            let searchParams = { all: [[trimmedSearchTerm]] };
            const selectedFolder = $(config.folderTreeSelector + ' li.folder-selected');

            // Handle thesaurus-specific folder restrictions
            if (config.searchFunction === 'exploreSearchThesaurus') {
                const hasPrefix = prefix && searchTerm.trim().includes(prefix);

                // Apply folder restriction for thesaurus search
                if (hasPrefix && selectedFolder.length > 0 && selectedFolder.attr('folderId')) {
                    searchParams.folder = selectedFolder.attr('folderId');
                } else if (!hasPrefix && selectedFolder.length > 0 && selectedFolder.attr('folderId') && selectedFolder.attr('folderId') !== config.projectId) {
                    searchParams.folder = selectedFolder.attr('folderId');
                }
            } else {
                // Standard folder restriction for regular search
                if (selectedFolder.length > 0 && selectedFolder.attr('folderId') && selectedFolder.attr('folderId') !== config.projectId) {
                    searchParams.folder = selectedFolder.attr('folderId');
                }
            }

            // Execute the search
            searchFunction(config.projectId, encodeURIComponent(JSON.stringify(searchParams)));

            // Update UI
            if (config.keepMenuCentreVisible) {
                $(config.menuCentreSelector).show();
            } else {
                $(config.menuCentreSelector).hide();
            }
            $(config.exploreDivSelector).css('display', 'flex');
        }
    }

    /**
     * Debounce function for search input
     * @param {Function} func - Function to debounce
     * @param {number} delay - Delay in milliseconds
     */
    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, delay);
        };
    }

    // Create debounced search function
    const debouncedRealTimeSearch = debounce(executeSearch, DEFAULT_CONFIG.debounceDelay);

    /**
     * Perform search based on current input and selection
     */
    function performSearch() {
        const searchSelect = document.getElementById(config.searchSelectId);
        const customSearchInputContainer = searchSelect?.parentElement.querySelector('.custom-select-container');
        const searchInput = customSearchInputContainer?.querySelector('.custom-select-input');

        if (searchInput && searchInput.value.trim() !== '') {
            const selectedOption = searchSelect.options[searchSelect.selectedIndex];
            if (selectedOption && selectedOption.dataset.type === 'folder') {
                if (typeof exploreFolderVitrine === 'function') {
                    exploreFolderVitrine(selectedOption.value);
                }
            } else if (selectedOption && selectedOption.dataset.type === 'thesaurus-folder') {
                // Handle thesaurus folder selection
                if (typeof window.handleThesaurusFolderSelection === 'function') {
                    window.handleThesaurusFolderSelection(selectedOption.value);
                }
            } else {
                executeSearch(searchInput.value.trim());
            }
        } else {
            // Handle empty search - revert to folder view or default view
            const selectedFolderLi = $(config.folderTreeSelector + ' li.folder-selected');
            if (selectedFolderLi.length > 0 && selectedFolderLi.attr('folderId')) {
                // Handle thesaurus-specific folder exploration
                if (config.searchFunction === 'exploreSearchThesaurus' && typeof exploreThesaurusItems === 'function') {
                    const folderId = selectedFolderLi.attr('folderId');
                    const currentThesaurus = window.currentThesaurusContext?.thesaurus;
                    const currentType = window.currentThesaurusContext?.type;
                    if (currentThesaurus && currentType) {
                        exploreThesaurusItems(currentThesaurus, folderId, currentType);
                    }
                } else if (typeof exploreFolderVitrine === 'function') {
                    exploreFolderVitrine(selectedFolderLi.attr('folderId'));
                }
            } else {
                $(config.menuCentreSelector).show();
                $(config.exploreDivSelector).hide();
            }
        }
    }

    /**
     * Navigate to advanced search page
     */
    function goToAdvancedSearch() {
        window.location.href = `${config.advancedSearchPath}${config.projectId}`;
    }

    /**
     * Setup event listeners for search functionality
     */
    function setupSearchListeners() {
        const searchSelectElement = document.getElementById(config.searchSelectId);

        if (!searchSelectElement) {
            console.warn(`[VitrineSearch] Search select element '${config.searchSelectId}' not found. Will retry when element becomes available.`);
            // Set up a mutation observer to watch for the element to be added
            const observer = new MutationObserver(function(mutations) {
                const element = document.getElementById(config.searchSelectId);
                if (element) {
                    observer.disconnect();
                    setupSearchListenersForElement(element);
                }
            });
            observer.observe(document.body, { childList: true, subtree: true });
            return;
        }

        setupSearchListenersForElement(searchSelectElement);
    }

    /**
     * Setup event listeners for a specific search element
     */
    function setupSearchListenersForElement(searchSelectElement) {

        // Listen for searchable-select-ready event
        searchSelectElement.addEventListener('searchable-select-ready', function(e) {
            customSearchInputGlobalRef = e.detail.customInputElement;

            if (customSearchInputGlobalRef) {
                // Update debounce delay if different from default
                const debouncedSearch = config.debounceDelay !== DEFAULT_CONFIG.debounceDelay
                    ? debounce(executeSearch, config.debounceDelay)
                    : debouncedRealTimeSearch;

                customSearchInputGlobalRef.addEventListener('input', function() {
                    debouncedSearch(this.value);
                });
            }
        });

        // Handle dropdown selection changes
        searchSelectElement.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];

            if (selectedOption && selectedOption.dataset.type === 'folder') {
                const folderId = selectedOption.value;
                if (folderId && typeof exploreFolderVitrine === 'function') {
                    exploreFolderVitrine(folderId);

                    // Update folder tree selection
                    $(config.folderTreeSelector + ' li').removeClass('folder-selected');
                    const folderInTree = $(config.folderTreeSelector + ` li[folderId='${folderId}']`);
                    if (folderInTree.length) {
                        folderInTree.addClass('folder-selected');
                        folderInTree.parentsUntil(config.folderTreeSelector, 'li').each(function() {
                            $(this).children('input[type="checkbox"]').prop('checked', true);
                            $(this).find('> .d-flex > .folder-parent').addClass('folder-parent-down');
                            $(this).children('.folder-children').addClass('active').show();
                        });
                    }
                }
            } else if (selectedOption && (selectedOption.dataset.type === 'search' || selectedOption.value === 'search')) {
                if (customSearchInputGlobalRef) {
                    const debouncedSearch = config.debounceDelay !== DEFAULT_CONFIG.debounceDelay
                        ? debounce(executeSearch, config.debounceDelay)
                        : debouncedRealTimeSearch;
                    debouncedSearch(customSearchInputGlobalRef.value);
                }
            }
        });
    }

    /**
     * Setup global functions that can be called from other scripts
     */
    function setupGlobalFunctions() {
        // Make functions available globally
        window.performSearch = performSearch;
        window.goToAdvancedSearch = goToAdvancedSearch;

        // Also make the executeSearch function available for direct calls
        window.vitrineExecuteSearch = executeSearch;
    }

    /**
     * Update configuration after initialization
     * @param {Object} newConfig - New configuration options
     */
    function updateConfig(newConfig) {
        config = { ...config, ...newConfig };
    }

    /**
     * Get current configuration
     * @returns {Object} Current configuration
     */
    function getConfig() {
        return { ...config };
    }

    // Public API
    window.VitrineSearch = {
        init,
        executeSearch,
        performSearch,
        goToAdvancedSearch,
        updateConfig,
        getConfig
    };

})();
