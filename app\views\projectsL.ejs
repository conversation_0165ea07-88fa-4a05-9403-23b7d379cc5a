<%- include('utils/title-content', { active: "list" }) %>
<div class="row">
    <div class="col-0 col-md-3 menuIndex ">
        <div id="menuGauche">
            <% if (user.user_status  === 'admin' ) {  %>
            <button type="button" class="btn btn-sm" style="float: right;"
                    onclick="createProject('<%= root %>');"><%=__('newProject') %></button>
            <% } %>
            <div class="button" id="toptop"><a href="#"><i class="fas fa-folder"></i><% if (root === 'pft3d') {%> <%=__('extp3d')%> (<%=__('by')%>
                    site)<% } else { %> <%=__('Corpus')%><%} %></a></div>
            <ul id="main-ul" style="padding-left:1em!important;">
                <% for ( let i = 0; i < data.length; i++) { %>
                <li><% if ((data[i]['status'] === 'public') &&  (branch === 'corpus')) { %><%# site vitrine que pour les corpus pour le moment %>
                    <a href="/projectV/<%= data[i]['id'] %>" title="<%= data[i]['name']%>">
                        <i class="fas fa-folder"></i><span id="libelleProjet"> <%= data[i]['name']%></span>
                        <% if (data[i]['nb_images'] !== '0' ) { %> (<%= data[i]['nb_images']%>)
                        <% } %>
                    </a>
                    <% } else if ((data[i]['status'] === 'private') && (branch === 'corpus')) { if (user.id) { %>
                    <a href="/projectV/<%= data[i]['id'] %>" title="<%= data[i]['name']%>"><% } %>
                        <i class="fas fa-folder"></i><span id="libelleProjet"> <%= data[i]['name']%></span>
                        <% if (user.id) {%></a><% } %>
                    <%} else if (branch !== 'corpus') { %>
                    <a href="/projectV/<%= data[i]['id'] %>" title="<%= data[i]['name']%>">
                        <i class="fas fa-folder"></i><span id="libelleProjet"> <%= data[i]['name']%></span>
                        <% if (data[i]['nb_images'] !== '0' ) { %> (<%= data[i]['nb_images']%>)
                        <% } %>
                    </a><% } %>
                </li>
                <% }%>
            </ul>
        </div><!-- menu gauche -->
    </div>
    <div class="col-12 col-md-9" id="menuCentre">
        <% if (locals.flash && locals.flash.ok) { %>
        <div class="alert alert-success ">
            <%= flash.ok %>
        </div>
        <% } %>
        <div id="tabloid">
            <img src="/assets/images/image_<%= root %>.jpg"
                 style="max-height:99%;max-width:99%;margin-left:auto;margin-right:auto;display:block;" />
        </div>
    </div>
</div>
</div> <!-- Main ouvert dans le header -->
<div class="loader"></div>