/**
 * Lazy Loading Module
 *
 * This module implements lazy loading for images in the application.
 * It uses IntersectionObserver to detect when images should be loaded
 * and loads them only when they come into view.
 *
 * Optimized version: Only observes images in visible sections,
 * and defers observation of images in collapsed sections until they're expanded.
 */

if (typeof globalLazyImageObserver === 'undefined') {
    var globalLazyImageObserver = null;
}

if (typeof globalLazyMetadataObserver === 'undefined') {
    var globalLazyMetadataObserver = null;
}

// Function to fetch metadata for an item
function fetchMetadata(element) {
    const itemId = element.dataset.itemId;
    const itemType = element.dataset.itemType;
    const branch = element.dataset.branch || 'pft3d'; // Default branch
    const model = element.dataset.model; // Optional model parameter

    if (!itemId || !itemType) {
        return;
    }

    // Check if metadata is already being fetched or has been fetched
    if (element.dataset.metadataLoading === 'true' || element.dataset.metadataLoaded === 'true') {
        return;
    }

    // Mark as loading
    element.dataset.metadataLoading = 'true';

    // Find the title element within this item
    const titleElement = element.querySelector('.item-title, .card-title, .list-item-title');
    
    if (titleElement) {
        // Add loading indicator
        const originalText = titleElement.textContent;
        titleElement.classList.add('metadata-loading');
    }

    // Build URL with optional model parameter
    let url = `/metadata/${branch}/${itemType}/${itemId}`;
    if (model && model.trim() !== '') {
        url += `?model=${encodeURIComponent(model)}`;
    }

    // Fetch metadata using the full getFirstMetadata logic
    fetch(url)
        .then(response => response.json())
        .then(data => {
            element.dataset.metadataLoading = 'false';
            element.dataset.metadataLoaded = 'true';

            if (titleElement) {
                titleElement.classList.remove('metadata-loading');
                
                if (data.success && data.title && data.title.trim() !== '') {
                    let title = data.title;
                    if (title.length > 45) {
                        title = title.substring(0, 45) + '...';
                    }
                    titleElement.innerHTML = title;
                    titleElement.classList.add('metadata-loaded');
                } else {
                    // Keep original title if no metadata title found
                    titleElement.classList.add('metadata-no-title');
                }
            }
        })
        .catch(error => {
            console.error('Error fetching metadata:', error);
            element.dataset.metadataLoading = 'false';
            
            if (titleElement) {
                titleElement.classList.remove('metadata-loading');
                titleElement.classList.add('metadata-error');
            }
        });
}

// Initialize lazy loading for images and metadata
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        if (globalLazyImageObserver) {
            globalLazyImageObserver.disconnect();
        }

        if (globalLazyMetadataObserver) {
            globalLazyMetadataObserver.disconnect();
        }

        globalLazyImageObserver = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const lazyImage = entry.target;
                    if (lazyImage.dataset.src) {
                        lazyImage.src = lazyImage.dataset.src;
                        lazyImage.removeAttribute('data-src');
                        lazyImage.classList.remove('lazy-image');

                        const parentCard = lazyImage.closest('.card-link, .list-link, .lazy-load-card');
                        if (parentCard) {
                            parentCard.classList.add('loaded');
                        }

                        globalLazyImageObserver.unobserve(lazyImage);
                    }
                }
            });
        }, {
            rootMargin: '200px 0px',
            threshold: 0.01
        });

        // Create metadata observer
        globalLazyMetadataObserver = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const metadataElement = entry.target;
                    fetchMetadata(metadataElement);
                    globalLazyMetadataObserver.unobserve(metadataElement);
                }
            });
        }, {
            rootMargin: '300px 0px',
            threshold: 0.01
        });

        document.querySelectorAll('img[src^="/thumb/"], img[src^="/small/"], img[src^="/crop/"], img[src^="/hhdd/"], img[src^="http"]').forEach(function(loadedImage) {
            const parentCard = loadedImage.closest('.card-link, .list-link, .lazy-load-card');
            if (parentCard) {
                parentCard.classList.add('loaded');
            }
        });

        // Also mark cards with default object images as loaded
        document.querySelectorAll('.default-object-image').forEach(function(defaultImage) {
            const parentCard = defaultImage.closest('.card-link, .list-link, .lazy-load-card');
            if (parentCard) {
                parentCard.classList.add('loaded');
            }
        });

        observeVisibleImages();
        observeVisibleMetadata();
    } else {
        document.querySelectorAll('.lazy-image').forEach(function(lazyImage) {
            if (lazyImage.dataset.src) {
                lazyImage.src = lazyImage.dataset.src;
                lazyImage.removeAttribute('data-src');

                const parentCard = lazyImage.closest('.card-link, .list-link, .lazy-load-card');
                if (parentCard) {
                    setTimeout(function() {
                        parentCard.classList.add('loaded');
                    }, 100);
                }
            }
        });

        document.querySelectorAll('img[src^="/thumb/"], img[src^="/small/"], img[src^="/crop/"], img[src^="/hhdd/"], img[src^="http"]').forEach(function(loadedImage) {
            const parentCard = loadedImage.closest('.card-link, .list-link, .lazy-load-card');
            if (parentCard) {
                parentCard.classList.add('loaded');
            }
        });

        // Also mark cards with default object images as loaded
        document.querySelectorAll('.default-object-image').forEach(function(defaultImage) {
            const parentCard = defaultImage.closest('.card-link, .list-link, .lazy-load-card');
            if (parentCard) {
                parentCard.classList.add('loaded');
            }
        });
    }
}

// Helper function to check if an element is visible (not in a collapsed section)
function isElementVisible(element) {
    let currentElement = element;

    while (currentElement) {
        const style = window.getComputedStyle(currentElement);

        if (style.display === 'none') {
            return false;
        }

        if (currentElement.classList && currentElement.classList.contains('collapse') &&
            !currentElement.classList.contains('show')) {
            return false;
        }

        currentElement = currentElement.parentElement;
    }

    // Special check for compact view: if the image is in a list-vitrine-image that's hidden by compact view
    const listImageContainer = element.closest('.list-vitrine-image');
    if (listImageContainer && document.body.classList.contains('compact-list-view')) {
        return false;
    }

    return true;
}

// Function to observe only images that are in visible sections
function observeVisibleImages() {
    if (!globalLazyImageObserver) return;

    const lazyImages = document.querySelectorAll('.lazy-image');

    lazyImages.forEach(function(lazyImage) {
        if (lazyImage.dataset.src && isElementVisible(lazyImage)) {
            globalLazyImageObserver.observe(lazyImage);
        }
    });
}

// Function to observe metadata elements that are in visible sections
function observeVisibleMetadata() {
    if (!globalLazyMetadataObserver) return;

    const metadataElements = document.querySelectorAll('.lazy-metadata, [data-item-id][data-item-type]');

    metadataElements.forEach(function(metadataElement) {
        if (metadataElement.dataset.itemId && metadataElement.dataset.itemType && isElementVisible(metadataElement)) {
            globalLazyMetadataObserver.observe(metadataElement);
        }
    });
}

// Function to handle newly visible containers (when a collapsed section is expanded)
function handleNewlyVisibleContainer(container) {
    if (!container) return;

    // Handle images
    if (globalLazyImageObserver) {
        const lazyImages = container.querySelectorAll('.lazy-image');

        lazyImages.forEach(function(lazyImage) {
            if (lazyImage.dataset.src) {
                globalLazyImageObserver.observe(lazyImage);
            }
        });

        container.querySelectorAll('img[src^="/thumb/"], img[src^="/small/"], img[src^="/crop/"], img[src^="/hhdd/"], img[src^="http"]').forEach(function(loadedImage) {
            const parentCard = loadedImage.closest('.card-link, .list-link, .lazy-load-card');
            if (parentCard) {
                parentCard.classList.add('loaded');
            }
        });

        // Also mark cards with default object images as loaded
        container.querySelectorAll('.default-object-image').forEach(function(defaultImage) {
            const parentCard = defaultImage.closest('.card-link, .list-link, .lazy-load-card');
            if (parentCard) {
                parentCard.classList.add('loaded');
            }
        });
    }

    // Handle metadata
    if (globalLazyMetadataObserver) {
        const metadataElements = container.querySelectorAll('.lazy-metadata, [data-item-id][data-item-type]');

        metadataElements.forEach(function(metadataElement) {
            if (metadataElement.dataset.itemId && metadataElement.dataset.itemType) {
                globalLazyMetadataObserver.observe(metadataElement);
            }
        });
    }
}

// Function to refresh lazy loading for images and metadata that have become visible
function refreshLazyLoadingForVisibleImages() {
    if (!globalLazyImageObserver || !globalLazyMetadataObserver) {
        initLazyLoading();
        return;
    }

    // Find all lazy images that are now visible but not yet observed
    const lazyImages = document.querySelectorAll('.lazy-image[data-src]');
    
    let reObservedCount = 0;
    lazyImages.forEach(function(lazyImage) {
        if (isElementVisible(lazyImage)) {
            globalLazyImageObserver.observe(lazyImage);
            reObservedCount++;
        }
    });

    // Find all metadata elements that are now visible but not yet observed
    const metadataElements = document.querySelectorAll('.lazy-metadata, [data-item-id][data-item-type]');
    
    metadataElements.forEach(function(metadataElement) {
        if (metadataElement.dataset.itemId && metadataElement.dataset.itemType && 
            isElementVisible(metadataElement) && 
            metadataElement.dataset.metadataLoaded !== 'true' && 
            metadataElement.dataset.metadataLoading !== 'true') {
            globalLazyMetadataObserver.observe(metadataElement);
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    initLazyLoading();

    window.addEventListener('resize', function() {
        initLazyLoading();
    });

    document.addEventListener('shown.bs.collapse', function(event) {
        handleNewlyVisibleContainer(event.target);
    });
});

window.addEventListener('load', function() {
    setTimeout(function() {
        initLazyLoading();
    }, 100);
});

// Make functions globally accessible
window.refreshLazyLoadingForVisibleImages = refreshLazyLoadingForVisibleImages;
window.handleNewlyVisibleContainer = handleNewlyVisibleContainer;
