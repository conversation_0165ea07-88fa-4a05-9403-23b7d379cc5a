<script>
    function initMapGeolocColl(name, lat, lng) {

        let mymap = L.map(name, { scrollWheelZoom: false }).setView([lat, lng], 4)
        L.tileLayer('//{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>' +
                ' contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>',
            maxZoom: 18,
        }).addTo(mymap)
        let marker = L.marker([lat, lng]).addTo(mymap)

    }

</script>
<div class="container-fluid" id="mainbandeau">

    <div class="row">
        <div class="col-md-3">
        </div>
        <div class="col-md-6">
            <p><a href="/projectv/9772"><img class="img_respARCHEOGRID" src="/assets/images/bandeau3dicons.jpg" /></a>
            </p>
        </div>
        <div class="col-md-3" id="menuDroit">
            <a href="/fr/collectionId,<%= idColl %>"><button type="button" class="btn btn-sm">fr</button></a>
            <a href="/en/collectionId,<%= idColl %>"><button type="button" class="btn btn-sm">en</button></a>

        </div>
    </div>
</div>


<div id="main" class="container">
    <% if (data['id_file_representative'] !== '0') { %>
    <div class="row">
        <div class="col-0 col-md-2 menuIndex " id="menuIndex">
            <a class="btn btn-sm btn-light mx-1" href="/collections" title="collections" id="search-link"
               style="border:2px solid #242943;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;float:right;">
                <i class="fas fa-book"></i> <%=__('all') %> Collections
            </a>
        </div>
        <div class="col-12 col-md-10 " id="menuCentre">
            <img src="/thumb/<%= idFolder %>_<%= data['id_file_representative']%>">

            <% if (collectionInfo === '' ) {%>
            <h3>Collection : <%= data['name'] %></h3><br /><br />
            <% } else { %>
            <h3>Collection : <%= collectionInfo['title']['_'] %></h3><br />
            <% } %>
            <div id="tabloid" style="padding-right: 10px;">
                <% if (collectionInfo === '' ) {%>
                Description: <%= data['description'] %>
                <%} else {%>
                <strong><%=__('copyrights')%>:</strong> <%= collectionInfo['source']%><br />
                <% if ((collectionInfo['coverage']['temporal']['timeSpan']['startDate']) &&
                    (collectionInfo['coverage']['temporal']['timeSpan']['endDate']) &&
                    (collectionInfo['coverage']['temporal']['periodName']) ) { %>
                <strong><%=__('period')%></strong>: from
                <%= collectionInfo['coverage']['temporal']['timeSpan']['startDate'] %> to
                <%= collectionInfo['coverage']['temporal']['timeSpan']['endDate'] %>
                - <%= collectionInfo['coverage']['temporal']['periodName']['_'] %>
                <% } %>
                <% if (collectionInfo['coverage']['spatial']['locationSet']['namedLocation'] ) { %>
                <br /><strong><%=__('location')%></strong>:
                <%= collectionInfo['coverage']['spatial']['locationSet']['namedLocation']['_']%>
                <% } %>
                <% if (collectionInfo['contacts']['email']) {%>
                <br /><strong>Contact</strong>: <%= collectionInfo['contacts']['email']%><br /><% } %>
                <% } %>
                <br />
                <div>
                    <div id="mapCarareCol" style="height:300px;"></div>
                </div>

            </div>
        </div>
    </div>
    <% } else { %>
    <div class="row">
        <div class="col-0 col-md-2 menuIndex " id="menuIndex">
            <a class="btn btn-sm btn-light mx-1" href="/collections" title="collections" id="search-link"
               style="border:2px solid #242943;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;float:right;">
                <i class="fas fa-book"></i> <%=__('all') %> Collections
            </a>
        </div>
        <div class="col-12 col-md-10 " id="menuCentre">

            <%=__('no4')%>element
        </div>
    </div>
    <% } %>





    <% if (locals.flash && locals.flash.ok) { %>
    <div class="alert alert-success ">
        <%= locals.flash.ok %>
    </div>
    <% } %>
</div>

</div>

<script>
<% if (data['id_file_representative'] !== '0') {
        if ((collectionInfo !== '') && (collectionInfo['coverage']['spatial']['geometry']['quickpoint'])) {
            let name = 'mapCarareCol'
            let lat = collectionInfo['coverage']['spatial']['geometry']['quickpoint']['x']
            let lng = collectionInfo['coverage']['spatial']['geometry']['quickpoint']['y'] %>
                initMapGeolocColl('<%= name %>', '<%= lat %>', '<%= lng %>');
<% }
    } %>


</script>