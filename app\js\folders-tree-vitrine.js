// Folders Tree Vitrine - Core functionality for the folders tree in vitrine views

// Utility functions that can be called from other scripts
function expandTreeNodes() {
  $('.folder-parent').addClass('folder-parent-down');
  $('.folder-children').addClass('active');
}

function collapseTreeNodes() {
  $('.folder-parent').removeClass('folder-parent-down');
  $('.folder-children').removeClass('active');
}

function clearTreeSelection() {
  $('.folders-tree li').removeClass('folder-selected');
  
  // Notify searchable dropdowns that folder selection was cleared
  setTimeout(() => {
    const folderSelectionEvent = new CustomEvent('folderSelectionChanged', {
      detail: { folderId: null },
      bubbles: true
    });
    document.dispatchEvent(folderSelectionEvent);
  }, 10);
}

// Function to recursively expand a folder and all its subfolders
function expandFolderRecursively($folder) {
  const $folderParent = $folder.find('> .d-flex > .folder-parent');

  if ($folderParent.length > 0) {
    const $children = $folder.children('.folder-children');

    $folderParent.addClass('folder-parent-down');
    $children.addClass('active');
    $children.show();

    $children.find('> li').each(function() {
      expandFolderRecursively($(this));
    });
  }
}

// Function to recursively close a folder and all its subfolders
function closeFolderRecursively($folder) {
  const $folderParent = $folder.find('> .d-flex > .folder-parent');

  if ($folderParent.length > 0) {
    const $children = $folder.children('.folder-children');

    $children.find('> li').each(function() {
      closeFolderRecursively($(this));
    });

    $folderParent.removeClass('folder-parent-down');
    $children.removeClass('active');
    $children.hide();
  }
}

// Function to add middle-click support to folder elements
function addMiddleClickSupport(config) {
  $('.folders-tree .folder-name, .folders-tree .folder-number, .folders-tree .folder-name-container').each(function() {
    const $li = $(this).closest('li');
    const folderId = $li.attr('folderId');
    if (!folderId) return;

    const projectId = config.projectId || $li.closest('.folders-tree').data('project-id') || '';

    if (!$(this).find('a.folder-link').length) {
      const $link = $('<a></a>')
        .addClass('folder-link')
        .attr('href', `/projectV/${projectId}?folder=${folderId}`)
        .css({
          'position': 'absolute',
          'width': '100%',
          'height': '100%',
          'top': 0,
          'left': 0,
          'z-index': 1,
          'opacity': 0
        });

      $(this).css('position', 'relative').append($link);
    }
  });
}

// Main initialization function
function initFoldersTreeVitrine(config) {
  const defaultConfig = {
    projectId: '',
    lng: 'en',
    selectedFolder: null
  };

  config = Object.assign({}, defaultConfig, config || {});

  $(document).on('click', '#add-collection-btn', function(e) {
    e.preventDefault();
    e.stopPropagation();

    const $selectedFolder = $('.folders-tree li.folder-selected');
    let parentFolderId = config.projectId;

    if ($selectedFolder.length > 0) {
      parentFolderId = $selectedFolder.attr('folderId');
    }

    if (typeof addVirtualFolder === 'function') {
      addVirtualFolder(this, parentFolderId, config.lng);
    } else {
      console.error('addVirtualFolder function is not available');
    }
  });

  $(document).on('click', '.folders-tree .folder-parent', function(e) {
    const $children = $(this).closest('li').children('.folder-children');
    const isOpen = $children.hasClass('active');

    if (isOpen) {
      $(this).removeClass('folder-parent-down');
      $children.removeClass('active');
      $children.hide();
    } else {
      $(this).addClass('folder-parent-down');
      $children.addClass('active');
      $children.show();
    }

    e.stopPropagation();
    e.preventDefault();
  });

  addMiddleClickSupport(config);

  $(document).on('click', '.folders-tree .folder-name, .folders-tree .folder-number, .folders-tree .folder-name-container', function(e) {
    const $li = $(this).closest('li');
    const folderId = $li.attr('folderId');
    if (!folderId) return;

    const $folderParent = $li.find('> .d-flex > .folder-parent');
    const hasChildren = $folderParent.length > 0;

    if (hasChildren) {
      const $children = $li.children('.folder-children');
      const isOpen = $children.hasClass('active');

      if (!isOpen) {
        $folderParent.addClass('folder-parent-down');
        $children.addClass('active');
        $children.show();
      }
    }

    $('.folders-tree li').removeClass('folder-selected');
    $li.addClass('folder-selected');

    // Add a small delay to ensure searchable dropdown MutationObserver catches the selection change
    setTimeout(() => {
      // Trigger a custom event to notify searchable dropdowns of folder selection change
      const folderSelectionEvent = new CustomEvent('folderSelectionChanged', {
        detail: { folderId: folderId },
        bubbles: true
      });
      document.dispatchEvent(folderSelectionEvent);
    }, 10);

    $.ajax({
      url: `/exploreFolderNb/${folderId}`,
      method: 'GET',
      success: function(data) {
        if (data.nbRes > 0 || data.nbRes === -1) {
          if (typeof exploreFolderVitrine === 'function') {
            exploreFolderVitrine(folderId);
          }
        } else {
          if ($("#explore-results").find(".spinner-border").length) {
            $("#explore-results").empty();
          }

          if (!explorePage.id) {
            $("#explore-div").hide();
            $("#menuCentre").show();
          }
        }
      },
      error: function() {
        console.error('Failed to check folder content');
        if (typeof exploreFolderVitrine === 'function') {
          exploreFolderVitrine(folderId);
        }
      }
    });

    e.preventDefault();
    e.stopPropagation();
  });

  $(document).on('click', '.folders-tree li', function(e) {
    if (e.target === this) {
      e.stopPropagation();
    }
  });

  let hoverTimer;
  $(document).on('mouseover', '.folders-tree li > .d-flex, .folder-name-container', function(e) {
    const $li = $(this).closest('li');
    clearTimeout(hoverTimer);
    hoverTimer = setTimeout(function() {
      $li.addClass('folder-hover');
    }, 50);

    e.stopPropagation();
  });

  $(document).on('mouseout', '.folders-tree li > .d-flex, .folder-name-container', function(e) {
    clearTimeout(hoverTimer);
    $(this).closest('li').removeClass('folder-hover');
    e.stopPropagation();
  });

  // Also remove hover when leaving the li element itself
  $(document).on('mouseout', '.folders-tree li', function(e) {
    if (!$(e.relatedTarget).closest('li').is(this)) {
      clearTimeout(hoverTimer);
      $(this).removeClass('folder-hover');
    }
  });

  // Unselect folder when clicking anywhere outside the folder tree
  $(document).on('click', function(e) {
    if (!$(e.target).closest('.folders-tree').length && 
        !$(e.target).closest('#ctxmenu1').length && 
        !$(e.target).closest('#add-collection-btn').length) {
      clearTreeSelection();
    }
  });

  function initializeTree() {
    setTimeout(function() {
      const newFolderId = localStorage.getItem('newFolderId');

      if (newFolderId) {
        localStorage.removeItem('newFolderId');

        const $newFolder = $('li[folderId="' + newFolderId + '"]');

        if ($newFolder.length) {
          $('.folders-tree li').removeClass('folder-selected');
          $newFolder.addClass('folder-selected');

          $newFolder.parents('.folder-children').each(function() {
            $(this).addClass('active');
            $(this).siblings('.d-flex').find('.folder-parent').addClass('folder-parent-down');
          });

          const $folderParent = $newFolder.find('> .d-flex > .folder-parent');
          if ($folderParent.length) {
            $folderParent.addClass('folder-parent-down');
            $newFolder.children('.folder-children').addClass('active');
          }

          // Notify searchable dropdowns of programmatic folder selection
          setTimeout(() => {
            const folderSelectionEvent = new CustomEvent('folderSelectionChanged', {
              detail: { folderId: newFolderId },
              bubbles: true
            });
            document.dispatchEvent(folderSelectionEvent);
          }, 10);

          $.ajax({
            url: `/exploreFolderNb/${newFolderId}`,
            method: 'GET',
            success: function(data) {
              if (data.nbRes > 0 || data.nbRes === -1) {
                if (typeof exploreFolderVitrine === 'function') {
                  exploreFolderVitrine(newFolderId);
                }
              } else {
                if ($("#explore-results").find(".spinner-border").length) {
                  $("#explore-results").empty();
                }

                if (!explorePage.id) {
                  $("#explore-div").hide();
                  $("#menuCentre").show();
                }
              }
            },
            error: function() {
              console.error('Failed to check folder content');
              if (typeof exploreFolderVitrine === 'function') {
                exploreFolderVitrine(newFolderId);
              }
            }
          });

          setTimeout(function() {
            const isVirtual = $newFolder.hasClass('virtual');
            const container = isVirtual ?
              $('#virtual-folders-tree') :
              $('#normal-folders-tree');

            if (container.length) {
              const scrollTo = $newFolder.offset().top - container.offset().top + container.scrollTop();
              container.animate({ scrollTop: scrollTo - 50 }, 500);
            }
          }, 200);

          return;
        }
      }

      if (config.selectedFolder) {
        const $selectedFolder = $('li[folderId="' + config.selectedFolder + '"]');
        if ($selectedFolder.length) {
          $selectedFolder.addClass('folder-selected');
          
          // Notify searchable dropdowns of initial folder selection
          setTimeout(() => {
            const folderSelectionEvent = new CustomEvent('folderSelectionChanged', {
              detail: { folderId: config.selectedFolder },
              bubbles: true
            });
            document.dispatchEvent(folderSelectionEvent);
          }, 10);
        }
      }
    }, 100);
  }

  initializeTree();

  setupRightClickMenu();

  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        addMiddleClickSupport(config);
      }
    });
  });

  observer.observe(document.querySelector('.folders-tree'), {
    childList: true,
    subtree: true
  });
}

// Setup right-click menu functionality
function setupRightClickMenu() {
  $(document).on('contextmenu', '.folders-tree .folder-name, .folders-tree .folder-name-container', function(e) {
    e.preventDefault();
    e.stopPropagation();

    const $this = $(this);
    const $li = $this.closest('li');
    const folderId = $li.attr('folderId');
    const folderName = $this.text().trim().replace(/\[.*\]|\(.*\)/g, '').trim();

    const isVirtual = $li.hasClass('virtual') ? 1 : 0;
    const isDownloadable = $li.data('downloadable') == true || $li.data('downloadable') == 1;

    const userStatus = $li.closest('.folders-tree').data('user-status');
    const lng = $li.closest('.folders-tree').data('lng');
    const userWrite = $li.closest('.folders-tree').data('user-write') || [];
    const quality = $li.data('quality') || (isVirtual ? 'slave' : 'master'); // Default quality

    // Retrieve global variables for branch, model, and model_type
    const branch = window.currentBranch;
    const model = window.currentModel;
    const modelType = window.currentModelType;

    // Ensure userWrite is an array
    const userWriteArray = Array.isArray(userWrite) ? userWrite : [];
    const writePermission = userWriteArray.includes(folderId) ? userWriteArray.indexOf(folderId) : -1;

    if (!$this.attr('id')) {
      $this.attr('id', 'context_' + folderId);
    }

    const self = this;
    const showMenu = (nbItems) => {
        try {
          let existingMenu = document.getElementById('ctxmenu1');
          if (existingMenu) {
            existingMenu.parentNode.removeChild(existingMenu);
          }

          let menu = document.createElement('div');
          menu.setAttribute('class', 'ctxmenu');
          menu.setAttribute('id', 'ctxmenu1');
          document.body.appendChild(menu);

          window.xMousePosition = e.clientX;
          window.yMousePosition = e.clientY;

          menu.style.position = "fixed";
          menu.style.left = "-1000px";
          menu.style.top = "-1000px";
          menu.style.visibility = "hidden";

          setTimeout(function() {
            const closeMenu = function(event) {
              if (!menu.contains(event.target)) {
                let existingMenu = document.getElementById('ctxmenu1');
                if (existingMenu) {
                  existingMenu.parentNode.removeChild(existingMenu);
                }
                document.removeEventListener('click', closeMenu);
              }
            };
            document.addEventListener('click', closeMenu);
          }, 10);

          if (typeof window.monmenu === 'function') {
            window.monmenu(
              self,
              isVirtual,
              isDownloadable,
              folderId,
              folderName,
              userStatus,
              writePermission,
              quality,
              lng,
              branch,
              model,
              modelType,
              nbItems,
            );

            const menuHeight = menu.offsetHeight;

            menu.style.left = e.clientX + "px";
            menu.style.top = (e.clientY - menuHeight) + "px";
            menu.style.visibility = "visible";

            return false;
          } else {
            let existingMenu = document.getElementById('ctxmenu1');
            if (existingMenu) {
              existingMenu.parentNode.removeChild(existingMenu);
            }

            let menu = document.createElement('div');
            menu.setAttribute('class', 'ctxmenu');
            menu.setAttribute('id', 'ctxmenu1');
            document.body.appendChild(menu);

            menu.style.position = "fixed";
            menu.style.left = "-1000px";
            menu.style.top = "-1000px";
            menu.style.visibility = "hidden";

            let menuItem = document.createElement('p');
            menuItem.setAttribute('class', 'ctxline');
            menuItem.innerHTML = 'Folder: ' + folderName;
            menu.appendChild(menuItem);

            const menuHeight = menu.offsetHeight;

            menu.style.left = e.clientX + "px";
            menu.style.top = (e.clientY - menuHeight) + "px";
            menu.style.visibility = "visible";

            setTimeout(function() {
              const closeMenu = function(event) {
                if (!menu.contains(event.target)) {
                  if (menu && menu.parentNode) {
                    menu.parentNode.removeChild(menu);
                  }
                  document.removeEventListener('click', closeMenu);
                }
              };
              document.addEventListener('click', closeMenu);
            }, 10);

            return false;
          }
        } catch (error) {
          return true;
        }
    }

    $.ajax({
        url: `/exploreFolderNb/${folderId}`,
        method: 'GET',
        success: function(data) {
            showMenu(data.nbRes);
        },
        error: function() {
            console.error(`Failed to get item count for folder ${folderId}. Batch options will be hidden.`);
            showMenu(0);
        }
    });
  });
}

// Export utility functions to global scope
window.expandTreeNodes = expandTreeNodes;
window.collapseTreeNodes = collapseTreeNodes;
window.clearTreeSelection = clearTreeSelection;
window.expandFolderRecursively = expandFolderRecursively;
window.closeFolderRecursively = closeFolderRecursively;
window.initFoldersTreeVitrine = initFoldersTreeVitrine;
window.setupRightClickMenu = setupRightClickMenu;
window.addMiddleClickSupport = addMiddleClickSupport;
