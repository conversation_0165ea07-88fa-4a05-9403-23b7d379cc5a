<style>
    #addingMode-metadata-container {
        display: flex;
        gap: 20px;
        margin-bottom: 10px;
    }

    .step-addingMode-metadata {
        border: 2px solid #ccc;
        padding: 1.5rem;
        border-radius: 8px;
        width: 15%;
        min-height: 30vh;
        overflow: hidden;
        transition: all 0.5s ease-in-out;
        overflow: hidden;
        position: relative;
    }

    div.selector-placeholder {
        z-index: 2;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
    }

    div.value p {  
        opacity: 0;
        margin-top: 1rem;
        padding: 0.375rem;
        border-radius: 8px;
        background-color: #242943;
        color: white;
        position: absolute;
        left: 50%;
        bottom: 2rem;
        transform: translateX(-50%);
        padding: 0.375rem 0.7rem;
        max-width: 90%;
        text-overflow: ellipsis;
        overflow: hidden;
        transition: opacity 0.5s ease-in-out;
        white-space: nowrap;
    }

    .step-addingMode-metadata.done div.value p{
        opacity: 1;
    }

    div.selector-placeholder, div.content {
        position: absolute;
        opacity: 0;
    }

    .step-addingMode-metadata.current {
        border-color: #242943;
        width: 60%;
    }

    .step-addingMode-metadata.done {
        border-color: #24294388;
    }

    .step-addingMode-metadata.done p {
        display: block;
    }

    .step-addingMode-metadata.current div.selector-placeholder {
        opacity: 0;
    }

    .step-addingMode-metadata.current div.content {
        opacity: 1;
        transition-delay: 0.3s;
    }

    .step-addingMode-metadata:not(.current, .done) {
        color: gray;
        background-color: lightgrey;
        opacity: 0.55;
        border-color: lightgrey;
    }

    .step-addingMode-metadata:not(.current) div.selector-placeholder {
        opacity: 1;
    }

    .step-addingMode-metadata:not(.done) div.selector-placeholder p {
        display: none;
    }

    .step-addingMode-metadata:not(.current) div.content {
        opacity: 0;
        animation: disappear 0.5s linear forwards;
    }

    .step-addingMode-metadata:not(.current) input[type="submit"] {
        opacity: 0;
    }

    div.actions {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }

    #addingMode-metadata-container fieldset {
        margin-bottom: 0.5rem;
    }

    #addingMode-metadata-container fieldset input{
        margin-right: 0.5rem;
    }

    #results-add-metadata-keywords {
        display: none;
        opacity: 0;
        transition: opacity 0.5s ease-in-out;

        border: 2px solid #242943;
        padding: 1.5rem;
        border-radius: 8px;   
        min-height: 30vh;
        margin-bottom: 2rem;
    }

    #results-add-metadata-keywords.current {
        display: block;
        opacity: 1;
    }

    @keyframes disappear {
        0% {
            opacity: 1;
        }

        99% {
            opacity: 0;
        }

        100% {
            display: none;
            opacity: 0;
        }
    }

    #addingMode-metadata-container.done {
        animation: disappear 0.5s linear forwards;
    } 

    #results-add-metadata-keywords #results-container {
        display: grid;
        grid-template-columns: 2fr 3fr;
        gap: 0.5rem;
    }

    #results-container div:is(#summary, #results) {
        max-height: 60vh;
        overflow-y: auto;
    }

    #results-container #summary {
        padding-right: 1rem;
        border-right: solid 2px #242943;
    }

    .keyword {
        border: 2px solid #242943;
        padding: 0.5rem;
        border-radius: 5px;
        text-align: center;
    }

    .no-keyword {
        color: gray;
        padding: 0.5rem;
        border-radius: 5px;
        text-align: center;
        opacity: 0.5;
    }

    #thesFilters {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        flex-direction: row;
        margin-bottom: 1rem;
    }

    .thesFilter {
        background-color: #3f4a8b24;
        border-radius: 8px;
        padding: 0.375rem 1rem;
        font-size: .85rem;
    }

    masonry-layout {
        margin-bottom: 1.5rem;
    }
</style>

<div id="addingMode-metadata-container">
    <div id="in-metadata-selector" class="step-addingMode-metadata current">
        <div class="selector-placeholder">
            <h4><%= __('addKeyword.addFromMetadata.form.inMetadataLabel') %></h4>
        </div>
        <div class="value">
            <p title=""></p>
        </div>
        <div class="content">
            <h4><%= __('addKeyword.addFromMetadata.form.inMetadataLabel') %></h4>
            <p><%= __('addKeyword.addFromMetadata.form.inMetadataDescription') %></p>
            <form id="in-metadata-selector-form">
                <label for="in-metadata"><%= __('addKeyword.addFromMetadata.metadataName') %> (model)</label>
                <select name="in-metadata" id="in-metadata" class="form-control" onchange="updateItemType(this)">
                    <% for(const m of In_metadataForKeywords) { %>
                        <option value="<%= m.id %>" data-item_type="<%= m.modeltype %>"><%= m.label %> (<%= m.modelname %>)</option>  
                    <% } %>
                </select>

                <div class="actions">
                    <input type="submit" class="btn btn-primary" value="<%= __('Next') %>">
                </div>
            </form>
        </div>
    </div>

    <div id="delimiter-selector" class="step-addingMode-metadata">
        <div class="selector-placeholder">
            <h4><%= __('addKeyword.addFromMetadata.form.delimiterLabel') %></h4>
        </div>
        <div class="value">
            <p title=""></p>
        </div>
        <div class="content">
            <h4><%= __('addKeyword.addFromMetadata.form.delimiterLabel') %></h4>
            <p><%= __('addKeyword.addFromMetadata.form.delimiterDescription') %></p>
            <form id="delimiter-selector-form">
                <input type="text" maxlength="1" name="delimiter" id="delimiter" class="form-control" required>
                
                <div class="actions">
                    <div class="btn btn-secondary prev-step"><%= __('Previous') %></div>
                    <input type="submit" class="btn btn-primary" value="<%= __('Next') %>">
                </div>
            </form>
        </div>
    </div>

    <div id="thesaurus-selector" class="step-addingMode-metadata">
        <div class="selector-placeholder">
            <h4><%= __('addKeyword.addFromMetadata.form.thesaurusLabel') %></h4>
        </div>
        <div class="value">
            <p title=""></p>
        </div>
        <div class="content">
            <h4><%= __('addKeyword.addFromMetadata.form.thesaurusLabel') %></h4>
            <p><%= __('addKeyword.addFromMetadata.form.thesaurusDescription') %></p>
            <form id="thesaurus-selector-form">
                <fieldset id="thesaurusTypeSelector">
                    <label for="thesaurusTypeMulti">Multi</label>
                    <input type="radio" value="multi" name="thesaurusType" id="thesaurusTypeMulti" checked>
                    <label for="thesaurusTypeSimple">Simple</label>
                    <input type="radio" value="simple" name="thesaurusType" id="thesaurusTypeSimple">
                    <label for="thesaurusTypePactols">Pactols</label>
                    <input type="radio" value="pactols" name="thesaurusType" id="thesaurusTypePactols">
                </fieldset>

                <select class="form-select" id="thesaurus" name="thesaurus">
                    <% for(const thes of thesaurusByType.multi) { %>
                        <option value="<%= thes %>"><%= thes %></option>
                    <% } %>
                </select>

                <div id="thesaurusType-simple-warning" class="alert alert-warning d-none" style="margin-top: 0.5rem;">
                    <%= __('addKeyword.addFromMetadata.form.thesaurusSimpleWarning') %>
                </div>

                <div class="actions">
                    <div class="btn btn-secondary prev-step"><%= __('Previous') %></div>
                    <input type="submit" class="btn btn-primary" value="<%= __('Next') %>">
                </div>
            </form>
        </div>
    </div>

    <div id="out-metadata-selector" class="step-addingMode-metadata">
        <div class="selector-placeholder">
            <h4><%= __('addKeyword.addFromMetadata.form.outMetadataLabel') %></h4>
        </div>
        <div class="value">
            <p title=""></p>
        </div>
        <div class="content">
            <h4><%= __('addKeyword.addFromMetadata.form.outMetadataLabel') %></h4>
            <p><%= __('addKeyword.addFromMetadata.form.outMetadataDescription') %></p>

            <h6><%= __('addKeyword.addFromMetadata.form.outMetadataFilters') %></h6>
            <div id="thesFilters">
            </div>

            <form id="out-metadata-selector-form">
                <label for="out-metadata"><%= __('addKeyword.addFromMetadata.metadataName') %> (model)</label>
                <select name="out-metadata" id="out-metadata" class="form-control" style="margin-bottom: 0.5rem;">
                    <% for(const metadata of Out_metadataForKeywords) { %>
                        <option value="<%= metadata.id %>" 
                                data-item_type="<%= metadata.modeltype %>" 
                                data-thesaurus_type="<%= metadata.thesaurus_type %>"
                                data-thesaurus_list="<%= metadata.list %>">
                                <%= metadata.label %> (<%= metadata.modelname %>)
                        </option>
                    <% } %>
                </select>

                <div style="display: flex; flex-direction: row; gap: 0.5rem; color: gray;">
                    <label for="free-tags-checkbox"><%= __('addKeyword.addFromMetadata.form.outMetadataFreeTags') %></label>
                    <input type="checkbox" name="free-tags" id="free-tags-checkbox" onchange="toggleFreeTags(this)">
                </div>

                <div style="display: flex; flex-direction: row; gap: 0.5rem; color: gray;">
                    <label for="create-tags-checkbox"><%= __('addKeyword.addFromMetadata.form.outMetadataCreateTags') %></label>
                    <input type="checkbox" name="create-tags" id="create-tags-checkbox">
                </div>

                <div class="actions">
                    <div class="btn btn-secondary prev-step"><%= __('Previous') %></div>
                    <input type="submit" class="btn btn-primary" value="<%= __('Next') %>">
                </div>
            </form>
        </div>
    </div>
</div>

<div id="results-add-metadata-keywords">
    <div id="results-container">
        <div id="summary">
            <h4><%= __('addKeyword.addFromMetadata.results.parameters') %></h4> 

            <b id="summary-in-metadata"><%= __('addKeyword.addFromMetadata.form.inMetadataLabel') %></b> 
            <p id="value-in-metadata"></p>

            <b id="summary-delimiter"><%= __('addKeyword.addFromMetadata.form.delimiterLabel') %></b>
            <p id="value-delimiter"></p>

            <b id="summary-thesaurus"><%= __('addKeyword.addFromMetadata.form.thesaurusLabel') %></b>
            <p id="value-thesaurus"></p>

            <b id="summary-out-metadata"><%= __('addKeyword.addFromMetadata.form.outMetadataLabel') %></b>
            <p id="value-out-metadata"></p>
        </div>
        <div id="results">
            <h4><%= __('addKeyword.addFromMetadata.results.results') %></h4>
            <div id="ignoredItems-container" class="d-none">
                <h5><%= __('addKeyword.addFromMetadata.results.ignoredItems') %></h5>
                <masonry-layout id="ignoredItems"></masonry-layout>
            </div>
            <h5><%= __('addKeyword.addFromMetadata.results.foundThes') %></h5>
            <masonry-layout id="foundThes"></masonry-layout>
            <h5><%= __('addKeyword.addFromMetadata.results.foundFreeTags') %></h5>
            <masonry-layout id="foundFreeTags"></masonry-layout>
            <div id="createdTags-container" class="d-none">
                <h5><%= __('addKeyword.addFromMetadata.results.createdTags') %></h5>
                <masonry-layout id="createdTags"></masonry-layout>
            </div>
            <div id="notFoundTags-container" class="d-none">
                <h5><%= __('addKeyword.addFromMetadata.results.notFoundTags') %></h5>
                <masonry-layout id="notFoundTags"></masonry-layout>
            </div>
        </div>
    </div>
    <div>
        <div id="restart" class="btn btn-primary" onclick="window.location.reload()"><%= __('Restart') %></div>
    </div>
</div>

<!-- Custom masory layout -->
<script src="/assets/js/webComponents/masonry-layout.js"></script>

<script>
    const id_item = '<%= idItem %>';

    // If we use it from the csvMetadata page
    // The idItem will be ignored, items will be recovered from the csv_metadata_id
    const csv_metadata_id = JSON.parse(`<%= typeof csv_metadata_id !== 'undefined' ? csv_metadata_id : 'null' %>`);
    
    // In a case of multiple indexation, we select the item_type of the first metadata selected by default
    // if(In_metadataForKeywords[0] !== undefined){
        // let item_type = ` type == 'none' ? In_metadataForKeywords[0].modeltype : type `;
    // }else{
    //     let item_type = 'file';
    // }

    const stepAddingModeMetadata = ['in-metadata-selector', 'delimiter-selector', 'thesaurus-selector', 'out-metadata-selector'];
    const stepAddingModeMetadataValue = ['in-metadata', 'delimiter', 'thesaurus', 'out-metadata'];

    document.addEventListener('DOMContentLoaded', () => {
        const stepsContainer = document.getElementById('addingMode-metadata-container');
        const steps = document.getElementsByClassName('step-addingMode-metadata');
        for(const step of steps) {
            const form = step.querySelector('form');
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                
                const index = stepAddingModeMetadata.indexOf(step.id);
                
                if(index + 1 === stepAddingModeMetadata.length){
                    let i = 0;
                    for(const step of stepAddingModeMetadataValue){
                        const stepInput = document.getElementById(step);
                        let getValue = () => {
                            switch (i) {
                                case 1:
                                    return stepInput.value;
                                case 3:
                                    return stepInput ? Array.from(stepInput.options).find(option => option.selected)?.innerText : '';
                                default:
                                    return Array.from(stepInput.options).find(option => option.selected).innerText;
                            }
                        }
                        const value = getValue();
                        const summaryValue = document.getElementById(`value-${step}`);
                        summaryValue.innerText = value || '---'; 
                        i++;
                    }

                    const inMetadataValue = Number.parseInt(document.getElementById('in-metadata').value);
                    const delimiterValue = document.getElementById('delimiter').value;
                    const thesaurusCollectionValue = document.getElementById('thesaurus').value;
                    const thesaurusTypeValue = document.querySelector(`input[name="thesaurusType"]:checked`).value;
                    const outMetadataValue = Number.parseInt(document.getElementById('out-metadata')?.value);
                    const freeTagsValue = document.getElementById('free-tags-checkbox').checked;
                    const createTagsValue = document.getElementById('create-tags-checkbox').checked;

                    if(isNaN(inMetadataValue)){
                        console.error(`inMetadataValue is NaN!`);
                        return;
                    };

                    const datapost = {inMetadataValue, delimiterValue, thesaurusCollectionValue, thesaurusTypeValue, outMetadataValue, freeTagsValue, createTagsValue};

                    stepsContainer.classList.add('done');

                    // Using timeout to wait for the animation to finish
                    setTimeout(() => {
                        document.getElementById('results-add-metadata-keywords').classList.add('current');
                        addKeyWordsFromMetadata(id_item, item_type, datapost);
                    }, 700);

                    return; 
                }

                step.classList.remove('current');
                step.classList.add('done');

                const next_step_id = stepAddingModeMetadata[index + 1];
                const next_step = document.getElementById(next_step_id)
                next_step.classList.add('current');
                next_step.querySelector('input').focus();

                const input_id = stepAddingModeMetadataValue[index];

                // If it's the text input of delimiter
                if(index === 1){
                    step.querySelector("div.value p").innerText = document.getElementById(input_id).value;
                    step.querySelector("div.value p").title = document.getElementById(input_id).value;
                // If it's a select
                }else{
                    const options = document.getElementById(input_id).querySelectorAll('option')
                    for(const opt of options){
                        if(opt.selected){
                            const detailIndex = /\(.*\)/.exec(opt.innerText)?.index;
                            if(detailIndex){
                                step.querySelector("div.value p").innerText = opt.innerText.slice(0, detailIndex);
                                step.querySelector("div.value p").title = opt.innerText.slice(0, detailIndex) ;
                            }else{
                                step.querySelector("div.value p").innerText = opt.innerText;
                                step.querySelector("div.value p").title = opt.innerText;
                            }
                        }
                    }
                }

                
            });

            const prevButton = step.querySelector('.prev-step');
            if(prevButton)
            prevButton.addEventListener('click', () => {
                const prev_step_id = stepAddingModeMetadata[stepAddingModeMetadata.indexOf(step.id) - 1];
                const prev_step = document.getElementById(prev_step_id);
                step.classList.remove('current');

                prev_step.classList.remove('done');
                prev_step.classList.add('current');
                prev_step.querySelector('input').focus();
            });

            step.querySelector('.content').style.width = `${stepsContainer.getBoundingClientRect().width / 2}px`;

            const observer = new ResizeObserver(entries => {
                for(const entry of entries){
                    if(step.classList.contains('current')){
                        const newHeight = entry.contentRect.height;
                        step.style.height = `calc(${newHeight}px + 3rem)`;
                    }else{
                        step.style.height = '30vh';
                    }
                }
            })

            observer.observe(step.querySelector('.content'));
        }

        const thesaurusTypeSelector = document.querySelectorAll('#thesaurusTypeSelector input');
        for(const radioInput of thesaurusTypeSelector){
            radioInput.addEventListener('change',() => changeThesaurusType(radioInput));
        }

        const thesaurusListSelector = document.querySelector('select#thesaurus');
        thesaurusListSelector.addEventListener('change', () => changeThesaurusList(thesaurusListSelector.value));

        OutMetadataFilters.item_type = item_type;
        filterOutMetadata();
    }) 

    // Only used in the case of multiple indexation 
    // item_type should be define as soon as the in-metadata is selected
    function updateItemType(selector) {
        if(id_item == 0){
            // Multiple indexation
            item_type = Array.from(selector.options).find(option => option.selected).dataset.item_type;
            OutMetadataFilters.item_type = item_type;
            filterOutMetadata();
        }
    }

    function toggleFreeTags(checkbox) {
        const outMetadataSelector = document.getElementById('out-metadata');
        if(!outMetadataSelector){
            outMetadataSubmitButton.disabled = !checkbox.checked;
        }else{
            if (checkbox.checked) {
                outMetadataSelector.disabled = true;
                outMetadataSelector.selectedIndex = -1;
            } else {
                outMetadataSelector.disabled = false;
                outMetadataSelector.selectedIndex = Array.from(outMetadataSelector.options).findIndex(option => option.hidden === false);
            }
        }
    }

    const thesaurusByType = <%- JSON.stringify(thesaurusByType) %>;

    function changeThesaurusType(radioType) {
        const thesaurusType = radioType.value;
        const thesaurusSelector = document.querySelector('#thesaurus-selector-form select');
        thesaurusSelector.selectedIndex = -1;
        thesaurusSelector.innerHTML = '';
        const thesaurus = thesaurusByType[thesaurusType];
        for(const thes of thesaurus){
            const option = document.createElement('option');
            option.value = thes;
            option.innerText = thes    
            thesaurusSelector.appendChild(option);
        }
        thesaurusSelector.selectedIndex = 0;
        OutMetadataFilters.thesaurus_list = thesaurus[0];
        OutMetadataFilters.thesaurus_type = thesaurusType;
        filterOutMetadata();

        // Display the warning message when choosing the simple type
        if (thesaurusType === 'simple') {
            const simpleWarning = document.querySelector('#thesaurusType-simple-warning');
            simpleWarning.classList.remove('d-none');
        } else {
            const simpleWarning = document.querySelector('#thesaurusType-simple-warning');
            simpleWarning.classList.add('d-none');
        }
    }

    function changeThesaurusList(thesaurus_list){
        OutMetadataFilters.thesaurus_list = thesaurus_list;
        filterOutMetadata();
    }

    const outMetadataSubmitButton = document.querySelector('#out-metadata-selector-form input[type="submit"]');
    const outMetadataSelector = document.getElementById('out-metadata') 
    const outMetadataOptions = outMetadataSelector.options;
    let OutMetadataFilters = {item_type: item_type, thesaurus_type: 'multi', thesaurus_list: thesaurusByType.multi[0]};

    function filterOutMetadata(){
        const outMetadataError = document.getElementById('out-metadata-error');
        if(outMetadataError){
            outMetadataError.replaceWith(outMetadataSelector);
            outMetadataSubmitButton.disabled = false;
        }

        for(const option of outMetadataOptions){
            if(Object.entries(OutMetadataFilters).every(([key, value]) => option.dataset[key] === value || value === 'all')){
                option.hidden = false;
            }else{
                option.hidden = true;  
            }
        }

        if(Array.from(outMetadataOptions).every(option => option.hidden)){
            const errorDiv = document.createElement('div');
            errorDiv.classList.add('alert', 'alert-danger');
            errorDiv.id = 'out-metadata-error';
            errorDiv.innerText = `<%= __('addKeyword.addFromMetadata.outMetadataError') %>`;
            outMetadataSelector.replaceWith(errorDiv);
            outMetadataSubmitButton.disabled = true;
        }else{
            outMetadataSelector.selectedIndex = -1;
            Array.from(outMetadataOptions).find(option => !option.hidden).selected = true;
        }

        updateOutMetadataFiltersDiv();
    }

    function addKeyWordsFromMetadata(idItem, itemType, data){
        const url = new URL(`/addkeywordFromMetadata`, window.location.origin);
        url.searchParams.set('branch', '<%= branch %>');
        url.searchParams.set('idItem', idItem);
        url.searchParams.set('itemType', itemType);
        url.searchParams.set('inMetadataId', data.inMetadataValue);
        url.searchParams.set('delimiter', data.delimiterValue);
        url.searchParams.set('thesaurusCollection', data.thesaurusCollectionValue);
        url.searchParams.set('thesaurusType', data.thesaurusTypeValue);

        // if the freeTags checkbox is checked, the outMetadataId is not used
        if(data.freeTagsValue){
            url.searchParams.set('outMetadataId', -1);
        }else{
            url.searchParams.set('outMetadataId', data.outMetadataValue);
        }

        url.searchParams.set('createTags', data.createTagsValue);
        
        // Indexation multiple
        if(idItem == 0){
            url.searchParams.set('idFolder', '<%= idFolder %>');
        }

        // If we use it from the csvMetadata page
        // The idItem will be ignored, items will be recovered from the csv_metadata_id
        if(csv_metadata_id && !isNaN(parseInt(csv_metadata_id))){
            url.searchParams.set('csv_metadata_id', csv_metadata_id);
        }

        fetch(url, {
            method: 'GET'
        })
        .then(async (response) => {
            if(response.status !== 200){
                console.error(`Error : `, response);
                const errorContainer = document.querySelector('#results-container #results');
                errorContainer.innerHTML = '';
                createErrorDiv(errorContainer, `<%= __('error') %> ${response.status} : ${response.message}`);
                return;
            }

            const responseJSON = await response.json();

            // In both single and multiple indexation the result is an array
            // {id_item: number, status: 'ok'|'partial'|'ko', message: string, foundThes: string[], foundFreeTags: string[], createdTags: string[], notFoundTags: string[]}[]
            const errorContainer = document.querySelector('#results-container #results');
            const warningContainer = document.getElementById('results-add-metadata-keywords');


            if(responseJSON.length > 1 && Array.from(responseJSON).some(item => item.status !== 'ko')){
                document.getElementById('ignoredItems-container').classList.remove('d-none')
            }

            for(const item_result of responseJSON){
                switch(item_result.status){
                    case 'ko':
                        if(responseJSON.length > 1){
                            createItemIgnoredDiv(document.getElementById('ignoredItems'), item_result);
                        }else{
                            createErrorDiv(errorContainer, item_result.message);
                        }
                        break;
                    case 'partial':
                        createWarningDiv(warningContainer, item_result.message);
                        break;
                    case 'ok':
                        for(const k of item_result.foundThes){
                            const label = responseJSON.length > 1 ? `item n°${item_result.id_item} : ${k}` : k;
                            createKeywordDiv(document.getElementById('foundThes'), label);
                        };
                        for(const k of item_result.foundFreeTags){
                            const label = responseJSON.length > 1 ? `item n°${item_result.id_item} : ${k}` : k;
                            createKeywordDiv(document.getElementById('foundFreeTags'), label);
                        };
                        if(item_result.createdTags.length > 0){
                            document.getElementById('createdTags-container').classList.remove('d-none');
                            for(const k of item_result.createdTags){
                                const label = responseJSON.length > 1 ? `item n°${item_result.id_item} : ${k}` : k;
                                createKeywordDiv(document.getElementById('createdTags'), label);
                            };
                        }
                        if(item_result.notFoundTags.length > 0){
                            document.getElementById('notFoundTags-container').classList.remove('d-none');
                            for(const k of item_result.notFoundTags){
                                const label = responseJSON.length > 1 ? `item n°${item_result.id_item} : ${k}` : k;
                                createKeywordDiv(document.getElementById('notFoundTags'), label);
                            };  
                        }  
                        break;
                    default:
                        console.error('Unknown response status for item n°', item_result.id_item, ' : ', item_result);
                        break;
                }
            }

            return responseJSON.length;
        }).then((nbItem) => {
            // Potentielement ajouter des noKeywordDiv
            if(nbItem > 1 && document.getElementById('ignoredItems').childElementCount === 0){
                document.getElementById('ignoredItems-container').classList.add('d-none')
            }
            if(document.getElementById('foundThes').childElementCount === 0){
                createNoKeywordDiv(document.getElementById('foundThes'));
            }
            if(document.getElementById('foundFreeTags').childElementCount === 0){
                createNoKeywordDiv(document.getElementById('foundFreeTags'));
            }
            if(document.getElementById('createdTags').childElementCount === 0){
                createNoKeywordDiv(document.getElementById('createdTags'));
            }
            if(document.getElementById('notFoundTags').childElementCount === 0){
                createNoKeywordDiv(document.getElementById('notFoundTags'));
            }
        }).catch(error => {
            console.error(error);
        });
    }

    function createItemIgnoredDiv(container, item){
        const div = document.createElement('div');
        div.classList.add('item-ignored');
        div.innerText = `Item n°${item.id_item} : ${item.message}`;
        container.appendChild(div);
    }

    function createWarningDiv(container, message){
        const div = document.createElement('div');
        div.innerText = message;
        div.classList.add('alert', 'alert-warning');
        container.insertBefore(div, container.firstChild);
    }

    function createErrorDiv(container, message){
        const div = document.createElement('div');
        div.innerText = message;
        div.classList.add('alert', 'alert-danger');
        container.appendChild(div);
    }

    function createKeywordDiv(container, keyword){
        const div = document.createElement('div');
        div.classList.add('keyword');
        div.innerText = keyword;
        container.appendChild(div);
    }

    function createNoKeywordDiv(container){
        const div = document.createElement('div');
        div.classList.add('no-keyword');
        div.innerText = "<%= __('None') %>";
        container.appendChild(div);
    }

    function updateOutMetadataFiltersDiv(){
        const thesFiltersDiv = document.getElementById('thesFilters');
        if(!thesFiltersDiv){ return; }
        thesFiltersDiv.innerHTML = '';
        for(const filter of Object.entries(OutMetadataFilters)){
            const filterDiv = document.createElement('div');
            filterDiv.classList.add('thesFilter');
            filterDiv.innerText = `${filter[0]} : ${filter[1]}`;
            thesFiltersDiv.appendChild(filterDiv);
        }
    }
</script>