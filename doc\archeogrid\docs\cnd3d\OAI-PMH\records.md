---
sidebar_position: 2
---

# ListRecords

In order to create OAI-PMH records, the CND3D uses the ***conservatoire3d_moissonnage*** and ***conservatoire3d_oai_records*** tables. . The objective is to gather informations from multiple tables into a single one (***conservatoire3d_oai_records***), in order to create OAI-PMH records easily. On CND3D application the ***oai/listOfRecords*** route (accessible [here](https://3d.humanities.science/oai?verb=ListRecords&metadataPrefix=oai_dc)) is used to create the XML file that compile all the informations/metadata of database's objects using the ***conservatoire3d_oai_records*** table. 

The ***record model*** is base on the one of openArchives available [here](https://www.openarchives.org/OAI/2.0/oai_dc.xsd). Here's what a ***record*** looks like in the XML file output:

```xml
    <record>
        <header>
            <identifier></identifier>
            <datestamp></datestamp>
            <setSpec></setSpec>
        </header>
        <metadata>
            <oai_dc:dc>
                <dc:identifier></dc:identifier>
                <dc:title></dc:title>
                <dc:creator></dc:creator>
                <dc:constributor></dc:constributor>
                <dc:publisher></dc:publisher>
                <dc:subject></dc:subject>
                <dc:description></dc:description>
                <dc:date></dc:date>
                <dc:type></dc:type>
                <dc:format></dc:format>
                <dc:source></dc:source>
                <dc:language></dc:language>
                <dc:coverage></dc:coverage>
                <dc:relation></dc:relation>
                <dc:rights></dc:rights>
            </oai_dc:dc>
        </metadata>
    </record>
```

## Mapping

Here's the mapping used to feed the ***conservatoire3d_oai_records*** table in order to create the list of records. The mapping has changed in early 2025 because of the new deposit model implemented in the CND3D (V1 to V2). 

### Object V1 (old deposit model)

The table ***conservatoire3d_moissonnage*** is feed with the ***feed_conservatoire3d_moissonnage.sql*** script. The ***conservatoire3d_oai_records*** table is feed with the ***feed_conservatoire3d_oai_records.sql*** script. In this version of the deposit metadata model, there is no distinction made between the virtual object and the physical object.

#### identifier

`An unambiguous reference to the resource within a given context.`

Retrieve directly from the ***conservatoire3d_object*** table with the field ***doi***.

```xml
    <identifier>https://doi.org/10.34969/CND3D/207944.o.2023</identifier>
```
---

#### datestamp

`The date and time of the most recent change to the resource.`

Retrieve directly from the ***conservatoire3d_object*** table with the field ***date_integration***.

```xml
    <datestamp>2025-01-01T00:00:00Z</datestamp>
```
---

#### setSpec

`The set to which the resource belongs.`

Always 'oai_dc:cnd3d_set0'.

```xml
    <setSpec>oai_dc:cnd3d_set0</setSpec>
```
---

#### dc:identifier

`An unambiguous reference to the resource within a given context.`

Retrieve directly from the ***conservatoire3d_object*** table with the field ***doi***.

```xml
    <dc:identifier>https://doi.org/10.34969/CND3D/207944.o.2023</dc:identifier>
```
---

#### dc:title

`A name given to the resource.`

Retrieve directly from the ***conservatoire3d_object*** table with the field ***title***. Any carriage return or newline characters are replace by a whitespace.

```xml
    <dc:title>Vase_31</dc:title>
```
---

#### dc:creator

`The creator of the resource.`

Retrieved thanks to the ***get_creators_from_object_for_DOI()*** function of the database, with the ***id*** of the object. This function use the metadata ***createur*** of the object and gets it's value in the ***conservatoire3d_passport*** table.

```xml
    <dc:creator>Pauline VANDENBROUCK</dc:creator>
```
---

#### dc:contributor

`A contributor to the resource.`

Retrieve thanks to the ***get_contributors_from_object()*** function of the database, with the ***id*** of the object. This function use the metadata ***contributeur*** of the object and gets it's value in the ***conservatoire3d_passport*** table.

```xml
    <dc:contributor>Théa Poullain</dc:contributor>
```
---

#### dc:publisher

`The entity responsible of making the resource available.`

Always 'Archeovision'.

```xml
    <dc:publisher>Archeovision</dc:publisher>
```
---

#### dc:subject

`A topic link to the resource.` 

Retrieve thanks to ***conservatoire3d_get_pactols_tag_from_object()*** and ***conservatoire3d_get_tags_from_object()*** functions of the database, with the ***id*** of the object. This function find values in the ***conservatoire3d_pactol*** and ***conservatoire3d_tag*** tables.

```xml
    <dc:subject>expérimentation</dc:subject>
    <dc:subject>poterie</dc:subject>
    <dc:subject>Vase</dc:subject>
```
---

#### dc:description

`An account of the resource.`

Retrieve from ***description*** and ***note*** metadatas of the object, through the ***conservatoire3d_passport*** table. Any carriage return or newline characters are replace by a whitespace.

```xml
    <dc:description>Modele 3D de l'eglise Saint Jean Abbetot et de sa crypte (interieur/exterieur)</dc:description>
```
---

#### dc:date

`The date and time of the most recent change to the resource.`

Retrieve from ***date3D*** metadata of the object, through the ***conservatoire3d_passport*** table.

```xml
    <dc:date>2025-01-01T00:00:00Z</dc:date>
```
---

#### dc:type

`The nature of the resource.`

Always '3D object'.

```xml
    <dc:type>3D object</dc:type>
```
---

#### dc:format

`The file format, physical medium, or dimensions of the resource.`

Retrieve from ***structureDocument*** metadata of object's deposit, through the ***conservatoire3d_passport*** table.

```xml
    <dc:format>1 fichier 3D (ply), 3 fichiers image (jpg)</dc:format>
```
---

#### dc:source

`A related resource from which the described resource is derived.`

Retrieve from the ***conservatoire3d_doi*** table using the object's deposit ***id***. It's the DOI of the folder.

```xml
    <dc:source>https://doi.org/10.34969/CND3D/324720.d.2023</dc:source>
```
---

#### dc:language

`A language of the resource.`

Always 'fr'.

```xml
    <dc:language>fr</dc:language>
```
---

#### dc:coverage

`The spatial and/or temporal topic of the resource, spatial applicability of the resource, or jurisdiction under which the resource is relevant.`

This field is a combination of 4 others inserted into ***conservatoire3d_moissonnage*** table :
- ***coverage_da*** : dateArcheologique metadata of the object using the ***conservatoire3d_passport*** table.
- ***coverage_p*** : a thesaurus 'periodo' linked to the object using the ***conservatoire3d_periodo*** table.
- ***coverage_latlng*** and ***coverage_loc_name***: a thesaurus 'geo' linked to the object using 'latlng' and 'name' fields of the ***conservatoire3d_thesaurus_multi*** table. 

These 4 fields are then aggregated in the ***conservatoire3d_oai_records*** table.

```xml
    <dc:coverage>49.49822,0.38533</dc:coverage>
    <dc:coverage>Médiaval</dc:coverage>
    <dc:coverage>Moyen Âge</dc:coverage>
    <dc:coverage>Saint-Jean-d'Abbetot</dc:coverage>
```

#### dc:relation

`A related resource from which the described resource is derived.`

Using the ***conservatoire3d_thesaurus_multi_item*** table the ***id*** of the collection linked to the object is retrieved. Then the name of the collection is retrieved from the ***conservatoire3d_thesaurus_multi*** table. The result is the start of the URL to the collection page and the name of the collection.

```xml
    <dc:relation>https://3d.humanities.science/collection/Eglise de Saint-Jean-d'Abbetot</dc:relation>
```

> **Note**: We asume that an object can only be in one collection.
---

#### dc:rights

`Information about rights held in and over the resource.`

Retrieve from ***proprietaireObjet*** metadata of the object, through the ***conservatoire3d_passport*** table.

```xml
    <dc:rights>Commune de la Cerlangue</dc:rights>
```

### Object V2 (new deposit model)

The table ***conservatoire3d_moissonnageV2*** is feed with the ***feed_conservatoire3d_moissonnageV2.sql*** script. The ***conservatoire3d_oai_records*** table is feed with the ***feed_conservatoire3d_oai_records.sql*** script.

The documentation of the model of metadata used inside the CND3D can be found [here](https://3d.humanities.science/docs/).

#### identifier

`An unambiguous reference to the resource within a given context.`

Retrieve directly from the ***conservatoire3d_object*** table with the field ***doi***.

```xml
    <identifier>https://doi.org/10.34969/CND3D/207944.o.2023</identifier>
```
---

#### datestamp

`The date and time of the most recent change to the resource.`

Retrieve directly from the ***conservatoire3d_object*** table with the field ***date_integration***.

```xml
    <datestamp>2025-01-01T00:00:00Z</datestamp>
```
---

#### setSpec

`The set to which the resource belongs.`

Always 'oai_dc:cnd3d_set0'.

```xml
    <setSpec>oai_dc:cnd3d_set0</setSpec>
```
---

#### dc:identifier

`An unambiguous reference to the resource within a given context.`

Retrieve directly from the ***conservatoire3d_object*** table with the field ***doi***.

```xml
    <dc:identifier>https://doi.org/10.34969/CND3D/207944.o.2023</dc:identifier>
```
---

#### dc:title

`The title of the resource.`

It's the value of the virtual object ***object_name*** metadata with the string '(3D object) at the end. 

```xml
    <dc:title>Lingot bipyramidal (3D object)</dc:title>
```
---

#### dc:creator

`The creators of the resource.`

Retrieved from the ***conservatoire3d_actor*** table. It's the aggregation of actors in the ***object_creator*** metadata of the virtual object. 

```xml
    <dc:creator>Pauline VANDENBROUCK</dc:creator>
    <dc:creator>Barrier, Sylvie</dc:creator>
```
---

#### dc:constributor

`The contributors of the resource.`

Retrieved from the ***conservatoire3d_actor*** table. It's the aggregation of actors in the ***object_contributor*** metadata of the virtual object.

```xml
   <dc:contributor>Durost, Sébastien</dc:contributor>
    <dc:contributor>Guillaume Reich</dc:contributor>
```
---

#### dc:publisher

`The entity responsible of making the resource available.`

This information is retrieved from the ***conservatoire3d_actor*** table introduced by the new deposit model. The information is accessible by reaching the virtual object's ***deposit*** on the ***dm_responsibleEntity*** metadata. Most of the time there's only one responsible entity, but for more safety the aggregation of all the responsible entitie's names is done. 

```xml
    <dc:publisher>Bibracte</dc:publisher>
```
---
 
#### dc:subject

`A topic link to the resource.`

This information is retrieved from the 3 thesaurus tables ***conservatoire3d_thesaurus***, ***conservatoire3d_thesaurus_multi***, ***conservatoire3d_thesaurus_pactols*** and the tag table ***conservatoire3d_tag***. Those informations can be linked to a metadata or not. 

```xml
    <dc:subject>poterie</dc:subject>
    <dc:subject>terre cuite</dc:subject>
    <dc:subject>ACQUISITION</dc:subject>
    <dc:subject>Artefact</dc:subject>
    <dc:subject>coupe</dc:subject>
    <dc:subject>Bibracte, Mont-Beuvray. La nécropole de la Croix du Rebout</dc:subject>
```

> **Note**: The metadatas ***dm_responsibleEntity*** and ***dm_rights*** linked to the deposit of the virtual object are not retrieved here even if they are linked to a thesaurus. The license can be found on the rights tag and the responsible entity on the publisher tag.
---

#### dc:description

`An account of the resource.`

Retrieved from the virtual object's ***object_description*** metadata.

```xml
    <dc:description>
        Bas-relief en forme d'ogive. 
        Dans un cadre un ange auréolé à gauche avec épée et bouclier en combat un autre tombé à droite sans ailes.
    </dc:description>
```
---

#### dc:date

`A point or period of time associated with an event in the lifecycle of the resource.`

Retrieved from the virtual object's ***object_documentedDate*** metadata. If the ***object_documentedDate*** metadata can't be found, it's the value of the ***dp_date*** metadata of the linked deposit that's retrieved. This date respect the ISO 8601 standard (https://en.wikipedia.org/wiki/ISO_8601).

```xml
    <dc:date>2024</dc:date>
```
---

#### dc:type

`The nature or genre of the resource.`

Always '3D object'.

```xml
    <dc:type>3D object</dc:type>
```
---

#### dc:format

`The file format, physical medium, or dimensions of the resource.`

Retrieved from the deposit's ***ds_content*** metadata of the deposit linked to the virtual object.

```xml
    <dc:format>1 fichier 3D (ply) 1 fichier image (png)</dc:format>
```
---

#### dc:source

`A related resource from which the described resource is derived.`

Retrieved from the physical object's ***po_name*** metadata. If the virtual object doesn't have a physical object, it's the DOI of the folder that is retrieved.

```xml
    <dc:source>Tonnelet (Inv. 998.9.5262.58)</dc:source>
```
---

#### dc:language

`A language of the resource.`

Always 'fr'.

```xml
    <dc:language>fr</dc:language>
```
---

#### dc:coverage

`The spatial and/or temporal topic of the resource, spatial applicability of the resource, or jurisdiction under which the resource is relevant.`

Retrieved from the ***conservatoire3d_datatation*** table using the physical object's ***po_discovery_date*** and ***po_creation_datation*** metadatas. It agregates ***date_min***, ***date_max*** and ***date_litteral*** of each datatation. 

It also retrieved data from the ***conservatoire3d_location*** table using the ***po_discovery_location***, ***po_creation_location*** and ***po_retention_location*** metadatas of the physical object. It aggregates ***name*** and ***longlat*** of each location.

The values are filter to only keep distinct values.

A link to the ***conservatoire3d_thesaurus_periodo*** table is done using the ***conservatoire3d_datation*** table.

```xml
    <dc:coverage>-0100-01-01</dc:coverage>
    <dc:coverage>1994-01-01</dc:coverage>
    <dc:coverage>1er siècle avant notre ère</dc:coverage>
    <dc:coverage>1998</dc:coverage>
    <dc:coverage>Ie siècle AEC</dc:coverage>
    <dc:coverage>46.93228,4.0489</dc:coverage>
    <dc:coverage>46.92726,4.03717</dc:coverage>
    <dc:coverage>Bibracte - Site et musée</dc:coverage>
    <dc:coverage>Bibracte, Mont-Beuvray. Îlot des Grandes Forges</dc:coverage>
```
---

#### dc:relation

`A related resource.`

Retrieved from the virtual object's ***object_collection*** metadata. It is found in the ***conservatoire3d_thesaurus_multi*** table using the value in the ***conservatoire3d_passport*** table. 

```xml
    <dc:relation>Collection 1</dc:relation>
```

> **Note**: We asume that a virtual object can only be in one collection.
---

#### dc:rights

`Information about rights held in and over the resource.`

Retrieved from the deposit's ***ds_rights*** metadata.

```xml
    <dc:rights>CC BY SA</dc:rights>
```

