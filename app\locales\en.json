{"admin": {"category": {"folder": "Folders", "file": "Files", "user": "Users", "group": "Groups", "metadata": "<PERSON><PERSON><PERSON>", "thesaurus": "Thesaurus", "organization": "Agent / Actor & Organization"}, "folder": {"manage": "Folders management (order, edit)", "order": "Edit folders tree order", "download": "Download folders", "maintenance": "Switch folders to maintenance state", "permissions": "Status public/private", "public": "Public folders", "private": "Private folders"}, "file": {"sync": "Files synchronization", "json": "JSON file import", "syncIPTC_EXIF": "Ajouter métadonnées IPTC/EXIF"}, "user": {"addTemp": "New repository users", "list": "Users list", "add": "Create users", "edit": "Edit users", "permissions": "Users permissions", "assign": "Assign scribes to projects", "entity": "Entity management", "deleteTemp": "Delete temporary users", "history": "Changes history"}, "group": {"list": "List groups", "add": "Create groups", "edit": "Edit groups", "permissions": "Groups permissions"}, "metadata": {"csv": "Upload CSV metadata", "tags": "Upload tags", "addModel": "Create a metadata model", "editModel": "Edit metadata model"}, "thesaurus": {"order": "Change thesaurus order", "add": "Add thesaurus elements", "link": "Link thesaurus and projects"}, "organization": {"sort": "Organizations - View, edit, delete", "add": "Add organizations"}, "actor": {"sort": "Actors - View, edit, delete", "add": "Add an actor"}, "content": {"BranchSelection": "Impact branch", "ProjectSelection": "Impact project", "SearchProject": "Search a project", "folder": {"manage": {"submit": "Submit order", "branchFormTitle": "Which Archeogrid branch to manage folders from?", "edit": {"displayFile": "Choose a display image to represent the project: "}}, "order": {"projectSelection": "Choose a project to modify its tree order", "submit": "Submit per level", "depth": "De<PERSON><PERSON>"}, "download": {"title": "Which branch do you want to authorize downloads from?", "downloadable": "Downloadable folders", "notDownloadable": "Non downloadable folders"}, "permissions": {"makePublic": "Set public", "makePublicExplanation": "If a folder is declared public, then all its parent folders are made public", "makePrivate": "Set private", "makePrivateExplanation": "If a folder is declared private, then all its subfolders become private", "noneAssigned": "No projects assigned"}}, "file": {"sync": {"title": "Synchronize files in database", "rules": "The following folders will never be synchronized in the database:", "rule1": "Folders whose names begin with '_', example: '_images'", "rule2": "Archivage3D folders, created by default for any project", "chooseFolder": "Choose a folder", "modes": "Synchronization mode: ", "mode1": "Folders only", "mode2": "Files only", "mode3": "Folders and files", "delete": "Synchronization and delete database file references that do not exist anymore in the file system?", "recursion": "Enable recursion?", "time1": "Synchronization in", "time2": "step(s)", "timeInfo": "You executed", "summaryStep1": "Synchronization in", "summaryStep2": "step(s)", "summaryDelete": "Enable deletion?"}}, "group": {"edit": {"form": "Which group?"}}, "metadata": {"addModel": {"form": {"name": "Name of the new model", "visible": "Visibility", "now": "Now", "later": "Later", "projects": "Which projects are affected?", "all": "All", "select": "Selection", "type": "Type of the affected data", "desc": "Description for the model"}}, "selectModel": "Selected model", "confirmDeleteTitle": "Delete the model", "confirmDelete": "Do you really want to delete this metadata model?"}, "thesaurus": {"link": {"form": {"project": "Project:", "type": "Thesaurus type:"}}}, "user": {"history": {"type": "Modification type"}}, "csvMetadata": {"CreateTable": "Create table", "UpdateTable": "Update table", "CreateModel": "Create model", "UpdateModel": "Update model", "IngestCSV": "Ingest CSV", "TableContent": "Table content", "ModelContent": "Model content", "ListOfCSV": "List of available CSV for", "ModelItemType": "Type of items for the model", "TableItemType": "Type of items for the table", "LastTableUpdate": "Last ingestion in the table", "LastModelUpdate": "Last ingestion in the model", "TableName": "Table name", "ModelName": "Model name", "Menu": "Menu CSV metadata", "searchCsvMetadataLabel": "Search by project or csv file name", "MetadataCSVTitle": "Creation of metadata via CSV", "MetadataCSVDescription": "Feature allowing to introduce data within ArcheoGRID via a CSV file.", "csvMetadataTableOverview": "Previous ingest", "moreOptionButton": "More options", "ingest": {"IngestCSVTitle": "Ingest a CSV", "IngestCSVDescription": "Ingest a CSV file into a table and/or a metadata model. Model update uses the data in the table and not directly the ones in the CSV.", "InfoSelectRow": "Select a row to access the actions", "InfoSelectRowIngestAvailable": "You can ingest the CSV or update the table and/or the model", "InfoSelectRowCreateTable": "Create the table before being able to ingest the CSV", "InfoSelectRowCreateModel": "Create the model before being able to ingest the CSV"}, "model": {"CreateModelTitle": "Create a model", "CreateModelDescription": "Create a metadata model from the columns of a CSV file. The different fields will be mapped to the available types (status).", "Code": "Code of the metadata", "Label": "Label of the metadata (table metadata_label)", "Description": "Description of the metadata (table metadata_label)", "Type": "Type of the metadata (status of the table metadata)", "IsUnique": "Indicates if the metadata has only one value (isunique of the table metadata)", "IsRequired": "Indicates if the metadata is required (y of the table metadata)", "CreateModelEndTitle": "Model creation success", "CreateModelEndDescription": "The metadata model has been created with success. You can now ingest the CSV in the model or if it is not yet the case, create the corresponding table."}, "table": {"CreateTableTitle": "Create a table", "CreateTableDescription": "Create a table in the database from the columns of a CSV file.", "ConfirmUpdate": "Do you really want to update the table", "AllDataWillBeLost": "Previous data of the table will be lost", "CreateTableEndTitle": "Table creation success", "CreateTableEndDescription": "The table has been created with success. You can now create the corresponding metadata model or if it is already done, ingest the CSV."}, "quick": {"summary": {"table_creation": "Table creation", "model_creation": "Model creation", "metadata_creation": "<PERSON><PERSON><PERSON> creation for the model", "link_creation": "Linking of the table and the model", "table_ingestion": "Data ingestion in the table", "model_ingestion": "Data ingestion in the model"}, "SummaryTitle": "Quick ingestion finished", "SummaryDescription": "Here is a summary of the actions performed. If one of the actions has failed, you can re-launch it from the menu.", "NewQuickIngest": "New quick ingestion", "SeeContent": "See ingested data", "SeeModelContent": "See model content", "SeeTableContent": "See table content", "QuickIngestTitle": "CSV ingestion", "QuickIngestDescription": "A step by step guide to ingest a CSV in the database with default options. Make sure to place your CSV in the 'metadata' folder of your project, and that the first column contains the names of the items.", "OtherFeaturesTitle": "More options", "OtherFeaturesDescription": "If you want to manage the ingestion of the CSV manually, or if you want to update the ingested data, you can use specific features by clicking on the button below"}, "NoCSV": "No CSV available", "UseTab": "Use tab", "DelimiterError": "Provide a delimiter, or click on the 'use tab' button", "TableNameError": "Provide a table name", "StartQuickIngest": "Start ingestion", "extractKeywords": "Extract keywords", "addKeywordsFromMetadata": {"Title": "Extract keywords", "Description": "Allow to extract keywords from a metadata."}, "moreInfo": "More information", "TableContentDescription": "When ingesting, all raw data from the CSV file are transferred to a database table. These data are not directly accessible or displayed, but are used to create the metadata of the items following the created model.", "ModelContentDescription": "Once ingestion is completed, metadata of the items are created following the model. These data are displayed within ArcheoGRID. Only the items that have been processed/found are displayed. You can update the metadata of the items by clicking on the 'Ingest' button at the top. You can also extract keywords from a metadata by clicking on the 'Extract keywords' button."}}}, "scribe": {"noUserModel": "No accessible user model", "noAssignedUser": "No assigned user", "noAssignedProjects": "No assigned project"}, "errors": {"user_not_exist": "User does not exist", "bad_password": "Incorrect Password"}, "advanced_model": {"actor_literal_label": "Full name", "actor_literal_placeholder": "ex: <PERSON> or CNRS", "actor_identifier_label": "URI", "actor_identifier_placeholder": "(orcid, viaf...) ex: http://viaf.org/123456789", "actor_type_label": "Actor type", "actor_type_person_label": "Person", "actor_type_organization_label": "Organization", "actor_mandatory_error": "Both name and type of the actor are required", "actor_invalidUri_error": "Invalid URI", "actor_modify_actor_warning": "Warning, modification of an existing actor. To create a new actor, enter another name.", "datation_date_min_label": "Start date", "datation_date_min_placeholder": "ex: 2024-01-01 or 2024-01 or 2024", "datation_date_max_label": "End date", "datation_date_max_placeholder": "ex: 2024-12-31 or 2024-12 or 2024", "datation_date_literal_label": "Literal date", "datation_date_literal_placeholder": "ex: The whole year 2024", "datation_mandatory_error": "Start date of the datation is required", "datation_invalidDateMax_error": "Use the format AAAA-MM-JJ, AAAA-MM, AAAA or leave the end date empty", "datation_invalidDate_error": "Use the format AAAA-MM-JJ, AAAA-MM or AAAA", "location_name_label": "Localisation name", "location_name_placeholder": "ex: Bordeaux city center", "location_geonames_label": "Geonames code", "location_geonames_placeholder": "ex: 6455058", "location_latlng_label": "GPS coordinates", "location_latlng_placeholder": "ex: 44.833333,-0.566667", "location_mandatory_error": "Fill at least one of the location fields", "location_geonames_error": "Invalid geonames code"}, "addKeyword": {"title": "Add keyword", "addingMode": "Adding mode", "addFromScratch": {"title": "Free add"}, "addFromMetadata": {"title": "Add from metadata", "metadataName": "Metadata name", "form": {"inMetadataLabel": "Input metadata", "inMetadataDescription": "Choose the metadata to add keywords from", "delimiterLabel": "Delimiter", "delimiterDescription": "Choose the delimiter of the keywords", "thesaurusLabel": "Thesaurus", "thesaurusDescription": "Choose the thesaurus of destination", "thesaurusSimpleWarning": "The simple thesaurus type allows only one concept to be linked to the item. If the metadata contains several concepts, only the first will be used.", "outMetadataLabel": "Output metadata", "outMetadataDescription": "Choose the metadata in which the keywords will be added. You can also define keywords as free.", "outMetadataFreeTags": "Free tags", "outMetadataCreateTags": "Create free tags if they don't exist", "outMetadataFilters": "Filters"}, "results": {"parameters": "Parameters", "results": "Results", "ignoredItems": "Ignored items", "foundThes": "Keywords found in thesaurus", "foundFreeTags": "Keywords found as free tags", "createdTags": "Mots clé libres créer", "notFoundTags": "Mots clé non trouvés"}, "outMetadataError": "No matching metadata found with given filters"}}, "Restart": "Recommencer", "3dmodel": "3D Model", "3dmodellower": "3D model", "3dmodels": "3D Models", "3dmodeldescription": "3D Model description", "8charmin": "8 characters minimum", "a": "a", "ae": "a", "about": "about", "actions": "Actions", "actor": "Actor / Person", "actorexists": "This person already exists (same name, same surname)", "activity": "Activity", "add": "Add", "addCollection": "Add collection", "added": "Added", "addactor_success": "Actor created successfully", "addgroup_success": "Group created successfully", "addObjVirt": "Add Virtual Object to deposit", "address": "Address", "addSearchbar": "Add a new searchbar (AND)", "addactor_head": "Create new actor / agent / person", "addorganization_head": "Create new organization", "addorganization_success": "Organization created successfully", "addthesauruselem": "Add new or translate concept", "adduser_head": "Create new user", "adduser_success": "User created successfully", "addvirtualfolder": "Add folder", "addzip": "Add to a zip to download later", "advancedSearch": "Advanced search", "agent": "one of its agents", "aligned": "aligned", "alignedPlural": "aligned", "all": "All", "allFields": "All fields", "allfolderitems": "all items of folder", "allow": "allow", "already": "already", "amharique": "amharique", "AND": "AND", "and": "and", "annotation": "Annotation", "archeogriddef": "collaborative tool for the management of all documentation (iconograhy, surveys, ...) of projects in Digital Humanities integrating 3D : annotation, indexing, preservation, dissemination", "askproject": "Ask your administrator to assign a project", "assigned": "assigned", "associated": "associated", "associatedFile": "associated file", "associatedFileplur": "associated files", "associatedObject": "associated object", "associatedObjectplur": "associated objects", "at": "at", "atleast": "at least", "aton3dtext": "Gathering all documentation about the ancient city of Amarna and worship of <PERSON><PERSON>", "author": "Author", "authorizedFolder": "Authorized folder(s)", "back": "Back", "backprojectpage": "Back to the project page", "backtotop": "Back to top", "begin": "Begins with", "beginEntertext": "Please, begin entering text", "bienvenue": "Welcome ", "branch": "branch", "browse": "Browse", "browseThesauri": "<PERSON><PERSON><PERSON>", "browseThesaurus": "Browse thesaurus", "by": "by ", "byteam": "by Archeovision team", "cancel": "Cancel", "changeOrder": "Change order", "checkAll": "Check all", "checkPage": "Check page", "checkAuthorRightsLeave": "I have all the rights on this/these document(s) and I assign all my rights for the use  of this/these images to the scientific association", "choose": "<PERSON><PERSON>", "chronology": "Chronology", "citation": "citation", "click": "click", "cnd3d": "The National 3D Data Repository", "cnd3dInfo": "The National 3D Data Repository for Humanities", "collapseFolders": "Collapse folders", "collection": "Collections", "collectionexists": "This collection already exists", "collections": "Collections", "foldersHeader": "Folders", "codecorpus": "Corpus code", "codeproject": "Project code", "comment": "Add comment", "concept": "concept", "concerned": "concerned", "concerned_plur": "concerned", "confirm": "Confirm", "confirmDeleteAll": "Delete all items in this virtual folder ?", "confirmpassword": "Please confirm password", "connecte": "Log in", "connecteArcheoGRID": "Log in with ArcheoGRID account", "connecteNH": "Log in with HumanId of Huma-Num account", "content": "Content", "copyrights": "Copyright Credit Line", "Corpus": "ArcheoGRID Corpus", "corpus": "corpus", "corpustext": "Iconographic Corpus of Archeovision", "country": "Country", "create": "create", "Create": "Create", "createGroup": "Create a group", "createUser": "Create a user", "creationDate": "creation date", "crowdsourcing": "Crowdsourcing documentation", "crowdsourcingModeration": "Image(s) will be visible on the site only if they have been approved by our scientific moderators.", "deconnecte": "Log out", "dashboard": "Dashboard", "data": "data", "datation": {"date_min_label": "Start", "date_max_label": "Fin", "date_literal_label": "Literal"}, "date": "Date", "dateExplicite": "For the date of", "delete": "delete", "deleted": "deleted", "Delete": "Delete", "deleteAll": "Delete all", "deleteSuccessful": "Successful deletion", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositor": "Depositing entity", "depotname": "Name of the deposit", "depotfinal": "Finalize the deposit", "different": "Different", "digitalResource": "Digital Resource", "digitalResources": "Digital Resources", "dirname": "Folder name", "disconnected": "You are not connected.", "displayGrid": "Display grid", "displayList": "Display list", "displayMode": "Display mode (grid/list)", "documentsDisplayed": "Displayed documents", "documentsSelected": "Selected documents", "selected": "Selected", "download": "Download", "eachFields": "Each fields", "edit": "Edit/Change", "editGroup": "Edit a group", "editUser": "Edit a user", "emptyName": "Empty name", "enrichData": "Add metadata", "enter": "Enter", "enterDate": "Enter date", "entity": "Entity", "equal": "Equal", "eraseLot": "Overwriting existing data ?", "eraseSearch": "Erase all search parameters", "eraseTree": "Erase folders selection", "error": "Error", "EuropeanaRights": "Europeana Rights", "event": "event", "everyExtensions": "Extensions", "everyFolders": "Every folders", "expandFolders": "Expand folders", "extp3d": "Collaborative 3D Projects", "extp3d_sing": "Collaborative 3D Project", "extp3dtext": "ArcheoGRID dedicated to any 3D SHS project", "file": "File", "file2": "file", "files": "files", "fileEnterDate": "File enter date in ", "fileExtension": "fileExtension", "filename": "File name", "filesize": "File size", "fileSynchro": "Files Synchronization", "filesSynchro": "Synchronization of files in database", "fileupload": "Upload file(s)", "fillerror": "Error in form", "fillform": "Thanks to fill in the form.", "firstDepotDemand": "Ask for a deposit", "folder": "folder", "folders": "folders", "for": "for", "forbid": "forbid", "forbidden": "Forbidden - Not enough rights", "forbiddenFolder": "Forbidden folder(s)", "forwriting": "for writing", "freetags": "free tags", "from": "from", "From": "From", "generalType": "General type", "give": "Give", "globalFile": "Global file", "goandsee": "Go and See", "group": "the group", "groupSimple": "group", "groupsList": "List of groups", "groupPermissions": "Permissions for the group", "groupname": "Name of the group", "groups": "Groups", "havetoconnect": "You have to be connected to have access to the service", "heritageAsset": "Heritage Asset", "heritageAssetType": "Heritage Asset Type", "hide": "<PERSON>de", "idParentNewThesaurusElem": "Identifier of the parent element in thesaurus", "Identifier": "Identifier", "identifier_other": "Other identifier (idREF, idHAL...)", "identifier_other_comment": "Please give complete URI of the identifier. For instance", "imagesize": "Image size", "in": "in", "include": "Include", "index": "Index", "indexed": "indexed", "indexItems": "indexed item(s) ", "infoDepot": "You can upload multiple documents at a time if they have approximately the same dates (1 form to fill in this case).", "information": "Information", "inNameOf": "In the name of", "insertImpossible": "Insert of folder not possible", "itemType": "itemType", "karnaktext": "ArcheoGRID Karnak, dedicated to data extracted from Karnak temples, managed by Cfeetk", "keyword": "Keyword", "keywordUpdated": "Keyword added or updated", "keywords": "Keywords", "license": "License", "licenses": "licenses", "link": "Link", "location": "Location", "loginbad": "Oups, Something goes wrong with your login...", "mail": "Email address", "mailcontact": "Email address (public) for any contact", "mailto": "send an email", "maintenance": "Site under maintenance", "mandatory": "mandatory field", "mandatoryMessageCheck": "Please select at least one option", "menu": "menu", "Menu": "<PERSON><PERSON>", "message_addSelection": "Error while inserting selection items ... duplicate rows have been found. Try to eradicate them in folder or in selection and try again ...", "message_notlogged": "You have to be logged in to access this document", "message_notaccess": "You do not have access to this document", "message_norightstopath": "Not enough rights to patch", "message_errordelete": "Error during delete", "message_norightstodel": "Not enough rights to delete", "metadata": "<PERSON><PERSON><PERSON>", "model": "Model", "moreinfo": "More information", "moreinfotext": "Visit Archeovision / Archeovision production website", "multipleIndex": "Multiple indexing", "multipleIndexing": "Multiple indexing of the entire folder", "name": "Name", "Name": "Name", "fname": "First name", "lname": "Last name", "nameNewThesaurusElem": "Name of new thesaurus element", "new": "New", "newDepotDemand": "Make a deposit", "newDepotDemand2": "Upload another file", "newProject": "New site project", "newOP": "New scientific project", "no": "No", "no2": "No", "no3": "Not", "no4": "No ", "nologin": "No account ? Please, contact us", "noFolders": "No folders", "noVirtualFolders": "No collections", "nodata": "There is no metadata yet.", "nodirectory": "Please only upload file(s), no directories (folder).", "none": "none", "None": "None", "note": "Comment", "notFound": "Not found - Does not exist or deleted", "number": "Number", "object": "Object", "object2": "the object", "objectName": "Object name", "objectEnterDate": "Object enter date in ", "objectVirtuel": "Virtual Object(s)", "of": "of", "of2": "of", "of3": "of", "of4": "of a", "of5": "of the", "ofpluriel": "of the", "one": "one", "only": "only", "or": "or", "organization": "organization", "Organization": "Organization", "organizationexists": "This organization already exists", "organizationname": "Name of the organization", "organizationName": "Organization name", "other": "Other", "overall": "Multi-site projects", "overallprojects": "Multi-site projects", "overallproject": "Multi-site project", "passport": "Passport", "password": "Password", "passwordbad": "Incorrect password", "period": "Period", "person": "person", "Person": "Person", "phone": "Phone", "places": "Places", "placeholderdepot": "<GEOGRAPHICAL LOCATION>_<DEPOSIT OBJECT>", "polygonal_selection": "Click on the picture to add points.", "private": "Private", "project": "Project", "Project": "Project", "projectsing": "a project", "projectplur": "Projects", "projectpage": "Project page", "projects": "Archeovision 3D projects", "public": "public", "readRights": "Read only rights", "readWriteRights": "Read and Write rights", "registerSelectionSuccess": "Successfully registered select", "removeCollection": "Remove from collection", "removeSearchbar": "Remove the searchbar", "representing": "representing", "representative_image": "Representative image for the project", "required": "Required field", "requisit_for_deposit": "If the 3D data have been produced within a scientific research project in SHS, 3D data can be deposit in the conservatory. In order to do this, you should own a user account.", "reset": "Reset", "resource": "resource", "resourceMaj": "Resource", "resources": "resources", "result": "Result", "returnHome": "Return home", "rights": "Rights", "sameDate": "If you know the exact date of production of the document(s) to be filed, please enter the same date in both date fields", "save": "Save", "savechange": "Save changes", "saveSearch": "Save search", "saveUnico": "Save unico", "savedSearches": "Saved Searches", "savedSearchError": "Could not saved your search.", "savedSearchName": "Which name would you want to give to your search ?", "savedSearchSuccess": "Your search has been successfully saved.", "search": "Search for", "searchAllThesaurus": "Search all thesauri", "asearch": "Search ", "see": "See", "selectedZone": "Selected zone", "selectionDelete": "Delete selection", "myselection": "My selection", "selectionName": "You must give a name.", "selectionSave": "Save selected", "selectionOpen": "Open selection", "selectionSuccess": "Unico properly registered", "send": "Send message", "signature": "Full name for user signature, used in comments, informations for unique identification resources, ...", "signup_short": "Sign up", "signup": "Request for a login at the National 3D Repository for Humanities", "signupbademail": "Please, enter a valid email address", "signupbadpassword": "the 2 password are not equal, please try again", "signupfailure": "Unable to create account, please try again", "signupko": "I do not have any account (ask for one)", "signupok": "I have an account (login)", "signupsuccess": "Your request has been registred.", "simpleSearch": "Simple search", "simple": "simple", "size": "Expected amount of 3D data", "sourceFile": "source file", "soutien HN": "With TGIR  Huma-Num support", "spatial_coverage": "Spatial coverage", "start": "Get started", "start2": "Start", "status": "status", "Status": "Status", "subject": "Subjects", "submit": "Submit the form", "success": "Operation succeed", "synchronize": "synchronize", "synchronized": "synchronized", "synchronizeName": "the synchronization", "synchRec": "Enable recursion - synchronization for all subfolders", "synchRecInfo": "Recursively: ", "synchResult": "References in database", "synchResultFolder": "Number of folders synchronized: ", "synchResultFile": "Number of files synchronized: ", "synchResultEmpty": "No new item to synchronize", "synchResultDelete": "Number of database references", "synchResultMayDelete": "that could have been ", "synchLastTime": "Last step", "synchTimeInfo": "You have run", "synchExecute": "Please, execute again", "synchExecuteButton": "Execute", "talatattext": "Research initiated by <PERSON> on talatat stones discovered in Amarna", "temp_user_head": "Temporary user waiting for creation", "thanksfillform": "Thank you for filling out the form", "the": "the", "the2": "the", "the3": " ", "thepluriel": "the", "thesaurusManagement": "Thesaurus management", "thesaurus": "the thesaurus", "thesaurus_name_label": "Thesaurus name (code)", "thesaurusOnly": "thesaurus", "thesaurusType": "Thesaurus type", "this": "this", "time": "time(s)", "title": "Title", "to": "to", "to2": "to", "translateConcept": "Translation for a concept", "translation": "Translation", "translationLng": "Language of translation", "translationName": "Concept to translate", "type": "to type", "types": "Types", "txt_link_button": "Link", "txt_integrate": "Integrate selected items in a virtual folder: ", "txt_integrate_button": "Integrate", "txt_integrate_obj": "<PERSON>, associate selected items to an object: ", "txt_illustrate_obj": "Use the item to illustrate object: ", "txt_illustrate_button": "Illustrate", "uncheckAll": "Uncheck all", "uncheckPage": "Uncheck page", "unicoDeleteSuccess": "Unico succesfully deleted", "unicoType": "Type of the unico (shape)", "rawUnicoData": "Raw data of unico", "updateDate": "Update date", "upload": "upload", "user": "User", "user2": "user", "userconnected": "Number of connected users", "userDeleted": "User deleted !", "userexists": "User already exists", "userEntity": "User Entity", "usersList": "Users list", "userPermissions": "User's permissions", "username": "User name ", "userparam": "Parameters", "usernb": "Number of users", "validate": "validate", "visitnb": "Number of visit", "visualization": "Visualization", "visualizeItem": "Visualize items in a new tab", "which": "which", "with": "with", "writeRights": "Write rights", "wrongProject": "This project does not exist", "more": "more", "close": "Close", "backToProject": "Back to project", "selectionTitle": "Selection", "selectionDescription": "Selection description", "selectionFor": "Selection for", "items": "Items", "display": "View", "selectionControls": "Selection tools", "displayOptions": "Display settings", "gridView": "Grid view", "listView": "List view", "itemsPerPage": "Items per page", "saveSelection": "Save selection", "noItemsSelected": "No items selected", "selectAtLeastOne": "Please select at least one item", "selectOnlyOne": "Please select only one item", "virtualFolders": "Virtual folders", "createVirtualFolder": "Create a virtual folder", "noResultsFound": "No results found", "ofPages": "of {{count}} pages", "selectAll": "Select all", "loadingMapData": "Loading map data...", "fewerItemsPerRow": "Fewer items per row", "moreItemsPerRow": "More items per row", "errorLoadingMapLibrary": "Error loading map library.", "errorLoadingMap": "Error loading map.", "loadingMap": "Loading map...", "exploreRandomNoResult": "No random results found.", "noResult": "No result found.", "errorOccured": "An error occurred.", "emptyResponse": "No data to display.", "nbResultsText": "documents found", "no_title": "No title", "not_authorized": "Not Authorized", "normalView": "Normal view", "compactView": "Compact view", "selectCollection": "Select a collection", "selectObject": "Select an object", "browsePACTOLS": "Browse PACTOLS", "loading": "loading", "allThesauri": "All thesauri", "collection_label": "Collection", "all_collections": "All collections", "Next": "Next", "Previous": "Previous", "copyMetadata": "Copy the metadata", "pasteMetadata": "Paste the metadata", "Ingest": "Ingest", "File": "File", "Folder": "Folder", "Object": "Object", "AllFP": "All", "Branch": "Branch", "Today_at": "Today at", "ModelDescription": "Model description", "Table": "Table", "Model": "Model", "Delimiter": "Delimiter", "Step": "Step", "selectFolder": "Select folder", "tagsThesaurus": "Thesaurus tags", "select": "Select"}