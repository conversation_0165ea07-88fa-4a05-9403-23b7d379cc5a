<div class="container">
    <h2><%=__('admin.metadata.editModel') %></h2>
    <div class="d-flex justify-content-between mb-3">
        <h3 class="m-0"><%= metadata_model.name %></h3>
        <div>
            <button id="form-add-metadata" class="btn btn-sm btn-outline-secondary"><i class="fa fa-plus"></i></button>
            <% if (delete_allowed) { %>
            <button id="form-delete-model" class="btn btn-sm btn-danger" data-bs-toggle="modal"
                    data-bs-target="#delete-model"><i class="fa fa-times"></i></button>
            <% } %>
        </div>
    </div>
    <form id="main-form" name="main-form">
        <ul id="form-data" class="list-group mb-3"></ul>
        <button id="form-submit" type="submit" class="btn btn-primary"><%= __('submit') %></button>
    </form>
</div>

<div class="modal fade" id="delete-model" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered custom-modal">
        <div class="modal-content custom-modal">
            <div class="modal-header">
                <h1 class="modal-title fs-5"><%= __('admin.content.metadata.confirmDeleteTitle') %></h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <%= __('admin.content.metadata.confirmDelete') %>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <%= __('cancel') %>
                </button>
                <button id="confirm-delete-model" type="button" class="btn btn-outline-success">
                    <%= __('confirm') %>
                </button>
            </div>
        </div>
    </div>
</div>

<div class="toast-container bottom-0 end-0 mb-4 me-4">
    <div class="toast success text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                Update metadata model success
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<div class="toast-container bottom-0 end-0 mb-4 me-4">
    <div class="toast error text-bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                Update metadata model error
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
        </div>
    </div>
</div>

<script>
    const form = document.getElementById('main-form');
    const form_data = document.getElementById('form-data');
    const addMetadata = document.getElementById('form-add-metadata');
    const submit = document.getElementById('form-submit');
    const confirmDelete = document.getElementById('confirm-delete-model');

    const upperWords = (str) => {
        return str.charAt(0).toUpperCase() + str.substr(1);
    }

    const getRank = () => {
        return structuredClone(form_data.childElementCount + 1);
    }

    const createCheckboxGroup = (label, rank) => {
        const div = document.createElement('div');
        div.classList.add('form-check')

        const labelElement = document.createElement('label');
        labelElement.classList.add('form-check-label');
        labelElement.innerText = label;
        div.appendChild(labelElement);

        const input = document.createElement('input');
        input.setAttribute('type', 'checkbox');
        input.classList.add('form-check-input')
        input.name = `${rank}_${label}`
        div.appendChild(input);

        // [parent div, checkbox element]
        return [div, input];
    }

    const createInputGroup = (name, type = 'text') => {
        const div = document.createElement('div');

        const label = document.createElement('label');
        label.classList.add('form-label', 'opacity-75');
        label.innerText = name.slice(0, 1).toUpperCase() + name.slice(1);
        div.appendChild(label);

        if (type === 'text') {
            const input = document.createElement('input');
            input.name = getRank() + '_' + name;
            input.setAttribute('type', 'text');
            input.setAttribute('required', true);
            input.classList.add('form-control');
            div.appendChild(input);
        } else {
            const input = document.createElement('textarea');
            input.name = getRank() + '_' + name;
            input.setAttribute('required', true);
            input.classList.add('form-control', 'form-control-sm');
            div.appendChild(input);
        }

        return div;
    }

    const createListGroup = (name, elements, selected) => {
        const select = document.createElement('select');
        select.classList.add('form-select', 'form-control-sm');
        select.name = getRank() + '_' + name;

        for (const element of elements) {
            const option = document.createElement('option');
            option.innerText = element;
            option.value = element;
            option.selected = element === selected;
            select.appendChild(option);
        }

        return select;
    }

    const createThesaurusList = (rank, thesaurus, thesaurus_name, thesaurus_selected, collections, selected_collection)=> {
        const container = document.createElement('div');
        const label = document.createElement('label');
        label.classList.add('form-label', 'opacity-75');
        label.innerText = "<%= __('thesaurus_name_label') %>";
        container.appendChild(label);
        const list = document.createElement('select');
        list.classList.add('form-select', 'form-control-sm');
        list.name = `${rank}_thesaurus`;
        list.id = `${rank}_thesaurus`;
        list.setAttribute('thesaurus', thesaurus_name);

        for(const thes of thesaurus) {
            const option = document.createElement('option');
            option.innerText = upperWords(thes);
            option.value = thes;
            if(thes === thesaurus_selected) option.selected = true;
            list.appendChild(option);
        }
        if(thesaurus.length === 1) {
            list.childNodes[0].selected = true;
        }
        container.appendChild(list);

        if(thesaurus_name === 'pactols') {
            const collection_label = document.createElement('label');
            collection_label.classList.add('form-label', 'opacity-75');
            collection_label.innerText = "<%= __('collection_label') %>";
            collection_label.style.marginTop = '0.5rem';
            container.appendChild(collection_label);

            const select_collection = document.createElement('select');
            select_collection.classList.add('form-select', 'form-control-sm');
            select_collection.name = `${rank}_function`;
            select_collection.id = `${rank}_collection`;
            
            let selected = false;
            for(const collection of collections){
                let option = document.createElement('option');
                option.innerText = `${upperWords(collection.name)} (${collection.code})`;
                option.value = collection.thesaurus_path;
                select_collection.appendChild(option);
                if(selected_collection === collection.thesaurus_path){
                    option.selected = true;
                    selected = true;
                }
            }

            let option = document.createElement('option');
            option.innerText = "<%= __('all_collections') %>";
            option.value = 'all';
            select_collection.appendChild(option); 
            if(!selected) option.selected = true;

            container.appendChild(select_collection);
        }
        return container
    }

    const updateThesaurusList = (rank, thesaurus, thesaurus_name) => {
        const list = document.getElementById(`${rank}_thesaurus`);
        list.innerHTML = "";
        list.setAttribute('thesaurus', thesaurus_name);

        for(const thes of thesaurus) {
            const option = document.createElement('option');
            option.innerText = thes;
            option.value = thes;
            list.appendChild(option);
        }
        if(thesaurus.length === 1) {
            list.childNodes[0].selected = true;
        }
    }
    
    const createRangeGroup = (rank) => {
        const range = document.createElement('div');
        range.classList.add('d-none', 'd-flex', 'gap-2', 'align-items-center');
        const minLabel = document.createElement('label');
        minLabel.innerText = "Min";
        range.appendChild(minLabel)
        const min = document.createElement('input');
        min.classList.add('form-control');
        min.value = 0;
        min.name = `${rank}_min`;
        min.type = "number";
        range.appendChild(min);
        const maxLabel = document.createElement('label');
        maxLabel.innerText = "Max";
        range.appendChild(maxLabel)
        const max = document.createElement('input');
        max.classList.add('form-control');
        max.value = 0;
        max.name = `${rank}_max`;
        max.type = "number";
        range.appendChild(max);

        return range;
    }

    const createAppendGroup = (rank) => {
        const wrapper = document.createElement('div');
        wrapper.classList.add('d-flex', 'flex-column', 'gap-2');

        const first_line = document.createElement('div');
        first_line.classList.add('d-flex', 'justify-content-between');

        const choice_label = document.createElement('div');
        choice_label.innerText = "Choice list";
        first_line.appendChild(choice_label);

        const first_line_buttons = document.createElement('div');
        first_line_buttons.classList.add('d-flex', 'gap-1');

        const remove_button = document.createElement('button');
        remove_button.classList.add('btn', 'btn-sm', 'btn-danger', 'rounded-pill', 'd-none');
        const remove_icon = document.createElement('i');
        remove_icon.classList.add('fa', 'fa-minus');
        remove_button.appendChild(remove_icon);
        first_line_buttons.appendChild(remove_button);

        const add_button = document.createElement('button');
        add_button.classList.add('btn', 'btn-sm', 'btn-secondary', 'rounded-pill');
        const add_icon = document.createElement('i');
        add_icon.classList.add('fa', 'fa-plus');
        add_button.appendChild(add_icon);
        first_line_buttons.appendChild(add_button);

        remove_button.addEventListener('click', (remove) => {
            remove.preventDefault();
            wrapper.removeChild(wrapper.lastChild);
            if (wrapper.childElementCount <= 2) remove_button.classList.add('d-none');
            else remove_button.classList.remove('d-none');
        })

        const add_choice = (value) => {
            const new_option_wrapper = document.createElement('div');
            new_option_wrapper.classList.add('d-flex', 'justify-content-between', 'align-items-center', 'gap-2');

            const new_option = document.createElement('input');
            new_option.classList.add('form-control');
            new_option.name = `${rank}_choices`

            if (value) {
                new_option.value = value;
            }

            new_option_wrapper.appendChild(new_option);

            if (wrapper.childElementCount > 1) remove_button.classList.remove('d-none');

            wrapper.appendChild(new_option_wrapper);
        }

        first_line.appendChild(first_line_buttons);
        wrapper.appendChild(first_line);

        add_button.addEventListener('click', (e) => {
            e.preventDefault();
            add_choice();
        });

        return [wrapper, add_choice];
    }

    const update_ranks = () => {
        for (const [index, list_element] of Array.from(form_data.children).entries()) {
            const inputs = list_element.querySelectorAll('input, textarea, select');
            for(const input of inputs) {
                input.name = index + '_' + input.name.split('_')[1]
            }
        }
    }

    const createNewMetadata = (metadata) => {
        const element_wrapper = document.createElement('li');
        element_wrapper.classList.add('list-group-item', 'd-flex', 'justify-content-between', 'align-items-start', 'gap-3');

        const new_element = document.createElement('div');
        new_element.classList.add('d-flex', 'flex-column', 'gap-2', 'flex-grow-1');

        const current_rank = getRank();

        const rank = document.createElement('input');
        rank.type = 'hidden';
        rank.name = `${current_rank}_rank`;
        rank.value = current_rank;
        new_element.appendChild(rank);

        if (metadata?.id) {
            const id = document.createElement('input');
            id.type = 'hidden';
            id.name = `${current_rank}_id`;
            id.value = metadata.id;
            new_element.appendChild(id);
        }

        const x = document.createElement('input');
        x.type = 'hidden';
        x.name = `${current_rank}_x`;
        x.value = metadata?.x || 1;
        new_element.appendChild(x);

        const query = document.createElement('input');
        query.type = 'hidden';
        query.name = `${current_rank}_query`;
        query.value = metadata?.query || 'y';
        new_element.appendChild(query);

        const label = createInputGroup('label');
        new_element.appendChild(label);
        const description = createInputGroup('description', 'textarea');
        new_element.appendChild(description);

        const range = createRangeGroup(current_rank);

        const [include_range, range_check] = createCheckboxGroup("Range", current_rank);
        include_range.classList.add('d-none');
        range_check.addEventListener('change', (e) => {
            const { checked } = e.target;
            if (checked) {
                range.classList.remove('d-none');
            } else {
                range.classList.add('d-none');
            }
        });

        const type_select_line = document.createElement('div');
        const type_select_label = document.createElement('label');
        type_select_label.classList.add('form-label', 'opacity-75');
        type_select_label.innerText = "Metadata type:";
        type_select_line.appendChild(type_select_label);

        const type_select = createListGroup('type', JSON.parse(`<%- JSON.stringify( metadata_types ) %>`), 'char');
        const subThesaurus = JSON.parse(`<%- JSON.stringify( map_subthesaurus ) %>`);
        const pactolsCollections = JSON.parse(`<%- JSON.stringify( map_thesaurus_pactols_collections ) %>`);

        type_select.addEventListener('change', (e) => {
            const value = e.target.value;
            if (value === "int") {
                include_range.classList.remove('d-none');
                if (range_check.checked) range.classList.remove('d-none');
            } else {
                include_range.classList.add('d-none');
                range.classList.add('d-none');
            }

            if (value === "list" || value === "choice" || value === "choico") {
                choice_list.classList.remove('d-none');
                choice_list.querySelectorAll('input').forEach(input => input.setAttribute('required', true))
            } else {
                choice_list.classList.add('d-none');
                choice_list.querySelectorAll('input').forEach(input => input.removeAttribute('required'))
            }

            const prev_thesaurus_selector = document.getElementById(`${current_rank}_thesaurus`);
            if(value === "thesaurus" || value === "multi" || value === "pactols" ) {
                if(prev_thesaurus_selector){
                    updateThesaurusList(current_rank, subThesaurus[value], value);
                }else{
                    const thes_selector = createThesaurusList(
                        current_rank, 
                        subThesaurus[value], 
                        value, null, 
                        pactolsCollections, null
                    );
                    type_select.parentNode.after(thes_selector);
                }
            }else{
                if(prev_thesaurus_selector){
                    type_select.parentNode.parentNode.removeChild(prev_thesaurus_selector.parentNode);
                }
            }
        });

        type_select_line.appendChild(type_select);
        new_element.appendChild(type_select_line);

        if (metadata?.status === "thesaurus" || metadata?.status === "multi" || metadata?.status === "pactols") {
            const selected_collection = metadata.status === 'pactols' && metadata.function ? metadata.function : null;
            const thes_selector = createThesaurusList(
                current_rank, 
                subThesaurus[metadata.status], 
                metadata.status, 
                metadata.list, 
                pactolsCollections, 
                selected_collection
            );
            new_element.appendChild(thes_selector);
        }


        new_element.appendChild(include_range);
        new_element.appendChild(range);

        const [mandatory] = createCheckboxGroup('Mandatory', current_rank);
        const [unique] = createCheckboxGroup('Unique', current_rank);
        const [optional] = createCheckboxGroup('Optional', current_rank);
        new_element.appendChild(mandatory);
        new_element.appendChild(unique);
        new_element.appendChild(optional);

        const [choice_list, add_choice] = createAppendGroup(structuredClone(current_rank));
        new_element.appendChild(choice_list);
        choice_list.classList.add('d-none');

        const previous_element = form_data.lastElementChild

        element_wrapper.appendChild(new_element);

        const actions = document.createElement('div');
        actions.classList.add('d-flex', 'flex-column', 'gap-2');

        const portlet = document.createElement('div');
        portlet.classList.add('btn', 'btn-sm', 'portlet');
        const portlet_icon = document.createElement('i');
        portlet_icon.classList.add('fa', 'fa-bars')
        portlet.appendChild(portlet_icon);
        actions.appendChild(portlet);

        const remove_button = document.createElement('button');
        remove_button.classList.add('btn', 'btn-sm', 'btn-danger');
        const close_icon = document.createElement('i');
        close_icon.classList.add('fa', 'fa-times');
        remove_button.appendChild(close_icon);
        remove_button.addEventListener('click', () => { form_data.removeChild(element_wrapper); })
        actions.appendChild(remove_button);

        element_wrapper.appendChild(actions);

        form_data.appendChild(element_wrapper);

        return { el: element_wrapper, list: { choice_list, add_choice } };
    }

    addMetadata.addEventListener('click', createNewMetadata);

    confirmDelete.addEventListener('click', () => {
        fetch('/admin/deleteMetadataModel/<%= branch %>/<%= metadata_model.id %>', { method: "DELETE" }).then((response) => {
            if (response.ok) location.href = "/admin/editMetadataModel";
        })
    });

    $(() => {
        const success_toast_element = document.querySelector('.toast.success');
        const success_toast = new bootstrap.Toast(success_toast_element, { delay: 3000 });
        const error_toast_element = document.querySelector('.toast.error');
        const error_toast = new bootstrap.Toast(error_toast_element, { delay: 3000 });


        submit.addEventListener('click', (e) => {
            e.preventDefault();

            const body = new URLSearchParams(new FormData(form));

            fetch("/admin/editMetadataModel/<%= branch %>/<%= metadata_model.id %>", { method: "POST", body }).then(response => {
                response.json().then(data => {
                    console.log(data);
                    if (data.status === 200){
                        success_toast.show();
                    } else if(data.status === 400 || data.status === 404){
                        error_toast_element.querySelector('.toast-body').innerText = data.message;
                        error_toast.show();
                    }
                }).catch((e) => {
                    error_toast.show();
                })
            }).catch(() => {
                error_toast.show();
            })
        })

        $('#form-data').sortable({
            handle: '.portlet',
            placeholder: 'portlet-placeholder opacity-50 my-4 mx-0 p-0 rounded bg-primary-subtle',
            update(event, { item }) {
                update_ranks();
            }
        });

        update_ranks();
    });

    const metadata = JSON.parse(`<%- JSON.stringify(metadata) %>`);
    const existing_list_values = JSON.parse(`<%- JSON.stringify(Array.from(metadata_list_values)) %>`);

    for (const m of metadata) {
        const rank = metadata.indexOf(m) + 1;
        const { el: wrapper, list } = createNewMetadata(m);

        const setInput = (name) => {
            const input = wrapper.querySelector(`input[name="${rank}_${name}"],textarea[name="${rank}_${name}"]`);
            input.value = m[name];
        }

        setInput('x');
        setInput('label');
        setInput('description');

        const type_select = wrapper.querySelector(`select[name="${rank}_type"]`);
        type_select.value = m.status;

        if (m.status === "list" || m.status === "choice" || m.status === "choico") {
            const [_, list_values] = existing_list_values.find(([key]) => key === parseInt(m.list)) ?? [null, []];
            for (const list_value of list_values) {
                list.add_choice(list_value);
            }
            list.choice_list.classList.remove('d-none');
            list.choice_list.querySelectorAll('input').forEach(input => input.setAttribute('required', true));
        }

        if (m.y) {
            wrapper.querySelector(`input[name="${rank}_Mandatory"]`).checked = true;
        }

        if (m.isunique) {
            wrapper.querySelector(`input[name="${rank}_Unique"]`).checked = true;
        }

        if (m.optional) {
            wrapper.querySelector(`input[name="${rank}_Optional"]`).checked = true;
        }
    }
</script>

<style>
    .portlet-placeholder {
        border: 1px dotted black;
        height: 120px;
    }
</style>