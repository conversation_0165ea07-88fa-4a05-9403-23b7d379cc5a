<% if (context === 'p') { %>
<div class="row">
    <div class="col-2">
        <% if (mainFolder?.image) { %>
        <a href="/project/<%= mainFolder.mainfolder %>" title="<%=__('backprojectpage')%>">
            <img src="/assets/images/<%= mainFolder.image %>" style="height: 4em;margin-left: 0em;margin-bottom: 0em;margin-top: 0em;"></a>
        <% } else { %>
        <a href="/project/<%= mainFolder.mainfolder %>" title="<%=__('backprojectpage')%>">
            <img src="/assets/images/ArcheoGRID.png" style="height: 4em;margin-left: 0em;margin-bottom: 0em;margin-top: 0em;">
        </a>
        <% } %>
    </div>
    <div class="col-8  text-center">

    </div>
    <div class="col-2" id="menuDroit">
    </div>
</div>


<div class="container-fluid"><% } %>
    <div class="text-center">
        <% if (idFile !== '0') {%><div class="superpose"><div class="image_wrapp">
        <a href="/viewer/<%= image['fid'] %>_<%= image['id'] %>" target="_blank">
            <img src="/assets/images/pleinecran.png" class="image_superpose"
                 style="<% if (context === 'm') {%>width:1.5rem!important;display:block;<% } else {%>display: inherit;<% } %>">
            <% } %>
            <% if (idFile === '0') {%>
            <% if (dataObject['id_nakala'] ) { %>
            <img class="mx-auto" src="<%= dataObject['id_nakala'] %>" alt="<%= dataObject['name']%>"
                 >
            <%  } else { %>
            <img class="mx-auto" src="/assets/images/default_repre_image_object.png" alt="<%= image['filename']%>"
                 style="width: 50px;">
            <p><%=__('no2')%> <%=__('visualization')%> <%=__('for')%> <%=__('object2')%></p>
            <% } %>
            <% } else { %>
            <img class="mx-auto" src="/small/<%= image['fid'] %>_<%= image['id'] %>" alt="<%= image['filename']%>"
            >
            <% } %>
            <% if (idFile !== '0') {%>
        </a></div></div><% } %>
        <br>
        <p style="text-align: center"><strong><%=__('object')%> : </strong><%= dataObject['name'] %></p>
        <br>
    </div>
    <div class="row text-center" style="gap: 0.75rem;">
        <div class="col-12 col-md-3">
            <%# pas de téléchargement d'un object, c'est un concept %>
            <%# ajout de mots clés : que si on est connecté et qu'on est dans un répertoire avec des droits en écriture : %>
            <% if (Wrights) { %>
                <a href="/keyword,<%= root %>,object,<%= idObj %>,<%= folderId %>" class="btn btn-secondary">
                    <i class="fas fa-tag"></i> &nbsp;<%=__('addKeyword.title')%>
                </a>
            <% } %>
        </div>
        <div class="col-12 col-md-5">
            <% if (user.id !== 0) { %><% if (mainFolder['id_metadata_model']) { %>
            <a href="/comment,<%= root%>,<%= folderId %>,<%= idObj %>,object" type="button" class="btn btn-secondary"
               title="<%= mainFolder['description'] %>"><i class="far fa-comments"></i><%=__('comment')%> /
                <%= mainFolder['label']%></a><% } else { %>
            <a href="/comment,<%= root%>,<%= folderId %>,<%= idObj %>,object" type="button" class="btn btn-secondary"
               title="<%=__('note')%>"><i class="far fa-comments"></i> <%=__('comment')%></a><% } } %>
        </div>
        <div class="col-12 col-md-4" id="menuDroit">
            <% if (Wrights) { %>
            <a href="/edit,<%= root %>,<%= modelObject %>,object,<%= idObj %>,<%= folderId %>" class="btn btn-secondary"
               title="Indexer"><i class="fas fa-edit"></i> <%=__('enrichData')%></a>
            <% }%>
        </div>

        <% if (Wrights) { %>
            <div class="col-12 col-md-4">
                <a href="#" class="btn btn-secondary"
                title="Copy" onclick="copyMetadataItem('<%= root %>', 'object', '<%= idObj %>')"><i class="far fa-copy"></i> <%=__('copyMetadata')%></a>
            </div>
        <% }%>

        <% if (Wrights) { %>
            <div class="col-12 col-md-4">
                <a href="#" class="btn btn-secondary"
                title="Paste" onclick="pasteMetadataItem('<%= root %>', 'object', '<%= idObj %>', '<%= folderId %>', '<%= lng %>')"><i class="fas fa-copy"></i> <%=__('pasteMetadata')%></a>
            </div>
        <% }%>
    </div>
    <br>

</div>
<% if (context === 'p') { %></div>
<div class="container pb-5"><% } %>
    <% if (image['tag'].length > 0 ) { %>
    <%= __('keywords')%> :
    <% for (var i = 0; i < image['tag'].length; i++ ) { %>
    <button class="btn btn-light"><%= image['tag'][i] %></button>
    <% } %>

    <% } %>
    <br>
    <p><%= __('enterDate')%> <%=__('of2')%> <%=__('object2')%> <%=__('in') %> ArcheoGRID :
        <%= dataObject['date_integration'] %></p>
    <br>
    <% if (license) { %><p style="font-size: smaller"><%- license['html'] %></p><% } %>
    <% if (nbJson != 0) { let json_trouve = 0 ; %>
    <% for (let i in modelComplet) { %>
    <% if (modelComplet[i]['jsonInfo'] ) { %>
    <% json_trouve += 1 %>
    <% } %>
    <% } %>
    <% if (json_trouve && Wrights) { %><div style="border-radius: 10px; border: 1px dashed #2a2f4a;">
        &nbsp;<%=__('file')%><% if (json_trouve >1) %>s JSON à importer dans le passport de l'objet
        <form class="form-inline" id="importjson" action="/metadataImportJson" method="post">
            <input type="hidden" value="<%= mainFolder['mainfolder'] %>" name="rootproj" id="rootproj" />
            <input type="hidden" value="<%= idObj %>" name="idObject" id="idObject" />
            <input type="hidden" value="<%= folderId %>" name="idFolder" id="idFolder" />
            <input type="hidden" value="<%= idFile %>" name="idFile" id="idFile" />
            <input type="hidden" value="<%= context %>" name="context" id="context" />
            <input type="hidden" value="<%= Wrights %>" name="rights" id="rights" />
            <div class="form-group form-group mb-2 mx-sm-3">
                <label for="model">Choisir un modèle de métadonnées pour le passport&nbsp;&nbsp; </label>
                <select name="mmodel" id="mmodel">
                    <% for (let i = 0; i < modelComplet.length ; i++) { %>
                    <% if (modelComplet[i]['jsonInfo']) { %>
                    <option value="<%= modelComplet[i].id %>"><%= modelComplet[i].description %></option>
                    <% } %>
                    <% } %>
                </select>
            </div>
            <div class="form-group mb-2 mx-sm-3">
                <input class="btn btn-secondary btn-sm" type="submit" value="Valider" />
                &nbsp;<a href="#" class="btn btn-sm btn-outline-secondary"><%=__('cancel') %></a>

            </div>
        </form>
    </div>
    <% } %>
    <br>
    <% } %>

    <% if (metadata) { %>
    <% if (metadata['unico'] ) { %>
    <h4>
        <a href="/getSmallImage/<%= metadata['parent_unico'] %>/<%= root %>"
           onclick="window.open(this.href, 'photo', 'status=no, resizable=yes, scrollbars=yes');return false;">
            Parent unico
        </a>
    </h4>
    <% }  } %>

    <% for (let key in metadata) { %>
    <% for (let i in modelComplet) { if (modelComplet[i]['name'] === key) { %>
    <% if (Object.prototype.hasOwnProperty.call(metadata, key)) { %>

    <% if ( (metadata[key] !== null) && ((model.indexOf(key) !== -1) || (key === 'nomenclature') ||  (key === 'thesaurus') ||  (key === 'multi') ) ) { %>
    <% if ((key === 'nomenclature') || (key === 'thesaurus')) {%><%# a priori cas jamais rencontré A SUPPRIMER %>
    <table class="table table-sm">
        <tbody>
            <% for (let dat in metadata[key][0]) {%>
            <% if ({}.hasOwnProperty.call(metadata[key][0], dat)) { %>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= metadata[key][0][dat]['label'] %></td>
                <td style="width: 70%"><%- (metadata[key][0][dat]['value']) %></td>
            </tr>
            <% } %>
            <% }%>
        </tbody>
    </table>
    <% } else { %>
    <h4><%= __('metadata') %> - <%= modelComplet[i]['label'] %></h4>
    <table class="table table-sm">
        <tbody>
            <% for (meta_key in metadata[key]) { %>
            <% if ({}.hasOwnProperty.call(metadata[key], meta_key)) { %>
            <% if (metadata[key][meta_key]['displayable'] !== 0) { %>
            <% if (metadata[key][meta_key]['status'] === 'link') { %>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
                <td style="width: 70%">
                    <% for (let val of metadata[key][meta_key]['value']) { if (val.indexOf('http')!== -1) {%>
                        <a href="<%= val %>" target="_blank"><% } %>
                            <%- (val) %>
                        <% if (val.indexOf('http')!== -1) {%></a><% } %>
                        <br>
                    <% } %>
                </td>
            </tr>
            <% } else if (metadata[key][meta_key]['status'] === 'json') { %>
        </tbody>
    </table>
    <%- include('jsonMetadata.ejs', {meta : metadata[key][meta_key]['value']}) %>
            <% } else if (metadata[key][meta_key]['status'] === 'thesaurus') { %>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
                <td>
                    <% let multi = 0; let d = metadata[key][meta_key]['value'].length %>
                    <% for (let t = 0; t < thes.length; t++) { %>
                        <% for (let theskey in metadata) { %>
                            <% if (metadata[theskey]) { %>
                            <%for (let i = 0; i < metadata[theskey].length; i++) {  %>
                            <% for ( let metakey in metadata[theskey][i]['thesmaisoninfo'] ) {%>
                                    <% if (((metakey === 'list') && metadata[theskey][i]['thesmaisoninfo']['query']=== 'y')
                                            && (thes[t]['list'] === metadata[theskey][i]['thesmaisoninfo'][metakey])
                                            && (thes[t]['code'] === metadata[theskey][i]['thesmaisoninfo']['qualifier'])
                                            && (metadata[theskey][i]['thesmaisoninfo']['id_metadata']=== metadata[key][meta_key]['id_metadata'])
                                    )  { multi++; %>
                                    <button class="btn btn-sm btn-hand" style="margin-top: -9px;">
                                        <a href="/concept/<%= metadata[theskey][i]['thesmaisoninfo']['value']%>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= metadata[theskey][i]['thesmaisoninfo']['id_thes_thesaurus']%>&thesaurus=<%= metadata[theskey][i]['thesmaisoninfo']['thesaurus']%>">
                                            <%= metadata[theskey][i]['thesmaisoninfo']['value']%></a>
                                        <% if (Wrights) { %>
                                        <span class="btn-hand" title="<%=__('delete')%>"
                                        <% if (metadata[theskey][i]['thesmaisoninfo']['qualifier'] !== '') { %>
                                              onclick="delete_thes_item_qual('<%= root %>', '<%= idObj %>', 'object', '<%= metadata[theskey][i]['thesmaisoninfo']['id_thes_thesaurus']%>', '<%= metadata[theskey][i]['thesmaisoninfo']['list']%>', '<%= metadata[theskey][i]['thesmaisoninfo']['qualifier']%>')">
                                            <% } else { %>
                                                onclick="delete_thes_item('<%= root %>', '<%= idObj %>', 'object', '<%= metadata[theskey][i]['thesmaisoninfo']['thes_path']%>', '<%= metadata[theskey][i]['thesmaisoninfo']['list']%>')">
                                            <% } %>
                                            <i class="far fa-trash-alt"></i>
    </span><% } %>
                                    </button> <% if (d > 1) {%>,<%} %>
                                    <% d = d - 1 %>
                                    <% } %>
                    <% } } } } } %></td>
            </tr>
        <% } else if (metadata[key][meta_key]['status'] === 'multi') { %>
            <tr>
                <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
                <td>
                <% let multi = 0; let d = metadata[key][meta_key]['value'].length %>
                <% for (let t = 0; t < thes.length; t++) { %>
                <% for (let multikey in metadata) { %>
                    <% if (metadata[multikey]) { %>
                <%for (let i = 0; i < metadata[multikey].length; i++) {  %>
                <% for ( let metakey in metadata[multikey][i]['multiinfo'] ) {%>
                    <% if (((metakey === 'list') && metadata[multikey][i]['multiinfo']['query']=== 'y')
                            && (thes[t]['list'] === metadata[multikey][i]['multiinfo'][metakey])
                            && (thes[t]['code'] === metadata[multikey][i]['multiinfo']['qualifier'])
                            && (metadata[multikey][i]['multiinfo']['id_metadata']=== metadata[key][meta_key]['id_metadata'])
                    )  { multi++; %>
                    <button class="btn btn-sm btn-hand" style="margin-top: -9px;">
<a href="/concept/<%= metadata[multikey][i]['multiinfo']['value']%>?rootF=<%= mainFolder['mainfolder']%>&type=multi&idThes=<%= metadata[multikey][i]['multiinfo']['id_thes_thesaurus']%>&thesaurus=<%= metadata[multikey][i]['multiinfo']['thesaurus']%>">
    <%= metadata[multikey][i]['multiinfo']['value']%></a>
                        <% if (Wrights) { %>
                        <span class="btn-hand" title="<%=__('delete')%>"
                        <% if (metadata[multikey][i]['multiinfo']['qualifier'] !== '') { %>
                              onclick="delete_thesmulti_item_qual('<%= root %>', '<%= idObj %>', 'object', '<%= metadata[multikey][i]['multiinfo']['thes_path']%>', '<%= metadata[multikey][i]['multiinfo']['list']%>', '<%= metadata[multikey][i]['multiinfo']['qualifier']%>')">
                            <% } else { %>
                                onclick="delete_thesmulti_item('<%= root %>', '<%= idObj %>', 'object', '<%= metadata[multikey][i]['multiinfo']['thes_path']%>', '<%= metadata[multikey][i]['multiinfo']['list']%>')">
                            <% } %>
                            <i class="far fa-trash-alt"></i>
        </span><% } %>
                    </button> <% if (d > 1) {%>,<%} %>
                    <% d = d - 1 %>
                    <% } %>
                    <% } } } } } %></td>
            </tr>
    <% } else if(metadata[key][meta_key]['status'] === 'actor' || metadata[key][meta_key]['status'] === 'datation' || metadata[key][meta_key]['status'] === 'location') { %>
    <tr>
        <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
        <td style="width: 70%" class="advanced_model_display"><%- include('displayMetadataSwitch.ejs', {metadata: metadata[key][meta_key]}) %></td>
    </tr>
    <% } else { %>
    <tr>
        <td style="text-align: right;width: 30%"><strong><%= metadata[key][meta_key]['label'] %></td>
        <td style="width: 70%"><% for (let val  in  metadata[key][meta_key]['value']) {%>
            <%- (metadata[key][meta_key]['value'][val]) %><br><% } %></td>
    </tr>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    </tbody>
    </table>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>

    <% let multi = 0; %><%# pour les mot-clés intégrés dans un modèle de métadonnées %>
    <% for (let t = 0; t < thes.length; t++) { %>
    <% for (let key in metadata) { %>
    <% if (metadata[key]) { %>
    <% if (key === 'pactols') { let display = ''; let d = 0; %>
    <% for (let i = 0; i < metadata[key].length; i++) { %>
    <% for (let p in metadata[key][i]['thespactolsinfo']) { %>
    <% if (p === 'list') {%>
    <% if (thes[t]['list'] === metadata[key][i]['thespactolsinfo'][p]) { %>
    <% display = thes[t]['label'] %><% d++ %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% if (display !== '') { %>
    <br><%= display %> :
    <%for (let i = 0; i < metadata[key].length; i++) { %>
    <% for ( let metakey in metadata[key][i]['thespactolsinfo'] ) { %>
    <% if ((metakey === 'list') && (thes[t]['list'] === metadata[key][i]['thespactolsinfo'][metakey]))  { multi++; %>
    <button class="btn btn-sm btn-hand">
        <a href="<%= metadata[key][i]['thespactolsinfo']['identifier'] %>">
            <%= metadata[key][i]['thespactolsinfo']['value']%></a>
    </button> <% if (d > 1) {%>,<%} %>
    <% d = d - 1 %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } else { let display = ''; let d = 0; %>
    <% let surkey = key + 'info' %>
    <% for (let i = 0; i < metadata[key].length; i++) { %>
    <% for (let p in metadata[key][i][surkey]) {
        if ( (p === 'list') && (!metadata[key][i][surkey]['id_metadata']) ) { %>
    <% if (thes[t]['list'] === metadata[key][i][surkey][p]) { %>
    <% display = thes[t]['label'] %><% d++ %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% if (display !== '') { %>
    <br><%= display %> :
    <%for (let i = 0; i < metadata[key].length; i++) { %>
    <% for ( let metakey in metadata[key][i][surkey] ) { if (!metadata[key][i][surkey]['id_metadata']) { %>
    <% if ((metakey === 'list') && (thes[t]['list'] === metadata[key][i][surkey][metakey]))  {%>
    <button class="btn btn-sm btn-hand">
        <a href="#">
            <%= metadata[key][i][surkey]['value']%></a>
    </button> <% if (d > 1) {%>,<%} %>
    <% d = d - 1 %>
    <% } %>
    <% } %>
    <% } } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    <%# tag de thesaurus SANS MODELE De METADONNEES RATTACHEES%>
    <%# deja des tag multi présents dans des modèles de métadonnées ?  s'il y en a alors on ne les affiche pas à nouveau %>
    <% if (((tagThesmulti.length > 0 ) && (multi === 0 )) || ( tagPactols.length > 0 ) || ( tagMaison.length > 0 ) ) {
       // on boucle sur les multi info des metadata pour voir s'il y a des tags en plus à afficher
    let affTab = [] ; // on crée un tableau des tags déjà affichés dans les metadata
    if (metadata.multi.length ) {
        for (let tt = 0; tt < tagThesmulti.length; tt++) {
            for ( let m =0; m < metadata['multi'].length; m++ ) {
              if (tagThesmulti[tt]['thesaurus'] === metadata['multi'][m]['multiinfo']['thesaurus'] && tagThesmulti[tt]['name'] === metadata['multi'][m]['multiinfo']['value']) {
                // C'est le même on ne l'affiche pas
                     tagThesmulti[tt]['affichage'] = 0;
                     affTab.push(tagThesmulti[tt]) ;
                }
            }
        }
    }
    if (affTab.length < tagThesmulti.length ) {
    // Créer un Set des IDs de affTab (sur le id_thesaurus)
    const idsB = new Set(affTab.map(item => item.id));
    %><hr><i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%><br>
    <% if (tagThesmulti.length > 0 ) {
        tagThesmulti.forEach(element => {
            if (!idsB.has(element.id)) { %><%# si l'id n'est pas présent dans le tableau des tags déjà affichés (affTab) mappé avec les id (idsB) alors seulement on affiche %>
        <a type="button" href="/concept/<%= element.name %>?rootF=<%= mainFolder['mainfolder']%>&type=multi&idThes=<%= element['id_thes']%>&thesaurus=<%= element['thesaurus']%>"
        class="btn btn-sm btn-light"><%= element.name %></a>
        <% if (Wrights) { %>
        <span class="btn-hand" title="<%=__('delete')%>"
              onclick="delete_thesmulti_item('<%= root %>', '<%= idObj %>', 'object', '<%= element['thes_path']%>', '<%= element['thesaurus']%>')">
        <i class="far fa-trash-alt"></i>
        </span><% } %>
    <%      }
        });
       }
    } %>

    <% if (tagPactols.length > 0 ) { %>
    <% for (var i = 0; i < tagPactols.length; i++ ) { %>
    <button class="btn btn-sm btn-hand"><a
           href="/concept/<%= tagPactols[i].name %>?rootF=<%= mainFolder['mainfolder']%>&type=pactols&idThes=<%= tagPactols[i]['id_thes']%>&thesaurus=<%= tagPactols[i]['thesaurus']%>"
           target="_blank">
            <%= tagPactols[i].name %></a>
        <% if (Wrights) { %>
        <span class="btn-hand" title="<%=__('delete')%>"
              onclick="delete_thespactols_item('<%= root %>', '<%= idObj %>', 'object', '<%= tagPactols[i]['id']%>', '<%= tagPactols[i]['id_thes']%>', '<%= tagPactols[i]['thesaurus']%>')">
            <i class="far fa-trash-alt"></i>
        </span><% } %>
    </button>
    </a><% } %>
    <br>
    <% } %>
    <% if (tagPactolsGeo.length > 0 ) { %>
    <% for (let i = 0; i < tagPactolsGeo.length; i++ ) { %>
    <button class="btn btn-sm btn-hand"><a href="/conceptName/<%= tagPactolsGeo[i].name %>" target="_blank">
            <%= tagPactolsGeo[i].name %></a>
        <% if (Wrights) { %>
        <span class="btn-hand" title="<%=__('delete')%>"
              onclick="delete_thespactolsgeo_item('<%= root %>', '<%= idObj %>', 'object', '<%= tagPactolsGeo[i]['id']%>', '<%= tagPactolsGeo[i]['id_thes']%>')">
            <i class="far fa-trash-alt"></i>
        </span><% } %>
    </button>
    </a><% } %>
    <br>
    <% } %>
    <% if (tagMaison.length > 0 ) {
        if (metadata.thesaurus.length ) {
        for (let tt = 0; tt < tagMaison.length; tt++) {
        for ( let m =0; m < metadata['thesaurus'].length; m++ ) {
        if (tagMaison[tt]['thesaurus'] === metadata['thesaurus'][m]['thesmaisoninfo']['thesaurus'] && tagMaison[tt]['name'] === metadata['thesaurus'][m]['thesmaisoninfo']['value']) {
        // C'est le même on ne l'affiche pas
        tagMaison[tt]['affichage'] = 0;
        affTab.push(tagMaison[tt]) ;
        }
        }
        }
        }
    if (affTab.length < tagMaison.length ) {
        // Créer un Set des IDs de affTab (sur le id_thesaurus)
        const idsB = new Set(affTab.map(item => item.id));
        %><hr><i class="fa fa-tag" aria-hidden="true"></i> <%=__('keywords')%><br>
        <% if (tagMaison.length > 0 ) {
            tagMaison.forEach(element => {
                if (!idsB.has(element.id)) { %><%# si l'id n'est pas présent dans le tableau des tags déjà affichés (affTab) mappé avec les id (idsB) alors seulement on affiche %>
                <a type="button" href="/concept/<%= element.name %>?rootF=<%= mainFolder['mainfolder']%>&type=simple&idThes=<%= element['id_thes']%>&thesaurus=<%= element['thesaurus']%>"
                   class="btn btn-sm btn-light"><%= element.name %></a>
                <% if (Wrights) { %>
                <span class="btn-hand" title="<%=__('delete')%>"
                      onclick="delete_thes_item('<%= root %>', '<%= idObj %>', 'object', '<%= element['thes_path']%>', '<%= element['thesaurus']%>')">
                    <i class="far fa-trash-alt"></i>
                    </span><% }
                }
            });
        }
    } %>
    <br>
    <% } } %>
    <%# tag non alignés %>
    <% if (tags.length > 0 )  { %>
    <hr>
    <h4><i class="fas fa-tags"></i> &nbsp;<%= __('keywords')%> : (<%=__('no3') %> <%=__('alignedPlural')%>)</h4>
    <% for (let t = 0; t < tags.length; t++ ) { %>
    <div class="badge bg-secondary p-2"><%= tags[t].name %></div>
    <% if (Wrights) { %>
    <span class="btn-hand" title="<%=__('delete')%>"
          onclick="delete_tag_item('<%= root %>', '<%= idObj %>', 'object', '<%= tags[t]['id']%>')"> <i
           class="far fa-trash-alt"></i>
    </span><% } %>
    <% } %>

    <% } %>

    <% if (comments.length > 0) { %>
    <br>
    <h4><%= comments.length %> <%=__('note')%><% if (comments.length > 1) {%>s<% }%></h4>
    <% for (let i = 0; i <  comments.length; i++) { %>
    <dl class="row">
        <dt class="col-sm-3"><strong><%=__('content')%></strong></dt>
        <dd class="col-sm-9"><%- comments[i]['content'] %></dd>
    </dl>
    <dl class="row">
        <dt class="col-sm-3"><strong><em><%=__('author')%></em></strong></dt>
        <dd class="col-sm-9"><%= comments[i]['signature']%></dd>
    </dl><% if (comments[i]['author'].length > 0 ) {%><dl class="row">
        <dt class="col-sm-3"><strong><em><%=__('inNameOf')%>:</em></strong></dt>
        <dd class="col-sm-9"><%= comments[i]['author']%></dd>
    </dl><% } %>
    <dl class="row">
        <dt class="col-sm-3"><strong><%=__('date')%></strong></dt>
        <dd class="col-sm-9"><%= comments[i]['date']%></dd>
    </dl><% if (comments[i]['other_date'].length > 0 ) {%><dl class="row">
        <dt class="col-sm-3"><strong><%=__('dateExplicite')%>: </strong></dt>
        <dd class="col-sm-9"><%- (comments[i]['other_date']) %></dd>
    </dl><% } %>
    </dl><% if (comments.length > 1) {%>
    <hr><% } %>
    <% }%>
    <% } %>

    <% if (items.length > 0) { %>
    <%if (nbObjects != 0) { %>
    <h3><% if (nbObjects >1) {%><%= __('associatedObjectplur')%><%} else {%><%= __('associatedObject')%><% } %> :
        <%= nbObjects %></h3>
    <% }%>
    <% if (nbFiles != 0) { %>
    <hr>
    <h3><% if (nbFiles >1) {%><%= __('associatedFileplur')%><%} else {%><%= __('associatedFile')%><% } %> :
        <%= nbFiles %></h3>
    <% } %>
    <% } %>
    <div class="row text-center">
        <%# lien vers la page globale%>
        <a href="/globalObject,<%= root %>,<%= idObj%>,<%= folderId%>" target="_blank" ><img
                 src="/assets/images/object.png" style="width: 3.3%;">
            <%=__('globalFile')%> <%=__('of5')%><%=__('object')%></a>
    </div>
    <% if (context === 'p') { %>
</div>
<div class="loader"></div>
<script src="/js/lazy-loading.js"></script>
<% }%>
