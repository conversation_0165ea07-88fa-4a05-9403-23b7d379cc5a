DROP TABLE IF EXISTS archeogrid_csv_metadata;

CREATE TABLE IF NOT EXISTS archeogrid_csv_metadata (
    id SERIAL PRIMARY KEY,
    branch TEXT NOT NULL,
    project_name TEXT NOT NULL,
    csv_file_name TEXT NOT NULL,
    csv_delimiter CHARACTER (10) NOT NULL,
    metadata_model_id INTEGER,
    item_type TEXT,
    table_name TEXT,
    last_ingestion_table TIMESTAMP,
    last_ingestion_model TIMESTAMP,
    UNIQUE (branch, project_name, csv_file_name, metadata_model_id, table_name)
);

ALTER TABLE archeogrid_csv_metadata OWNER TO admin;

GRANT ALL ON TABLE archeogrid_csv_metadata TO admin;

