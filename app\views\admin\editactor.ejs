<script type='text/javascript'>
    let indice = 0;
    function addFields(column, type, c ){
        //add one input for type AND one input for value !
        indice = indice +1 ;
        console.log(indice)
        //add one input
        // Container <div> where dynamic content will be placed
        let container = document.getElementById("parent_"+c)

        // Create an <input> element, set its type and name attributes
        let rowtype = document.createElement("div")
        rowtype.className = 'row form-group'
        container.appendChild(rowtype)
        let labelt = document.createElement("label")
        labelt.className = 'col-sm col-form-label'
        labelt.innerHTML = 'Nom du type de l\'identifiant'
        rowtype.appendChild(labelt)
        let sm6 = document.createElement("div")
        sm6.className = 'col-sm'
        rowtype.appendChild(sm6)

        let inputt = document.createElement(type);
        inputt.type = "text";
        inputt.className = "form-control"
        inputt.placeholder = 'ROR, ISNI, ...'
        inputt.name = column+'#'+indice;
        sm6.appendChild(inputt);
        let sm2 = document.createElement("span")
        sm2.className = 'col-sm'
        rowtype.appendChild(sm2)
        let minus = document.createElement("a")
        minus.onclick = function () {
            rowtype.parentElement.removeChild(rowtype)
            rowvalue.parentElement.removeChild(rowvalue)
            this.parentElement.removeChild(this);
        };
        minus.href = "#"
        sm2.appendChild(minus)
        let ic = document.createElement("i")
        ic.className = 'fa fa-minus-circle'
        //        ic.aria-hidden = 'true'
        minus.appendChild(ic)

        let rowvalue = document.createElement("div")
        rowvalue.className = 'row form-group'
        container.appendChild(rowvalue)
        let labelv = document.createElement("label")
        labelv.className = 'col-sm-4 col-form-label'
        labelv.innerHTML = 'Valeur de l\'identifiant (URI)'
        rowvalue.appendChild(labelv)
        let sm10 = document.createElement("div")
        sm10.className = 'col-sm-6'
        rowvalue.appendChild(sm10)
        let input2 = document.createElement(type);
        input2.type = "url";
        input2.placeholder = 'URI complete http....'
        input2.className = "form-control"
        input2.name = column+'#'+indice;
        sm10.appendChild(input2);
        //}
    }
</script>
<div id="editactor">

    Quel acteur voulez-vous éditer ?

    <form class="form-horizontal" id="editactor" action="/admin/editActor" method="post">
        <div class="form-group row">
            <label for="model" class="col-sm-2 col-form-label"><%=__('actor')%> : </label>
                <div class="col-sm-2">
                    <select name="actorSelect" id="actorSelect">
                        <% for (i = 0; i < actors.length ; i++) {  %>
                        <option value="<%= actors[i]['id'] %>" <% if (actors[i]['id'] === parseInt(actorid)) { %>selected<% } %>><%= actors[i]['firstname'] %> <%= actors[i]['family_name'] %></option>
                        <% } %>
                    </select>
                </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-5 col-sm-7">
                <input class="btn btn-secondary" type="submit" value="Choisir cet acteur" />
                &nbsp;<a href="/admin/editActor" class="btn btn-outline-secondary" ><%=__('cancel') %></a>
            </div>
        </div>
    </form>

</div>
<hr>


<% if (message === '') { %>
<div class="container" id="editactorid">
<% if (actor !=='') { %>
    <form class="form-horizontal" id="addactor" action="/admin/editActor" method="post">
        <div class="form-group">
            <div class="col-sm-offset-5 col-sm-7">
                <input class="btn btn-secondary" type="submit" value="Valider les modifications pour cet acteur" />
                &nbsp;<a href="/admin/editActor" class="btn btn-outline-secondary" ><%=__('cancel') %></a>
            </div>
        </div>

    <% let c = 0; for (item in actor ) { c++; %>
        <% if (item === 'id') { %>
        <input type="hidden" value="<%= actor[item]%>" name="actorSelect" id="actorSelect" />
        <% } else if (item === 'id_organization') { %>
            <input type="hidden" value="<%= actor[item]%>" name="id_organization" id="id_organization" />
        <% } else if (item === 'id_entity') { %>
            <input type="hidden" value="<%= actor[item]%>" name="id_entity" id="id_entity" />
        <% } else if (item !== 'organization'){ %>
        <div class="form-group row">
            <label for="<%= item %>" class="col-sm-4 col-form-label">
                <% for (let i = 0; i <  col.length; i++ ) { %>
                    <% if (col[i]['column_name'] === item) {%><%= col[i]['label_fr'].charAt(0).toUpperCase() +  col[i]['label_fr'].slice(1) %><% } %>
                <% } %>
            </label>
            <div class="col-sm-8" id="parent_<%= c %>">
                <% if (item === 'identifier') { if (actor[item]) { %>
                <% for (let t = 0; t < actor[item].length; t++) { %>
                    <div class="form-group row">
                    <label for="<%= actor[item][t]['type'] %>" class="col-sm-2 col-form-label">
                        <%= actor[item][t]['type'] %>
                    </label><div class="col-sm-6">
                    <input type="text" class="form-control" value="<%= actor[item][t]['value']%>" name="ident_<%= actor[item][t]['type'] %>" id="ident_<%= actor[item][t]['type'] %>"  />
                        </div>
                    </div>
                <% } %>

                <% } else { %>
                <%# il n'y a aucun udentifiant proposer ORCID et VIAF  et de quoi ajouter un autre couple de valeur (bouton addFields... %>
                    <div class="form-group row">
                        <label for="typeORCID" class="col-sm-2 col-form-label">
                            ORCID
                        </label><div class="col-sm-6">
                            <input type="text"  class="form-control" name="ORCID" id="ORCID" placeholder="XXXX-XXXX-XXXX-XXXX" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="typeVIAF" class="col-sm-2 col-form-label">
                            VIAF
                        </label><div class="col-sm-6">
                            <input type="url"  class="form-control" name="VIAF" id="VIAF" placeholder="http://viaf.org/viaf/..." />
                        </div>
                    </div><% } %>
                    <div class="form-group row" id="parent_1">
                        <label for="identifierAutre" class="col-sm-5 col-form-label">
                            Autre type d'identifiant
                        </label>
                        <div class="col-sm-3">
                            <%# on appelle la function avec le nom de la colonne à modifier : ici item = identifier%>
                            <a href="#" id="plus" onclick="addFields('<%= item %>', 'input', <%= c %>)"><i class="fa fa-plus-circle" aria-hidden="true"></i></a>
                        </div>

                    </div>

                <% } else if (item === 'Signature') { %>
                <textarea class="form-control" rows="3" name="<%= item %>" id="<%= item %>" ><%= actor[item]%></textarea>
                <% } else if (item === 'email') { %>
                <input type="email" class="form-control" name="<%= item %>" id="<%= item %>" />
                <% } else { %>
                <input type="text" class="form-control" value="<%= actor[item]%>" name="<%= item %>" id="<%= item %>" <% if ((item ==='family_name') || (item === 'firstname')) {%>required<% } %> />
                <% } %>
            </div>
        </div>
        <% } %>
    <% } %>
        <div class="form-group row">

            <label for="org" class="col-sm-4 col-form-label">
                Organisation
            </label>
            <div class="col-sm-8">
                <select id="org" name="org">
                    <option value=""><I>... <%=__('choose') %> ...</I></option>
                    <option value="">... Aucune à ce jour ...</option>
                    <% for  (ind in organization ) { %>
                    <option value="<%= organization[ind]['id_organization'] %>" <% for (prop in actor) { %><% if ((prop === 'id_organization') && (actor[prop] === organization[ind]['id_organization']) ) { %>selected<% } %><% } %>><%= organization[ind]['name'] %></option>
                    <% } %>
                </select>
            </div>
        </div>
    </form>

<% } %>
</div>
<% } %>