<script>
    window.onload = function () {
        exploreThesaurusPactolsGeo(<%= projectId %>,  <%= data['id_thes'] %>)
    }
</script>

<!-- Main -->
<div class="row" id="main">
    <div class="col-5 col-lg-3">
        <%#img src="../assets/images/archeovision.jpg" style="height: 4em;"%>
    </div>
    <div class="col-5 col-lg-7 text-center">
        <%- include('utils/buttons-header') %>
        <h2>
            <a href="/projectsL"
               class="link-underline link-underline-opacity-0 link-underline-opacity-100-hover"><%= __('extp3d')%>
            </a>
        </h2>
    </div>
    <div class="col-2 col-lg-2" id="menuDroit">
        <%- include('utils/login-header') %>
    </div>
</div>
<hr>

<H3>Concept "<a href="<%=data['identifier']%>" class="fakelink"
       title="<%=__('see')%> <%=__('concept')%> <%=__('in')%> <%=__('thesaurus')%>"
       style="color: #0a6aa1"><%= conceptName %></a>"
    <%=__('for')%> <%= ('project')%> "<a href="/project/<%= rootF%>"><%= projectName[0]['value'][0]%></a>"</H3>
<div class="container-fluid">
    <div class="row">

        <div class="d-flex flex-column col-0 col-xl-1"></div>
        <div id="explore-div" class="d-flex flex-column col-0 col-xl-9" style="display: none !important;">
            <div class="d-flex flex-column mb-2">
                <span id="nb-results"></span>
                <div class="d-flex align-items-baseline justify-content-between">
                    <div id="display-menu" class="d-flex">
                        <div class="input-group group-borders">
                            <div class="input-group-addon">
                                <div class="input-group-text"><%=__('documentsDisplayed')%> :</div>
                            </div>

                            <select id="nb-results-page" class="btn-secondary btn-hand">
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>

                    <nav class="ml-auto">
                        <ul class="pagination m-0"></ul>
                    </nav>
                </div>
            </div>
            <div id="explore-results" class="d-flex flex-column mb-2"></div>
        </div>


    </div>
</div>