import type { NextFunction, Request, Response } from "express";
import fsp from "node:fs/promises";
import http from "node:http";
import querystring from "node:querystring";
import nodemailer from "nodemailer";

import type { Config3DHOP, ConfigPotree } from "../../server/types/schemas/files";
import { archeogrid_client } from "./request";
import config from "./config";

export const server_host = config.server_host;
export const server_port = config.server_port;
export const app_port = config.app_port;
export const paginer = config.nbImagesPage;
export const xmlPath = config.xml_path;
export const outilsPath = config.outils_path;
export const logPath = config.log_path;
export const wgetDOI = config.wget_DOI;
export const prefixDOI = config.prefix_DOI; // pour la landing page

export const emailPort = config.email_config_port;
export const emailHost = config.email_config_host;
export const emailSecure = config.email_config_secure;
export const emailConfig = config.email_main;
export const sessionduration = config.time_session_duration;
export const clientId = config.client_id; // OpenId Connect
export const clientSecret = config.client_secret; // OpenId Connect
export const callbackURL = config.callback_URL;
export const branchConfig = config.branch; //config.branch
export const API_promise_limit:number = config.API_promise_limit;
export const API_worker_number:number = config.API_worker_number;

const protocol = config.protocol ?? "http"; // TODO REWORK WHEN USING HTTPS WITH CANTALOUPE

const isProcotolDefined = (url: string) =>
  url.startsWith("http://") || url.startsWith("https://");
export const cantaloupeServer = isProcotolDefined(config.cantaloupe_server)
  ? config.cantaloupe_server
  : `${protocol}://${config.cantaloupe_server}`;

export const urlBase = config.video_host;
export const urlOrigin = config.origin_host;

export const downloadOrigin = config.download_folder;

export const acceptedVideoFormats = [
  "mov",
  "avi",
  "3g2",
  "webm",
  "wmv",
  "ogv",
  "flv",
  "mkv",
  "mp4",
  "mts",
];
export const acceptedImageFormats = ["jpg", "tiff", "tif", "png", "dng"];
export const acceptedPrevisuFormat = ["pdf", "JPG", "wav", "zip", "obj", "ply"]
  .concat(acceptedVideoFormats)
  .concat(acceptedImageFormats);

export const realAcceptedCantaloupeFormat = [
  "avi",
  "bmp",
  "gif",
  "jpeg",
  "jpf",
  "jpg",
  "pdf",
  "png",
  "tif",
  "tiff",
];
export const acceptedCantaloupeFormat = [
  "bmp",
  "gif",
  "jpeg",
  "jpf",
  "jpg",
  "png",
  "tif",
  "tiff",
];
export const videoFormat = ["avi", "mp4", "webm"];
export const audioFormat = ["mp3", "wav"];
export const viewerFormat = ["json", "pdf", "xml"]
  .concat(videoFormat)
  .concat(audioFormat)
  .concat(acceptedCantaloupeFormat);

export const cantaloupeFormatIcon = [
  "ai",
  "avi",
  "bak",
  "db",
  "doc",
  "docx",
  "json",
  "log",
  "mov",
  "mp4",
  "mpg",
  "obj",
  "ply",
  "ppt",
  "psd",
  "rtf",
  "ttf",
  "txt",
  "wav",
  "xls",
  "xlsx",
  "zip",
];

export const metadata_types = [
  "actor", 
  "char",
  "choice",
  "choico",
  "datation", 
  "date",
  "doc",
  "float",
  "int",
  "integer",
  "json",
  "link",
  "multi",
  //"nomenclature",
  "list",
  "location", 
  "map",
  "pactols",
  "periodo",
  "text",
  "thesaurus",
];

export const addLanguage = config.addLanguage;

export const tabLocales = addLanguage !== undefined ? ["fr", "en", addLanguage] : ["fr", "en"];
// Si on ajoute une langue dans le fichier de configuration, on met l'anglais par defaut, sinon on met le français par defaut
export const defaultLanguage = addLanguage !== undefined ? "en" : "fr";

export const icone3d = "public/images/virtualObject.jpg";

export function validateEmail(email: string) {
  const re =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
}

export function performRequest(
  endpoint: string,
  method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE" = "GET",
  data?: Record<string, string | number> | null,
  success?: (data: any, code: number, headers: string) => void,
) {
  let dataString = data ? querystring.stringify(data) : querystring.stringify();
  let headers: Record<string, string | number> = {};
  let path_option = endpoint;
  if (method === "GET") {
    path_option += `?${data ? querystring.stringify(data) : querystring.stringify()}`;
    dataString = querystring.stringify();
  } else {
    headers = {
      "Content-Type": "application/x-www-form-urlencoded",
      "Content-Length": dataString.length,
    };
  }

  const options = {
    host: server_host,
    path: path_option,
    port: server_port,
    method,
    headers,
  };

  const req = http.request(options, (res) => {
    res.setEncoding("utf-8");
    let responseString = "";

    res.on("data", (data) => {
      responseString += data;
    });

    res.on("end", () => {
      const responseObject = JSON.parse(responseString);
      if (success) success(responseObject, res.statusCode ?? 200, JSON.stringify(res.headers));
    });
  });
  req.on("error", (err) => {
    console.log("ERROR HTTP REQUEST", err.message);
  });

  req.write(dataString);
  req.end();
}

// protect route from not logged-in users
export function isLoggedIn(req: Request, res: Response, next: NextFunction) {
  req.session.returnTo = req.originalUrl;
  if (req.isAuthenticated()) {
    next();
    return;
  }
  req.flash("error", "Not logged in!");
}

// middleware function to check for perm user to access to a ressource
export async function permChecker(req: Request, res: Response, next: NextFunction) {
  const user = res.locals.user as CustomUser;

  const folder_id = Number.parseInt(req.params.idFolder);

  if (user.read.includes(folder_id)) {
    next();
    return;
  }

  const { rights } = await archeogrid_client.users.getUserAccessFolder.query({
    branch: "pft3d",
    user_id: user.id,
    folder_id,
  });

  if (rights) {
    next();
    return;
  }

  req.flash("error", "Not allowed");
}

// simple function to send a log alert mail to sarah and bruno:
export function sendMail(subject: string, body: string | Buffer) {
  const transporter = nodemailer.createTransport({
    host: emailHost,
    port: emailPort,
    secure: emailSecure,
  });
  // setup e-mail data with unicode symbols
  const mailOptions = {
    from: "Archeogrid <<EMAIL>>", // sender address
    to: "<EMAIL>,<EMAIL>",
    subject: `[Archeogrid][ALERT] ${subject}`,
    //text: '', // plaintext body
    html: body,
  };
  if (process.env.NODE_ENV !== "development") {
    // send mail with defined transport object
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error(error);
        return;
      }
      console.log("Message sent:", info.response);
    });
  }
}

// function to read .3d file and parse it:
export async function readAndParse3DFile(
  path: string,
): Promise<
  | null
  | ({ type: "3DHOP" | "Potree" } & (
      | { type: "3DHOP"; data: Config3DHOP }
      | { type: "Potree"; data: ConfigPotree }
    ))
> {
  const content = await fsp.readFile(path, "utf-8").catch((err) => {
    sendMail("Error opening .3d file", `${path}<br>${err}`);
  });

  if (!content) {
    sendMail("Error loading .3d file", "ReadFile failed");
    return null;
  }

  try {
    const data = JSON.parse(content);
    if (data["3dhop"]) {
      if (process.env.NODE_ENV === "development") console.log("Read 3DHOP data", data);
      return { type: "3DHOP", data };
    }
    if (data.potree) {
      if (process.env.NODE_ENV === "development") console.log("Read Potree data", data);
      return { type: "Potree", data };
    }
    throw new Error("Could not determine a supported 3D viewer");
  } catch (err) {
    sendMail("Error parsing .3d file", `${path}<br>${JSON.stringify(err)}`);
  }

  return null;
}
