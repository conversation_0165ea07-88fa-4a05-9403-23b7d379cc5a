<!DOCTYPE HTML>
<html>

<head>
    <%- include('head_admin.ejs') %>
    <% if (locals.branch) { %>
        <link rel="stylesheet" href="/assets/css/<%= locals.branch %>.css">
    <% } else { %>
        <link rel="stylesheet" href="/assets/css/pft3d.css">
    <% } %>
</head>

<body>
    <%- include('header_admin.ejs') %>

    <div class="d-flex" style="padding-top: 32px;">
        <div class="offcanvas-xl offcanvas-start overflow-y-auto border menuIndex" id="menuGaucheAdmin">
            <div class="offcanvas-body d-flex flex-column gap-4 p-2">
                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fa fa-folder-open"></i><%= __n('admin.category.folder') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/manageFolders", text: __('admin.folder.manage'), icon: "fas fa-edit" }) %>
                        <%- include('../utils/list-link', { target: "/admin/folderOrder", text: __('admin.folder.order'), icon: "fas fa-arrows-alt-v" }) %>
                        <%- include('../utils/list-link', { target: "/admin/downloadFolders", text: __('admin.folder.download'), icon: "fa fa-download" }) %>
                        <%- include('../utils/list-link', { target: "#", text: __('admin.folder.maintenance'), icon: "fa fa-cogs" }) %>
                        <%- include('../utils/list-link', { target: "/admin/permFolders", text: __('admin.folder.permissions'), icon: "fa fa-unlock" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fas fa-file"></i><%= __n('admin.category.file') %>
                    </h4>
                    <div class="list-group"> 
                        <%- include('../utils/list-link', { target: "/admin/synchro", text: __('admin.file.sync'), icon: "fas fa-sync" }) %>
                        <%- include('../utils/list-link', { target: "/admin/json", text: __('admin.file.json'), icon: "fa fa-file-code" }) %>
                        <%- include('../utils/list-link', { target: "/admin/addIPTC_EXIF", text: __('admin.file.syncIPTC_EXIF'), icon: "fa fa-file-image" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <span><i class="fa fa-female"></i><i
                               class="fa fa-male"></i></span><%= __n('admin.category.user') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/tempusers", text: __('admin.user.addTemp'), icon: "far fa-smile" }) %>
                        <%- include('../utils/list-link', { target: "/admin/users", text: __('admin.user.list'), icon: "fas fa-list-ul" }) %>
                        <%- include('../utils/list-link', { target: "/admin/adduser", text: __('admin.user.add'), icon: "fa fa-user-plus" }) %>
                        <%- include('../utils/list-link', { target: "/admin/editUser", text: __('admin.user.edit'), icon: "fas fa-edit" }) %>
                        <%- include('../utils/list-link', { target: "/admin/permUser", text: __('admin.user.permissions'), icon: "fa fa-lock" }) %>
                        <%- include('../utils/list-link', { target: "/admin/assignScribeUser", text: __('admin.user.assign'), icon: "fas fa-key" }) %>
                        <%- include('../utils/list-link', { target: "/admin/entity", text: __('admin.user.entity'), icon: "fas fa-university" }) %>
                        <%- include('../utils/list-link', { target: "#", text: __('admin.user.deleteTemp'), icon: "fa fa-ban" }) %>
                        <%- include('../utils/list-link', { target: "/admin/userHistoryForm", text: __('admin.user.history'), icon: "fas fa-copy" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fa fa-users"></i><%= __n('admin.category.group') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/groups", text: __('admin.group.list'), icon: "fas fa-list" }) %>
                        <%- include('../utils/list-link', { target: "/admin/addgroup", text: __('admin.group.add'), icon: "fa fa-plus" }) %>
                        <%- include('../utils/list-link', { target: "/admin/groupUsers", text: __('admin.group.edit'), icon: "fa fa-cogs" }) %>
                        <%- include('../utils/list-link', { target: "/admin/permGroup", text: __('admin.group.permissions'), icon: "fa fa-lock" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fas fa-file-alt"></i><%= __n('admin.category.metadata') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/addMetadataModel", text: __('admin.metadata.addModel'), icon: "fa fa-plus" }) %>
                        <%- include('../utils/list-link', { target: "/admin/editMetadataModel", text: __('admin.metadata.editModel'), icon: "fa fa-edit" }) %>
                        <%- //include('../utils/list-link', { target: "/admin/metadata", text: __('admin.metadata.csv'), icon: "fas fa-file-code" }) %>
                        <%- include('../utils/list-link', { target: "/admin/csvMetadata", text: __('admin.metadata.csv'), icon: "fas fa-file-code" }) %>
                        <%- include('../utils/list-link', { target: "/admin/tag", text: __('admin.metadata.tags'), icon: "fas fa-tags" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fas fa-book"></i><%= __n('admin.category.thesaurus') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/thesaurusOrder", text: __("admin.thesaurus.order"), icon: "fas fa-book" }) %>
                        <%- include('../utils/list-link', { target: "/admin/thesaurusAdd", text: __("admin.thesaurus.add"), icon: "fas fa-plus" }) %>
                        <%- include('../utils/list-link', { target: "/admin/thesaurusLinkSelection", text: __('admin.thesaurus.link'), icon: "fas fa-clone" }) %>
                    </div>
                </div>

                <div>
                    <h4 class="d-flex align-items-center gap-2">
                        <i class="fa fa-address-card"></i><%= __n('admin.category.organization') %>
                    </h4>
                    <div class="list-group">
                        <%- include('../utils/list-link', { target: "/admin/organizationsort/name/1", text: __("admin.organization.sort"), icon: "fas fa-list-ul" }) %>
                        <%- include('../utils/list-link', { target: "/admin/addorganization", text: __("admin.organization.add"), icon: "fas fa-university" }) %>
                        <%- include('../utils/list-link', { target: "/admin/actorsort/family_name/1", text:  __("admin.actor.sort"), icon: "fas fa-list-ul" }) %>
                        <%- include('../utils/list-link', { target: "/admin/addactor", text:  __("admin.actor.add"), icon: "fas fa-user" }) %>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid mt-1" id="menuCentreAdmin">
            <div class="d-flex align-items-center justify-content-between" id="admin">
                <div class="d-flex gap-2 align-items-center">
                    <button class="block d-xl-none btn btn-secondary" data-bs-toggle="offcanvas" data-bs-target="#menuGaucheAdmin">
                        <i class="fas fa-bars fa-lg"></i>
                    </button>
                    <button class="btn btn-primary" onclick="window.history.go(-1); return;">Back</button>
                    <a href="/admin/docs<% if (lang === "fr") { %>/fr<% } %>" target="_blank" class="link-secondary">Docs</a>
                </div>

                <div class="text-center">
                    <h2><a href="/admin"><%=__('dashboard')%>&nbsp;ArcheoGRID</a></h2>
                </div>

                <div class="d-flex gap-4 align-items-center">
                    <div class="text-center">
                        <%- include("../utils/language-switch", { lang_route: "admin" }) %>
                    </div>

                    <div class="d-flex flex-column gap-1" id="menuDroit">
                        <a type="button" href="/profil" class="btn btn-sm btn-light"><%= user.username %></a>
                        <a href="/logout" title="<%= __('deconnecte')%>"><%= __('deconnecte')%></a>
                    </div>
                </div>

            </div>

            <% if (typeof message !== "undefined" && message && message !== "") { %>
                <div class="alert alert-success"><%= message %></div>
            <% } %>

            <%- body %>
        </div>
    </div>

    <%- include('footer_admin.ejs') %>
</body>

</html>