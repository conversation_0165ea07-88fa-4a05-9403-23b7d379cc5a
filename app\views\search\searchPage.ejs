<link rel="stylesheet" href="/css/search-page.css">
<script src="/assets/js/search.js"></script>
<%- include('../utils/title-content', { project: project, home: "home", projectId: projectId }) %>
<%- include('tagsFunctions.ejs') %>
<%- include('advancedModelFunctions.ejs') %>

<% if (info === 'yes') { %>

<% let saveSearchButton = '<button class="btn btn-hand" type="submit" onclick="var name = prompt(\'' + __('savedSearchName') + '\'); if (name !== null) saveSearch(name);" title="' + __('saveSearch') + '"><i class="fas fa-save"></i></button>' %>

<% let alertSuccess = '<div class="alert alert-success mb-2 save-success" role="alert" style="display: none;">' + __('savedSearchSuccess') + ' <a class="alert-link" href="/profil#searches" target="_blank">' + __('link') + '</a></div>' %>
<% let alertError = '<div class="alert alert-danger mb-2 save-error" role="alert" style="display: none;">' + __('savedSearchError') + '</div>' %>
<% let alertDisconnected = '<div class="alert alert-danger mb-2 save-notconnected" role="alert" style="display: none;">' + __('disconnected') + '</div>' %>

<!-- Hidden fields for searchable dropdown -->
<input type="hidden" id="no-results-text" value="<%=__('noResult')%>">



<div class="d-flex justify-content-between" style="height: 79vh;" id="GROS">

  <div id="menuGauche" class="hide-on-mobile d-flex flex-column justify-content-between col-3"
       style="max-height: none; overflow: visible; width: calc(25% + 20px);">
    <div id="search-left-div" class="search-overflow">

      <div id="advanced-search" class="d-flex flex-column">
        <div class="d-flex flex-column mb-2">
          <div class="d-flex">
            <div class="input-group no-gap">
              <div class="d-flex align-items-center" style="width: 100%;">

                <div class="custom-autocomplete" style="width: 70%;">
                  <input type="text" id="searchSelectAdvancedInput" class="form-control" placeholder="<%= __('selectFolder') %>..." autocomplete="off">
                  <div id="searchSelectAdvancedDropdown" class="autocomplete-items"></div>
                </div>

                <select id="item-type" class="custom-select-input form-control" style="min-width: 0; width: 30%; border-radius: 0 0.25rem 0.25rem 0;">
                    <option value="allFiles" <% if (!search['type']) {%> selected <%}%>><%=__('types')%></option>
                      <optgroup label="Types"></optgroup>
                    <option value="objects" <% if (search['type'] === "objects") {%> selected
                            <%}%>><%=__('object')%>s</option>
                    <option value="files" <% if (search['type'] === "files") {%> selected <%}%>><%=__('file')%>s</option>
                    <option value="unicos" <% if (search['type'] === "unicos") {%> selected <%}%>> Unico</option>
                    <optgroup label="Extensions"></optgroup>
                    <% for (let i=0; i < extensions.length; ++i) { %>
                            <option value="<%=extensions[i].file_ext%>" <% if (search['type'] === extensions[i].file_ext) {%>
                            selected <%}%>><%=extensions[i].file_ext%></option>
                      <% } %>
                </select>

                <button class="btn btn-vitrine-secondary" style="margin-left: 0.5rem;" type="submit" onclick="newSearch()" title="<%=__('asearch')%>">
                  <i class="fas fa-search fa-fw secondary-icon-color"></i>
                </button>

              </div>
            </div>
          </div>
          <div class="d-flex gap-2 mt-2" style="width: 100%;">
            <% if (user['id'] ) {  %>
            <div class="flex-fill"><%- saveSearchButton %></div>
            <% } %>
            <button id="remove-btn" class="btn btn-hand flex-fill" type="submit" title="<%=__('eraseSearch')%>"
                    onclick="emptySearch()">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>

        <%- alertSuccess %>
        <%- alertDisconnected %>
        <%- alertError %>

        <h5><%=__('allFields')%> :</h5>
        <div id="all-search-bar-list" class="form-group search-bar-list">
          <% if (search['all'] && search['all'].length > 0) { %>
          <% for (let i = 0; i < search['all'].length; ++i) { %>
          <%- include('searchBar.ejs', {multiple: true, tags: search['all'][i]}) %>
          <% } %>
          <% } else {%>
          <%- include('searchBar.ejs', {multiple: true}) %>
          <% } %>
        </div>

        <% if (thesaurus.length > 0) { %>
        <% let isExpanded = search && (search['thesaurus'] || search['multi']) ? true : false %>
        <div id="thesaurus" class="mb-2">
          <div class="d-flex justify-content-between mb-2">
            <h5 style="margin-top: 1rem; "><%=__('tagsThesaurus')%> :</h5>
            <button class="btn btn-vitrine-primary btn-collapse-search <% if (!isExpanded) { %>collapsed<% } %>"
                    type="button" data-bs-toggle="collapse" data-bs-target="#thesaurus-search">
              <i class="fas fa-chevron-right fa-xs"></i>
            </button>
          </div>
          <div id="thesaurus-search" class="collapse <% if (isExpanded) { %>show<% } %>">
            <% for (let j=0; j < thesaurus.length; ++j) { %>
            <% let status = thesaurus[j]['status'] %>
            <% let thes = thesaurus[j]['thesaurus'] %>
            <% let multiple = false %>
            <% if (status === 'multi') { multiple = true } %>

            <div id="<%=thesaurus[j]['thesaurus']%>-search" class="form-group" style="margin-bottom: 2rem!important;">
              <span><%=thesaurus[j]['name']%> :</span>
              <div id="<%=thesaurus[j]['thesaurus']%>-search-bar-list" class="search-bar-list"
                   status="<%=thesaurus[j]['status']%>" thesaurus="<%=thesaurus[j]['thesaurus']%>">
                <% if (search && search[status] && search[status][thes]) { %>
                <% for (let tags in search[status][thes]) { %>
                <%- include('searchBar.ejs', {multiple: multiple, tags: search[status][thes][tags]}) %>
                <% } %>
                <% } else { %>
                <%- include('searchBar.ejs', {multiple: multiple}) %>
                <% } %>
              </div>
            </div>
            <% } %>
          </div>
        </div>
        <% } %>

        <% if (models.length > 0) { %>
        <% let isPassportExpanded = search && search['passport'] ? true : false %>
        <div id="passport" class="mb-2">
          <div class="d-flex justify-content-between mb-2">
            <h5><%=__('metadata')%> :</h5>
            <button class="btn btn-vitrine-primary btn-collapse-search <% if (!isPassportExpanded) { %>collapsed<% } %>"
                    type="button" data-bs-toggle="collapse" data-bs-target="#passport-search">
              <i class="fas fa-chevron-right fa-xs"></i>
            </button>
          </div>

          <div id="passport-search" class="collapse <% if (isPassportExpanded) { %>show<% } %>">
            <% for (let i = 0; i < models.length; ++i) { %>
            <% let model = models[i] %>
            <% if (model) { %>
            <% let isModelExpanded = false %>
            <% for (let j = 0; j < model.metadata.length && !isModelExpanded; ++j) { %>
            <% let id = model.metadata[j].id_metadata %>
            <% if (search && search['passport'] && search['passport'][id]) { isModelExpanded = true } %>
            <% } %>
            <div id="<%=model.name%>" class="mb-2">
              <div class="d-flex justify-content-between">
                <h6><%=model.label%> <i class="fas fa-info-circle" title="<%=model.description%>"></i> :</h6>
                <button class="btn btn-vitrine-secondary btn-collapse-search <% if (!isModelExpanded) { %>collapsed<% } %>"
                        type="button" data-bs-toggle="collapse" data-bs-target="#<%=model.name%>-search-bar-list">
                  <i class="fas fa-chevron-right fa-xs"></i>
                </button>
              </div>

              <div id="<%=model.name%>-search-bar-list" class="collapse <% if (isModelExpanded) { %>show<% } %>">
                <% for (let j = 0; j < model.metadata.length; ++j) { %>
                  <% let id = model.metadata[j].id_metadata %>
                  <% let label = model.metadata[j].label %>
                  <% let description = model.metadata[j].description %>
                  <% let type = model.metadata[j].type %>
                  <div class="form-group mb-1" id-metadata="<%=id%>">
                    <span><%=label%> <i class="fas fa-info-circle" title="<%=description%>"></i> :</span>
                    <% switch (type) {
                      case 'actor': %>
                        <%- include('searchBar_actor.ejs', {multiple: false}) %>
                        <% break;
                      case 'datation': %>
                        <%- include('searchBar_datation.ejs', {multiple: false, id_metadata: id}) %>
                        <% break;
                      default:
                        if (search && search['passport'] && search['passport'][id]) { %>
                          <%- include('searchBar.ejs', {multiple: false, tags: search['passport'][id]}) %>
                        <% } else { %>
                          <%- include('searchBar.ejs', {multiple: false}) %>
                        <% }
                        break;
                    } %>
                  </div>
                <% } %>
              </div>
            </div>
            <% } %>
            <% } %>
          </div>
        </div>
        <% } %>
      </div>
    </div>
  </div> <%# close menuGauche %>

  <div class="col-12 col-md-9" id="menuCentre">
    <div class="loader"></div>
    <%- include('../explore/exploreVitrine.ejs') %>
  </div> <%# close menuCentre %>

</div> <%# close GROS %>

<% } else { %>

<div class="container-fluid border d-flex justify-content-center flex-column">
  <h1 class="display-3 text-danger align-self-center"><%=__('wrongProject')%></h1>
  <a href="/" class="align-self-center"><%=__('returnHome')%></a>
</div>

<% } %>

<script>
window.folders = <%- JSON.stringify(folders || []) %>;
</script>

<script>
(function() {
    'use strict';

    document.addEventListener('DOMContentLoaded', function() {
        const folders = window.folders;
        const input = document.getElementById('searchSelectAdvancedInput');
        const dropdown = document.getElementById('searchSelectAdvancedDropdown');

        function showDropdown(matches) {
          dropdown.innerHTML = '';
          matches.forEach(f => {
            const div = document.createElement('div');
            div.textContent = f.name + (f.nb_tot_items > 0 ? ` [${f.nb_tot_items}]` : '');
            div.className = 'autocomplete-item';
            div.onclick = function() {
              input.value = f.name;
              window.pendingFolderId = f.id;
              dropdown.innerHTML = '';
              dropdown.style.display = 'none';
            };
            dropdown.appendChild(div);
          });
          dropdown.style.display = matches.length ? 'block' : 'none';
        }

        input.addEventListener('input', function() {
          const val = this.value.toLowerCase();
          if (!val) {
            showDropdown(folders.filter(f => f.name && f.name !== 'tif'));
            window.pendingFolderId = null; // Clear if input is empty
            return;
          }
          const matches = folders.filter(f => f.name && f.name.toLowerCase().includes(val) && f.name !== 'tif');
          showDropdown(matches);
        });
        input.addEventListener('focus', function() {
          showDropdown(folders.filter(f => f.name && f.name !== 'tif'));
        });
        input.addEventListener('blur', function() {
          setTimeout(() => { dropdown.style.display = 'none'; }, 200);
        });

        window.exploreFolderVitrine = function(folderId) {
            // console.log('Folder selected for search:', folderId);
            window.pendingFolderId = folderId;
        };

        const originalNewSearch = window.newSearch;
        window.newSearch = function() {
            const searchParams = searchBarsToJSON();
            const decodedParams = JSON.parse(decodeURIComponent(searchParams));
            if (window.pendingFolderId) {
              decodedParams.folder = window.pendingFolderId;
            }
            const hasSearchContent = decodedParams.all && decodedParams.all.length > 0 && decodedParams.all[0].length > 0 && decodedParams.all[0][0].trim().length > 1;
            const hasFolder = decodedParams.folder || window.pendingFolderId;
            const hasOtherCriteria = decodedParams.type || decodedParams.thesaurus || decodedParams.multi || decodedParams.passport || decodedParams.advancedModel;
            const hasFolderUpdated = decodedParams.folder || window.pendingFolderId;
            if (hasSearchContent || hasFolderUpdated || hasOtherCriteria) {
                if (originalNewSearch) {
                    originalNewSearch();
                }
            } else {
                console.log('Search cancelled: no meaningful search criteria');
                $("#explore-div").hide();
                $("#menuCentre").show();
            }
        };

        const originalExploreSearch = window.exploreSearch;
        window.exploreSearch = function(id, searchParameters) {
            if (typeof cleanupPreviousContent === 'function') {
                cleanupPreviousContent();
            }
            $("#explore-results").html('<div class="d-flex justify-content-center my-5"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
            $("#explore-div").css("display", "flex");
            $("#menuCentre").show();
            window.explorePage = {
                url: window.exploreSearchUrl || function(page) { return `/exploreSearchPage/${id},${page}?display=${window.display || 'grid'}&search=${searchParameters}&limit=0`; },
                id: id,
                search: searchParameters,
            };
            if (typeof exploreNb === 'function') {
                exploreNb(`/exploreSearchNb/${id}?search=${searchParameters}`);
            }
        };
    });
})();
</script>