<% if (data.length > 0) { %>
<div class="explore-list">
    <% for (let i = 0; i <  data.length; i++) { %>

    <% let item = data[i] %>
    <% let type = '' %>
    <% if (item.item_type === 'file') { %>
    <% type = 'i' %>
    <% } else if (item.item_type === 'object') { %>
    <% type = 'o' %>
    <% } else if (item.item_type === 'unico') { %>
    <% type = 'u' %>
    <% } else { %>
    <% type = 'd' %>
    <% } %>

    <%
         let href = '#';
         if (item.has_access !== false && item.idfile !== null) {
             if (item.item_type === 'file') {
                 href = `/visionneuse,${item.idfile}-${item.idfolder},${branch}-v,${WRights}`;
             } else if (item.item_type === 'object') {
                 href = `/visionneuseObj,${item.id_item},${item.idfile}-${item.idfolder},${branch}-v,${WRights}`;
             } else if (item.item_type === 'unico') {
                 href = `/visionneuseUnicoV2,${item.id_item},${item.idfolder},${branch}-v,${WRights}`;
             }
         }
     %>
     <a href="<%= href %>" class=" <% if (item.has_access !== false) { %>lazy-list-item lazy-card lazy-metadata<% } %>"
        style="text-decoration: none; color: inherit; display: block;"
        data-index="<%= i %>"
        <% if (item.has_access !== false) { %>
            data-item-id="<%= item.id_item %>"
            data-item-type="<%= item.item_type %>"
            data-branch="<%= branch %>"
            <% if (locals.model && model !== '0') { %>data-model="<%= model %>"<% } %>
        <% } %>>
         <%
           let contextMenuHandlerString = null;
           if ((selection && user.id) && (user.user_status !== 'guest')) {
             const escapedItemType = item.item_type ? item.item_type.replace(/'/g, "\\'") : '';
             contextMenuHandlerString = `updateItemSelection(${item.id_item}, '${escapedItemType}', ${item.idfolder}); return false;`;
           }
         %>
         <div id="<%= `item-${type}-${item.id_item}_${item.idfolder}` %>"
              class="list-vitrine-item d-flex check_item update-item-selector position-relative list-link"
              <% if (contextMenuHandlerString) { %>
                  oncontextmenu="<%= contextMenuHandlerString %>"
              <% } %>>

        <div class="list-vitrine-image">
            <% if (item.has_access === false) { %>
                <img class="list-img"
                     src="/images/not_authorized.png"
                     alt="Not Authorized">
            <% } else if ((item.item_type === 'object') && (item.previsu)) { %>
            <img class="list-img lazy-image"
                 data-src="<%= item.previsu %>"
                 src="/assets/images/placeholder.png"
                 alt="<%= item.name %>">
            <% } else if ((item.item_type === 'object') && ((item.idfile === '0') || (!item.idfile))) { %>
            <img class="list-img"
                 src="/assets/images/default_repre_image_object.png"
                 alt="<%= item.name %>">
            <% } else if (item.item_type === 'unico' && item.width && item.height) { %>
            <% if (item.type === 'rect') { %>
            <img class="list-img lazy-image"
                 data-src="<%= `/crop/${item.idfolder}_${item.idfile}/${item.x},${item.y},${item.width},${item.height}` %>"
                 src="/assets/images/placeholder.png"
                 alt="<%= item.name %>">
            <% } else if (item.type === 'poly') { %>
            <% let points = item.polygon.split(' ') %>
            <% for (let j in points) { let e = points[j].split(','); e[0] = parseInt(e[0]) - item.x; e[1] = parseInt(e[1]) - item.y; points[j] = e.join(',') } %>
            <% points = points.join(' ') %>
            <svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
                 class="list-img" viewBox="0 0 <%- item.width %> <%- item.height %>"
                 id="unico-svg-<%- item.id_item %>">
                <mask id="svgmask-<%- item.id_item %>">
                    <polygon fill="#ffffff" points="<%- points %>" />
                </mask>
                <image xmlns:xlink="http://www.w3.org/2000/svg"
                       xlink:href="<%= `/crop/${item.idfolder}_${item.idfile}/${item.x},${item.y},${item.width},${item.height}` %>"
                       width="<%- item.width %>" height="<%- item.height %>"
                       mask="url(#svgmask-<%- item.id_item %>)" />
            </svg>
            <% } %>
            <% } else { %>
            <img class="list-img lazy-image"
                 data-src="/thumb/<%= item.idfolder %>_<%= item.idfile %>"
                 src="/assets/images/placeholder.png"
                 alt="<%= item.name %>">
            <% } %>
        </div>

        <div class="list-vitrine-content">
            <div class="list-vitrine-title">
                <% if (item.has_access === false) { %>
                    <h5><heading class="list-vitrine-item-text item-title"><%=__('not_authorized')%></heading></h5>
                <% } else if (item.title && item.title.trim() !== '') { %>
                    <h5><heading class="list-vitrine-item-text item-title"><%= item.title %></heading></h5>
                <% } else { %>
                    <% if (item.name) { %>
                        <% if (item.name.length > 100) { %>
                            <h5><heading class="list-vitrine-item-text item-title"><%= item.name.substring(0, 100) %>...</heading></h5>
                        <% } else { %>
                            <h5><heading class="list-vitrine-item-text item-title"><%= item.name %></heading></h5><% } %>
                    <% } else if (item.object_name && item.item_type === 'object') { %>
                        <% if (item.object_name.length > 100) { %>
                            <h5><heading class="list-vitrine-item-text item-title"><%= item.object_name.substring(0, 100) %>...</heading></h5>
                        <% } else { %>
                            <h5><heading class="list-vitrine-item-text item-title"><%= item.object_name %></heading></h5>
                        <% } %>
                    <% } else if (item.filename && item.item_type === 'file') { %>
                        <% if (item.filename.length > 100) { %>
                            <h5><heading class="list-vitrine-item-text item-title"><%= item.filename.substring(0, 100) %>...</heading></h5>
                        <% } else { %>
                            <h5><heading class="list-vitrine-item-text item-title"><%= item.filename %></heading></h5>
                        <% } %>
                    <% } else { %>
                        <h5><heading class="list-vitrine-item-text item-title"><%=__('no_title')%></heading></h5>
                    <% } %>
                <% } %>
            </div>
            <div class="d-flex align-items-center justify-content-end mt-1">
                <% if (item.has_access !== false && item.idfile !== null && user.id != 0) { %>
                    <% if (item.item_type === 'object') { %>
                        <button type="button" title="Commenter" class="list-icon-btn" onclick="window.location.href='/comment,<%=branch%>,<%= item.idfolder%>,<%= item.idfile%>,object'">
                          <i class="fa fa-comments"></i>
                        </button>
                        <% if (typeof WRights !== 'undefined' && WRights) { %>
                            <button type="button" title="Ajouter un tag" class="list-icon-btn" onclick="window.location.href='/keyword,<%= branch %>,object,<%= item.id_item %>,<%= item.idfolder %>'">
                              <i class="fa fa-tag"></i>
                            </button>
                        <% } %>
                    <% } else { %>
                        <button type="button" title="Commenter" class="list-icon-btn" onclick="window.location.href='/comment,<%=branch%>,<%= item.idfolder%>,<%= item.idfile%>,file'">
                          <i class="fa fa-comments"></i>
                        </button>
                        <% if (typeof rights !== 'undefined' && rights) { %>
                            <button type="button" title="Ajouter un tag" class="list-icon-btn" onclick="window.location.href='/keyword,<%= branch %>,file,<%= item.idfile %>,<%= item.idfolder %>'">
                              <i class="fa fa-tag"></i>
                            </button>
                        <% } %>
                    <% } %>
                <% } %>
                <% if (locals.virtual) { %>
                    <%
                      const escapedItemTypeForVirtualDelete = item.item_type ? item.item_type.replace(/'/g, "\\'") : '';
                      const virtualDeleteItemHandlerString = `event.stopPropagation(); deleteItem(${item.id_item}, '${escapedItemTypeForVirtualDelete}', ${item.fid});`;
                    %>
                    <% if (item.item_type === 'object') { %>
                        <% if (((item.user_link) && (item.user_link === user.id)) || (user.user_status === 'admin') || (user.user_status === 'scribe')) { %>
                            <button type="button" class="list-icon-btn" onclick="<%= virtualDeleteItemHandlerString %>">
                              <i class="fa fa-trash" aria-hidden="true"></i>
                            </button>
                        <% } %>
                    <% } else { %>
                        <button type="button" class="list-icon-btn" onclick="<%= virtualDeleteItemHandlerString %>">
                          <i class="fa fa-trash" aria-hidden="true"></i>
                        </button>
                    <% } %>
                <% } else { %>
                    <% if (item.item_type === 'object') { %>
                        <% if ((item.user_link) && (item.user_link === user.id)) { %>
                            <%
                              const escapedItemTypeForRealDelete = item.item_type ? item.item_type.replace(/'/g, "\\'") : '';
                              const realDeleteItemHandlerString = `event.stopPropagation(); deleteItemReal(${item.id_item}, '${escapedItemTypeForRealDelete}', ${item.fid});`;
                            %>
                            <button type="button"
                               class="list-icon-btn" onclick="<%= realDeleteItemHandlerString %>">
                              <i class="fa fa-trash" aria-hidden="true"></i>
                            </button>
                        <% } %>
                    <% } %>
                <% } %>
            </div>
        </div>
    </div>
    </a>
    <% } %>
</div>

<!-- Add selection rectangle div -->
<div id="drag-selection-rectangle"></div>
<% } %>

<script>
    $('#item-selection').show();
    $('#index-all-btn').show();
    $('#tag-all-btn').show();
    $('#delete-all-btn').show();

    $("#main-ul li ul li input:checked").prop("checked", false);
</script>