<%- include('utils/title-content', { project: mainFolder, projectId: mainFolder.mainfolder, home: "home" }) %>

<!-- Main -->
<div id="main" class="container">
    <% if (type === 'file') { %>
    <% if (typeof image !== 'string') { %>
    <div class="text-center">
        <% if (image['file_ext'] === 'url') { %>
        <a href="<%= image['urlTarget'] %>" target="_blank">
            <img class="mx-auto" src="/thumb/<%= visuFolder %>_<%= image['id'] %>" alt="<%= image['filename']%>"
                 style="width: 200px;">
        </a>
        <% } else if (image['file_ext'] === '3d'){ %>
        <a href="/viewer3d,<%= image['srcImg3d'] %>,<%= folder %>,<%= root%>" title="The 3D viewer" target="_blank">
            <img class="card-img mx-auto d-block"
                 src="/media/image?fid=<%= folder %>&id=<%= image['id']%>&format=<%= image['srcImgThumb']%>&type=thumb&root=<%= root %>"
                 alt="<%= image['filename'] %>" style="width: 200px;">
        </a>
        <% } else { %>
        <% if (viewerFormat.indexOf(image['file_ext'].toLowerCase()) !== -1) { %>
        <a href="/visionneuse,<%= image['id'] %>-<%= visuFolder %>,<%= root %>-v,0" target="_blank">
            <img class="mx-auto" src="/thumb/<%= visuFolder %>_<%= image['id'] %>" alt="<%= image['filename']%>">
        </a>
        <% } else { %>
        <img class="mx-auto" src="/thumb/<%= visuFolder %>_<%= image['id'] %>" alt="<%= image['filename']%>">
        <% } %>
        <% } %>
    </div>
    <%  } %>
    <% } else if (type === 'object') { %>
    <% if (image['id']) { %>
    <img class="card-img mx-auto d-block" src="/thumb/<%= visuFolder %>_<%= image['id'] %>"
         alt="<%= image['filename']%>">
    <% } else { %>
    <img class="card-img mx-auto d-block" src="/assets/images/default_repre_image_object.png"
         alt="<%= image['filename']%>">
    <% } %>
    <% } %>

    <% if (typeof image !== 'string') { %>
    <div class="text-center">
        <% if (type === 'file') { %>
        <%=__('file') %> : <%= image['filename']%>
        <% } else if (type === 'object') { %>
        <%=__('object') %> <%= object['name']%>
        <% } %>
        <br>
        <a href="/projectv/<%= mainFolder['mainfolder'] %>?folder=<%= image['fid'] %>">
            <%= mainFolder['folder_name'] %>
        </a>
    </div>
    <% }%>

    <h4><%=__('note')%><% if (mainFolder['id_metadata_model']) { %> / <%= mainFolder['label']%><% } %></h4>


    <form id="passport" action="" method="post" class="my-form">
        <%# Une partie du formulaire n'est accessible que aux utilisateurs connectés %>
        <%# Et cette partie existe s'il y a un metadata model attache au main folder%>
        <% if (mainFolder['model_name']) {%>
        <div class="form-group row">
            <label for="date_manuel" class="col-sm-4 col-form-label"><%= metadata[0]['label'] %> <a href="#"
                   title="<%= metadata[0]['description'] %>" style="text-decoration: none;"><i
                       class="fas fa-info-circle"></i></a>
            </label>
            <div class="col-sm-8">
                <select class="form-control" name="<%= metadata[0]['isunique'] %>#<%= metadata[0]['id'] %>"
                        id="<%= metadata[0]['id'] %>">
                    <option value=""><%=__('choose') %></option>
                    <% for  (ind in listeMetadata ) { %>
                    <option value="<%= listeMetadata[ind]['name'] %>"><%= listeMetadata[ind]['name'] %></option>
                    <% } %>

                </select>
            </div>
        </div>
        <% } %>
        <%# Cette partie commentaire est ouverte à tous ? %>
        <div class="form-group row">
            <label for="comment" class="col-sm-4 col-form-label"><%=__('add')%> <%=__('a')%> <%=__('note')%></label>
            <div class="col-sm-8">
                <textarea class="form-control" rows="3" name="comment" id="comment"></textarea>
            </div>
        </div>
        
        <div class="form-group row">
            <!-- <label for="author" class="col-sm-4 col-form-label"><%=__('author')%> <a href="#"
                   title="Par défaut, nom de la personne connectée" style="text-decoration: none;"><i
                       class="fas fa-info-circle"></i></a></label> -->
            <div class="col-sm-8">
                <input type="hidden" class="form-control" name="author" id="author">
            </div>
        </div>
        <div class="form-group row">
            <!-- <label for="other_date" class="col-sm-4 col-form-label"><%=__('date')%><a href="#"
                   title="Par défaut, la date du jour" style="text-decoration: none;"><i
                       class="fas fa-info-circle"></i></a></label></label> -->
            <div class="col-sm-8">
                <input type="hidden" class="form-control" name="other_date" id="other_date">
            </div>
        </div>

        <input type="hidden" value="<%= mainFolder['mainfolder'] %>" name="mainFolder">
        <input type="hidden" value="<%= user.id %>" name="userId">
        <input type="hidden" value="<%= idItem %>" name="itemId">
        <input type="hidden" id="reqUrl" />
        <input type="hidden" id="redirectUrl" name="redirectUrl" />

        <div class="form-group">
            <div class="col-sm-offset-5 col-sm-7">
                <a href="#" id="save-link" class="btn btn-vitrine-primary"><%=__('save') %></a>
                &nbsp;<a href="#" id="cancel-link" class="btn btn-vitrine-secondary">
                   <%=__('cancel') %>
                </a>
            </div>
        </div>
    </form>
</div>

</div>



<script>
    function addkey(thesaurus) {
        let dataPost = {};

        console.log(thesaurus)
        dataPost['value'] = $('#opentheso_' + thesaurus + '_value').val()
        dataPost['id'] = $('#opentheso_' + thesaurus + '_id').val()
        dataPost['thesaurus'] = thesaurus

        $.ajax({
            type: 'POST',
            url: '/addkeyword,<%= root %>,<%= type %>,<%= idItem %>,<%= mainFolder %>',
            data: dataPost
        })
            .done(function (data) {
                console.log(data)
                // si indexation multiple et que le concept est déjà indexé sur un des terme mais pas sur les autres,
                // il ne faut pas le remonter sinon on croit à une erreur
                if (<%= idItem %> !== 0)
        {
            if (data)
                alert('<%= __('keyword') %> <%= __('added') %>')
            else alert('<%= __('keyword') %> <%= __('already') %> <%= __('indexed') %> ')
        } else {
            // TODO : vérifier quelque chose ??? avec data  ?
            alert('<%= __('keyword') %> <%= __('added') %>')
        }
        location.reload();
    })
            .fail(function () {
        alert('ERROR')
        location.reload();
    })

    }
    // Autocomplete
    $(function () {
        <% for (let k = 0; k < metadata.length; k++) { %>
            <% if (metadata[k]['status'] === 'thesaurus') { %>
                $('#opentheso_<%= metadata[k]['list'] %>_value').autocomplete({
                    minLength: 3,
                    autofocus: true,
                    source: function (request, response) {
                        $.ajax({
                            url: '/thesaurusPactolsName/<%= root %>,<%= metadata[k]['list'] %>,'+request.term,
                            dataType: "json",
                            success: function (data) {
                                response(data)
                            }
                        });
                    },
                    select: function (event, ui) {
                        //event.preventDefault();
                        $('#opentheso_<%= metadata[k]['list'] %>_id').val(ui.item.id);
                    }
                });
            <% } %>
        <% } %>

    })


</script>

<script type="text/javascript" src="/js/redirect.js"></script>
<script>
    setupRedirection({
        inputId: 'reqUrl',
        cancelBtnId: 'cancel-link',
        serverUrl: '<%= locals.reqUrl || "" %>',
        defaultUrl: 'projectv/<%= mainFolder["mainfolder"] %>',
        excludedPrefixes: ['comment']
    });

    // Set up save button to submit form normally with redirect URL
    document.addEventListener('DOMContentLoaded', function() {
        const saveBtn = document.getElementById('save-link');
        const cancelBtn = document.getElementById('cancel-link');
        const form = document.getElementById('passport');
        const redirectUrlInput = document.getElementById('redirectUrl');
        const reqUrlInput = document.getElementById('reqUrl');
        
        if (saveBtn && cancelBtn && form && redirectUrlInput) {
            // Copy the redirect URL from reqUrl to redirectUrl field
            if (reqUrlInput && reqUrlInput.value) {
                redirectUrlInput.value = reqUrlInput.value;
            }
            
            saveBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Ensure redirect URL is set to the same value as cancel button
                const cancelHref = cancelBtn.href;
                if (cancelHref) {
                    // Extract path from href (remove domain)
                    const url = new URL(cancelHref);
                    let path = url.pathname + url.search + url.hash;
                    if (path.startsWith('/')) {
                        path = path.substring(1);
                    }
                    redirectUrlInput.value = path;
                }
                
                // Submit the form normally
                form.submit();
            });
        }
    });
</script>
