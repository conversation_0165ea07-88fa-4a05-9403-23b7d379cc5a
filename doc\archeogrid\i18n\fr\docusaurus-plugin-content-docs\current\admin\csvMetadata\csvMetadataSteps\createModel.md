---
sidebar_position: 2
---

# Créer un modèle de metadonnées

Cette fonctionnalité est la deuxième étape pour ajouter vos données CSV à ArcheoGRID. Les métadonnées qui composent le modèle sont les colonnes du fichier CSV. Cette fonctionnalité est divisée en trois étapes. Si vous venez du menu principal, vous commencerez par la sélection du projet, mais si vous venez d'une autre fonctionnalité (création de table ou ingestion de CSV), vous commencerez par la sélection du fichier CSV avec les informations déjà renseignées.

### Sélectionner le projet

Cette étape vous permet de choisir le projet où se trouve votre fichier CSV. Vous pouvez utiliser la barre de recherche pour trouver un projet spécifique.

![](projectSelector-CM-FR.png)

> **Note**: Seuls les projets auxquels vous avez accès sont affichés.

### Sélectionner le fichier CSV

Cette étape vous permet de choisir le fichier CSV que vous souhaitez utiliser. Cela nécessite de définir le délimiteur utilisé dans le fichier CSV, le nom souhaité du modèle et le type d'éléments que le CSV décrit (fichier, dossier ou objet). Le nom par défaut pour le modèle est le nom de la branche suivi du nom du fichier CSV.

![](csvFileSelector-CM-FR.png)

> **Erreur**: Une erreur sera affichée si le dossier de métadonnées n'existe pas et vous serez redirigé vers le menu. S'il n'y a pas de fichier CSV dans le dossier, un message d'information sera affiché vous permettant de revenir à la sélection du projet.

### Détails du modèle de métadonnées

Cette étape vous permet de définir plus précisément le modèle de métadonnées. Vous pouvez ajouter une description du modèle. Pour chaque métadonnée, vous pouvez définir son étiquette, sa description, son type, si elle est unique et si elle est requise/obligatoire. Des valeurs par défaut sont fournies pour chaque métadonnée. À la fin du formulaire, vous pouvez cliquer sur le bouton Créer pour créer le modèle.

![](modelDetails-CM-FR.png)

Si le modèle existe déjà, même (branch, projet, fichier csv), un message informatifs vous l'indiquera et les valeurs affichera seront celles du modèle existant. Lorse que vous validerez le formulaire, le modèle sera mis à jour avec les nouvelles valeurs.

> **Erreur**: Une erreur sera affichée si le fichier csv n'existe pas, s'il n'y a pas d'en-tête dans le fichier ou s'il n'y a qu'une seule colonne dans le fichier.

### Résultat

Si le modèle a été créé ou mis à jour, un message de succès sera affiché. Vous pouvez soit créer la table correspondante, ingérer les données du CSV dans le modèle ou revenir au menu.

![](success-CM-FR.png)
