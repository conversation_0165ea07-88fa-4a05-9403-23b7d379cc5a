import archiver from "archiver";
import bodyParser from "body-parser";
import type { Busboy } from "busboy";
import cookieParser from "cookie-parser";
import { renderFile } from "ejs";
import express, { type Request } from "express";
import asyncHandler from "express-async-handler";
import ejsLayouts from "express-ejs-layouts";
import session from "express-session";
import fs from "fs-extra";
import i18n from "i18n";
import morgan from "morgan";
import multer from "multer";
import http from "node:http";
import path from "node:path";
import querystring from "node:querystring";
import nodemailer from "nodemailer";
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Strategy as OidcStrategy, type Profile, type VerifyCallback } from "passport-openidconnect";
import favicon from "serve-favicon";
import store from "store";
import { Worker } from "worker_threads";

import { upload } from "./middleware/multer";

import type { Branch, Responses } from "@api";

import * as helpers from "./tools/helpers_tools";
import { archeogrid_client } from "./tools/request";

import adminRouter from "./routes/admin";
import authRouter from "./routes/auth";
import collectionsRouter from "./routes/collections";
import editRouter from "./routes/edit";
import exploreRouter from "./routes/explore";
import fileRouter from "./routes/file";
import imageRouter from "./routes/image";
import karnakRouter from "./routes/karnak";
import projectsRouter from "./routes/projects";
import scribeRouter from "./routes/scribe";
import thesaurusRouter from "./routes/thesaurus";
import viewer3DRouter from "./routes/viewer3d";

import contextMiddleware from "./middleware/context";
import flashMiddleware from "./middleware/flash";
import myrootMiddleware from "./middleware/myroot";

import user_table, { validPassword, type UserModel } from "./models/archeogrid_user";
import {
  acceptedVideoFormats,
  branchConfig,
  callbackURL,
  clientId,
  clientSecret,
  defaultLanguage,
  downloadOrigin,
  emailConfig,
  emailHost,
  emailPort,
  emailSecure,
  icone3d,
  isLoggedIn,
  sessionduration,
  outilsPath,
  performRequest,
  prefixDOI,
  readAndParse3DFile,
  server_host,
  server_port,
  app_port,
  urlOrigin,
  validateEmail,
  viewerFormat,
  wgetDOI,
  xmlPath,
  API_promise_limit,
  API_worker_number
} from "./tools/globals";
import { branch } from "../server/types/schemas";

const md5 = require("md5");

const app = express();

const thesaurus = [];
const listeMetadata = [];
type UploadFile = { type: "object" | "folder"; name: string; dir: string } & (
  | { type: "object"; objectId: number }
  | { type: "folder"; idFolder: number }
);
let filenamesUpload: UploadFile[] = [];
let mess_front = "";

const __dirname = process.cwd();

const uploadDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir);
}

// security
app.disable("x-powered-by");
// template engine
app.set("view engine", "ejs");
app.use(ejsLayouts);

app.engine("html", renderFile);

// static assets serving
app.use(express.static(`${__dirname}/`));
app.use("/assets", express.static(`${__dirname}/public`));
app.use("/assetsnode_modules", express.static(`${__dirname}/node_modules`));

// set morgan to log info about our requests for development use.
app.use(morgan("dev"));
// application/json parser
app.use(bodyParser.json());
// parse incoming parameters requests to req.body
app.use(bodyParser.urlencoded({ extended: true }));

// allow cookies access stored in the browser.
app.use(cookieParser());

// internationalization config
i18n.configure({
  autoReload: true,
  defaultLocale: defaultLanguage,
  directory: `${__dirname}/locales`,
  objectNotation: true,
  cookie: "i18n",
});
app.use(i18n.init);

// allow logged user tracking across sessions.
// disconnects after 10 minutes of inactivity
app.use(
  session({
    //cookie: { maxAge: 1000 * 60 * 30 }, // (en ms) passer de 10 à 30mn pour la deconnexion auto et maintenant on paramètre TODO : pouvoir deconnecter certain user
    cookie: { maxAge: parseInt(sessionduration) }, // reprend le paramètre time_session_duration ! TODO : pouvoir deconnecter certain user
    rolling: true,
    secret: "untrucsympapourchanger",
    resave: false, // vu dans https://code.tutsplus.com/tutorials/using-passport-with-sequelize-and-mysql--cms-27537
    saveUninitialized: true, // ancient false, set to true when installing OpenIdConnect
  }),
);

app.use(passport.initialize());
app.use(passport.session());

// add context middleware
app.use(contextMiddleware);

passport.use(
  "local-signin",
  new LocalStrategy(
    {
      usernameField: "username",
      passwordField: "password",
      passReqToCallback: true, // allow to pass the full request object to the callback
    },
    (req, username, password, done) => {
      user_table
        .findOne({ where: { username } })
        .then((user: UserModel | null) => {
          if (!user) {
            done(null, false, { message: req.__("errors.user_not_exist") });
            return;
          }
          // check deleted_user property first
          const userinfo = user.get();
          if (userinfo.deleted_user) {
            done(null, false, { message: req.__("errors.user_not_exist") });
            return;
          }
          if (!user) {
            done(null, false, { message: req.__("errors.user_not_exist") });
            return;
          }
          if (!validPassword(userinfo, password)) {
            done(null, false, { message: req.__("errors.bad_password") });
            return;
          }
          done(null, userinfo);
        })
        .catch((err) => {
          console.error(err);
        });
    },
  ),
);

passport.use(
  "oidc",
  new OidcStrategy(
    {
      issuer: "https://humanid.huma-num.fr",
      clientID: clientId,
      clientSecret: clientSecret,
      authorizationURL: "https://humanid.huma-num.fr/oauth2/authorize",
      userInfoURL: "https://humanid.huma-num.fr/oauth2/userinfo",
      tokenURL: "https://humanid.huma-num.fr/oauth2/token",
      callbackURL: callbackURL,
      scope: "openid profile",
    },
    (issuer: string, profile: Profile, cb: VerifyCallback) => {
      return cb(null, profile);
    },
  ),
);

passport.serializeUser((user, done) => {
  done(null, user);
});

passport.deserializeUser((user: { id: string | number }, done) => {
  // 2 cas
  if (typeof user.id === "string") {
    // CAS 1 : humanId strategy
    user_table.findOne({ where: { humanid: user.id } }).then(async (user: UserModel | null) => {
      if (!user) {
        done(new Error("HumanID user not found!"), false);
        return;
      }

      const user_data = user.get();
      if (user_data.deleted_user) {
        done(null, false);
        return;
      }

      const { read, write, ff } = await archeogrid_client.users.userRightsFolders.query({
        branch: branchConfig,
        user_id: user_data.id,
      });

      done(null, { ...user_data, read, write, ff });
    });
  } else if (typeof user.id === "number") {
    // Cas 2 : local strategy
    user_table.findByPk(user.id).then(async (user: UserModel | null) => {
      if (!user) {
        done(new Error("Local user not found!"), null);
        return;
      }

      const user_data = user.get();

      const { read, write, ff } = await archeogrid_client.users.userRightsFolders.query({
        branch: branchConfig,
        user_id: user_data.id,
      });

      done(null, { ...user_data, read, write, ff });
    });
  }
});

app.use(myrootMiddleware);

// This middleware will check if user's cookie is still saved in browser and user is not set, then automatically log the user out.
// This usually happens when you stop your express server after login, your cookie still remains saved in the browser.
app.use((req, res, next) => {
  //if (req.cookies.user_sid && !req.user) {
  //    res.clearCookie('user_sid')
  //}
  //req.session.returnTo = req.originalUrl;
  if (req.cookies.i18n === undefined) {
    req.setLocale(defaultLanguage);
  } else {
    req.setLocale(req.cookies.i18n);
  }
  if (req.user) {
    res.locals.username = req.user.username;
    res.locals.user_status = req.user.user_status;
  }
  next();
});

app.use((req, res, next) => {
  res.locals.route = req.url;
  next();
});

// test sendMail func
//sendMail("test", "test de mail d'alerte.")

app.use(flashMiddleware);

app
  .use(favicon("favicon.ico"))

  .get("/", (req, res) => {
    res.locals.branch = branchConfig
    res.locals.myroot = "/";
    if (!req.cookies.i18n) {
      req.setLocale(defaultLanguage);
    } else {
      req.setLocale(req.cookies.i18n);
    }
    performRequest("/api/userSession", "GET", null, (data) => {
      res.render("home", {
        infouser: data,
        branch: branchConfig,
        layout: "layout_html5up",
      });
    });
  })

  .get("/changeLanguage/:language/:root?", (req, res) => {
    const new_language = req.params.language;
    if (i18n.getLocales().includes(new_language)) {
      res.cookie("i18n", req.params.language);
    }

    if (!req.params.root) {
      if (req.query.redirect) {
        res.redirect(req.query.redirect as string);
      } else {
        res.redirect("/");
      }
      return;
    }

    if (req.params.root.includes("project,") && !req.params.root.includes("edit"))
      res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("op,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("thesaurus,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("thesaurusp,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes(branchConfig.toUpperCase()+",")) res.redirect(`/${req.params.root.replace(/,/g, "/")}`);
    else if (req.params.root.includes("concept,")) res.redirect(`/${req.params.root.replace(/,/g, "/")}`);
    else if (req.params.root === "default") res.redirect("/");
    else if (req.params.root.includes("projectsList")) res.redirect("/projectsList");
    else if (req.params.root.includes("search")) res.redirect(req.query.redirect as string);
    else if (req.params.root.includes("3dicons")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("heritageAsset")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("collectionId")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("admin")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else res.redirect(`/${req.params.root}`);
  })

  .get("/fr/:root?", (req, res) => {
    res.redirect(`/changeLanguage/fr${req.params.root ? `/${req.params.root}` : ""}`);
  })

  .get("/en/:root?", (req, res) => {
    res.redirect(`/changeLanguage/en${req.params.root ? `/${req.params.root}` : ""}`);
  })

  .get("/fr,:root", (req, res) => {
    res.cookie("i18n", "fr");
    if (req.params.root.includes("project,") && !req.params.root.includes("edit"))
      res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("op,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("thesaurus,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("thesaurusp,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes(branchConfig.toUpperCase()+",")) res.redirect(`/${req.params.root.replace(/,/g, "/")}`);
    else if (req.params.root.includes("concept,")) res.redirect(`/${req.params.root.replace(/,/g, "/")}`);
    else if (req.params.root === "default") res.redirect("/");
    else if (req.params.root.includes("projectsList")) res.redirect("/projectsList");
    else if (req.params.root.includes("search")) res.redirect(req.query.redirect as string);
    else if (req.params.root.includes("3dicons")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("heritageAsset")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("collectionId")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("admin")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else res.redirect(`/${req.params.root}`);
  })

  .get("/en,:root", (req, res) => {
    res.cookie("i18n", "en");
    if (req.params.root.includes("project,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("op,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("thesaurus,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("thesaurusp,")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes(branchConfig.toUpperCase()+",")) res.redirect(`/${req.params.root.replace(/,/g, "/")}`);
    else if (req.params.root.includes("concept,")) res.redirect(`/${req.params.root.replace(/,/g, "/")}`);
    else if (req.params.root === "default") res.redirect("/");
    else if (req.params.root.includes("projectsList")) res.redirect("/projectsList");
    else if (req.params.root.includes("search")) res.redirect(req.query.redirect as string);
    else if (req.params.root.includes("3dicons")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("heritageAsset")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("collectionId")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else if (req.params.root.includes("admin")) res.redirect(`/${req.params.root.replace(",", "/")}`);
    else res.redirect(`/${req.params.root}`);
  });

app.get("/error", (req, res) => {
  const user = res.locals.username ? req.user : {};
  if (!res.locals.myroot) res.locals.myroot = "/";

  if (!res.locals.flash?.error) {
    res.redirect("/");
    return;
  }

  res.render("error", { user, redirectroot: req.session.root });
});

app.get("/success", (req, res) => {
  if (!res.locals.myroot) res.locals.myroot = "/";
  res.render("success", {
    root: req.session.root,
    user: req.user,
  });
});

app

  // données pour les tag
  .get("/tag,:root,:type,:itemId", (req: Request<{ root: Branch; type: string; itemId: string }>, res) => {
    performRequest(`/api/tag/${req.params.root},${req.params.type},${req.params.itemId}`, "GET", null, (data) => {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data));
    });
  })

  // données projects lights pour menuGauche
  .get("/dataProjects,:root", (req, res) => {
    req.session.myroot = "/projectsL";
    const dataget = {
      root: req.params.root,
    };
    performRequest("/api/projects", "GET", dataget, (data) => {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data));
    });
  })

  // données pour générer la div tabloid des projects
  .get(
    "/tabloidProject,:id,:root",
    asyncHandler(async (req: Request<{ id: string; root: Branch }>, res) => {
      const data = await req.fetchApi(`/api/projectFull/${req.params.root},${req.params.id},${res.locals.lang}`);
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data[0]?.project ?? []));
    }),
  )

  // données pour exploiter l'arbre d'un projet pour pft3d (ou extp3d ?)
  .get("/getTree/:projectId,:root", (req: Request<{ projectId: string; root: Branch }>, res) => {
    const root = req.params.root;
    performRequest(`/api/csstree/${req.params.projectId},${root}`, "GET", null, (data) => {
      res.setHeader("Content-Type", "application/json");
      res.send(data);
    });
  })

  // donnees pour ff à récupérer dans js
  .get(
    "/getff/:userId,:root",
    asyncHandler(async (req: Request<{ userId: string; root: Branch }>, res) => {
      const data = await req.fetchApi(`/api/userFobiddenFolders/${req.params.userId},${req.params.root}`);
      if (req.user) {
        const ffdata = data[0].forbidden_array;
        res.setHeader("Content-Type", "application/json");
        res.send(ffdata);
      } else {
        res.setHeader("Content-Type", "application/json");
        res.send("0");
      }
    }),
  )

  // access au folder ?
  // TODO : différencier en lecture et en ecriture
  // Pour le moment accès en lecture ou ecriture = 1
  .get(
    "/accessRights/:userId,:folderId,:root",
    (req: Request<{ userId: string; folderId: string; root: Branch }>, res) => {
      if (req.params.folderId === "0") {
        res.setHeader("Content-Type", "application/json");
        res.send({ rights: 0 });
      } else {
        if (req.user) {
          let data = {};
          // TODO : garantir que le req.user a toujours un tableau de write et de read !!
          // l'admin a droit à tout
          if (req.user.user_status === "admin") {
            res.setHeader("Content-Type", "application/json");
            res.send({ rights: 1 });
          }
          // il faut voir si c'est un user conservatoire : il n'a droit à rien sur les projets
          // TODO Verifier que c'est bien le cas dans la base via le server
          else if (req.user.conservatoire_user && req.params.root === branchConfig) {
            res.setHeader("Content-Type", "application/json");
            res.send({ rights: 0 });
          } else {
            // TODO : directement donner les drots read ou write depuis le tableau du req.user ?
            //performRequest('/api/userAccessFolder/' + req.params.userId + ',' + req.params.folderId + ',' + req.params.root, 'GET', null,
            //    function (data) => {
            if (req.user.write.includes(Number.parseInt(req.params.folderId))) data = { rights: 2 };
            else if (req.user.read.includes(Number.parseInt(req.params.folderId))) data = { rights: 1 };
            else data = { rights: 0 };
            res.setHeader("Content-Type", "application/json");
            res.send(data);
            //    })
          }
        } else {
          res.setHeader("Content-Type", "application/json");
          res.send({ rights: 0 });
        }
      }
    },
  )

  .get("/statusRights/:userId", (req, res) => {
    if (req.user) {
      res.setHeader("Content-Type", "application/json");
      res.send({ status: req.user.user_status });
    } else {
      res.setHeader("Content-Type", "application/json");
      res.send({ status: "notconnected" });
    }
  })

  .get(
    "/createProject,:projectName,:root,:site",
    (req: Request<{ projectName: string; root: string; site: string }>, res) => {
      let datako = {};
      if (req.user) {
        if (req.params.projectName) {
          const dataput = {
            projectName: req.params.projectName,
            root: req.params.root,
            site: req.params.site,
            user: req.user.id,
            user_status: req.user.user_status,
          };
          performRequest("/api/projects", "PUT", dataput, (data, code) => {
            if (code === 201) {
              performRequest(`/api/fsdirSynchFin/${req.params.root},${data[0].id}`, "GET", null, (dataUpdate) => {
                const dataok = {
                  name: req.params.projectName,
                  texte: "OK",
                  code: data,
                };
                // TODO : Par défaut à la création d'un projet, on ajoute les infos dans la table _folder_thesaurus_pactols
                //  pour pouvoir indexer / mettre des mot-clés des pactols
                // Seulement le thesaurus sujet, avec info nom et description
                const listThesau = ["sujet"];
                for (let li = 0; li < listThesau.length; li++) {
                  const dataput = {
                    thesaurus: listThesau[li],
                    orderThes: li + 1,
                    idThes: 0,
                    visible: 1,
                    en_name: "Subject",
                    fr_name: "Sujet",
                    fr_description: "Thesaurus Sujet des Pactols",
                    en_description: "Pactols Subject Thesaurus",
                  };
                  performRequest(
                    `/api/thesaurusProject/${req.params.root},${data[0].id},${i18n.getLocale(req)}`,
                    "PUT",
                    dataput,
                    (dataUpdate) => {
                      console.log(`ajout du thes ${listThesau[li]} pour le projet ${req.params.projectName}`);
                    },
                  );
                }
                // TODO : par défault, on utilise le modèle de données DublinCore pour les fichiers et les objets
                const model = archeogrid_client.metadata.createDefaultLinkModelProject.mutate({
                  branch: branchConfig,
                  folder_id: data[0].id,
                });
                res.setHeader("Content-Type", "application/json");
                res.send(dataok);
              });
            } else {
              datako = {
                name: req.params.projectName,
                texte: "INSERT IMPOSSIBLE",
                code: 0,
              };
              res.setHeader("Content-Type", "application/json");
              res.send(datako);
            }
          });
        } else {
          datako = {
            texte: "VOUS DEVEZ ENTRER UN NOM de projet valide",
            code: 0,
          };
          res.setHeader("Content-Type", "application/json");
          res.send(datako);
        }
      } else {
        datako = {
          texte: "VOUS DEVEZ ETRE CONNECTE POUR CREER UN PROJET",
          code: 0,
        };
        res.setHeader("Content-Type", "application/json");
        res.send(datako);
      }
    },
  )

  .get(
    "/createOverallProject,:projectname,:root,:site",
    (req: Request<{ projectname: string; root: string; site: string }>, res) => {
      let datako = {};
      if (req.user) {
        if (req.params.projectname) {
          const dataput = {
            projectName: req.params.projectname,
            root: req.params.root,
            site: req.params.site,
            user: req.user.id,
            user_status: req.user.user_status,
          };
          performRequest("/api/projectsOverall", "PUT", dataput, (data, code) => {
            console.log(code);
            console.log(data);
            if (code === 201) {
              console.log("201");
              performRequest(`/api/fsdirSynchFin/${req.params.root},${data[0].id}`, "GET", null, (dataUpdate) => {
                const dataok = {
                  name: req.params.projectname,
                  texte: "OK",
                  code: data,
                };
                res.setHeader("Content-Type", "application/json");
                res.send(dataok);
              });
            } else {
              datako = {
                name: req.params.projectname,
                texte: "INSERT IMPOSSIBLE",
                code: 0,
              };
              res.setHeader("Content-Type", "application/json");
              res.send(datako);
            }
          });
        } else {
          datako = {
            texte: "VOUS DEVEZ ENTRER UN NOM de projet valide",
            code: 0,
          };
          res.setHeader("Content-Type", "application/json");
          res.send(datako);
        }
      } else {
        datako = {
          texte: "VOUS DEVEZ ETRE CONNECTE POUR CREER UN PROJET",
          code: 0,
        };
        res.setHeader("Content-Type", "application/json");
        res.send(datako);
      }
    },
  )

  .get("/getRootProjects", (req, res) => {
    const dataGet = {
      order: "name",
    };
    performRequest("/api/rootFolders/"+branchConfig, "GET", dataGet, (rootfolders) => {
      res.setHeader("Content-Type", "application/json");
      res.send(rootfolders);
    });
  })

  .get("/getRootProjectsOverall", (req, res) => {
    const dataGet = {
      root: branchConfig,
    };
    performRequest("/api/projectsOverall", "GET", dataGet, (overallFolders) => {
      res.setHeader("Content-Type", "application/json");
      res.send(overallFolders);
    });
  })

  .post("/createLinkFolderOverall", (req, res) => {
    let rights = 0;
    if (req.user) {
      if (req.user.user_status === "admin") rights = 1;
    }
    const idOverallProject = req.body.idProjectOverall;
    const dataPatch = {
      idFolder: req.body.idfolderToLink,
    };
    performRequest(`/api/overallProject/${branchConfig},${req.body.idProjectOverall}`, "PATCH", dataPatch, (data, code) => {
      if (req.session) req.session.root = `op/${idOverallProject}`;
      performRequest(`/api/overallProject/${branchConfig},${idOverallProject}`, "GET", null, (projects) => {
        performRequest(`/api/folderFull/${idOverallProject},${branchConfig},${i18n.getLocale(req)}`, "GET", null, (project) => {
          performRequest("/api/projectsCard", "GET", null, (data) => {
            const resu = [];
            for (let i = 0; i < data.length; i++) {
              for (let j = 0; j < projects.length; j++) {
                if (data[i].id === projects[j].id) {
                  resu.push(data[i]);
                } else continue;
              }
            }
            res.redirect(`/op/${req.body.idProjectOverall}`);
          });
        });
      });
    });
  })

  .get(
    "/createVirtualFolder,:folderName,:idParent",
    (req: Request<{ folderName: string; idParent: string }>, res) => {
      let datako = {};
      if (req.user) {
        const user = req.user;
        if (req.params.folderName) {
          const dataput = {
            folderName: req.params.folderName,
            branche: branchConfig,
            idParent: req.params.idParent,
            user: req.user.id,
            user_status: req.user.user_status,
          };
          performRequest(
            `/api/virtualFolder/${branchConfig},${req.params.idParent}`,
            "PUT",
            dataput,
            (data, code) => {
              if (code === 201) {
                const dataok = {
                  name: req.params.folderName,
                  texte: "OK",
                  code: data,
                  status: user.user_status,
                };
                if (user.user_status === "user" || user.user_status === "scribe") {
                  const datap = {
                    idFolder: data.id,
                  };
                  performRequest(
                    `/api/userAccessWrite/${user.id},${branchConfig}`,
                    "PUT",
                    datap,
                    (data, code) => {
                      console.log("update rank, giver write rights and create virtual folder");
                      res.setHeader("Content-Type", "application/json");
                      res.send(dataok);
                    },
                  );
                } else {
                  console.log("update rank and create virtual folder");
                  res.setHeader("Content-Type", "application/json");
                  res.send(dataok);
                }
              } else {
                datako = {
                  name: req.params.folderName,
                  texte: req.__("insertImpossible"),
                  code: 0,
                };
                res.setHeader("Content-Type", "application/json");
                res.send(datako);
              }
            },
          );
        } else {
          datako = {
            texte: req.__("insertImpossible"),
            code: 0,
          };
          res.setHeader("Content-Type", "application/json");
          res.send(datako);
        }
      } else {
        datako = {
          texte: "VOUS DEVEZ ETRE CONNECTE POUR CREER UN Dossier Virtuel",
          code: 0,
        };
        res.setHeader("Content-Type", "application/json");
        res.send(datako);
      }
    },
  )
  .get("/deleteVirtualFolder/:idFolder", (req: Request<{ idFolder: string }>, res) => {
    let datako = {};
    if (req.user) {
      const branche = branchConfig;
      // 1/ est-ce que le folder est vide ?
      performRequest(`/api/rootFolder/${branchConfig},${req.params.idFolder}`, "GET", null, (rootData) => {
        const rootF = rootData.root;
        performRequest(`/api/virtualFolderLink/${branchConfig},0,${req.params.idFolder}`, "GET", null, (datanb) => {
          if (!Number.parseInt(datanb.nb_tot)) {
            // nb_tot = 0
            // 2/ OK on peut supprimer
            performRequest(`/api/virtualFolder/${branchConfig},${req.params.idFolder}`, "DELETE", null, (data, code) => {
              if (code === 200) {
                const dataok = {
                  texte: "OK Dossier Virtuel supprimé",
                  rootFolder: rootF,
                  code: 1,
                };
                console.log("virtual folder successfuly deleted");
                res.setHeader("Content-Type", "application/json");
                res.send(dataok);
              } else {
                datako = {
                  texte: "Impossible de supprimer le dossier virtuel",
                  rootFolder: rootF,
                  code: 0,
                };
                res.setHeader("Content-Type", "application/json");
                res.send(datako);
              }
            });
          } else {
            datako = {
              texte: "Dossier Virtuel non vide",
              rootFolder: rootF,
              code: 0,
            };
            res.setHeader("Content-Type", "application/json");
            res.send(datako);
          }
        });
      });
    } else {
      datako = {
        texte: "VOUS DEVEZ ETRE CONNECTE POUR CREER UN Dossier Virtuel",
        rootFolder: "",
        code: 0,
      };
      res.setHeader("Content-Type", "application/json");
      res.send(datako);
    }
  })

  .get(
    "/renameFolder/:idFolder,:oldName,:newName",
    (
      req: Request<{
        idFolder: string;
        oldName: string;
        newName: string;
      }>,
      res,
    ) => {
      const dataPatch = {
        name: req.params.newName,
      };
      performRequest(`/api/folderName/${branchConfig},${req.params.idFolder}`, "PATCH", dataPatch, (data, code) => {
        if (code === 200) {
          data.texte = "OK";
          res.setHeader("Content-Type", "application/json");
          res.send(data);
        } else {
          data.texte = "Impossible de mettre à jour le nom du dossier";
        }
      });
    },
  )

  .get(
    "/getVirtualFolders/:branche,:idUser,:idProject",
    (req: Request<{ branche: Branch; idUser: string; idProject: string }>, res) => {
      //TODO : est-ce que tout le monde voit les dossiers virtuels créés par les autres ???
      // Pour le moment tout le monde voit tous les dossiers virtuels (pas de distinction par le user)
      performRequest(`/api/virtualFolder/${req.params.branche},${req.params.idProject}`, "GET", null, (data) => {
        res.setHeader("Content-Type", "application/json");
        res.send(data);
      });
    },
  )
  .post("/addSelectionToVirtualFolder/:idUser,:idFolder", (req: Request<{ idUser: string; idFolder: string }>, res) => {
    const virtualFolder = Number.parseInt(req.body.virtualFolder);
    const selection = req.body.vitrineSelection ? JSON.parse(req.body.vitrineSelection) : req.body.selection;

    performRequest(
      `/api/virtualFolderLink/${branchConfig},${req.params.idUser},${virtualFolder}`,
      "PUT",
      { selection: selection },
      (data, code) => {
        if (code === 201) {
          res.redirect(`/projectv/${req.params.idFolder}`);
        } else {
          mess_front = req.__("message_addSelection");
          req.flash("error", mess_front);
        }
      },
    );
  })
  .post("/addSelectionToVirtualObject/:idUser,:idFolder", (req: Request<{ idUser: string; idFolder: string }>, res) => {
    const selectedObject = Number.parseInt(req.body.idObject);
    const selection = req.body.vitrineSelection ? JSON.parse(req.body.vitrineSelection) : req.body.selection;

    performRequest(
      `/api/virtualObjectLink/${branchConfig},${req.params.idUser},${selectedObject}`,
      "PUT",
      {
        selection: selection,
        idFolder: req.params.idFolder,
        idUser: req.params.idUser
      },
      (_, code) => {
        if (code === 201) {
          res.redirect(`/projectv/${req.params.idFolder}`);
        } else {
          mess_front = req.__("message_addSelection");
          req.flash("error", mess_front);
        }
      },
    );
  })

  .post(
    "/addSelectionToIllustrateObject/:idUser,:idFolder",
    (req: Request<{ idUser: string; idFolder: string }>, res) => {
      const selectedObject = Number.parseInt(req.body.idObject);
      const selection = req.body.vitrineSelection ? JSON.parse(req.body.vitrineSelection) : req.body.selection;

      const dataput = {
        idFile: req.body.idFile,
        selection: selection
      };
      performRequest(`/api/objectIllustrate/${branchConfig},${selectedObject}`, "PUT", dataput, (_, code) => {
        if (code === 201) {
          res.redirect(`/projectv/${req.params.idFolder}`);
        } else {
          mess_front = req.__("message_addSelection");
          req.flash("error", mess_front);
        }
      });
    },
  )

  .get(
    "/virtualFolderItemDelete/:idItem,:itemType,:idFolder",
    (
      req: Request<{
        idItem: string;
        itemType: string;
        idFolder: string;
      }>,
      res,
    ) => {
      console.log("virtualFolderItemDelete: ");
      // TODO Que fait-on si on delete un link d'un objet qui est dans le folder en question : il faut supprimer le lien ET l'objet en le signifiant au user?
      // Récupérer l'info sur le folder de creation de l'item :
      const user = res.locals.user as CustomUser;
      if (req.params.itemType === "object") {
        const dataget = {
          object_id: req.params.idItem,
          lang: i18n.getLocale(req),
          user_id: user.id,
        };
        performRequest("/api/object/"+branchConfig, "GET", dataget, (dataObj, codeO) => {
          if (dataObj.direct.id_folder === Number.parseInt(req.params.idFolder)) {
            // l'objet a été créé dans ce folder, si on le supprime ici, on le supprime complètement
            const datadelete = {
              idItem: req.params.idItem,
            };
            performRequest("/api/object/"+branchConfig, "DELETE", datadelete, (dataDel, codeDel) => {
              performRequest(
                `/api/virtualFolderLink/${branchConfig},0,${req.params.idFolder}`,
                "DELETE",
                req.params,
                (data, code) => {
                  res.setHeader("Content-Type", "application/json");
                  res.send(data);
                },
              );
            });
          } else {
            // l'objet a été créé ailleurs, on ne fait que supprimer le lien
            performRequest(
              `/api/virtualFolderLink/${branchConfig},0,${req.params.idFolder}`,
              "DELETE",
              req.params,
              (data, code) => {
                res.setHeader("Content-Type", "application/json");
                res.send(data);
              },
            );
          }
        });
      } else {
        // ce n'est pas un object pour le moment, on ne détruit que le lien
        performRequest(`/api/virtualFolderLink/${branchConfig},0,${req.params.idFolder}`, "DELETE", req.params, (data, code) => {
          res.setHeader("Content-Type", "application/json");
          res.send(data);
        });
      }
    },
  )

  .get(
    "/realFolderItemDelete/:idItem,:itemType,:idFolder",
    asyncHandler(
      async (
        req: Request<{
          idItem: string;
          itemType: string;
          idFolder: string;
        }>,
        res,
      ) => {
        if (!req.user) {
          res.send([]);
          return;
        }

        // TODO pour le moment on ne supprime que des items de type object
        // Pour supprimer un item reel : 1/ Vérifier que le user a le droit de supprimer l'item : lien user_item
        const dataU = await req.fetchApi(`/api/objectLinkUser/${branchConfig},${req.params.idItem}`);

        const ok = dataU.includes(req.user.id);
        if (!ok) {
          res.send([]);
          return;
        }

        // 2 détacher l'objet du user
        await req.fetchApi(
          `/api/objectLinkUser/${branchConfig},${req.params.idItem}`,
          { idUser: req.user.id.toString() },
          "DELETE",
        );
        // 3/ supprimer d'un coup les liens de l'objet et l'objet
        await req.fetchApi(`/api/object/${branchConfig}`, req.params, "DELETE");

        res.send([]);
      },
    ),
  )
  .get(
    "/virtualFolderContentDelete/:idVirtualFolder",
    (req: Request<{ idVirtualFolder: string }>, res) => {
      performRequest(`/api/virtualFolderContent/${branchConfig},${req.params.idVirtualFolder}`, "DELETE", null, (data) => {
        res.setHeader("Content-Type", "application/json");
        res.send(data);
      });
    },
  )
  .get("/getProjectSelection/:idUser,:idProject", (req: Request<{ idUser: string; idProject: string }>, res) => {
    console.log(`[Server Selection Get] Called with idUser=${req.params.idUser}, idProject=${req.params.idProject}`);
    performRequest(`/api/favoritesItems/${branchConfig},${req.params.idUser},${req.params.idProject}`, "GET", null, (data) => {
      console.log(`[Server Selection Get] Retrieved ${data ? data.length : 0} items from database`);
      if (data && data.length > 0) {
        console.log(`[Server Selection Get] First few items:`, data.slice(0, 3));
      }
      res.setHeader("Content-Type", "application/json");
      res.send(data);
      console.log(`[Server Selection Get] Response sent to client`);
    });
  })
  .post(
    "/registerSelection/:idUser,:idFolder",
    asyncHandler(async (req: Request<{ idUser: string; idFolder: string }>, res) => {
      console.log(`[Server Selection Register] Called with idUser=${req.params.idUser}, idFolder=${req.params.idFolder}`);

      const user_id = Number.parseInt(req.params.idUser);
      const items = req.body.selection;

      console.log(`[Server Selection Register] Items to save:`, items);

      if (!items) {
        console.error(`[Server Selection Register] Error: No items specified!`);
        throw new Error("No items specified!");
      }

      // on récupère les fav de la base pour les accumuler (inutile)
      /*const data = await archeogrid_client.files.getFavoriteItems.query({
        branch: branchConfig,
        user_id,
        folder_id: parseInt(req.params.idFolder),
      });
      const oldies = data.map(i => i.fav);

      const tada = [...new Set([...items, ...oldies])]
      //console.log(tada)

       */

      console.log(`[Server Selection Register] Deleting existing favorites for user ${user_id}`);
      const resull = await archeogrid_client.files.deleteFavoriteItem.mutate({
        branch: branchConfig,
        user_id: Number.parseInt(req.params.idUser),
      });

      if (resull) {
        console.log(`[Server Selection Register] Successfully deleted existing favorites`);
      } else {
        console.log(`[Server Selection Register] No existing favorites to delete or deletion failed`);
      }

      console.log(`[Server Selection Register] Creating ${items.length} new favorite items`);
      const result = await archeogrid_client.files.createfavoriteItem.mutate({
        branch: branchConfig,
        user_id,
        items,
      });

      if (result) {
        console.log(`[Server Selection Register] Successfully created favorites`);
        res.status(200).send(req.__("registerSelectionSuccess"));
        return;
      }

      console.error(`[Server Selection Register] Error creating favorites`);
      res.status(500);
    }),
  )
  .get(
    "/removeSelection/:idUser,:idFolder",
    async (req: Request<{ idUser: string; idFolder: string }>, res) => {
      console.log(`[Server Selection Remove] Called with idUser=${req.params.idUser}, idFolder=${req.params.idFolder}`);

      const user_id = Number.parseInt(req.params.idUser);
      console.log(`[Server Selection Remove] Deleting all favorites for user ${user_id}`);

      const result = await archeogrid_client.files.deleteFavoriteItem.mutate({
        branch: branchConfig,
        user_id: user_id,
      });

      if (result) {
        console.log(`[Server Selection Remove] Successfully deleted all favorites`);
        res.status(200).send(req.__("deleteSuccessful"));
        return;
      }

      console.error(`[Server Selection Remove] Error deleting favorites`);
      res.status(500);
    },
  )
  .get(
    "/getObjectsProject/:branche,:idUser,:idProject",
    (req: Request<{ branche: Branch; idProject: string }>, res) => {
      performRequest(`/api/objectsFolder/${req.params.branche},${req.params.idProject}`, "GET", null, (data) => {
        res.setHeader("Content-Type", "application/json");
        res.send(data);
      });
    },
  )

  .get(
    "/createObject,:name,:idFolder",
    (req: Request<{ name: string; idFolder: string }>, res) => {
      // récupérer le root_dir du projet
      // 1/ on récupère l'id
      if (req.user) {
        const user = req.user;
        performRequest(`/api/rootFolder/${branchConfig},${req.params.idFolder}`, "GET", null, (datarf) => {
          performRequest(`/api/folderSimple/${datarf.root},${branchConfig}`, "GET", null, (datan) => {
            const dataput = {
              version: "V0",
              name: encodeURI(req.params.name),
              idFolder: req.params.idFolder,
              rootDir: datan.folder_name, // au cas où on ait renommé le dossier maitre du projet, il faut continuer à utiliser le nom du repertoire folder_name
            };
            performRequest(`/api/object/${branchConfig}`, "PUT", dataput, (data, code) => {
              if (code === 201) {
                const datapuser = {
                  idUser: user.id,
                };
                performRequest(`/api/objectLinkUser/${branchConfig},${data.id}`, "PUT", datapuser, (datap, codep) => {
                  res.setHeader("Content-Type", "application/json");
                  res.send(data);
                });
              } else {
                req.flash("error", "Unable to create object");
                res.redirect("error");
              }
            });
          });
        });
      } else {
        mess_front = req.__("message_notlogged");
        res.send(mess_front);
      }
    },
  )

  // URL TO get all info about an item ( with QRCode access ?)
  .get(
    "/globalObject,:root,:idItem,:idFolder",
    asyncHandler(async (req: Request<{ root: Branch; idItem: string; idFolder: string }>, res) => {
      if (req.session) req.session.root = `globalObject,${req.params.root},${req.params.idItem},${req.params.idFolder}`; // pour que le login/out retourne sur cette page
      const object_id = Number.parseInt(req.params.idItem);
      const folder_id = Number.parseInt(req.params.idFolder);
      const branch = req.params.root;
      const user: CustomUser = res.locals.user;
      const type = "object";
      const language = res.locals.lang; // Par défault, on met la langue en français

      // Récupérer les droits
      // Admin users have access to all folders
      const Rrights = user && user.user_status === "admin" ? true : Boolean((await archeogrid_client.users.accessFolderGlobal.query({
          branch,
          folder_id,
          user_id: user.id,
        })).rights
      );

      // Récupérer le projet
      const mainFolder: Responses["folders"]["mainFolder"] & ({ image?: string | null } | null) =
        await archeogrid_client.folders.mainFolder.query({
          branch,
          language,
          folder_id,
        });

      if (!mainFolder?.mainfolder) throw new Error("Project not found!");

      if (branch === branchConfig) {
        console.log("On a reussi legalite de la branch ")
        // get image logo ?
        const project = await archeogrid_client.projects.projectsFull.query({
          branch: branch,
          language: res.locals.lang,
          project_id: Number.parseInt(mainFolder.mainfolder),
        });
        if (!project) throw new Error("Project does not exist!");

        mainFolder.image = project.image;
      }

      const dataTag = await req.fetchApi(`/api/tag/${branch},object,${object_id}`);
      const dataLicense = await req.fetchApi(`/api/licenseItem/${branch},${object_id},object,${language}`);

      if (!Rrights) {
        let modelview = (req.query.context === 'v') ? 'visionneusevitrine' : 'globalObj';
        let layout = (req.query.context === 'v') ? 'layout' : 'layout_noheader';

        res.render(modelview, {
          idObj: object_id,
          dataObject: "",
          tagThesmulti: "",
          viewer: "",
          idFile: "",
          user: user,
          root: req.params.root,
          folderId: req.params.idFolder,
          image: "",
          model: "",
          modelComplet: "",
          metadata: "",
          modelFile: "",
          rights: {read: false, write: false},
          thes: "",
          items: "",
          nbObjects: 0,
          nbFiles: 0,
          mainFolder: mainFolder,
          comments: "",
          tags: dataTag,
          license: dataLicense,
          layout: layout,
          viewerFormat,
          itemType: 'object',
        });
        return;
      }

      // Récupérer les commentaires
      const comments = await archeogrid_client.comments.getComments.query({
        branch,
        type,
        user_id: user.id,
        item_id: object_id,
      });

      // Récupérer les infos sur l'objet
      const dataobject = await req.fetchApi(`/api/objectId/${branch},${object_id}`);

      const file_id = dataobject.id_file_representative ?? 0;

      const image = await helpers.getImageDataFromFile(branch, dataobject.id_file_representative);

      // les models
      const model = await req.fetchApi(`/api/metadatamodelProject/${branch},${mainFolder.mainfolder},${language}`);

      const modellist = [];
      const modelComplet = [];

      // On récupère tous les modèles utilisés par le projet pour le type d'item montré : ici l'object
      for (const i in model) {
        if (model[i].metadata_type === type) {
          modellist.push(model[i].name);
          modelComplet.push({
            name: model[i].name,
            label: model[i].label,
            description: model[i].description,
          });
        }
      }
      // Tous les thesaurus utilisés par le projet
      const thes = await req.fetchApi(`/api/allThesaurusNameProject/${branch},${mainFolder.mainfolder},${language}`);

      const datathesPactols = await req.fetchApi(`/api/ThesaurusPactolsItem/${branchConfig},${type},${object_id}`);

      const datathesPactolsGeo = await req.fetchApi(`/api/ThesaurusPactolsGeoItem/${branchConfig},${type},${object_id}`);

      const datathesMaison = await req.fetchApi(`/api/ThesaurusMaisonItem/${branchConfig},${type},${object_id}`);

      // Toutes les métadonnées liées aux modèles de métadonnées utilisées
      const allmetadata = await archeogrid_client.metadata.allMetadata.query({
        branch,
        language,
        type,
        id: object_id,
      });

      for(let [name_model, meta_model] of Object.entries(allmetadata)){
        if(!meta_model) continue;
        for(let [index, meta] of Object.entries(meta_model)){
          if(["actor", "datation", "location"].includes(meta.status)){
            try{
              meta.value = JSON.parse(meta.value);
            }catch(e){
              console.log("Can't parse ${meta.status} value of the metadata ${meta.label} =>", meta.value);
            }
          }
        }
      }

      //tous les item de thesaurus multli (sans modèle de métadonnées forcément)
      const thesmulti = await req.fetchApi(`/api/ThesaurusMultiItem/${branch},${type},${object_id}`);

      // TODO :  Tous les items rattachés à cet object
      const itemdatafromObj = await req.fetchApi(`/api/itemsFromObject/${branch},${object_id}`);

      const itemsViewer = [];
      let nbObjects = 0;
      let nbFiles = 0;

      const objectViewer = await req.fetchApi(`/api/objectViewer/${branch},${object_id}`);

      // On récupère un path correspondant au fichier dont il existe un viewer. si ce fichier à un small, on s'en sert pour afficher
      // la miniature et pointer vers le viewer avec cette prévisu :
      const imageViewer = [];
      if (objectViewer) {
        let path = "";
        for (let i = 0; i < objectViewer.length; i++) {
          if (objectViewer[i].path) {
            path = objectViewer[i].path;
            imageViewer.push({
              value: objectViewer[i].value,
              id: objectViewer[i].id,
              folderId: objectViewer[i].id_folder,
              file: helpers.getFileName(objectViewer[i].path),
              srcImgThumb: helpers.setImageThumb(helpers.getFileName(objectViewer[i].path), objectViewer[i].path),
            });
          }
        }
      }
      const objectData = await req.fetchApi("/api/object/"+branchConfig, {
        object_id: req.params.idItem,
        lang: language,
        user_id: user.id.toString(),
      });
      const online = objectData.direct?.hash3d;

      const Wrights =
      (user.user_status === "user" &&
        user.write.includes(Number.parseInt(req.params.idFolder))) ||
      (user.user_status === "scribe" &&
        user.read.includes(Number.parseInt(req.params.idFolder))) ||
      user.user_status === "admin";

      // Changement de stratégie : on ne montre pas les métadonnées des items rattachés à un objet :
      // seulement son nom et un lien vers sa "page" !
      let modelview = (req.query.context === 'v') ? 'visionneusevitrine' : 'globalObj';
      let layout = (req.query.context === 'v') ? 'layout' : 'layout_noheader';

      // Create objects array for the vitrine template (only object-type items)
      const objects = itemsViewer.filter(item => item.type === 'object');
      
      res.render(modelview, {
        idObj: object_id,
        dataObject: dataobject,
        tagThesmulti: thesmulti.tagthes,
        online,
        viewer: imageViewer,
        idFile: file_id,
        user: user,
        root: req.params.root,
        folderId: req.params.idFolder,
        image,
        model: modellist,
        modelComplet: modelComplet,
        metadata: allmetadata,
        modelFile: "",
        rights: {read: Rrights, write: Wrights},
        thes: thes,
        tagPactols: datathesPactols,
        tagPactolsGeo: datathesPactolsGeo,
        tagMaison: datathesMaison,
        items: itemsViewer,
        objects: objects,
        nbObjects: nbObjects,
        nbFiles: nbFiles,
        mainFolder: mainFolder,
        comments: comments,
        tags: dataTag,
        license: dataLicense,
        layout: layout,
        itemType: 'object',
        viewerFormat,
      });
    }),
  )

  .get("/moreInfoImage/:idFile,:root", (req: Request<{ idFile: string; root: Branch }>, res) => {
    performRequest(`/api/moreInfoImage/${req.params.idFile},${req.params.root}`, "GET", null, (data) => {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data[0]));
    });
  })

  .get("/exifIptc/:root", (req, res) => {
    performRequest(`/api/IPTC_EXIFCode/${req.params.root},${i18n.getLocale(req)}`, "GET", null, (iptc_exif) => {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(iptc_exif));
    });
  });

app.get(
  "/keyword,:root,:type,:idItem,:idFolder",
  isLoggedIn,
  asyncHandler(async (req, res) => {
    // ajouter un mot-cle à un item
    // on propose tous les thesaurus (multi et simple ) utilisés dans le projet
    // sauf si  il y a un terme pour le simple (un seul terme autorisé)
    // recupérer le mainfolder
    const mainfolder = await req.fetchApi(
      `/api/mainFolder/${req.params.root},${req.params.idFolder},${res.locals.lang}`,
    );
    const virtual = mainfolder.folder_name ?? "virtual";
    // récupérer tous les thesaurus possibles pour l'indexation des mots-cles :
    // tables
    //     _folder_thesaurus_multi
    //     _folder_thesaurus
    //     _folder_thesaurus_pactols
    const thes = await req.fetchApi(
      `/api/thesaurusProject/${req.params.root},${mainfolder.mainfolder},${res.locals.lang}`,
    );
    const thesdataPactols = await req.fetchApi(`/api/thesaurusPactolsOrigin/${branchConfig},${mainfolder.mainfolder}`, {
      lng: res.locals.lang,
    });

    if (req.params.idItem !== "0") {
      // indexation d'un item simple

      // On récupére les metadata de type char et text si on fait un ajout à partir de métadonnées
      const In_metadataForKeywords = await archeogrid_client.metadata.InMetadataForKeywords.query({
        branch: branchConfig,
        id_item: parseInt(req.params.idItem),
        item_type: req.params.type,
        language: res.locals.lang
      });

      let Out_metadataForKeywords = await archeogrid_client.metadata.OutmetadataForKeywords.query({
        branch: branchConfig,
        item_type: req.params.type,
        language: res.locals.lang
      });

      const thesaurusByType = {
        simple: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'simple'}),
        multi: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'multi'}),
        pactols: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'pactols'})
      }

      if (req.params.type === "object") {
        // on récupère l'id de l'image repr et le nom de l'object
        const object = await req.fetchApi(`/api/objectId/${req.params.root},${req.params.idItem}`);
        res.render("addkeyword", {
          branch: branchConfig,
          thes,
          thesPactols: thesdataPactols,
          item: object,
          mainFolder: mainfolder,
          idItem: req.params.idItem,
          type: req.params.type,
          idFolder: req.params.idFolder,
          root: req.params.root,
          virtual,
          In_metadataForKeywords,
          Out_metadataForKeywords,
          thesaurusByType
        });
      } else if(req.params.type === "unico") {
        const unico = await archeogrid_client.unico.getUnico.query({ branch: branchConfig, id: parseInt(req.params.idItem) });
        res.render("addkeyword", {
          branch: branchConfig,
          thes,
          thesPactols: thesdataPactols,
          item: unico,
          mainFolder: mainfolder,
          idItem: req.params.idItem,
          type: req.params.type,
          idFolder: req.params.idFolder,
          root: req.params.root,
          virtual,
          In_metadataForKeywords,
          Out_metadataForKeywords,
          thesaurusByType
        });
      }else{
        // on récupère le nom du fichier ? ça peut être plus parlant !
        const file = await req.fetchApi(`/api/prepareImage/${req.params.idItem},${req.params.root}`);
        res.render("addkeyword", {
          branch: branchConfig,
          thes: thes,
          thesPactols: thesdataPactols,
          item: file,
          mainFolder: mainfolder,
          idItem: req.params.idItem,
          type: req.params.type,
          idFolder: req.params.idFolder,
          root: req.params.root,
          folder: [],
          virtual,
          In_metadataForKeywords,
          Out_metadataForKeywords,
          thesaurusByType
        });
      }
    } else {
      // indexation mot-clé de tout le répertoire
      const folderInfo = await req.fetchApi(`/api/folderSimple/${req.params.idFolder},${req.params.root}`);

      const In_metadataForKeywords = await archeogrid_client.metadata.InMetadataForKeywordsIndexMulti.query({
        branch: branchConfig,
        id_folder: parseInt(req.params.idFolder),
        language: res.locals.lang
      });

      const Out_metadataForKeywords = await archeogrid_client.metadata.OutmetadataForKeywords.query({
        branch: branchConfig,
        language: res.locals.lang
      });

      const thesaurusByType = {
        simple: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'simple'}),
        multi: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'multi'}),
        pactols: await archeogrid_client.thesaurus.getSubThesaurus.query({branch: branchConfig, thesaurus: 'pactols'})
      }

      res.render("addkeyword", {
        branch: branchConfig,
        thes,
        thesPactols: thesdataPactols,
        mainFolder: mainfolder,
        idItem: req.params.idItem,
        type: "none",
        idFolder: req.params.idFolder,
        root: req.params.root,
        item: folderInfo,
        virtual,
        In_metadataForKeywords,
        Out_metadataForKeywords,
        thesaurusByType
      });
    }
  }),
);

app.route("/addProjectToScribe,:root,:idUser").post((req: Request<{ root: Branch; idUser: string }>, res) => {
  // lier le scribe au projet avec la table de lien qui sert pour l'administration dans le tableau de bord
  performRequest(
    `/api/scribeUserLinkProject/${req.params.root},${req.params.idUser},${req.body.id}`,
    "PUT",
    null,
    (data, code) => {
      if (code === 201) {
        // Pour tous les sous répertoires du projet sélectionné, donner les droits au user
        performRequest(
          `/api/scribeAccessAdminlistSubFolders/${req.params.root},${req.body.id},${req.params.idUser}`,
          "GET",
          null,
          (dataList, code) => {
            res.send(JSON.stringify(dataList.length));
          },
        );
      } // code = 500
      else {
        console.log("bad insert");
        res.send("0");
      }
    },
  );
});

app.get("/widgetOT", (req, res) => {
  res.render("widgetOT", {
    layout: "layout_widget.ejs",
  });
});

app.route("/checkeywordOpentheso,:root,:type,:idItem,:idFolder,:genre")
    .post(
    isLoggedIn,
    asyncHandler(
        async (
            req: Request<{
              root: Branch;
              type: "object" | "file" | "folder" | "unico";
              idItem: string;
              idFolder: string;
              genre: string;
            }>,
            res,
        ) => {
        const branch = req.params.root;
        const thesaurus = req.body.thesaurus;
        const thesaurus_type = req.params.genre;
        const id_thes  = Number.parseInt(req.body.id_thes);
        const name = req.body.value;
        const data = await archeogrid_client.thesaurus.getIdThesaurusByNameAndId.query({
            branch,
            thesaurus,
            name,
            thesaurus_type,
            id_thes
          });
          let resu = req.body

          if (data) {
            // on peut indexer avec la route existante !
            resu['ok'] = '1'
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(resu));
          } else { // data est null donc il n'y a pas déjà ce concept il faut le créer
            resu['ok'] = '0'
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(resu));
          }
          return;
        }
    ),
);

app
  .route("/addkeyword,:root,:type,:idItem,:idFolder,:genre")
  .get(
    isLoggedIn,
    asyncHandler(
      async (
        req: Request<{
          root: Branch;
          type: string;
          idItem: string;
          idFolder: string;
          genre: string;
        }>,
        res,
      ) => {
        // récupérer le nom de l'item et en fonction , créer un lien pour retourner à l'item
        const data = await archeogrid_client.folders.getFull.query({
          branch: req.params.root,
          language: res.locals.lang,
          folder_id: Number.parseInt(req.params.idFolder),
        });
        if (!data) {
          req.flash("error", "Folder not found!");
          return;
        }

        const virtual = data.folder_name ?? "virtual";

        res.render("addkeyword", {
          branch: branchConfig,
          root: req.params.root,
          name: data.name,
          type: req.params.type,
          item: req.params.idItem,
          virtual,
        });
      },
    ),
  )
  .post(
    isLoggedIn,
    asyncHandler(
      async (
        req: Request<{
          root: Branch;
          type: "object" | "file" | "folder" | "unico";
          idItem: string;
          idFolder: string;
          genre: string;
        }>,
        res,
      ) => {
        const item = Number.parseInt(req.params.idItem);
        const genre = req.params.genre;
        const user = res.locals.user;
        // TODO : ET les droits ??

        const branch = req.params.root;
        const thesaurus = req.body.thesaurus;
        const type = req.params.type;
        const item_id = Number.parseInt(req.params.idItem);
        const id = req.body.id;

        if (id.indexOf('opentheso') !== -1) {
          // on a un tag venant d'un thesaurus extérieur
          // etape 1 : est-ce qu'il existe déjà dans le thesaurus en local
          // si non, on l'ajoute ici et maintenant
          // etape 2 : tagguer avec !
            console.log('id body : '+id )
        } else {
          //
        }

        if (item) {
          const dataput = {
            root: req.params.root,
            id: req.body.id,
            thesaurus: req.body.thesaurus,
            item: req.params.idItem,
            type: req.params.type,
            id_user: user.id,
          };
          if (genre === "multi") {
            const data = await archeogrid_client.thesaurus.addMulti.mutate({
              branch,
              thesaurus,
              id,
              type,
              item_id,
              user_id: user.id,
              qualifier: req.body.qualifier,
              id_metadata: parseInt(req.body.id_metadata)
            });
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(data));
            return;
          }
          if (genre === "thesaurus") {
            const data =await archeogrid_client.thesaurus.addSimple.mutate({
              branch: req.params.root,
              user_id: user.id,
              thesaurus: req.body.thesaurus,
              item_id: parseInt(req.params.idItem),
              type: req.params.type,
              id_thesaurus: parseInt(req.body.id.split("_")[0]),
              id_thes_thesaurus: parseInt(req.body.id.split("_")[1]),
              qualifier: req.body.qualifier,
              id_metadata: req.body.id_metadata
            })

            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(data));
            return;
          } else if (genre === "geopactols") {
            const value = encodeURI(req.body.value);
            performRequest(`/api/thesaurusPactolsGeo/${req.params.root},${value}`, "PUT", dataput, (data, code) => {
              //res.setHeader('Content-Type', 'application/json');
              if (code === 201) {
                // retourne '0' si le mot clé est déjp indexé
                // retourne '1' si on a bien ajouter le mot clé
                res.setHeader("Content-Type", "application/json");
                res.send(JSON.stringify(data));
                return;
              }

              console.log("bad insert in thesaurus pactols geo ");
              res.sendStatus(code);
              return;
            });
          } else {
            // PACTOLSV2 : un seul thesaurus du nom de sujet pour le moment
            const data = await archeogrid_client.thesaurus.addThesaurusPactolsItem.mutate({
              branch: req.params.root,
              item_type: req.params.type,
              id_item: parseInt(req.params.idItem),
              thesaurus: req.body.thesaurus,
              id_user: user.id,
              id_pactols: req.body.id,
              qualifier: req.body.qualifier,
              id_metadata: req.body.id_metadata
            });
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(data));
            return;
          }
        } else {
          // Ajout multiple de mot-clés sur tous les items d'un répertoire
          // il faut renseigner tous les item du folder
          const item = await req.fetchApi(`/api/explore/${req.params.idFolder},${req.params.root}`, {
            userId: user.id,
          });

          let error = 0, ind = 0, resu = {};
          for (let f = 0; f < item.length; f++) {
            const dataputOLD = {
              root: req.params.root,
              id: req.body.id,
              //thesaurus: req.body.thesaurus,
              item: item[f].id,
              type: item[f].item_type,
              id_user: user.id,
            };
            const dataput = {
              root: req.params.root,
              id: req.body.id,
              thesaurus: req.body.thesaurus,
              item: item[f].id,
              type: item[f].item_type,
              id_user: user.id,
            };
            if (genre === "multi") {
              const data = await archeogrid_client.thesaurus.addMulti.mutate({
                branch,
                thesaurus,
                id,
                type : item[f].item_type,
                item_id: parseInt(item[f].id),
                user_id: user.id,
                qualifier: req.body.qualifier,
                id_metadata: parseInt(req.body.id_metadata)
              });
              if (!data) { error++ } else {
                ind++
              }
              if (f === item.length - 1) {
                    // dernier item on renvoit
                    resu['nberror'] = error;
                    resu['success'] = ind;
                    if (error) {
                      console.log('bad insert for nb items : ', error)
                      res.setHeader("Content-Type", "application/json");
                      res.send(JSON.stringify(resu));
                    } else {
                      res.setHeader("Content-Type", "application/json");
                      res.send(JSON.stringify(resu));
                    }
              }

            } else if (genre === "thesaurus") {
              const data = await archeogrid_client.thesaurus.addSimple.mutate({
                branch: req.params.root,
                user_id: user.id,
                thesaurus: req.body.thesaurus,
                item_id: parseInt(item[f].id),
                type: item[f].item_type,
                id_thesaurus: parseInt(req.body.id.split("_")[0]),
                id_thes_thesaurus: parseInt(req.body.id.split("_")[1]),
                qualifier: req.body.qualifier,
                id_metadata: req.body.id_metadata
              })
              if (!data) { error++ } else {
                ind++
              }
              if (f === item.length - 1) {
                    // dernier item on renvoit
                    resu['nberror'] = error;
                    if (error) {
                      console.log('bad insert for nb items : ', error)
                      res.setHeader("Content-Type", "application/json");
                      res.send(JSON.stringify(resu));
                    } else {
                      res.setHeader("Content-Type", "application/json");
                      res.send(JSON.stringify(resu));
                    }
                  }
            } else if (genre === "multiOLD") {
              performRequest(
                `/api/thesaurusMulti/${req.params.root},${req.body.thesaurus}`,
                "PUT",
                dataput,
                (data, code) => {
                  //console.log(code)
                  if (code !== 201) {
                    error = 1;
                  }
                  if (f === item.length - 1) {
                    // dernier item on renvoit
                    if (error) {
                      //console.log('bad insert')
                      res.sendStatus(code);
                    } else {
                      res.setHeader("Content-Type", "application/json");
                      res.send(JSON.stringify([]));
                    }
                  }
                },
              );
            } else if (genre === "geopactols") {
              const value = encodeURI(req.body.value);
              performRequest(`/api/thesaurusPactolsGeo/${req.params.root},${value}`, "PUT", dataput, (data, code) => {
                if (code !== 201) {
                  error = 1;
                }
                if (f === item.length - 1) {
                  // dernier item on renvoit
                  if (error) {
                    res.sendStatus(code);
                  } else {
                    res.setHeader("Content-Type", "application/json");
                    res.send(JSON.stringify([]));
                  }
                }
              });
            } else {
              // PACTOLSV2 : toujours le même thesaurus sujet
              performRequest(`/api/thesaurusPactols/${req.params.root},sujet`, "PUT", dataput, (data, code) => {
                if (code !== 201) {
                  error = 1;
                }
                if (f === item.length - 1) {
                  // dernier item on renvoit
                  if (error) {
                    //console.log('bad insert')
                    res.sendStatus(code);
                  } else {
                    res.setHeader("Content-Type", "application/json");
                    res.send(JSON.stringify([]));
                  }
                }
              });
            }
          }
        }
      },
    ),
  );

app
  .get(
    "/addkeywordFromMetadata", // ?branch=...&idItem=...&itemType=...&inMetadataId=...&delimiter=...&thesaurusCollection=...&thesaurusType=...&?outMetadataId=...&createTags=...&?csv_metadata_id=...
    isLoggedIn,
    asyncHandler(
      async (req,res) => {

      const branch = req.query.branch as Branch;
      const id_item = parseInt(req.query.idItem as string);
      const item_type = req.query.itemType as 'file' | 'object' | 'folder';
      const id_folder = parseInt(req.query.idFolder as string);
      const csv_metadata_id = parseInt(req.query.csv_metadata_id as string);

      let items = [id_item];
      // Indexation multiple
      if(id_folder && id_item === 0 && isNaN(csv_metadata_id)){
        items = await archeogrid_client.folders.getFolderItems.query({
                        branch: branch, 
                        folder_id: id_folder, 
                        depth: 0
                }).then(result => result.map((f: {items: {id: number}[]}) => f.items.map((i: {id: number}) => parseInt(i.id))).flat());
      }else if(!isNaN(csv_metadata_id)){
        items = await archeogrid_client.csv.getCSVMetadataItems.query({ csv_metadata_id }).then(result => result.map((f: {id_item: number, item_type: string}) => parseInt(f.id_item)));
      }

      if(items.length === 0 || items.every((item) => isNaN(item) || !item)) {
        console.error(`Error addKeywordFromMetadata : items is empty or contains invalid items`);
        res.sendStatus(500);
        return;
      }

      const in_metadata_id = parseInt(req.query.inMetadataId as string);
      const delimiter = req.query.delimiter as string;
      const thesaurus_collection = req.query.thesaurusCollection as string;
      const thesaurus_type = req.query.thesaurusType as string;
      const should_create_tags = (req.query.createTags as string)=== "true";

      let datapost: any = {
        branch,
        item_type,
        id_user: req.user!.id,
        in_metadata_id,
        delimiter,
        thesaurus_collection,
        thesaurus_type,
        should_create_tags
      };

      const out_metadata_id = parseInt(req.query.outMetadataId as string);
      if(!isNaN(out_metadata_id) && out_metadata_id !== -1){ 
        datapost['out_metadata_id'] = out_metadata_id;
      }

      const add_promises = items.map((id_item) => {
        return archeogrid_client.thesaurus.addKeywordsFromMetadataPassport.mutate({id_item, ...datapost}).then(result => {
                return {id_item, ...result}
              });
      });

      Promise.all(add_promises).then((result)=>{ // result = {status = 'ok' | 'partial', message, foundThes, foundFreeTags, createdTags, notFoundTags} | {status = 'ko', message}
        res.status(200);
        res.send(JSON.stringify(result));
      }).catch((err) => {
        console.log(err)
        res.sendStatus(500);  
      })
    }) 
  );

// pour les tags des pactols
// http://www.mondomaine.fr/concept/##value##
// on a /concept/mobilier?rootF=5369&type=pactols&idThes=15683&thesaurus=sujet
app.get(
  "/concept/:value",
  asyncHandler(
    async (
      req: Request<
        { value: string },
        unknown,
        unknown,
        {
          rootF: string;
          idThes: string;
          thesaurus: string;
          type: "pactols" | "pactolsgeo" | "multi" | "simple";
        }
      >,
      res,
    ) => {
      // afficher tous les item qui ont ce concept en mot clef
      // traiter le cas où value contient des espaces
      // TODO : Attention, par defaut cette route va chercher dans le thesaurus multi => maintenant aussi le thes pactols et le thes pactols_geo !
      // TODO : utiliser le type de thesaurus pour déclencher des routes différentes : soit pactols, soit multi ?
      const value = encodeURI(req.params.value);
      const rootFolder = req.query.rootF;
      const type = req.query.type;
      const user = res.locals.user as CustomUser;
      // on explore les concepts du thesaurus multi pour les projets
      const projectName = await req.fetchApi(`/api/metadataValueFromCode/${branchConfig},${rootFolder},p.title`);

      let dataConcept: unknown;
      if (type === "pactols") {
        dataConcept = await req.fetchApi(
          `/api/ThesItemFromPactolsCodeThesaurus/${branchConfig},${req.query.idThes},${req.query.thesaurus}`,
        );
      } else if (type === "pactolsgeo") {
        dataConcept = await req.fetchApi(`/api/ThesItemFromPactolsGeoCodeThesaurus/${branchConfig},${req.query.idThes}`);
      } else if (type === "multi") {
        dataConcept = await req.fetchApi(
          `/api/ThesItemFromMultiCodeThesaurus/${branchConfig},${req.query.idThes},${req.query.thesaurus}`,
        );
      } else if (type === "simple") {
        dataConcept = await req.fetchApi(
          `/api/ThesItemFromSimpleCodeThesaurus/${branchConfig},${req.query.idThes},${req.query.thesaurus}`,
        );
      }
      let ppname =''
      if (projectName.length) {
        ppname = projectName[0]['value'][0]
      } else {
        ppname = ''
      }
      const view_data = {
        conceptName: req.params.value,
        value: req.params.value,
        projectId: rootFolder,
        projectName: ppname,
        data: dataConcept,
        thesaurus: req.query.thesaurus,
        rootF: rootFolder,
        idThes: req.query.idThes,
        type: type,
        branch: branchConfig,
        lng: res.locals.lang, // Added lng from res.locals
        user: user, // Ensure user is passed to the template
      };

      // Always render the new unified view
      res.render("conceptExploreVitrine", view_data);
    },
  ),
);

app.get(
  "/conceptName/:value",
  asyncHandler(async (req, res) => {
    // UTILISEE PAR L'API OPENTHESO : rediriger vers la page de recherche de l'id du concept du thesaurus principal d'OpenTheso Frollo
    // TODO : envisager une page de recherche élargie à tous les projets ? et rediriger le concept OpenTheso dessus ?
    const value = encodeURI(req.params.value);
    let urisearch = "";

    const branch = branchConfig;
    const thesaurus = req.params.value;

    // Si plusieurs thesaurus trouvé, on prend nd_th13
    const data = await archeogrid_client.thesaurus.getMultiFromName.query({
      branch,
      thesaurus,
    });
    const id_thes = data.find((t) => t.thesaurus === "nd_th13")?.id_thes ?? 0;
    // donc on établi la base de recherche sur le projet 5535 soit l'id de Notre-Dame ...
    urisearch = `/search/5535?search={"multi"%3A{"nd_th13"%3A[[${id_thes}]]}}`;
    console.log(urisearch);
    res.redirect(urisearch);
  }),
);

//http://mondomaine.fr/concept/##conceptId##/total
app.get("/concept/:conceptId/total", (req, res) => {
  // afficher pour un concept le nb de fois où ce concept est présent/ a fait l'objet d'un tag
  const id_thes = req.params.conceptId;
  const user = res.locals.user as CustomUser;
  performRequest(`/api/exploreConceptThesTotal/${branchConfig},${id_thes},multi`, "GET", null, (data) => {
    res.render("conceptTot", {
      data: data,
      user: user,
      idConcept: req.params.conceptId,
      branch: branchConfig,
    });
  });
});

//http://mondomaine.fr/concept/##conceptId## {"count": 14}
// UTILISE PAR OPENTHESO WEBSERVICE
app.get("/concept/total/:conceptId", (req, res) => {
  // rendre en json pour le concept id,  le nb de fois où ce concept est présent/ a fait l'objet d'un tag
  const id_thes = req.params.conceptId;
  performRequest(`/api/exploreConceptThesTotal/${branchConfig},${id_thes},multi`, "GET", null, (data) => {
    const result = { count: Number.parseInt(data.nb_item) };
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(result));
  });
});

app.get(
  "/exploreThes,:root,:thesaurus,:idthesaurus",
  (req: Request<{ root: Branch; thesaurus: string; idthesaurus: string }>, res) => {
    let pathFile = "";
    let file = "";
    performRequest(
      `/api/explorethes/${req.params.root},${req.params.thesaurus},${req.params.idthesaurus}`,
      "GET",
      null,
      (data) => {
        let imgPrincipale = [];
        for (const i in data) {
          imgPrincipale = data[i];
          if (data[i].path) {
            const pathFile = data[i].path;
            file = helpers.getFileName(pathFile);
            imgPrincipale.srcImgThumb = helpers.setImageThumb(file, pathFile);
            imgPrincipale.srcImgSmall = helpers.setImageSmall(file, pathFile);
            imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
          } else {
            pathFile = icone3d;
            file = helpers.getFileName(pathFile);
            imgPrincipale.srcImgThumb = helpers.setImageHd(file, pathFile);
            imgPrincipale.srcImgSmall = helpers.setImageHd(file, pathFile);
            imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
          }
          data[i].path = "";
        }
        const result = Object.assign({}, { data_length: data.length }, data);
        res.setHeader("Content-Type", "application/json");
        res.send(JSON.stringify(result));
      },
    );
  },
);

app
  .get("/explore,:idFolder", (req: Request<{ idFolder: string }>, res) => {
    // TODO : donner l'information ici si le user a le droit d'indexer ou pas ?
    // obsolete ? comme les functions allChecked et  allUnChecked ??
    let rightW = 0;
    if (
      req.user &&
      (req.user.user_status === "admin" ||
        (req.user.user_status === "scribe" && req.user.read.includes(Number.parseInt(req.params.idFolder))) ||
        (req.user.user_status === "user" && req.user.write.includes(Number.parseInt(req.params.idFolder))))
    )
      rightW = 1;

    performRequest(
      `/api/folderFull/${req.params.idFolder},${branchConfig},${i18n.getLocale(req)}`,
      "GET",
      null,
      (folder) => {
        const lefolder = folder[0];
        performRequest(
          `/api/allmetadata/${req.params.idFolder},folder,${branchConfig},${i18n.getLocale(req)}`,
          "GET",
          null,
          (allmetadata) => {
            performRequest(`/api/explore/${req.params.idFolder},${branchConfig}`, "GET", null, (data) => {
              for (const i in data) {
                data[i].path = "";
              }
              const result = Object.assign({}, { data_length: data.length }, data, allmetadata[0], lefolder, {
                rightW: rightW,
              });
              //console.log(result)
              res.setHeader("Content-Type", "application/json");
              res.send(JSON.stringify(result));
            });
          },
        );
      },
    );
  })

  .get("/exploreObj/:root,:idFolder", (req: Request<{ root: Branch; idFolder: string }>, res) => {
    let pathFile = "";
    let file = "";
    performRequest(
      `/api/folderFull/${req.params.idFolder},${req.params.root},${i18n.getLocale(req)}`,
      "GET",
      null,
      (folder) => {
        const lefolder = folder[0];
        performRequest(
          `/api/allmetadata/${req.params.idFolder},folder,${req.params.root},${i18n.getLocale(req)}`,
          "GET",
          null,
          (allmetadata) => {
            performRequest(
              `/api/exploreObj/${req.params.root},${req.params.idFolder},${i18n.getLocale(req)}`,
              "GET",
              null,
              (data) => {
                //console.log(data)
                let imgPrincipale = [];
                for (const i in data) {
                  imgPrincipale = data[i];

                  if (data[i].path) {
                    pathFile = data[i].path;
                    file = helpers.getFileName(pathFile);
                    imgPrincipale.srcImgThumb = helpers.setImageThumb(file, pathFile);
                    imgPrincipale.srcImgSmall = helpers.setImageSmall(file, pathFile);
                    imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
                  } else {
                    pathFile = icone3d;
                    file = helpers.getFileName(pathFile);
                    imgPrincipale.srcImgThumb = helpers.setImageHd(file, pathFile);
                    imgPrincipale.srcImgSmall = helpers.setImageHd(file, pathFile);
                    imgPrincipale.srcImgHd = helpers.setImageHd(file, pathFile);
                  }
                  data[i].path = "";
                }
                const result = Object.assign({}, { data_length: data.length }, data, allmetadata[0], lefolder);
                //console.log(result)
                res.setHeader("Content-Type", "application/json");
                res.send(JSON.stringify(result));
              },
            );
          },
        );
      },
    );
  })

  /**
   *
   *    ____                                     _
   *  /  ___|___  _ __ ___  _ __ ___   ___ _ __ | |_ ___
   * | |   / _ \| '_ ` _ \| '_ ` _ \ / _ \ '_ \| __/ __|
   * | |__| (_) | | | | | | | | | | |  __/ | | | |_\__ \
   * \____\___/|_| |_| |_|_| |_| |_|\___|_| |_|\__|___/
   *
   *
   */

  .get("/exploreComments/:branche,:idFolder", (req: Request<{ branche: Branch; idFolder: string }>, res) => {
    // TODO : donner l'information ici si le user a le droit de valider ou pas un commentaire ?
    const user = req.user;
    const rightW = Boolean(user);

    performRequest(`/api/exploreCommentedItem/${req.params.branche},${req.params.idFolder}`, "GET", null, (data) => {
      for (const i in data) {
        data[i].path = "";
      }
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data));
    });
  })

  .get(
    "/patchComment/:branche,:idMainFolder",
    (req: Request<{ branche: Branch; idMainFolder: string }, unknown, unknown, Record<string, string>>, res) => {
      const idMainFolder = Number.parseInt(req.params.idMainFolder);
      const branche = req.params.branche;
      const dataPatch = req.query;

      if (req.user) {
        if (req.user.user_status === "admin" || req.user.user_status === "scribe") {
          performRequest(`/api/comment/${branche},${idMainFolder},${req.user.id}`, "PATCH", dataPatch, (data, code) => {
            if (code === 200) {
              res.setHeader("Content-Type", "application/json");
              res.send(JSON.stringify(["ok"]));
            } else {
              req.flash("error", "bad patch");
            }
          });
        } else {
          mess_front = req.__("message_norightstopath");
          req.flash("error", mess_front);
        }
      } else {
        req.flash("error", "Not connected");
      }
    },
  )

  .get(
    "/deleteComment/:branche,:idMainFolder",
    (req: Request<{ branche: Branch; idMainFolder: string }, unknown, unknown, Record<string, string>>, res) => {
      const idMainFolder = Number.parseInt(req.params.idMainFolder);
      const branche = req.params.branche;
      const dataDel = req.query;

      if (req.user) {
        // un user et un guest peuvent aussi supprimer leurs propres commentaires
        //if (req.user.user_status === 'admin' || req.user.user_status === 'scribe'  || req.user.user_status === 'user'   ) {
        performRequest(`/api/comment/${branche},${idMainFolder},${req.user.id}`, "DELETE", dataDel, (data, code) => {
          if (code === 200) {
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify(["ok"]));
          } else {
            mess_front = req.__("message_errordelete");
            req.flash("error", mess_front);
          }
        });
      } else {
        req.flash("error", "Not connected");
      }
    },
  )

  .get("/prepareImage/:idFile,:root", (req, res) => {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify("oups"));
  })

  .get("/depotfileupload,:dirdepot,:idFolder", (req: Request<{ dirdepot: string; idFolder: string }>, res) => {
    const user = req.user;

    //todo : ajouter le répertoire de l'entité
    res.render("depotfileupload", {
      user: user,
      root: "conservatoire3d",
      dirdepot: req.params.dirdepot,
      files: "",
      idFolder: req.params.idFolder,
    });
  })

  .post("/depotfileupload,:dirdepot,:idFolder", (req: Request<{ dirdepot: string; idFolder: string }>, res) => {
    // TODO : Attention ajouter le répertoire de l'entité
    const rootDir = req.params.dirdepot;
    const idFolder = req.params.idFolder;
    const dir = `../web-archeogrid/conservatoire_data/${rootDir}/`;

    // raz tableau filenamesupload
    filenamesUpload = [];

    const uploadPath = path.join(__dirname, dir);

    fs.ensureDir(uploadPath);

    req.pipe(req.busboy); // Pipe it trough busboy

    req.busboy.on("file", (fieldname, file, { filename }) => {
      console.log(`Upload of '${filename}' started`);

      // Create a write stream of the new file
      const fstream = fs.createWriteStream(path.join(uploadPath, filename));
      // Pipe it trough
      file.pipe(fstream);

      // On finish of the upload
      fstream.on("close", () => {
        console.log(`Upload of '${filename}' finished`);
        filenamesUpload.push({
          type: "folder",
          name: filename,
          idFolder: Number.parseInt(idFolder),
          dir: dir,
        });
      });
    });
    req.busboy.on("finish", () => {
      // Synchroniser chaque file avec l'object (insert into corpus_file_object (id_file, id_object) ...
      performRequest(`/api/fsdirSynch/conservatoire3d,${idFolder},0,3`, "GET", null, (dataS) => {
        if (!res.locals.myroot) res.locals.myroot = `/depotfileuploadend,${req.params.dirdepot},${req.params.idFolder}`;
        res.render("depotfileupload", {
          user: req.user,
          root: "conservatoire3d",
          dirdepot: req.params.dirdepot,
          files: filenamesUpload,
          idFolder: req.params.idFolder,
        });
      });
    });
  })

  .get("/depotfileuploadend,:dirdepot,:idFolder", (req: Request<{ dirdepot: string; idFolder: string }>, res) => {
    // synch sur le depot...
    res.render("depotfileupload", {
      user: req.user,
      root: "conservatoire3d",
      dirdepot: req.params.dirdepot,
      files: "",
      idFolder: req.params.idFolder,
    });
  })
  .post("/depotfileuploadend,:dirdepot,:idFolder", (req: Request<{ dirdepot: string; idFolder: string }>, res) => {
    performRequest(`/api/nbImagesFolder/conservatoire3d,${req.params.idFolder}`, "PATCH", null, (nb) => {
      console.log(nb);
      req.flash("ok", "Depot OK");
      res.redirect("/success");
    });
  })

  /**
 * __  __      _            _       _
 |  \/  | ___| |_ __ _  __| | __ _| |_ __ _
 | |\/| |/ _ \ __/ _` |/ _` |/ _` | __/ _` |
 | |  | |  __/ || (_| | (_| | (_| | || (_| |
 |_|  |_|\___|\__\__,_|\__,_|\__,_|\__\__,_|
 *
*/
  .get("/model/:root", (req, res) => {
    performRequest(`/api/metadatamodel/${req.params.root},${res.locals.lang}`, "GET", null, (data) => {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data));
    });
  })

  .get("/metadata/:root,:model", (req: Request<{ root: string; model: string }>, res) => {
    performRequest(`/api/metadata/${req.params.root},${req.params.model},${res.locals.lang}`, "GET", null, (data) => {
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(data));
    });
  })

  .get(
    "/allmetadata/:idItem,:itemType,:root",
    (req: Request<{ idItem: string; itemType: string; root: Branch }>, res) => {
      const idItem = Number.parseInt(req.params.idItem);
      if(isNaN(idItem)) {
        res.status(500);
        res.send("The idItem is not a number");
        return;
      }

      if(!['file', 'object', 'folder'].includes(req.params.itemType)) {
        res.status(500);
        res.send("The itemType is not valid, must be 'file', 'object' or 'folder'");
        return;
      }
      const itemType: 'file' | 'object' | 'folder' = req.params.itemType as 'file' | 'object' | 'folder';
      archeogrid_client.metadata.allMetadata.query({
        branch: req.params.root,
        language: i18n.getLocale(req) as 'fr' | 'en' | 'am',
        id: idItem,
        type: itemType,
      }).then((allmetadata) => {
        // Add the viewer link
        switch (itemType) {
          case 'file':
            allmetadata.viewer = `https://www.archeogrid.fr/visionneuse,${idItem}-${allmetadata.id_folder},pft3d-p,0`;
            break;
          case 'object':
            allmetadata.viewer = `https://www.archeogrid.fr/visionneuseObj,${idItem},${allmetadata.repre_image}-${allmetadata.id_folder},pft3d-p,0`;
            break;
        }
        res.setHeader("Content-Type", "application/json");
        res.send(JSON.stringify(allmetadata));
      }).catch((err) => {
        console.log(err);
        res.send(err);
      })
    },
  )

  .get(
    "/metadatavalues/:idItem,:itemType,:model,:root",
    (
      req: Request<{
        idItem: string;
        itemType: string;
        model: string;
        root: Branch;
      }>,
      res,
    ) => {
      performRequest(
        `/api/metadatavalue/${req.params.idItem},${req.params.itemType},${req.params.model},${
          req.params.root
        },${i18n.getLocale(req)}`,
        "GET",
        null,
        (data) => {
          res.setHeader("Content-Type", "application/json");
          res.send(JSON.stringify(data));
        },
      );
    },
  )

  .get(
    "/metadataND/:itemType:idItem,:lang",
    (req: Request<{ itemType: string; idItem: string; lang: string }>, res) => {
      // ajout du typage : o / i / d (directory)
      const resu = {};
      const model = [];
      const infoviewer3d = { id: null, type: null };
      let final = {};
      let chaine = "";
      const type = req.params.itemType;
      let ty = "";
      if (type === "o") {
        ty = "object";
        chaine = `itemsFromObject/${branchConfig},${req.params.idItem}`;
      } else if (type === "i") {
        ty = "file";
        chaine = `objectsFromFile/${req.params.idItem},${branchConfig}`;
      } else if (type === "d") {
        ty = "folder";
      } else {
        // ERREUR
        ty = "bad";
      }
      // Récupérer l'info viewer si existe sur des items associés en gardant la hiérarchie
      performRequest("/api/metadatamodel/"+ branchConfig + ",fr", "GET", null, (datamodel) => {
        if (ty !== "bad") {
          performRequest(
            `/api/allmetadata/${req.params.idItem},${ty},${branchConfig},${req.params.lang}`,
            "GET",
            null,
            (data) => {
              // TODO différencier file et object
              performRequest(`/api/${chaine}`, "GET", null, (itemdatafromObj) => {
                // Recherche parmi les items associés ceux qui ont un viewer associé et si oui, donner le nom
                for (let i = 0; i < itemdatafromObj.length; i++) {
                  if (itemdatafromObj[i].viewerinfo !== "") {
                    infoviewer3d.id = itemdatafromObj[i].id;
                    infoviewer3d.type = itemdatafromObj[i].type;
                  }
                }
                const result = data[0];
                const fina: Record<string, Record<string, string>> = {};
                if (result) {
                  for (const r in result) {
                    if (result[r]) {
                      for (let i = 0; i < datamodel.length; i++) {
                        if (r === datamodel[i].name) {
                          // on est dans un modele validee
                          if (result[r] === null) {
                            delete result[r];
                          } else {
                            const mini: Record<string, string> = {};
                            for (let m = 0; m < result[r].length; m++) {
                              // ne récupérer que les infos utiles
                              const proName = result[r][m].label.toString();
                              mini[proName] = result[r][m].value.toString();
                            }
                            fina[r] = mini;
                          }
                        }
                      }
                    }
                  }

                  //final = Object.assign({name: result['name']}, resu)

                  final = Object.assign({ name: result.name }, fina, {
                    infoviewer3d: infoviewer3d,
                  });
                  res.setHeader("Access-Control-Allow-Origin", urlOrigin);
                  //res.setHeader("Access-Control-Allow-Credentials', 'true');
                  //res.setHeader("Access-Control-Allow-Origin", "*");
                  res.setHeader("Content-Type", "application/json");
                  res.send(JSON.stringify(final));
                } else {
                  final = Object.assign(fina, {
                    infoviewer3d: infoviewer3d,
                  });
                  res.setHeader("Access-Control-Allow-Origin", urlOrigin);
                  res.setHeader("Content-Type", "application/json");
                  res.send(JSON.stringify(final));
                }
              });
            },
          );
        } else {
          res.setHeader("Access-Control-Allow-Origin", urlOrigin);
          res.setHeader("Content-Type", "application/json");
          res.send(JSON.stringify([]));
        }
      });
    },
  )

  .get("/metadataModel/:model", (req, res) => {
    performRequest(`/api/metadataJSON/${branchConfig},${req.params.model}`, "GET", null, (datamodel) => {
      let final = {};
      final = Object.assign(datamodel);
      res.setHeader("Access-Control-Allow-Origin", urlOrigin);
      res.setHeader("Content-Type", "application/json");
      //res.send(JSON.stringify(datamodel));
      res.send(final);
    });
  });

app.get("/itemsFromCode/:model,:code,:value", (req: Request<{ model: string; code: string; value: string }>, res) => {
  // API pour récupérer une liste d'items avec leur id et leur type
  // sur la demande d'une valeur d'un code de métadonnées issu d'un modele
  // API au départ pour lier Notre-Dame Analyses de prélèvement et annotation aïoli
  // nom du model : nd_prelevement_analyses
  // nom du code de métadonnées : nom_aioli_annotation
  // on recherche sur la valeur d'une annotation aïoli
  performRequest(
    `/api/itemsFromModelCodeValue/${branchConfig},${req.params.model},${req.params.code},${req.params.value}`,
    "GET",
    null,
    (items) => {
      res.setHeader("Access-Control-Allow-Origin", urlOrigin);
      res.setHeader("Content-Type", "application/json");
      //res.send(JSON.stringify(datamodel));
      res.send(items);
    },
  );
});

app.get("/allmetadataItem/:item,:type", (req: Request<{ item: string; type: string }>, res) => {
  //API pour récupérer toutes les métadonnées d'un item (de tous les modèles présents pour le projet dans lequel est cet item
  // et de tous les tags de thesaurus du projet
  performRequest(
    `/api/allmetadata/${req.params.item},${req.params.type},${branchConfig},${i18n.getLocale(req)}`,
    "GET",
    null,
    (data) => {
      // on renvoie les résultats
      res.setHeader("Access-Control-Allow-Origin", urlOrigin);
      res.setHeader("Content-Type", "application/json");
      res.send(data[0]);
    },
  );
});

app.get("/InfoProjectItemsNB/:idproject", (async (req: Request<{ idproject: string }>, res) => {
  const items =  await archeogrid_client.projects.itemsProjectListNB.query({
    branch:branchConfig,
    project_id: parseInt(req.params.idproject)
  });
   res.setHeader("Content-Type", "application/json");
   res.send(items);
}));

app.get("/copyMetadataItem", (req, res) => {
  const user_id = res.locals.user.id;
  if(user_id){
    const branch = req.query.branch;
    const item_type = req.query.item_type;
    const item_id = parseInt(req.query.item_id as string);

    if(isNaN(item_id)){
      res.status(400).send({status: 'error', details: [{type: 'copy', success: false, error: 'Bad request'}]}); // Bad request
      return;
    }

    if(branch && item_type && item_id){
      req.session.copy_from_item = {branch: branch as string, item_type: item_type as string, item_id};
      res.status(200).send({status: 'success', details: [{type: 'copy', success: true, error: 'The item has been selected'}]}); // OK
      return
    }else{
      res.status(400).send({status: 'error', details: [{type: 'copy', success: false, error: 'Bad request'}]}); // Bad request
    }
  }else{
    res.status(403).send({status: 'error', details: [{type: 'copy', success: false, error: 'Forbidden'}]}); // Forbidden
  }
});

app.get("/pasteMetadataItem", async (req, res) => {
  const user_id = res.locals.user.id;

  if(!req.session.copy_from_item){
    res.send({status: 'error', details: [{type: 'copy', success: false, error: 'The item from which you want to copy metadata has not been selected'}]});
    return;
  }

  const copy_from_item: {branch: string, item_type: string, item_id: number} = req.session.copy_from_item as {branch: string, item_type: string, item_id: number};

  if(copy_from_item.item_id == parseInt(req.query.item_id as string)){
    res.send({status: 'error', details: [{type: 'copy', success: false, error: 'You cannot copy metadata to the same item'}]});
    return;
  }

  if(user_id){
    const branch = req.query.branch;
    const item_type = req.query.item_type;
    const item_id = parseInt(req.query.item_id as string);

    if(isNaN(item_id)){
      res.sendStatus(400); // Bad request
      return;
    }

    if(branch && item_type && item_id){
      const copy_to_item = {branch: branch as string, item_type: item_type as string, item_id};
      const result = await archeogrid_client.metadata.copypasteMetadataItem.mutate({
        from_item: copy_from_item,
        to_item: copy_to_item,
        language: i18n.getLocale(req) as "en" | "fr" | undefined
      });
      res.status(200); // OK
      console.log(result);
      res.send(result);
    }else{
      res.sendStatus(400); // Bad request
    }
  }else{
    res.sendStatus(403); // Forbidden
  }
});

    /**
   * API pour lister tous les items d'un projet
   * @param idproject L'identifiant du projet
   * @param lng La langue à utiliser
   * @param nbItem Le nombre d'items à lister (si =0 tous les items)
   * @param page Le numéro de page
   */
app.get("/paginatedInfoProjectItems/:idproject,:lng,:nbItem,:page",( async (req: Request<{ idproject: string, lng: string, nbItem: string, page: string }>, res) => {
  res.setHeader("Access-Control-Allow-Origin", urlOrigin);
  res.setHeader("Content-Type", "application/json");
  res.setHeader("Transfer-Encoding", "chunked");

  //1. get all items of a project : files, objects, unicos
  // With pagination
  const items =  await archeogrid_client.projects.itemsProjectList.query({
    branch:branchConfig,
    project_id: parseInt(req.params.idproject),
    nb_items: parseInt(req.params.nbItem),
    offset: (parseInt(req.params.page) -1 ) * parseInt(req.params.page)
  });

  if(items.length === 0) {
    res.write('No items found');
    res.end();
    return;
  }

  // 2. for each item get all metadata
  res.write('[');
  let workers = [];
  let counterItem = 0;
  let nb_slice = items.length < API_worker_number ? 1 : API_worker_number;

  for(let s=0; s<nb_slice; s++) {
    workers.push(new Promise((resolve, reject) => {
      let worker = new Worker('./tools/workers/projectInfo_worker.js', {
        workerData: {
          branch: branchConfig,
          language: req.params.lng,
          items: items.slice(s*items.length/nb_slice, (s+1)*items.length/nb_slice),
          promise_limit: API_promise_limit
        }
      });

      worker.on('message', (data: any) => {
        switch(data.status) {
          case 'done':
            resolve(true);
            break;
          case 'success':
            res.write(data.item as JSON);
            if(counterItem < items.length - 1) {
              res.write(',');
              counterItem++;
            }
            break;
          case 'error':
            reject(data.message);
            break;
        }
      });
    }).catch((error) => {
      console.log(error);
      res.write(']');
      res.end();
    }))
  }

  Promise.allSettled(workers).then((result) => {
    result.forEach((r,i) => {
      if(r.status === 'rejected') {
        console.log("Worker n°" + i + " failed : " + r.reason);
      }
    })
    res.write(']');
    res.end();
  }).catch((error) => {
    console.log(error);
    res.end();
  });
}));

app.get("/fullInfoProjectItems/:idproject,:lng",( async (req: Request<{ idproject: string, lng: string }>, res) => {
  res.redirect(`/paginatedInfoProjectItems/${req.params.idproject},${req.params.lng},0,1`);
}));

app
  .post("/metadataImportJson", (req, res) => {
    let rejected = "";
    if (req.user) {
      // { rootproj: '5535', idUser: '190', idObject: '652', mmodel: '29' }
      performRequest(
        `/api/ingestJSONpassport/${branchConfig},${req.body.rootproj},${req.user.id},${req.body.idObject},${req.body.mmodel}`,
        "PUT",
        null,
        (dataimport, code) => {
          if (code === 201) {
            // import OK
            // console.log(dataimport)
            // on récupère le dernier élément du tableau qui donne le nombre de rejected
            const badResu = Number.parseInt(dataimport.pop().split("_")[1]); // en faisant ça on supprime déjà le dernier element qui donne le nombre de rejected
            const goodResu = dataimport.pop().split("_")[1]; // on récupère le nombre de bon
            // [
            //      "CR204C_OK",
            //      "CR 204 C##KO##../web-archeogrid/extprojects_data/PARIS_NOTRE-DAME/Releves/Collecte/gt-numerique/Chantier_AnneauDeCompression/TEST_MEMoS2Archeogrid/CR204C/CR204C_FaceB.json",
            //      "accepted_1",
            //      "rejected_1"
            // ]
            //
            if (!badResu) {
              // tout est ok on retourne toujours sur la page complete (jamais sur la modale)
              res.redirect(
                `/visionneuseObj,${req.body.idObject},${req.body.idFile}-${req.body.idFolder},${branchConfig}-p,${req.body.rights}`,
              );
            } else {
              for (let r = 0; r < dataimport.length; r++) {
                if (dataimport[r].split("##")[1] === "KO") {
                  const tmp = dataimport[r].split("##")[0];
                  const tmp2 = dataimport[r].split("##")[2]; // on récupère la dernière partie qui est le nom du fichier et son emplacement
                  // on supprime les 4 premiers items du path
                  const tmp3 = tmp2.split("/").slice(4);
                  const tmpFinal = `  "${tmp}" IN : ${tmp3}`;
                  rejected += tmpFinal;
                }
              }
              req.flash(
                "error",
                `Import JSON KO, ${goodResu} json file(s) well imported ${rejected.length} Bad reference(s) found (see Bad reference follow by the name of the file concerned ):<br> ${rejected}`,
              );
              res.redirect("error");
            }
          }
        },
      );
    } else {
      mess_front = req.__("message_notlogged");
      res.send(mess_front);
    }
  })

  /**
   * FILE
   * TODO :
   * Vérifier que le user a droit à cette info :
   */
  .get(
    "/allFileInfo/:idFile,:idFolder,:root",
    (req: Request<{ idFile: string; idFolder: string; root: Branch }>, res) => {
      performRequest(`/api/statusFolder/${req.params.idFolder},${req.params.root}`, "GET", null, (status) => {
        if (status.status === "private") {
          if (req.user) {
            //let ffArray = Object.values(req.user.ff)
            if (req.user.ff.includes(Number.parseInt(req.params.idFolder))) {
              // le folder des dans la liste ff !
              mess_front = req.__("message_notaccess");
              res.send(mess_front);
              //res.send(' Access forbidden to this folder ... sorry')
            } else {
              // folder accessible OK

              performRequest(
                `/api/allmetadata/${req.params.idFile},file,${req.params.root},${i18n.getLocale(req)}`,
                "GET",
                null,
                (allmetadata) => {
                  //console.log(allmetadata)
                  performRequest(
                    `/api/objectsFromFile/${req.params.idFile},${req.params.root}`,
                    "GET",
                    null,
                    (objectdatafromfile) => {
                      //console.log(objectdatafromfile)
                      const result = Object.assign(allmetadata[0], objectdatafromfile[0]);
                      //console.log(result)
                      res.setHeader("Content-Type", "application/json");
                      res.send(JSON.stringify(result));
                    },
                  );
                },
              );
            }
          } else {
            mess_front = req.__("message_notlogged");
            res.send(mess_front);
            //res.send('this folder is private and you are not logged in ... There is no way you can access it')
          }
        } else {
          // public folder OK
          performRequest(
            `/api/allmetadata/${req.params.idFile},file,${req.params.root},${i18n.getLocale(req)}`,
            "GET",
            null,
            (allmetadata) => {
              //console.log(allmetadata)
              performRequest(
                `/api/objectsFromFile/${req.params.idFile},${req.params.root}`,
                "GET",
                null,
                (objectdatafromfile) => {
                  //console.log(objectdatafromfile)
                  const result = Object.assign(allmetadata[0], objectdatafromfile[0]);
                  //console.log(result)
                  res.setHeader("Content-Type", "application/json");
                  res.send(JSON.stringify(result));
                },
              );
            },
          );
        }
      });
    },
  );

/**
 *  _    _       _
 * | |  | |     (_)
 * | |  | |_ __  _  ___ ___
 * | |  | | '_ \| |/ __/ _ \
 * | |__| | | | | | (_| (_) |
 *  \____/|_| |_|_|\___\___/
 *
 */

app
  .post(
    "/createUnico",
    asyncHandler(async (req, res) => {
      const user = res.locals.user as CustomUser;
      if (!user.id) {
        res.sendStatus(401);
        return;
      }
      const informations = {
        idUser: user.id.toString(),
        idFile: req.body.id,
        name: encodeURI(req.body.name),
        annotation: encodeURI(req.body.annotation),
        x: req.body.x,
        y: req.body.y,
        width: req.body.width,
        height: req.body.height,
        type: req.body.type,
        points: req.body.points,
      };

      const [_, code] = await req.fetchApiCode(
        `/api/createUnico/${branchConfig},${encodeURIComponent(JSON.stringify(req.body.thesaurus))}`,
        informations,
        "POST",
      );

      res.sendStatus(code);
    }),
  )

  .post(
    "/updateUnico",
    asyncHandler(async (req, res) => {
      const user = res.locals.user as CustomUser;
      if (!user.id) {
        res.sendStatus(401);
        return;
      }

      const branch = branchConfig;
      const user_id = user.id;
      const name = req.body.name;
      const annotation = req.body.annotation;
      const unico_id = Number.parseInt(req.body.idUnico);
      const thesaurus = JSON.parse(JSON.stringify(req.body.thesaurus));

      const result = await archeogrid_client.unico.updateUnico.mutate({
        branch,
        user_id,
        name,
        annotation,
        unico_id,
        thesaurus,
      });

      if (result) res.sendStatus(200);
      else res.sendStatus(500);
    }),
  )

  .get("/deleteUnico/:id", (req, res) => {
    const user = res.locals.user as CustomUser;
    if (user.id) {
      const id = req.params.id;
      performRequest(`/api/getUnico/${branchConfig},${id}`, "GET", null, (unico, code) => {
        if (code === 200) {
          if (unico.id_user === user.id) {
            // TODO : tester les retours en cas de problème ?
            performRequest(`/api/prepareDeleteUnico/${branchConfig},${id}`, "GET", null, (dataPrep, code1) => {
              performRequest(`/api/deleteUnico/${branchConfig},${id}`, "DELETE", null, (data, code) => {
                res.sendStatus(code);
              });
            });
          } else {
            res.sendStatus(403);
          }
        } else {
          res.sendStatus(404);
        }
      });
    } else {
      res.sendStatus(401);
    }
  })

  .get(
    "/descaladen",
    (req: Request<{ id: string }, unknown, unknown, { type: string; idP: string; path: string }>, res, next) => {
      // /descaladen?type=E&idP=3D-ARD&path=Crane_mesange/0000/mesange.zip

      let fichier = req.query.path;
      let dbase = "";
      let root = "";
      dbase = helpers.setBasePath(req.query.type);
      if (req.query.idP === "3D-ARD") {
        // on télécharge depuis le répertoire précisé
        root = `${dbase + req.query.idP}/${downloadOrigin}/`;
      } else {
        // cas général : on donne le chemin complet du fichier à télécharger ...
        root = `${dbase + req.query.idP}/`;
      }
      fichier = root + fichier;
      res.download(fichier);
    },
  )

  .get(
    "/exists",
    (req: Request<{ id: string }, unknown, unknown, { type: string; idP: string; path: string }>, res) => {
      // exists?type=E&idP=3D-ARD&path=Crane_mesange/0000/mesange.zip
      const type = req.query.type;
      const dbase = helpers.setBasePath(type);
      const file =
        req.query.idP === "3D-ARD"
          ? // on prend le parametre downloadOrigin
            helpers.fileExists(`${dbase + req.query.idP}/${downloadOrigin}/${req.query.path}`)
          : // on ne prend pas de parametre
            helpers.fileExists(dbase + req.query.path);

      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(file));
    },
  )

  .get("/getSizeFolder,:idFolder", (req: Request<{ idFolder: string }>, res) => {
    performRequest(`/api/folderPath/${branchConfig},${req.params.idFolder}`, "GET", null, (data) => {
      const size = helpers.getTotalSize(data.path);
      res.setHeader("Content-Type", "application/json");
      res.send(JSON.stringify(size));
    });
  })

  .get("/getVirtualSizeFolder,:idFolder", (req: Request<{ idFolder: string }>, res) => {
    performRequest(`/api/rootFolder/${branchConfig},${req.params.idFolder}`, "GET", null, (rootData) => {
      const rootF = rootData;
      performRequest(`/api/virtualFolderSize/${branchConfig},${req.params.idFolder}`, "GET", null, (data) => {
        const size = helpers.convertBytes(data.size);
        const resu = { size, rootF: rootF.root };
        res.setHeader("Content-Type", "application/json");
        res.send(resu);
      });
    });
  })

  .get(
    "/downloadFileCl/:idFolder,:folderName,:isVirtual",
    (
      req: Request<{
        idFolder: string;
        folderName: string;
        isVirtual: string;
      }>,
      res,
    ) => {
      const isVirtual = Number.parseInt(req.params.isVirtual);
      const user = res.locals.user;
      let dataGet = {
        userId: user.id,
      }
      if (isVirtual) {
        performRequest(`/api/explore/${req.params.idFolder},${branchConfig}`, "GET", dataGet, (data) => {
          // https://stackoverflow.com/questions/20107303/dynamically-create-and-stream-zip-to-client
          // Tell the browser that this is a zip file.
          res.writeHead(200, {
            "Content-Type": "application/zip",
            "Content-disposition": `attachment; filename=${req.params.folderName}.zip`,
          });

          // prepare archive
          const zip = archiver("zip", {
            zlib: { level: 9 }, // Sets the compression level.
          });
          // Send the file to the page output directly !
          zip.pipe(res);

          // on ajoute à l'archive tous les fichiers concernés
          for (let i = 0; i < data.length; i++) {
            zip.append(helpers.getStream(data[i].path), {
              name: helpers.getFileName(data[i].path),
            });
          }

          zip.finalize();
        });
      } else {
        // not virtual folder, go get fsdir scan and download (without accessing DB, just to retrieve the init path)
        performRequest(`/api/folderPath/${branchConfig},${req.params.idFolder}`, "GET", null, (path) => {
          res.writeHead(200, {
            "Content-Type": "application/zip",
            "Content-disposition": `attachment; filename=${req.params.folderName}.zip`,
          });

          helpers.walk(path.path, (err, results) => {
            if (err) throw err;
            // prepare archive
            const zip = archiver("zip", {
              zlib: { level: 9 }, // Sets the compression level.
            });
            // Send the file to the page output directly !
            zip.pipe(res);
            // on ajoute à l'archive tous les fichiers concernés
            for (let i = 0; i < results.length; i++) {
              zip.append(helpers.getStream(results[i]), {
                name: helpers.getFileName(results[i]),
              });
            }

            zip.finalize();
          });
        });
      }
    },
  );

app.route("/entity/:entityId").get((req, res) => {
  res.locals.myroot = `/entity/${req.params.entityId}`;
  performRequest(`/api/entity/${req.params.entityId}`, "GET", null, (entity) => {
    if (entity.length !== 0) {
      res.render("entity", {
        entity: entity,
        layout: "layoutbis",
      });
    } else {
      req.flash("error", "Entity does not exist");
    }
  });
});

// app.get("/concept/:branche/:value", (req, res) => {
//   // afficher tous les item qui ont ce concept en mot clef
//   // traiter le cas où value contient des espaces
//   const value = req.params.value.replace(/ /g, "_");
//   performRequest(
//     `/api/exploreConceptThes/${req.params.branche},${value},${i18n.getLocale(req)}`,
//     "GET",
//     null,
//     (data) => {
//       res.render("concept", {
//         data: data,
//         concept: req.params.value,
//         branche: req.params.branche,
//       });
//     },
//   );
// });

/**
 * User Profil
 */
app
  .route("/profil")
  .get(
    asyncHandler(async (req, res) => {
      res.locals.branch = branchConfig
      res.locals.myroot = req.session.root;
      if (!req.user) {
        req.flash("error", "Not connected");
        return;
      }
      const data = await req.fetchApi(`/api/user/${req.user.id}`);
      const entity = await req.fetchApi(`/api/entity/${data.entity_id}`);
      const searches = await req.fetchApi(`/api/getUserSavedSearches/${branchConfig},${req.user.id}`);

      res.render("profil", {
        user: req.user,
        pass: 0,
        method: req.method,
        entity: entity.nom,
        data: data,
        entity_id: data.entity_id,
        success: 0,
        searches: searches,
      });
    }),
  )
  .post(
    asyncHandler(async (req, res) => {
      if (!req.user) {
        req.flash("error", "Not connected");
        return;
      }

      if (req.body.confirmpassword !== req.body.password) {
        res.render("profil", {
          user: req.user,
          pass: 1,
          method: req.method,
          data: req.body,
          entity: "",
          entity_id: 0,
          success: 0,
          searches: {},
        });
        return;
      }
      req.body.confirmpassword = undefined;
      const [data, code] = await req.fetchApiCode(`/api/user/${req.user.id}`, req.body, "PATCH");
      if (code === 200) {
        req.flash("ok", "Profil mis à jour");
        req.user.username = req.body.username;
        req.session.save((err) => {
          if (err) console.log(err);
          res.redirect("/projectsMap");
        });
      } else {
        req.flash("error", "Error in updating profil");
        res.render("profil", {
          user: req.user,
          data: data,
          pass: 0,
          method: req.method,
          entity: "",
          entity_id: 0,
          success: 0,
          searches: {},
        });
      }
    }),
  );

app
  .route("/editEntity,:entityId")
  .get((req, res) => {
    res.locals.branch = branchConfig
    if (!req.user) {
      req.flash("error", "Not connected");
      return;
    }

    performRequest(`/api/user/${req.user.id}`, "GET", null, (data) => {
      if (data.entity_id !== Number.parseInt(req.params.entityId)) {
        req.flash("error", "Not authorized");
        return;
      }
      performRequest(`/api/entity/${req.params.entityId}`, "GET", null, (dataentity) => {
        res.render("editEntity", {
          data: dataentity,
          root: req.session.root,
          user: data,
          message: "",
        });
      });
    });
  })
  .post((req, res) => {
    if (req.body.idEntity) {
      performRequest(`/api/user/${res.locals.user.id}`, "GET", null, (data) => {
        performRequest(`/api/entity/${req.body.idEntity}`, "PATCH", req.body, (dateupdate, code) => {
          if (code === 200) {
            performRequest(`/api/entity/${req.params.entityId}`, "GET", null, (dataentity) => {
              res.render("editEntity", {
                data: dataentity,
                root: req.session.root,
                user: data,
                message: "Entité modifiée",
              });
            });
          }
        });
      });
    }
  });

app
  .route("/comment,:root,:idFolder,:idItem,:itemType")
  .get(
    (
      req: Request<{
        root: Branch;
        idFolder: string;
        idItem: string;
        itemType: string;
      }>,
      res,
    ) => {
      const user = res.locals.user as CustomUser;
      const mainFolder = 0;
      let model = "";
      let listeMetadata = [];
      const type = req.params.itemType;
      let repre_image = "0";
      performRequest(`/api/objectId/${req.params.root},${req.params.idItem}`, "GET", null, (dataobject) => {
        const objectToComment = dataobject;
        if (req.params.itemType === "object") repre_image = objectToComment.id_file_representative;
        else repre_image = req.params.idItem;
        performRequest(
          `/api/mainFolder/${req.params.root},${req.params.idFolder},${i18n.getLocale(req)}`,
          "GET",
          null,
          (folder) => {
            model = folder.model_name;
            //TODO  Cas où on veut faire un commentaire sur un object : récupérer le nom de l'objet
            performRequest(`/api/prepareImage/${repre_image},${req.params.root}`, "GET", null, async (dataobj) => {
              if (dataobj) {
                let image: {
                  id: number;
                  fid: number | null;
                  file_ext?: string;
                  urlTarget?: string;
                  path?: string;
                  srcImgThumb?: string;
                  srcImg3d?: string;
                  srcImgSmall?: string;
                  srcImgHd?: string;
                };
                // si c'est bien une image sinon le type est folder et n'affichera pas d'image
                // on ajoute le traitement de type unico
                if (type === "file" || type === "unico" || (type === "object" && dataobj.id)) {
                  image = dataobj;
                  const file = dataobj.filename;
                  const pathFile: string = dataobj.path;
                  const pathtoutcourt = `${pathFile.substring(0, pathFile.lastIndexOf("/"))}/`;
                  if (image.file_ext === "url") {
                    if (image.path) {
                      const contenuUrl = fs.readFileSync(image.path, "utf-8");
                      const urlFich = JSON.parse(contenuUrl);
                      if (Object.hasOwn(urlFich, "url")) {
                        image.urlTarget = urlFich.url;
                      }
                      image.srcImgThumb = helpers.setImageThumb(file, pathFile);
                    }
                  } else if (image.file_ext === "3d") {
                    const srcImg3d = md5(pathFile);
                    store.set(`image3d_${srcImg3d}`, pathFile);
                    image.srcImg3d = srcImg3d;
                    if (image.path) {
                      const data3d = await readAndParse3DFile(image.path);
                      if (data3d == null) {
                        req.flash("error", "Unable to load .3d file");

                        return;
                      }
                      if (data3d.type === "3DHOP") {
                        if (data3d.data["3dhop"].thumbnail) {
                          const thumb_name = pathtoutcourt + data3d.data["3dhop"].thumbnail;
                          const srcImgThumb = md5(thumb_name);
                          const srcImgSmall = srcImgThumb;
                          store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                          store.set(`small_${srcImgThumb}`, thumb_name);

                          image.srcImgThumb = srcImgThumb;
                          image.srcImgSmall = srcImgSmall;
                        }
                      } else {
                        if (data3d.data.potree.thumbnail) {
                          const thumb_name = pathtoutcourt + data3d.data.potree.thumbnail;
                          const srcImgThumb = md5(thumb_name);
                          const srcImgSmall = srcImgThumb;
                          store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                          store.set(`small_${srcImgThumb}`, thumb_name);

                          image.srcImgThumb = srcImgThumb;
                          image.srcImgSmall = srcImgSmall;
                        }
                      }
                    } else {
                      req.flash("error", "Unable to get path for 3d file");
                    }
                  } else if (image.file_ext === "json") {
                    image.srcImgThumb = "json";
                    image.srcImgSmall = "json";
                    image.srcImgHd = helpers.setImageHd(file, pathFile);
                  } else {
                    image.srcImgThumb = helpers.setImageThumb(file, pathFile);
                    image.srcImgHd = helpers.setImageHd(file, pathFile);
                  }
                } else {
                  // cas des objets sans image rep
                  image = { id: 0, fid: null };
                }
                // 2 cas: si il y a un model ou s'il n'y en a pas
                if (folder.model_name) {
                  performRequest(
                    `/api/metadataModelType/${req.params.root},${folder.model_name},comment,${i18n.getLocale(req)}`,
                    "GET",
                    null,
                    (metadata) => {
                      performRequest(
                        `/api/metadatalistId/${req.params.root},${metadata[0].list}`,
                        "GET",
                        null,
                        (liste) => {
                          listeMetadata = Object.assign(liste);
                          res.render("comment", {
                            user: user,
                            branch: branchConfig,
                            object: objectToComment,
                            root: req.params.root,
                            idItem: req.params.idItem,
                            image: image,
                            folder: image.fid,
                            visuFolder: req.params.idFolder,
                            type: req.params.itemType,
                            mainFolder: folder,
                            mainFolderMetadata: folder.id_metadata_model,
                            video: acceptedVideoFormats,
                            viewerFormat,
                            metadata: metadata,
                            model: model,
                            tablist: metadata[0].list,
                            listeMetadata: listeMetadata,
                          });
                        },
                      );
                    },
                  );
                } else {
                  res.render("comment", {
                    user: user,
                    branch: branchConfig,
                    object: objectToComment,
                    image: image,
                    root: req.params.root,
                    idItem: req.params.idItem,
                    mainFolder: folder,
                    folder: image.fid,
                    visuFolder: req.params.idFolder,
                    type: req.params.itemType,
                    mainFolderMetadata: folder.id_metadata_model,
                    video: acceptedVideoFormats,
                    viewerFormat,
                    metadata: "",
                    model: model,
                    tablist: "",
                    listeMetadata: "",
                  });
                }
              } else {
                if (i18n.getLocale(req) === "fr") req.flash("error", "Pas d'image à commenter !");
                else req.flash("error", "No image to comment!");
                res.render("/error");
              }
            });
          },
        );
      });
    },
  )
  .post(
    asyncHandler(async (req: Request<{ root: Branch; idFolder: string; itemType: string }>, res) => {
      /*
        {
          '1#165': 'GT émotion',
          comment: 'ftyjfff',
          author: '',
          other_date: '',
          mainFolder: '5535',
          userId: '190',
          itemId: '84548'
        }
         */
      let redirect = "";
      req.body.itemType = req.params.itemType;
      if (!req.user) {
        req.flash("error", "Not connected");
        return;
      }

      const branche = req.params.root;
      const folder = await archeogrid_client.folders.mainFolder.query({
        branch: branche,
        folder_id: Number.parseInt(req.params.idFolder),
        language: res.locals.lang,
      });

      if (!folder) {
        req.flash("error", "Folder not found");
        return;
      }

      let modele = "";
      if (folder.model_name) {
        modele = folder.model_name;
      }
      const [datacomment, code] = await req.fetchApiCode(
        `/api/comment/${branche},${folder.mainfolder},${req.user.id}`,
        req.body,
        "PUT",
      );
      if (code === 201) {
        if (modele !== "") {
          // si commentaires évolués (GT ...) faire le lien entre le commentaire et toutes les autres infos (gt , date, heure)
          // en posant les métadonnées enregistrées sur le commentaire !
          const dataput: Record<string, string> = {
            userId: req.body.userId,
            rootDir: branchConfig,
          };
          for (const ind in req.body) {
            // on ne récupère que ce qui concerne des métadonnées 'standard' du modèle
            if (ind.includes("#")) {
              dataput[ind] = req.body[ind];
            }
          }
          // on crée les infos passport metadonnées pour le commentaire !
          const [datam, codem] = await req.fetchApiCode(
            `/api/metadatavalue/${datacomment.id},comment,${modele},${branche},${res.locals.lang}`,
            dataput,
            "PUT",
          );
          // Use custom redirect URL if provided, otherwise use default
          if (req.body.redirectUrl) {
            redirect = `/${req.body.redirectUrl}`;
          } else if (branche === branchConfig) {
            redirect = `/projectv/${folder.mainfolder}`;
          }
          res.redirect(redirect);
        } else {
          // Use custom redirect URL if provided, otherwise use default
          if (req.body.redirectUrl) {
            redirect = `/${req.body.redirectUrl}`;
          } else if (branche === branchConfig) {
            redirect = `/projectv/${folder.mainfolder}?folder=${req.params.idFolder}`;
          }
          res.redirect(redirect);
        }
      }
    }),
  );

app.route("/duplicateItem,:branch,:itemType,:idItem")
  .get(asyncHandler(async (req: Request<{ branch: Branch; itemType: string; idItem: string }>, res) => {
    if(!req.user) return {status: 500, message: "User not connected"};
    let status = 500, message = "", branch = req.params.branch;
    switch(req.params.itemType) {
      case "object":
        const object_id = Number.parseInt(req.params.idItem);
        if(object_id){
          const result = await archeogrid_client.objects.duplicateObject.mutate({branch, object_id, user_id: req.user.id});
          status = result.status;
          message = result.message;
        }else{
          status = 500;
          message = "Object not found";
        }
        break;
      default:
        status = 500;
        message = 'This item type is not supported by duplicate.';
    }
    res.contentType("application/json");
    res.status(status).send(JSON.stringify({status, message}));
}));

app.get("/thesaurus,:root,:thes", (req: Request<{ root: Branch; thes: string }>, res) => {
  performRequest(`/api/thesaurus/${req.params.root},${req.params.thes}`, "GET", null, (data) => {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  });
});

app.get("/thesaurusName,:root,:thes,:name", (req: Request<{ root: Branch; thes: string; name: string }>, res) => {
  performRequest(`/api/thesaurusName/${req.params.root},${req.params.thes},${req.params.name}`, "GET", null, (data) => {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  });
});

app.get("/list,:root", (req, res) => {
  performRequest(`/api/metadataList/${req.params.root}`, "GET", null, (data) => {
    res.setHeader("Content-Type", "application/json");
    res.send(JSON.stringify(data));
  });
});

app.get("/LesCorpus", (req, res) => {
  res.render("corpus", {
    layout: "layout_html5up",
  });
});

app.get("/corpus", (req, res) => {
  res.redirect("https://anc.archeogrid.fr/corpus");
});

app.get("/archeogridkarnak", (req, res) => {
  res.render("karnak", {
    layout: "layout_html5up",
  });
});

app.get("/karnak", (req, res) => {
  res.redirect("https://anc.archeogrid.fr/karnak");
});

app.get("/karnak/cfeetk_img.php", (req, res) => {
  res.redirect(`https://anc.archeogrid.fr/karnak/cfeetk_img.php?fic=${req.query.fic}`);
});
app.get("/talatat", (req, res) => {
  res.render("talatat", {
    layout: "layout_html5up",
  });
});

app.get("/Aton3D", (req, res) => {
  res.render("aton3d", {
    layout: "layout_html5up",
  });
});

app.get("/cgu", (req, res) => {
  res.render("cgu.ejs", {
    layout: "layout_html5up",
  });
});

app
  .route("/noscnt")
  .get((req: Request<unknown, unknown, unknown, { mess: string }>, res) => {
    let msg = "";
    if (req.query.mess) msg = req.query.mess;
    res.render("contact", {
      message: msg,
      layout: "layout_html5up",
    });
  })

  .post((req, res) => {
    let flamessage = "";
    const datapost = req.body;
    if (!datapost.message || !validateEmail(datapost.email)) {
      if (!datapost.message) {
        if (i18n.getLocale(req) === "fr") req.flash("error", "Message vide");
        else req.flash("error", "Empty message");
      }
      if (validateEmail(datapost.email) === false) {
        if (i18n.getLocale(req) === "fr") req.flash("error", "Adresse email invalide");
        else req.flash("error", "Unvalid email");
      }
      res.redirect(`/noscnt?mess=${req.body.message}`);
    } else {
      const transporter = nodemailer.createTransport({
        host: emailHost,
        port: emailPort,
        secure: emailSecure,
      });
      const mailOptions = {
        from: "Archeogrid <<EMAIL>>", // sender address
        to: `${emailConfig}`,
        bcc: "<EMAIL>",
        subject: `[Archeogrid] Demande de contact de ${datapost.name}`,
        //text: '', // plaintext body
        text: `Mail pour réponse : ${datapost.email}\nMessage : \n"${datapost.message}"`,
      };
      // send mail with defined transport object
      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          return console.log(error);
        }
        console.log(`Message sent: ${info.response}`);
      });
      if (i18n.getLocale(req) === "fr") flamessage = "Message envoyé";
      else flamessage = "Message sent";
      req.flash("ok", flamessage);
      res.redirect("/");
    }
  });

app.route("/home_crowdsourcing,:root").get((req, res) => {
  const dataputCrowd = {
    folderPassport: "crowdsourcing",
    folderName: "CROWDSOURCING",
  };
  const dir = "../web-archeogrid/corpus_data/CROWDSOURCING";
  const uploadPath = path.join(__dirname, dir);
  fs.ensureDir(uploadPath);
  performRequest("/api/rootFolders/corpus", "PUT", dataputCrowd, (dataC, codeC) => {
    if (codeC === 201) {
      const rootDir = req.params.root.replace(/ /g, "");
      const now = new Date();
      const dataPutFolder = {
        folderPath: `CROWDSOURCING/${rootDir}`,
        folderName: rootDir,
        dbParent: "CROWDSOURCING",
        dateT: now.toISOString(),
      };
      const dirRootCrowdsourcing = `${uploadPath}/${rootDir}`;
      fs.ensureDir(dirRootCrowdsourcing);
      performRequest("/api/folder/corpus", "PUT", dataPutFolder, (data, code) => {
        if (code === 201) {
          res.render("crowdsourcing", {
            root: req.params.root,
            idFolder: data.id,
            layout: "layout_html5up",
          });
        } else {
          req.flash("error", "Unable to create information ... sorry, try later");
        }
      });
    } else {
      req.flash("error", "Unable to create information ... sorry, try later");
    }
  });
});

app
  .route("/upload,:root,:idFolder")
  .get(
    asyncHandler(async (req: Request<{ root: Branch; idFolder: string }>, res) => {
      const thesaurus = [];
      const user = res.locals.user as CustomUser;
      res.locals.myroot = req.params.root;
      req.session.root = `/upload,${req.params.root},${req.params.idFolder}`;
      req.session.crowdsourcing = req.params.root;
      const metadata = await archeogrid_client.metadata.getMetadata.query({
        branch: req.params.root,
        language: res.locals.lang,
        model: "upload",
      });

      if (!metadata) throw new Error("metadata is not defined");

      const tabthes = metadata.filter((m) => m.status === "thesaurus").map((m) => m.list);
      for await (const th of tabthes) {
        // TODO : recuperer la bonne partie de l'un thesaurus au lieu de donner en "dur" l'index du subset ...
        const dataget = th === "chrono" ? { subset: "001.004" } : undefined;
        thesaurus.push(await req.fetchApi(`/api/thesaurus/corpus,${th}`, dataget));
      }
      // ajout d'un timeout pour attendre le resultat de la requete des thesaurus

      res.render("depotdemand", {
        user: user,
        root: "corpus",
        idFolder: req.params.idFolder,
        langue: i18n.getLocale(req),
        tabthes: tabthes,
        thesaurus: thesaurus,
        metadata: metadata,
      });
    }),
  )
  .post((req, res) => {
    let userId = 0;
    if (req.user) {
      userId = req.user.id;
    }
    // le nom de l'objet est l'url sans le premier "/" et sans ce qu'il y a derrière la première virgule
    let name = req.url.substring(1);
    name = name.substring(0, name.indexOf(","));
    const dataput = {
      name: name,
      rootDir: req.body.rootDir,
      idFolder: req.body.idFolder,
      userId: userId,
    };
    performRequest(`/api/object/${req.body.rootUpload}`, "PUT", dataput, (data, code) => {
      if (code === 201) {
        const objectId = data.id;
        performRequest(
          `/api/metadatavalue/${objectId},object,upload,corpus,${i18n.getLocale(req)}`,
          "PUT",
          req.body,
          (data, code) => {
            res.redirect(`/upload?idObj=${objectId}&rD=${req.body.rootDir}&idF=${req.body.idFolder}`);
          },
        );
      } else {
        req.flash("error", "unable to insert metadata, please try again");
        res.redirect("/upload,error");
      }
    });
  });

app
  .route("/importData")
  .get(isLoggedIn, (req, res) => {
    performRequest("/api/branche", "GET", null, (branche) => {
      res.render("import", {
        branche: branche,
        rootImport: "",
        model: "",
        modelist: "",
        //fileToImport: '',
        result: "",
      });
    });
  })
  .post(isLoggedIn, (req, res) => {
    let userId = 0;
    if (req.user) userId = req.user.id;

    if (req.body.rootImport) {
      if (!req.body.model) {
        console.log("on a une branche/root mais pas de modele");
        performRequest(`/api/metadatamodel/${req.body.rootImport},${i18n.getLocale(req)}`, "GET", null, (data) => {
          res.render("import", {
            branche: "",
            rootImport: req.body.rootImport,
            model: "",
            modelist: data,
            //fileToImport: '',
            result: "",
          });
        });
      } else {
        //console.log('demander le ficher')
        performRequest(`/api/metadataJSON/${req.body.rootImport},${req.body.model}`, "PUT", null, (resu) => {
          // 2 CAS : resu n'à qu'un element (fichier 4) ou une liste d'éléments
          if (resu.length) {
            for (let i = 0; i < resu.length; i++) {
              // entrer les metadonnées pour cet id Item pour cette métadonnée (resu[i]['name'])
              //console.log(dataId)
              const dataput = {
                champ: resu[i].name,
                value: resu[i].value,
                userId: userId,
              };

              performRequest(
                `/api/metadatavalueFromJson/${req.body.rootImport},${resu[i].omekaItemId},file,${req.body.model}`,
                "PUT",
                dataput,
                (data, code) => {
                  if (code === 500) {
                    req.flash("error", `Unable to insert metadata for ${resu[i].name}`);
                  } else {
                    console.log(`insert ok for ${resu[i].name}, for Omeka item : ${resu[i].omekaItemId}`);
                  }
                },
              );
            }
          } else {
            console.log(resu);
            const dataput = {
              champ: resu.name,
              value: resu.value,
              userId: userId,
            };
            performRequest(
              `/api/metadatavalueFromJson/${req.body.rootImport},${resu.omekaItemId},file,${req.body.model}`,
              "PUT",
              dataput,
              (data, code) => {
                if (code === 500) {
                  req.flash("error", `Unable to insert metadata for ${resu.name}`);
                } else {
                  console.log(`insert ok for ${resu.name}, for Omeka item : ${resu.omekaItemId}`);
                }
              },
            );
          }
          res.render("import", {
            branche: "",
            rootImport: req.body.rootImport,
            model: req.body.model,
            modelist: "",
            //fileToImport: '',
            result: resu,
          });
        });
      }
    }
  });

app.route("/uploadJSON").post(upload.single("fileToImport"), (req, res) => {
  // traiter l'upload avec multer, le fichier upload va dans le répertoire uploads sous app/
  // PUIS ON LE lit et on le transform en object JSON
  console.log("ready to get the file treat");

  if (!req.file) {
    req.flash("error", "File is not defined");
    return;
  }

  const file = req.file;
  const totoraw = fs.readFileSync(file.path, "utf-8");

  const toto = JSON.parse(totoraw);
  // TODO : unable to pass querystring with deep >1 !!! toto is a json object with multiple deep ...
  // TODO : TEST with passing file in put data (req.body)
  // this route became useless ...
  if (!file) {
    res.redirect("/error");
  }
  const dataput = {
    root: req.query.rootImport,
    filj: toto,
  };
  performRequest(`/api/metadataJSON/${req.query.rootImport},${req.query.model}`, "PUT", null, (data) => {
    res.render("import", {
      branche: "",
      rootImport: "",
      model: "",
      modelist: data,
      fileToImport: file,
    });
  });
});

// TODO : pour l'instant c'est pour corpus upload Crowdsourcing ...
app
  .route("/upload")
  .get((req: Request<unknown, unknown, unknown, { rD: string; idObj: string; idF: string }>, res) => {
    if (!req.user) {
      req.flash("error", "Not connected");
    }
    const date_integration = helpers.getDateOfTheDay();
    const now = new Date();
    const user = req.user as CustomUser;
    const rootDir = req.query.rD.replace(/ /g, "");

    // Creation du folder du jour si pas déjà dans la base de données
    const dataput = {
      dateT: now.toISOString(),
      folderName: date_integration,
      folderPath: `CROWDSOURCING/${rootDir}/${date_integration}`,
      dbParent: `CROWDSOURCING/${rootDir}`,
    };
    performRequest("/api/folder/corpus", "PUT", dataput, (data, code) => {
      console.log(data);
      if (code === 201) {
        // 1/ supprimer le lien fait en premier entre l'objet et le folder de crowdsourcing
        const datadelete = {
          idFolder: req.query.idF,
        };
        performRequest(`/api/objectLinkFolder/corpus,${req.query.idObj}`, "DELETE", datadelete, (datad, code) => {
          if (code === 200) {
            // DELETE OK

            // 2/ creéer le nouveau lien avec le bon folder sur l'objet
            const dataput = {
              idFolder: data.id,
            };
            performRequest(
              `/api/objectLinkFolder/corpus,${req.query.idObj}`,
              "PUT",
              dataput,
              (datainsert, codeinsert) => {
                console.log(datainsert);
                console.log(codeinsert);
                if (codeinsert === 201) {
                  res.render("upload", {
                    user: user,
                    objectId: req.query.idObj,
                    root: "corpus",
                    crowdsourcing: req.query.rD,
                    parent_folder: req.query.idF, // pour positionner la route dans la vue et revenir en arriere
                    files: "",
                    rootDir: req.query.rD,
                    folder_id: data.id,
                  });
                } else {
                  req.flash("error", "Unable to put folder link");
                }
              },
            );
          } else {
            req.flash("error", "Unable to delete folder link");
          }
        });
      } else {
        req.flash("error", "Unable to create information ... sorry, try later");
      }
    });
  })
  .post(
    asyncHandler(
      async (
        req: Request<unknown, unknown, unknown, { rD: string; idObj: string; idF: string }> & {
          busboy: Busboy;
        },
        res,
      ) => {
        const rootDir = req.query.rD.replace(/ /g, "");
        const idObject = req.query.idObj;
        const dir = `../web-archeogrid/corpus_data/CROWDSOURCING/${rootDir}/${helpers.getDateOfTheDay()}/`;

        // raz tableau filenamesupload
        filenamesUpload = [];

        const uploadPath = path.join(__dirname, dir);
        // ../web-archeogrid/corpus_data/CROWDSOURCING/NotreDameDeParis/<datedujour>/
        fs.ensureDir(uploadPath);

        req.pipe(req.busboy); // Pipe it trough busboy

        req.busboy.on("file", (name, file, { filename }) => {
          console.log(`Upload of '${filename}' started`);

          // Create a write stream of the new file
          const fstream = fs.createWriteStream(path.join(uploadPath, filename));
          // Pipe it trough
          file.pipe(fstream);

          // On finish of the upload
          fstream.on("close", () => {
            console.log(`Upload of '${filename}' finished`);
            filenamesUpload.push({
              type: "object",
              name: filename,
              objectId: Number.parseInt(idObject),
              dir: dir,
            });
          });
        });

        req.busboy.on("finish", async () => {
          const dataGet = {
            folderPath: `CROWDSOURCING/${rootDir}/${helpers.getDateOfTheDay()}`,
          };
          const data = await req.fetchApi("/api/folder/corpus", dataGet);
          const dataS = await req.fetchApi(`/api/fsdirSynch/corpus,${data.id},0,2`);
          // Synchroniser chaque file avec l'object (insert into corpus_file_object (id_file, id_object) ...
          // filter by object type so that every object id is defined (described in UploadFile type)
          for await (const element of filenamesUpload.filter(
            (object): object is UploadFile & { type: "object" } => object.type === "object",
          )) {
            const { dir, name, objectId } = element;
            await req.fetchApi("/api/object/corpus", { file: dir + name, objectId: objectId.toString() }, "PATCH");
          }

          if (!res.locals.myroot) res.locals.myroot = `/upload,${req.query.rD},${req.query.idF}`;
          res.render("success", {
            root: `/upload,${req.query.rD},${req.query.idF}`,
            user: req.user,
          });
        });
      },
    ),
  );

//app.route('/xmlForDOI/:idFile')
app.get("/xmlForDOI/:idFile", (req, res) => {
  // TODO : si cette url est lancée avec wget , comment s'assurer que celui qui le lance est bien connecté ?
  const idFile = req.params.idFile;
  const resu = {};
  let makemepretty = "";
  // Récupérer l'extension du fichier et le transmettre
  performRequest(`/api/prepareimage/${idFile},${branchConfig}`, "GET", null, (fileInfo) => {
    performRequest(`/api/generateDOI/${branchConfig},${idFile},file`, "PUT", null, (doi, code) => {
      if (code !== 500) {
        const datapatch = {
          type: "file",
          id_doi: doi[0],
          idFile: idFile,
          creation_year: 2021,
        };
        // TODO : récupérer le root_folder pour le passer en param pour récupérer la localisation du projet
        performRequest(`/api/rootFolderFromFile/${branchConfig},${idFile}`, "GET", null, (root) => {
          performRequest(`/api/manageDOI/${branchConfig},${doi[0]}`, "PATCH", datapatch, (dataFolderdoi, code) => {
            const resu = {
              user_id: 190, // pour les tests TODO : quel user récupérer pour le DOI ?? Comment en récupérer un ?
              depot_id: 0,
              folder_id: 0,
              entity_id: 0,
              doi: doi[0],
              type: "file",
              branche: branchConfig.toUpperCase(),
              root_folder: root.root,
              file_ext: fileInfo.file_ext,
            };

            performRequest(`/api/xmlGenerateDOI/id,${idFile}`, "PUT", resu, (data, code) => {
              if (code === 200) {
                // si le xml on génère une ligne dans le shell pour finaliser le xml (il faut ensuite exécuter ce shell
                makemepretty += `. pretty.sh ${xmlPath}${data[0].replace(/\//g, "_")}__${idFile}.xml\n`;
                fs.appendFile(`${outilsPath}makemepretty.sh`, makemepretty, (err) => {
                  if (err) throw err;
                });
              }
              res.setHeader("Content-Type", "application/json");
              res.send(JSON.stringify([code]));
            });
          });
        });
      } else {
        req.flash("error", "unable to create doi");
      }
    });
  });
});

app.get("/xmlForDOIProject/:idFolder", (req, res) => {
  // TODO : si cette url est lancée avec wget , comment s'assurer que celui qui le lance est bien connecté ?
  const idFolder = req.params.idFolder;
  const resu = {};
  let makemepretty = "";
  console.log(idFolder);

  performRequest(`/api/generateDOI/${branchConfig},${idFolder},folder`, "PUT", null, (doi, code) => {
    performRequest(`/api/dateCreationForDOI/${branchConfig},folder,${idFolder}`, "GET", null, (datecrea, code) => {
      if (code !== 500) {
        const datapatch = {
          type: "folder",
          id_doi: doi[0],
          idFolder: idFolder,
          creation_year: datecrea,
        };

        performRequest(`/api/manageDOI/${branchConfig},${doi[0]}`, "PATCH", datapatch, (dataFolderdoi, code) => {
          const resu = {
            user_id: 190, // pour les tests TODO : quel user récupérer pour le DOI ?? Comment en récupérer un ?
            depot_id: 0,
            folder_id: Number.parseInt(idFolder),
            entity_id: 0,
            doi: doi[0],
            type: "folder",
            branche: branchConfig.toUpperCase(),
          };

          performRequest(`/api/xmlGenerateDOI/id,${idFolder}`, "PUT", resu, (data, code) => {
            if (code === 200) {
              // si le xml on génère une ligne dans le shell pour finaliser le xml (il faut ensuite exécuter ce shell
              makemepretty += `. pretty.sh ${xmlPath}${data[0].replace(/\//g, "_")}__${idFolder}.xml\n`;
              fs.appendFile(`${outilsPath}makemepretty.sh`, makemepretty, (err) => {
                if (err) throw err;
              });
            }
            res.setHeader("Content-Type", "application/json");
            res.send(JSON.stringify([code]));
          });
        });
      } else {
        req.flash("error", "unable to create doi");
      }
    });
  });
});

/**
 *
 * DOI : landing page, /PFT3D/:doi  avec doi = XXXXXX.<itemType: p, f ou o>.<publicationYear>
 *
 *           1 doi par projet
 * (a venir) 1 doi par objet virtuel du dépôt  , un doi par fichier ????!
 *
 */
app.get("/"+branchConfig.toUpperCase()+"/:doi", (req, res) => {
  const doi = req.params.doi;
  const type = doi.split(".")[1];
  let itemType = "";

  if (type === "p") itemType = "folder";
  else if (type === "o") itemType = "object";
  else if (type === "f") itemType = "file";
  const result = [];
  const idFolder = 0;
  const nb_obj = 0;
  let publicationDate = "";

  const id_doi = Number.parseInt(doi.split(".")[0]);
  performRequest(`/api/itemFromDOI/${branchConfig},${id_doi}`, "GET", null, (item, code) => {
    if (code === 500) {
      req.flash("error", "No item found");
    } else {
      const idItem = item[0].id_item;
      performRequest(`/api/generateDOI/${branchConfig},${idItem},${itemType}`, "GET", null, (doiexists) => {
        performRequest(`/api/creatorForDOI/${branchConfig},folder,${idItem}`, "GET", null, (creators) => {
          performRequest(`/api/tag/${branchConfig},${itemType},${idItem}`, "GET", null, (tag) => {
            performRequest(`/api/projectFull/${branchConfig},${idItem},${i18n.getLocale(req)}`, "GET", null, (project) => {
              for (const d in project.project) {
                if (d === "creationDate") {
                  publicationDate = project.project[d].split("-")[0];
                }
              }
              if (type === "f") {
                // Aller chercher les infos du fichier
                performRequest(`/api/prepareImage/${idItem},${branchConfig}`, "GET", null, (datafile) => {
                  res.redirect(`/visionneuse,${idItem}-${datafile.fid},${branchConfig}-d,0`); // context d = DOI landing page
                });
              } else {
                // pour le moment on gère le  type projet
                res.render("doi", {
                  folderId: idItem,
                  type,
                  url: doi,
                  doi: doiexists,
                  data: project.project,
                  tag,
                  publicationDate,
                  creators: creators,
                  wgetDOI: wgetDOI,
                  prefixDOI: prefixDOI,
                  layout: "layout_doi_"+branchConfig,
                });
              }
            });
          });
        });
      });
    }
  });
});

// Validation du doi : écriture dans la table des DOI de l'URL
app.get(
  "/validateDOI/:type,:idItem",
  (req: Request<{ type: string; idItem: string }>, res) => {
    performRequest(`/api/validateDOI/${branchConfig},${req.params.type},${req.params.idItem}`, "GET", null, (data) => {
      const dataPatch = {
        doiComplet: data.doi,
      };
      performRequest(
        `/api/validateDOI/${branchConfig},${req.params.type},${req.params.idItem}`,
        "PATCH",
        dataPatch,
        (dataD, code) => {
          res.setHeader("Content-Type", "application/json");
          res.send(JSON.stringify(code));
        },
      );
    });
  },
);

app.get("/3diconsview3d/:idItem", (req, res) => {
  // Projet 3DIcons - ANR 2014
  // pour afficher les informations CARARE lié à un fichier
  // 1/ récupérer les infos du fichiers (dont le nom et le path)
  let carareName = "";
  const imagesTab: { name: string; id: string; link?: string }[] = [];
  performRequest(`/api/prepareImage/${req.params.idItem},${branchConfig}`, "GET", null, (data) => {
    if (data) {
      const pathYoutube = `${path.dirname(data.path)}/${data.filename.split(".")[0]}.youtube.txt`;
      const youtube: Buffer = fs.readFileSync(pathYoutube);
      const youT = youtube ?? "";
      carareName = `${data.filename.split(".")[0]}.carare.xml`;
      const pathCarare = `${path.dirname(data.path)}/${carareName}`;
      const dataGet = {
        path: pathCarare,
      };
      //2 récupérer le contenu du fichier carare
      performRequest(`/api/carare/${req.params.idItem}`, "GET", dataGet, (dataCarare, code) => {
        if (code !== 500) {
          const simpleCarare = dataCarare.carareWrap.carare;
          const heritageAssets = simpleCarare.heritageAssetIdentification;
          const collectionInformation = simpleCarare.collectionInformation;
          const resource = simpleCarare.digitalResource;
          const activity = simpleCarare.activity ?? [];
          const data2Get = {
            name: `${data.filename.split(".")[0]}.3d`,
            root_folder: data.root_folder,
          };
          //3 éventuellement récupérer les infos si un modèle 3D est dispo (meme nom de fichier mais extension différente)
          // ET  vérifier qu'on est dans le même projet
          performRequest(`/api/3DFromFile/${branchConfig},${req.params.idItem}`, "GET", data2Get, (data3D) => {
            // Si data3D.length = 0 => pas de fichier 3D à exploiter en ligne
            if (resource.length > 0) {
              if (dataCarare.type === "specific") {
                for (const r in resource) {
                  imagesTab.push({
                    name: resource[r].appellation.name._,
                    id: resource[r].appellation.id.replace("https://www.archeogrid.fr/3diconsview3d/", ""),
                  });
                }
              } else {
                // generic
                for (const r in resource) {
                  imagesTab.push({
                    name: resource[r].appellation.name._,
                    id: resource[r].isShownAt.replace("https://www.archeogrid.fr/3diconsdoc/", ""),
                    link: resource[r].isShownAt,
                  });
                }
              }
            }
            res.render("3diconsview", {
              idItem: req.params.idItem,
              carareType: dataCarare.type,
              name: data.filename,
              folderId: data.fid,
              heritageAsset: heritageAssets,
              carare: simpleCarare,
              resource: resource,
              youTube: youT,
              data3D: data3D,
              imagesTab: imagesTab,
              collectionInfo: collectionInformation,
              activity: activity,
              branch: branchConfig,
              layout: "layout3dicons",
            });
          });
        } else {
          req.flash("error", "No information to display");
        }
      });
    }
  });
});

app.route("/actor/:idItem").get((req, res) => {
  // Page d'infos sur une personne (pas forcément une personne qui a un compte mais une personne qui a donné des renseignements)
  performRequest(`/api/actor/${branchConfig},${req.params.idItem}`, "GET", null, (data) => {
    if (helpers.isObjectEmpty(data)) {
      req.flash("error", "Actor does not exist");
    } else {
      res.render("actor", {
        actor: data,
        layout: "layoutbis",
      });
    }
  });
});

app.route("/organization/:idItem").get((req, res) => {
  // Page d'infos sur une organization (pas forcément une entity qui est recensé mais une organization qui a donné des renseignements)
  performRequest(`/api/organization/${branchConfig},${req.params.idItem}`, "GET", null, (data) => {
    if (helpers.isObjectEmpty(data)) {
      req.flash("error", "Organization does not exist");
    } else {
      res.render("organization", {
        organization: data,
        layout: "layoutbis",
      });
    }
  });
});

app.get("/3diconsdoc/:idItem", (req, res) => {
  // Projet 3DIcons - ANR 2014
  // pour afficher les informations CARARE lié à un fichier images
  // 1/ récupérer les infos du fichiers (dont le nom et le path)
  const carareName = "";
  const imagesTab: { name: string; id: string; link?: string }[] = [];
  performRequest(`/api/prepareImage/${req.params.idItem},${branchConfig}`, "GET", null, (data) => {
    if (data) {
      const pathYoutube = `${path.dirname(data.path)}/${data.filename.split(".")[0]}.youtube.txt`;
      const youtube: Buffer = fs.readFileSync(pathYoutube);
      const youT = youtube ? youtube : "";
      // TODO ? récupérer le répertoire des images et dedans celui-ci récupérer le fichier generic xml
      //  qui peut avoir plusieurs noms différents ou bien toujours le même : generic_carare.xml ?
      //carareName = data.filename.split('.')[0] +'.carare.xml'
      const pathCarare = `${path.dirname(data.path)}/generic.carare.xml`;
      const dataGet = {
        path: pathCarare,
      };
      //2 récupérer le contenu du fichier carare
      performRequest(`/api/carare/${req.params.idItem}`, "GET", dataGet, (dataCarare, code) => {
        if (code !== 500) {
          const simpleCarare = dataCarare.carareWrap.carare;
          const heritageAssets = simpleCarare.heritageAssetIdentification;
          const collectionInformation = simpleCarare.collectionInformation;
          const resource = simpleCarare.digitalResource;

          if (resource.length > 0) {
            for (const r in resource) {
              imagesTab.push({
                name: resource[r].appellation.name._,
                id: resource[r].isShownAt.replace("https://www.archeogrid.fr/3diconsdoc/", ""),
              });
            }
          }
          res.render("3diconsdoc", {
            idItem: req.params.idItem,
            name: data.filename,
            folderId: data.fid,
            heritageAsset: heritageAssets,
            carare: simpleCarare,
            resource: resource,
            youTube: youT,
            imagesTab: imagesTab,
            collectionInfo: collectionInformation,
            layout: "layout3dicons",
          });
        } else {
          req.flash("error", "No information to display");
        }
      });
    }
  });
});

app.get("/heritageAsset/:idHA", (req, res) => {
  let idUser = 0;
  let Rights = 0;
  if (req.user) {
    idUser = req.user.id;
    if (req.user.user_status === "admin") Rights = 1;
  }

  performRequest(`/api/heritageAsset/${branchConfig},${req.params.idHA}`, "GET", null, (dataHA) => {
    performRequest(`/api/heritageAssetFile/${branchConfig},${req.params.idHA}`, "GET", null, (relatedData) => {
      // update pour afficher une image repr il faut le folder (2023 02 02)
      if (dataHA.id_file_representative !== "0") {
        // on récupère le folder de l'image
        performRequest(`/api/prepareImage/${dataHA.id_file_representative},${branchConfig}`, "GET", null, (data) => {
          res.render("heritageAsset", {
            data: dataHA,
            idFolder: data.fid,
            relation: relatedData,
            idHA: req.params.idHA,
          });
        });
      } else {
        res.render("heritageAsset", {
          data: dataHA,
          idFolder: 0,
          relation: relatedData,
          idHA: req.params.idHA,
        });
      }
    });
  });
});

// create nested routes because admin and scribe routers use auth middlewares for every route
app.use("/admin", adminRouter);
app.use("/scribe", scribeRouter);
app.use(authRouter);
app.use(collectionsRouter);
app.use(exploreRouter);
app.use(editRouter);
app.use(fileRouter);
app.use(imageRouter);
app.use(projectsRouter);
app.use(thesaurusRouter);
app.use(viewer3DRouter);
app.use("/karnak", karnakRouter);

app.route("/activity/:idItem").get((req, res) => {
  // Page d'infos sur une activité / un évenement  ... rattaché à un bien patrimonial / HA  ?? cd CARARE
  performRequest(`/api/activity/${branchConfig},${req.params.idItem}`, "GET", null, (data) => {
    if (helpers.isObjectEmpty(data)) {
      req.flash("error", "Activity does not exist");
    } else {
      res.render("activity", {
        data: data,
        idAC: req.params.idItem,
        layout: "layoutbis",
      });
    }
  });
});

app.route("/licenses").get((req, res) => {
  performRequest(`/api/license/${branchConfig},${i18n.getLocale(req)}`, "GET", null, (data) => {
    if (helpers.isObjectEmpty(data)) {
      req.flash("error", "Licenses information does not exist");
    } else {
      res.render("license", {
        licenses: data,
      });
    }
  });
});

app.get("/oai", (req: Request<unknown, unknown, unknown, Record<string, string>>, res) => {
  if (Object.keys(req.query).length === 0) {
    res.render("oai");
    return;
  }
  const query = querystring.stringify(req.query);

  const options = {
    host: server_host,
    port: server_port,
    path: `/api/oai/${branchConfig}?${query}`,
    method: "GET",
  };

  const httpReq = http.get(options, (response) => {
    res.writeHead(response.statusCode ?? 500, response.headers);
    response.pipe(res);
  });
});

app.post("/oai", (req, res) => {
  const query = querystring.stringify(req.body);

  const options = {
    host: server_host,
    port: server_port,
    path: `/api/oai/${branchConfig}?${query}`,
    method: "GET",
  };

  const httpReq = http.get(options, (response) => {
    res.writeHead(response.statusCode ?? 500, response.headers);
    response.pipe(res);
  });
});

app.route("/projectFolders,:projectId").get(async (req, res) => {
  const project_id = Number.parseInt(req.params.projectId);
  if(isNaN(project_id)) {
    res.status(404).send("Invalid project id");
    return;
  }

  const data = await archeogrid_client.projects.getProjectFolders.query({ branch: branchConfig, project_id });
  res.json(data);
});

app.route("/folderItems,:folderId").get(async (req, res) => {
  const folder_id = Number.parseInt(req.params.folderId);
  if(isNaN(folder_id)) {
    res.status(404).send("Invalid folder id");
    return;
  }

  const data = await archeogrid_client.folders.getFolderItems.query({ branch: branchConfig, folder_id });
  res.json(data);
});   

async function linkObject(req: Express.Request, array: { dir: string; name: string; objectId: number }[]) {
  for await (const element of array) {
    const { dir, name, objectId } = element;
    await req.fetchApi("/api/object/corpus", { file: dir + name, objectId: objectId.toString() }, "PATCH");
  }

  return array.length;
}

// route for handling 404 requests(unavailable routes)
app.use((req, res) => {
  if (req.url.includes("3DOnline")) {
    console.log("3DOnline pass through : ", req.url);
    res.end(fs.readFileSync(req.url));
  } else {
    res.status(404).send("Hmmm, C'est embarrassant...impossible de trouver cette page...");
  }
});

const PORT = app_port;

app.listen(PORT, () => {
  console.log(`ArcheoGRID app V2 started and listening on port ${PORT}`);
});
