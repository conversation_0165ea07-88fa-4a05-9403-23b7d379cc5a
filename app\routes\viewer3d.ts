import { type Request, type Response, Router } from "express";
import as<PERSON><PERSON><PERSON><PERSON> from "express-async-handler";
import fs from "node:fs";
import store from "store";

import type { Branch, Responses } from "../../server/types/api";

import {
  acceptedImageFormats,
  acceptedPrevisuFormat,
  acceptedVideoFormats,
  branchConfig,
  permChecker,
  readAndParse3DFile,
  viewerFormat,
} from "../tools/globals";
import * as helpers from "../tools/helpers_tools";
import { archeogrid_client } from "../tools/request";

const md5 = require("md5");

const viewer3d = Router();

const renderViewer3D =
  (suffix = "") =>
  async (
    req: Request<{
      srcImg3d: string;
      idFolder: string;
      branche: "conservatoire3d" | "pft3d" | "corpus";
    }>,
    res: Response,
  ) => {
    const { lang } = res.locals;
    if (!req.params.srcImg3d) {
      req.flash("error", "Unable to load 3d file (no source file specified)");
      return;
    }

    let config_file: string | undefined = store.get(`image3d_${req.params.srcImg3d}`);

    if (!config_file) {
      const data = await archeogrid_client.files.getFileFromHash.query({
        branch: req.params.branche,
        hash: req.params.srcImg3d,
      });
      config_file = data?.path;
    }

    if (!config_file) {
      req.flash("error", `Unable to get 3D file from hash (${req.params.srcImg3d})`);
      return;
    }

    const config_data = await readAndParse3DFile(config_file);
    if (!config_data) {
      req.flash("error", `Unable to read 3D file (${config_file})`);
      return;
    }

    const current_path = `${config_file.substring(0, config_file.lastIndexOf("/"))}/`;
    let clouds: { cloudjs: string; name: string }[] = [];

    if (config_data.type === "3DHOP") {
      for (const m in config_data.data["3dhop"].params.meshes) {
        config_data.data["3dhop"].params.meshes[m].url = `${config_file.substring(
          0,
          config_file.lastIndexOf("/"),
        )}/${config_data.data["3dhop"].params.meshes[m].url}`;
      }
    } else {
      const { potree } = config_data.data;

      if (potree.filenames.length >= 1) {
        clouds = potree.filenames.map((file_name) => {
          const pathDetail = file_name.split("/");
          let name = file_name;
          if (pathDetail.length === 2)
            // path is folder/cloud.js
            name = pathDetail[0];
          else if (pathDetail.length > 2)
            // path is folder/name/cloud.js
            name = pathDetail[1];
          if (name.startsWith("_"))
            // remove leading _
            name = name.substring(1);

          return { cloudjs: current_path + file_name, name: name };
        });
      }
    }

    const template_path =
      config_data.type === "3DHOP"
        ? config_data.data["3dhop"].template.path
        : config_data.data.potree.template.path;

    // display viewer
    if (config_data.type === "3DHOP") {
      let template_file = config_data.data["3dhop"].template.file;
      if (template_file.endsWith(".ejs"))
        template_file = template_file.substring(0, template_file.lastIndexOf(".ejs"));
      template_file = template_file.replace("3DHOP", "3DHop") + suffix;
      res.render(template_file, {
        path_to: `3DViewer/${template_path}/`,
        params_3dhop: JSON.stringify(config_data.data["3dhop"].params),
        layout: "layout_3d",
      });
    } else {
      let template_file = config_data.data.potree.template.file;
      if (template_file.endsWith(".ejs"))
        template_file = template_file.substring(0, template_file.lastIndexOf(".ejs"));
      template_file = template_file.replace("Potree", "potree") + suffix;
      res.render(template_file, {
        path_to: `3DViewer/${template_path}/`,
        cloud_js: current_path + config_data.data.potree.filenames[0],
        cloud_name: config_data.data.potree.name,
        clouds: JSON.stringify(clouds),
        layout: "layout_3d",
        params: JSON.stringify(config_data.data.potree.params),
        credits: config_data.data.potree.credits,
        lang,
      });
    }
  };

viewer3d
  .route("/viewer3d,:srcImg3d,:idFolder,:branche")
  .get(asyncHandler(permChecker), asyncHandler(renderViewer3D()));

viewer3d
  .route("/viewer3dless,:srcImg3d,:idFolder,:branche")
  .get(asyncHandler(permChecker), asyncHandler(renderViewer3D("_nude")));

viewer3d.get(
  "/visionneuseObj,:idObj,:idFile-:idFolder,:root-:context,:rights",
  asyncHandler(
    async (
      req: Request<{
        root: Branch;
        idObj: string;
        idFolder: string;
        idFile: string;
        context: unknown;
        rights: string;
      }>,
      res,
    ) => {
      const branch = req.params.root;
      const object_id = Number.parseInt(req.params.idObj);
      const folder_id = Number.parseInt(req.params.idFolder);
      const file_id = Number.parseInt(req.params.idFile);
      const visioUser = req.user ?? { id: 0, user_status: "guest" };
      const language = res.locals.lang ?? "fr";

      const type = "object";
      let modelObject = "DublinCore"; // donner un model par default : DublinCore ?
      const { lang } = res.locals;
      let Wrights = false;
      const image_data = {
        urlTarget: "",
        download: "",
        srcImgHd: "",
        srcImg3d: "",
        srcImgThumb: "",
        srcImgSmall: "",
      };

      const mainFolder: Responses["folders"]["mainFolder"] &
        Partial<Responses["projects"]["projectsFull"]> =
        await archeogrid_client.folders.mainFolder.query({
          branch,
          language,
          folder_id,
        });

      if (!mainFolder?.mainfolder) throw new Error("Main folder not found!");

      if (branch === branchConfig) {
        // get image logo ?
        const project = await archeogrid_client.projects.projectsFull.query({
          branch: branch,
          language: res.locals.lang,
          project_id: Number.parseInt(mainFolder.mainfolder),
        });
        if (!project) throw new Error("Project does not exist!");

        mainFolder.image = project.image;
      }

      const dataobject = await req.fetchApi(`/api/objectId/${branch},${object_id}`);
      const comments = await archeogrid_client.comments.getComments.query({
        branch,
        type,
        user_id: req.user?.id,
        item_id: object_id,
      });
      const status = await archeogrid_client.folders.getStatus.query({ branch, folder_id });
      // if (status['model_file'] !== '') modelObject = status['model_file'] // Nous sommes dans la visionneuse pour les objets
      if (status.folder_passport && status.folder_passport !== "")
        modelObject = status.folder_passport;
      // pour les modèles d'objets par défaut, on regarde dans la table des folders, le parametre folder_passport
      if (status.status === "private") {
        if (!req.user) {
          res.send(req.__("message_notlogged"));
          return;
        }

        Wrights =
          (req.user.user_status === "user" &&
            Boolean(req.user.write.includes(Number.parseInt(req.params.idFolder)))) ||
          (req.user.user_status === "scribe" &&
            Boolean(req.user.read.includes(Number.parseInt(req.params.idFolder)))) ||
          req.user.user_status === "admin";
        const allrights = helpers.privateRights(
          req.user.read,
          req.user.write,
          req.user.user_status,
          req.user.id,
          folder_id,
        );

        if (!allrights.Rrights) {
          res.send(" you are logged in but not access to this folder ... sorry");
          return;
        }
        // folder accessible OK
        const data = await archeogrid_client.files.prepareImage.query({ branch, file_id });
        const file_path = data.path;
        const file = data.filename;
        const short_path = `${file_path.substring(0, file_path.lastIndexOf("/"))}/`;

        if (data.file_ext === "url") {
          const url_content = fs.readFileSync(data.path, "utf-8");
          const parsed_url_content = JSON.parse(url_content);
          if (parsed_url_content.url) {
            image_data.urlTarget = parsed_url_content.url;
          }
          image_data.download = `${short_path}_html/${file.replace("url", "html")}`;
          image_data.srcImgHd = helpers.setImageHd(
            file.replace("url", "html"),
            `${short_path}_html/${file.replace("url", "html")}`,
          );
          image_data.srcImgThumb = helpers.setImageThumb(file, file_path);
          image_data.srcImgSmall = helpers.setImageSmall(file, file_path);
        } else if (data.file_ext === "3d") {
          // TODO : récupérer ce qui a été fait dans la partie explore
          const srcImg3d = md5(file_path);
          store.set(`image3d_${srcImg3d}`, file_path);
          image_data.srcImg3d = srcImg3d;

          if (data.path) {
            const data3d = await readAndParse3DFile(data.path);
            if (data3d == null) {
              req.flash("error", "Unable to load .3d file");
              return;
            }
            if (data3d.type === "3DHOP") {
              if (data3d.data["3dhop"].thumbnail) {
                //thumb_name = path_tocourt + data3d['3dhop']['thumbnail']
                const thumb_name = short_path + data3d.data["3dhop"].thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                image_data.srcImgThumb = srcImgThumb;
                image_data.srcImgSmall = srcImgSmall;
              }
            } else {
              if (data3d.data.potree.thumbnail) {
                const thumb_name = short_path + data3d.data.potree.thumbnail;
                const srcImgThumb = md5(thumb_name);
                const srcImgSmall = srcImgThumb;
                store.set(`thumbnail_${srcImgThumb}`, thumb_name);
                store.set(`small_${srcImgThumb}`, thumb_name);

                image_data.srcImgThumb = srcImgThumb;
                image_data.srcImgSmall = srcImgSmall;
              }
            }
          } else {
            req.flash("error", "Unable to get path for 3d file");
          }
        } else if (data.file_ext === "json") {
          image_data.srcImgThumb = "json";
          image_data.srcImgSmall = "json";
          //data['srcImgSmall'] = helpers.setImageSmallFake(file, pathFile)
          image_data.srcImgHd = helpers.setImageHd(file, file_path);
        } else if (data.file_ext === "zip") {
          image_data.srcImgThumb = "zip";
          image_data.srcImgSmall = "zip";
          image_data.srcImgHd = helpers.setImageHd(file, file_path);
        } else if (data.file_ext === "png") {
          image_data.srcImgThumb = helpers.setImageThumbFake(file, file_path);
          image_data.srcImgSmall = helpers.setImageSmallFake(file, file_path);
          image_data.srcImgHd = helpers.setImageHd(file, file_path);
        } else if (acceptedPrevisuFormat.includes(data.file_ext.toLowerCase())) {
          image_data.srcImgThumb = helpers.setImageThumb(file, file_path);
          image_data.srcImgSmall = helpers.setImageSmall(file, file_path);
          image_data.srcImgHd = helpers.setImageHd(file, file_path);
        } else {
          image_data.srcImgThumb = "";
          image_data.srcImgSmall = "";
          image_data.srcImgHd = helpers.setImageHd(file, file_path);
        }
        // TODO : watermark ??
        // les modeles seulement pour le projet
        const model = await req.fetchApi(
          `/api/metadatamodelProject/${req.params.root},${mainFolder.mainfolder},${lang}`,
        );

        const modellist = [];
        const modelComplet = [];
        // On récupère tous les modèles utilisés par le projet pour le type d'item visionné
        for (const model_element of model) {
          if (model_element.metadata_type === type) {
            modellist.push(model_element.name);
            modelComplet.push({
              name: model_element.name,
              label: model_element.label,
              description: model_element.description,
              jsonInfo: model_element.json_info, // pour pouvoir proposer d'importer les json dans le passeport
              id: model_element.id, // si ce n'est pas déjà fait
            });
          }
        }

        const thes = await req.fetchApi(`/api/allThesaurusName/${branch},${lang}`);
        const datathesPactols = await req.fetchApi(
          `/api/ThesaurusPactolsItem/${branchConfig},${type},${object_id}`,
          {
            lng: res.locals.lang,
          },
        );
        const datathesPactolsGeo = await req.fetchApi(
          `/api/ThesaurusPactolsGeoItem/${branchConfig},${type},${object_id}`,
        );
        const datathesMaison = await req.fetchApi(
          `/api/ThesaurusMaisonItem/${branchConfig},${type},${object_id}`,
        );
        const metadata = await archeogrid_client.metadata.allMetadata.query({
          branch,
          language,
          type,
          id: object_id,
        });

        helpers.parseAdvancedModelItems(metadata);

        //tous les item de thesaurus multli (sans modèle de métadonnées forcément)
        const thesmulti = await req.fetchApi(
          `/api/ThesaurusMultiItem/${branch},${type},${object_id}`,
          {
            lng: res.locals.lang,
          },
        );
        // TODO :  Tous les items rattachés à cet object
        const itemdatafromObj = await req.fetchApi(
          `/api/itemsFromObject/${branch},${object_id}`,
        );
        const itemsViewer = [];
        let nbObjects = 0;
        let nbFiles = 0;
        let nbJson = 0;

        for (const item_data of itemdatafromObj) {
          if (item_data.type === "object") {
            nbObjects++;
          } else if (item_data.type === "file") {
            if (item_data.name.substr(item_data.name.lastIndexOf(".") + 1) === "json") {
              if (item_data.json_file) nbJson++;
            }
            nbFiles++;
          }

          if (item_data.path) {
            itemsViewer.push({
              type: item_data.type,
              name: item_data.name,
              id: item_data.image_id,
              itemId: item_data.id,
              itemFolder: item_data.item_folder,
              filename: item_data.image_filename,
              idfolder: item_data.image_folder,
              file: helpers.getFileName(item_data.path),
              srcImgThumb: helpers.setImageThumb(
                helpers.getFileName(item_data.path),
                item_data.path,
              ),
            });
          } else {
            // This handles objects/files without a representative image (path)
            itemsViewer.push({
              type: item_data.type,
              name: item_data.name,
              id: item_data.image_id, // Will be '0' or null
              itemId: item_data.id,
              itemFolder: item_data.item_folder,
              filename: "",
              idfolder: 0,
              file: "",
              srcImgThumb: "",
            });
          }
        }
        const objectViewer = await req.fetchApi(`/api/objectViewer/${branch},${object_id}`);
        // On récupère un path correspondant au fichier dont il existte un viewer. si ce fichier à un small, on s'en sert pour afficher
        // la miniature et pointer vers le viewer avec cette prévisu :
        const imageViewer = [];
        for (const obj of objectViewer) {
          if (obj.path) {
            imageViewer.push({
              value: obj.value,
              id: obj.id,
              folderId: obj.id_folder,
              file: helpers.getFileName(obj.path),
              srcImgThumb: helpers.setImageThumb(helpers.getFileName(obj.path), obj.path),
            });
          }
        }

        const dataTag = await req.fetchApi(`/api/tag/${branch},object,${object_id}`);
        const dataLicense = await req.fetchApi(
          `/api/licenseItem/${branch},${object_id},object,${lang}`,
        );

        const periodo = await archeogrid_client.thesaurus.thesaurusPeriodO.query({ branch });

        let modelview = (req.params.context === 'v') ? 'visionneusevitrine' : 'visionneuseObj';
        let layout = (req.params.context === 'v') ? 'layout' : 'layout_simple';

        // Set the branch variable for proper CSS loading
        res.locals.branch = branch;

        // Get folder status to determine if files are downloadable
        const folderStatus = await archeogrid_client.folders.getStatus.query({
          branch,
          folder_id: folder_id,
        });

        const project_id = Number.parseInt(mainFolder.mainfolder);
        const thesaurus_info = await archeogrid_client.thesaurus.getAllThesaurusForProject.query({ branch, language, project_id });

        res.render(modelview, {
          idObj: req.params.idObj,
          dataObject: dataobject,
          idFile: req.params.idFile,
          user: visioUser,
          root: req.params.root,
          context: req.params.context,
          folderId: req.params.idFolder,
          image: { ...data, ...image_data },
          imageFormat: acceptedImageFormats,
          videoFormat: acceptedVideoFormats,
          viewerFormat: viewerFormat,
          model: modellist,
          modelComplet: modelComplet,
          metadata,
          items: itemsViewer,
          modelObject: modelObject,
          Wrights,
          HDrights: allrights.HDrights,
          thes: thes,
          tagThesmulti: thesmulti.tagthes,
          tagPactols: datathesPactols,
          tagPactolsGeo: datathesPactolsGeo,
          tagMaison: datathesMaison,
          mainFolder: mainFolder,
          comments: comments,
          viewer: imageViewer,
          nbObjects: nbObjects,
          nbFiles: nbFiles,
          nbJson: nbJson,
          tags: dataTag,
          itemType: 'object',
          layout: layout,
          license: dataLicense,
          periodo,
          lng: res.locals.lang,
          unicos: [],
          downloadable: folderStatus.uploadable,
          doi: { doi: false },
          doi_info: {},
          size: "",
          thesaurus_info: thesaurus_info,
        });
      } else {
        // public folder OK USER NOT logged in necessarily
        // item dans un folder public : tout le monde a accès à la HD et au téléchargement
        const allrights = helpers.publicRights(visioUser.user_status);
        if (req.user) {
          Wrights =
            (req.user.user_status === "user" && req.user.write.includes(folder_id)) ||
            (req.user.user_status === "scribe" && req.user.read.includes(folder_id)) ||
            req.user.user_status === "admin";
        }

        const data = await req.fetchApi(`/api/prepareImage/${file_id},${req.params.root}`);
        const file = data.filename;
        const pathFile = data.path;
        image_data.srcImgThumb = helpers.setImageThumb(file, pathFile);
        image_data.srcImgSmall = helpers.setImageSmall(file, pathFile);
        // C'est un objet donc on a juste une image
        if (acceptedPrevisuFormat.includes(data.file_ext.toLowerCase())) {
          image_data.srcImgThumb = helpers.setImageThumb(file, pathFile);
          image_data.srcImgSmall = helpers.setImageSmall(file, pathFile);
          image_data.srcImgHd = helpers.setImageHd(file, pathFile);
        } else {
          image_data.srcImgThumb = "";
          image_data.srcImgSmall = "";
          image_data.srcImgHd = helpers.setImageHd(file, pathFile);
        }
        image_data.srcImgHd = helpers.setImageHd(file, pathFile);
        // TODO : watermark ??
        // les modeles seulement pour le projet
        const model = await req.fetchApi(
          `/api/metadatamodelProject/${req.params.root},${mainFolder.mainfolder},${lang}`,
        );

        const modellist = [];
        const modelComplet = [];
        // On récupère tous les modèles utilisés par le projet pour le type d'item visionné
        for (const model_element of model) {
          if (model_element.metadata_type === type) {
            modellist.push(model_element.name);
            modelComplet.push({
              name: model_element.name,
              label: model_element.label,
              description: model_element.description,
              jsonInfo: model_element.json_info,
              id: model_element.id,
            });
          }
        }

        const thes = await req.fetchApi(`/api/allThesaurusName/${req.params.root},${lang}`);
        const datathesPactols = await req.fetchApi(
          `/api/ThesaurusPactolsItem/${branchConfig},${type},${object_id}`,
          {
            lng: language,
          },
        );
        const datathesPactolsGeo = await req.fetchApi(
          `/api/ThesaurusPactolsGeoItem/${branchConfig},${type},${object_id}`,
        );
        const datathesMaison = await req.fetchApi(
          `/api/ThesaurusMaisonItem/${branchConfig},${type},${object_id}`,
        );
        const metadata = await archeogrid_client.metadata.allMetadata.query({
          branch,
          language,
          type,
          id: object_id,
        });

        helpers.parseAdvancedModelItems(metadata);

        // tous les item de thesaurus multli (sans modèle de métadonnées forcément)
        const thesmulti = await req.fetchApi(
          `/api/ThesaurusMultiItem/${branch},${type},${object_id}`,
          {
            lng: res.locals.lang,
          },
        );
        // TODO :  Tous les items rattachés à cet object
        const itemdatafromObj = await req.fetchApi(
          `/api/itemsFromObject/${req.params.root},${object_id}`,
        );
        const itemsViewer = [];
        let nbObjects = 0;
        let nbFiles = 0;
        let nbJson = 0;
        for (const item_data of itemdatafromObj) {
          if (item_data.type === "object") {
            nbObjects++;
          } else if (item_data.type === "file") {
            if (item_data.name.substr(item_data.name.lastIndexOf(".") + 1) === "json") {
              if (item_data.json_file) nbJson++;
            }
            nbFiles++;
          }

          if (item_data.path) {
            itemsViewer.push({
              type: item_data.type,
              name: item_data.name,
              id: item_data.image_id,
              itemId: item_data.id,
              itemFolder: item_data.item_folder,
              filename: item_data.image_filename,
              idfolder: item_data.image_folder,
              file: helpers.getFileName(item_data.path),
              srcImgThumb: helpers.setImageThumb(
                helpers.getFileName(item_data.path),
                item_data.path,
              ),
            });
          } else {
            // This handles objects/files without a representative image (path)
            itemsViewer.push({
              type: item_data.type,
              name: item_data.name,
              id: item_data.image_id, // Will be '0' or null
              itemId: item_data.id,
              itemFolder: item_data.item_folder,
              filename: "",
              idfolder: 0,
              file: "",
              srcImgThumb: "",
            });
          }
        }

        const objectViewer = await req.fetchApi(
          `/api/objectViewer/${req.params.root},${object_id}`,
        );
        // On récupère un path correspondant au fichier dont il existte un viewer. si ce fichier à un small, on s'en sert pour afficher
        // la miniature et pointer vers le viewer avec cette prévisu :
        const imageViewer = [];
        for (let i = 0; i < objectViewer.length; i++) {
          if (objectViewer[i].path) {
            imageViewer.push({
              value: objectViewer[i].value,
              id: objectViewer[i].id,
              folderId: objectViewer[i].id_folder,
              file: helpers.getFileName(objectViewer[i].path),
              srcImgThumb: helpers.setImageThumb(
                helpers.getFileName(objectViewer[i].path),
                objectViewer[i].path,
              ),
            });
          }
        }

        const dataTag = await req.fetchApi(`/api/tag/${req.params.root},object,${object_id}`);
        const dataLicense = await req.fetchApi(
          `/api/licenseItem/${req.params.root},${object_id},object,${lang}`,
        );

        const periodo = await archeogrid_client.thesaurus.thesaurusPeriodO.query({ branch });

        let modelview = (req.params.context === 'v') ? 'visionneusevitrine' : 'visionneuseObj';
        let layout = (req.params.context === 'v') ? 'layout' : 'layout_simple';

        // Set the branch variable for proper CSS loading
        res.locals.branch = branch;

        let modelFile = "DublinCore"; // Default model

        // Default downloadable to true for admin users
        let downloadable = true;

        // Set rights based on user status and folder permissions
        const rights = Boolean(Number.parseInt(req.params.rights)) ||
                      (req.user && (req.user.user_status === 'admin' ||
                                   (req.user.user_status === 'scribe' && req.user.read.includes(folder_id)) ||
                                   (req.user.user_status === 'user' && req.user.write.includes(folder_id))));

        // Fetch unicos for the file
        const idProjectStr = mainFolder.mainfolder;
        const project_id = Number.parseInt(mainFolder.mainfolder);

        let unicos: any[] = [];

        if (!isNaN(project_id) && project_id > 0 && file_id > 0) {
          unicos = await archeogrid_client.unico.getFileUnicos.query({
            branch,
            language,
            project_id,
            file_id,
          });
        } else {
          // Log if we are skipping the call due to invalid IDs, helps in debugging
          console.log(`Skipping getFileUnicos call for file_id: ${file_id}, project_id: ${project_id} (original idProjectStr: ${idProjectStr})`);
        }

        // Fetch DOI data
        const dataDOI = await req.fetchApi(`/api/DOIItem/${branch},object,${object_id}`);
        let doi_info = {};
        try {
          // First try to get object info from objectFromId endpoint
          const response = await req.fetchApi(`/api/objectFromId/${branch},${object_id}`);
          // Check if the response contains an error property (indicating it's not valid JSON)
          if (response.error) {
            console.error(`Error fetching object info: ${response.error}`);
            // If we have raw content, try to extract useful information
            if (response.rawContent) {
              console.log(`Raw content preview: ${response.rawContent.substring(0, 100)}...`);
              // Try to extract useful information from the raw content
              try {
                // Some APIs might return JSON with incorrect content-type
                const parsedContent = JSON.parse(response.rawContent);
                doi_info = parsedContent;
                console.log('Successfully parsed raw content as JSON');
              } catch (parseError) {
                console.log('Raw content is not valid JSON');

                // Try fallback to objectId endpoint if objectFromId fails
                try {
                  console.log('Trying fallback to objectId endpoint...');
                  const fallbackResponse = await req.fetchApi(`/api/objectId/${branch},${object_id}`);
                  if (!fallbackResponse.error) {
                    doi_info = fallbackResponse;
                    console.log('Successfully fetched object info from fallback endpoint');
                  } else {
                    console.error(`Fallback also failed: ${fallbackResponse.error}`);
                  }
                } catch (fallbackError) {
                  console.error(`Exception in fallback: ${fallbackError}`);
                }
              }
            } else if (response.htmlContent) {
              console.log(`HTML content detected with title: ${response.title || 'Unknown'}`);

              // Try fallback to objectId endpoint if objectFromId returns HTML
              try {
                console.log('Trying fallback to objectId endpoint...');
                const fallbackResponse = await req.fetchApi(`/api/objectId/${branch},${object_id}`);
                if (!fallbackResponse.error) {
                  doi_info = fallbackResponse;
                  console.log('Successfully fetched object info from fallback endpoint');
                } else {
                  console.error(`Fallback also failed: ${fallbackResponse.error}`);
                }
              } catch (fallbackError) {
                console.error(`Exception in fallback: ${fallbackError}`);
              }
            }
          } else {
            doi_info = response;
          }
        } catch (error) {
          console.error(`Exception fetching object info: ${error}`);

          // Try fallback to objectId endpoint if objectFromId throws an exception
          try {
            console.log('Trying fallback to objectId endpoint after exception...');
            const fallbackResponse = await req.fetchApi(`/api/objectId/${branch},${object_id}`);
            if (!fallbackResponse.error) {
              doi_info = fallbackResponse;
              console.log('Successfully fetched object info from fallback endpoint');
            } else {
              console.error(`Fallback also failed: ${fallbackResponse.error}`);
            }
          } catch (fallbackError) {
            console.error(`Exception in fallback: ${fallbackError}`);
          }
        }

        // Set size variable
        let size = "";

        const thesaurus_info = await archeogrid_client.thesaurus.getAllThesaurusForProject.query({
          branch,
          language,
          project_id,
        });

        res.render(modelview, {
          idObj: req.params.idObj,
          dataObject: dataobject,
          idFile: req.params.idFile,
          user: visioUser,
          root: req.params.root,
          context: req.params.context,
          folderId: req.params.idFolder,
          image: { ...data, ...image_data },
          imageFormat: acceptedImageFormats,
          videoFormat: acceptedVideoFormats,
          viewerFormat: viewerFormat,
          model: modellist,
          modelComplet: modelComplet,
          metadata,
          items: itemsViewer,
          modelObject: modelObject,
          Wrights,
          HDrights: allrights.HDrights,
          thes: thes,
          tagThesmulti: thesmulti.tagthes,
          tagPactols: datathesPactols,
          tagPactolsGeo: datathesPactolsGeo,
          tagMaison: datathesMaison,
          mainFolder,
          comments: comments,
          viewer: imageViewer,
          nbObjects: nbObjects,
          nbFiles: nbFiles,
          nbJson: nbJson,
          tags: dataTag,
          itemType: 'object',
          layout: layout,
          license: dataLicense,
          lng: res.locals.lang,
          downloadable: downloadable,
          rights: rights,
          unicos: unicos,
          doi: dataDOI,
          doi_info: doi_info,
          size: size,
          periodo,
          modelFile,
          thesaurus_info
        });
      }
    },
  ),
);

/**
 * __     ___     _
 * \ \   / (_)___(_) ___  _ __  _ __   ___ _   _ ___  ___
 * \  \ / /| / __| |/ _ \| '_ \| '_ \ / _ \ | | / __|/ _ \
 *  \  V / | \__ \ | (_) | | | | | | |  __/ |_| \__ \  __/
 *   \__/  |_|___/_|\___/|_| |_|_| |_|\___|\__,_|___/\___|
 *
 *
 */
// context : p=page, m=modal , ( o = object ? d = ?)
// rights : est-ce que le user a le droit en ecriture ? admin ou scribe ? 0/1
// todo : distinguer user / scribe / admin => user  : droit en lecture mais pas en écriture
// sauf si on lui a mis ! (nouveauté 23/03/2020)
viewer3d.get(
  "/visionneuse,:idFile-:idFolder,:root-:context,:rights",
  asyncHandler(async (req, res) => {
    if (
      req.params.root !== "pft3d" &&
      req.params.root !== "corpus" &&
      req.params.root !== "conservatoire3d"
    )
      return;
    const branch = req.params.root;
    const visioUser = req.user ?? { id: 0, user_status: "guest" };
    const file_id = Number.parseInt(req.params.idFile);
    const folder_id = Number.parseInt(req.params.idFolder);
    const language = res.locals.lang ?? "fr";
    const type = "file";
    let modelFile = "DublinCore"; // donner un model par default : DublinCore ?
    let HDrights = false; // Les droits de télécharger / lire la HD
    let rights = Boolean(Number.parseInt(req.params.rights));
    let size = "";
    const idProjectStr = (await req.fetchApi(`/api/getProjectId/${branch},${file_id}`)) as string;
    const project_id = Number.parseInt(idProjectStr); // Will be NaN if idProjectStr is null or not a number string
    
    let unicos: any[] = []; // Initialize unicos to an empty array

    if (!isNaN(project_id) && project_id > 0 && file_id > 0) {
      unicos = await archeogrid_client.unico.getFileUnicos.query({
        branch,
        language,
        project_id,
        file_id,
      });
    } else {
      // Log if we are skipping the call due to invalid IDs, helps in debugging
      console.log(`Skipping getFileUnicos call for file_id: ${file_id}, project_id: ${project_id} (original idProjectStr: ${idProjectStr})`);
    }

    const thesaurus_info = await archeogrid_client.thesaurus.getAllThesaurusForProject.query({
      branch,
      language,
      project_id: project_id,
    });

    const dataDOI = await req.fetchApi(`/api/validateDOI/${branch},file,${file_id}`);
    const mainFolder: Responses["folders"]["mainFolder"] &
      Partial<Responses["projects"]["projectsFull"]> =
      await archeogrid_client.folders.mainFolder.query({ branch, folder_id });

    if (!mainFolder?.mainfolder) throw new Error("Main folder not found!");

    if (branch === branchConfig) {
      // get image logo ?
      const project = await archeogrid_client.projects.projectsFull.query({
        branch: branch,
        language: res.locals.lang,
        project_id: Number.parseInt(mainFolder.mainfolder),
      });
      if (!project) throw new Error("Project does not exist!");
      mainFolder.image = project.image;
    }

    const dataLicense = await req.fetchApi(
      `/api/licenseItem/${branch},${file_id},file,${language}`,
    );
    const comments = await archeogrid_client.comments.getComments.query({
      branch,
      type,
      user_id: req.user?.id,
      item_id: file_id,
    });

    const status = await archeogrid_client.folders.getStatus.query({ branch, folder_id });
    if (status.model_file !== "") modelFile = status.model_file;
    if (status.status === "private") {
      if (!req.user) {
        res.send(req.__("message_notlogged"));
        return;
      }
      const ffArray = Object.values(req.user.ff);
      if (ffArray.includes(folder_id)) {
        // le folder des dans la liste ff !
        res.send(" you are logged in but not access to this folder... sorry");
        return;
      }
      // folder accessible OK
      // si user simple user mais folder accessible en write : on change les droits pour lui mettre 1 au lieu de 0

      rights =
        (req.user.user_status === "user" && req.user.write.includes(folder_id)) ||
        (req.user.user_status === "scribe" && req.user.read.includes(folder_id)) ||
        req.user.user_status === "admin";
      if (req.user.user_status !== "guest") HDrights = true; // folder accessible en HD
      const data = await archeogrid_client.files.prepareImage.query({ branch, file_id });
      const pathFile = data.path;
      const file = data.filename;
      const short_path = `${pathFile.substring(0, pathFile.lastIndexOf("/"))}/`;
      const image_data = {
        urlTarget: "",
        download: "",
        srcImgHd: "",
        srcImgThumb: "",
        srcImgSmall: "",
        srcImg3d: "",
      };

      if (data.file_ext === "url") {
        const contenuUrl = fs.readFileSync(data.path, "utf-8");
        const urlFich = JSON.parse(contenuUrl);
        if (Object.hasOwn(urlFich, "url")) {
          image_data.urlTarget = urlFich.url;
        }
        image_data.download = `${short_path}_html/${file.replace("url", "html")}`;
        image_data.srcImgHd = helpers.setImageHd(
          file.replace("url", "html"),
          `${short_path}_html/${file.replace("url", "html")}`,
        );
        image_data.srcImgThumb = helpers.setImageThumb(file, pathFile);
        image_data.srcImgSmall = helpers.setImageSmall(file, pathFile);
      } else if (data.file_ext === "3d") {
        // TODO : récupérer ce qui a été fait dans la partie explore
        const srcImg3d = md5(pathFile);
        store.set(`image3d_${srcImg3d}`, pathFile);
        image_data.srcImg3d = srcImg3d;

        if (!data.path) {
          req.flash("error", "Unable to get path for 3d file");
          return;
        }

        const data3d = await readAndParse3DFile(data.path);
        if (data3d == null) {
          req.flash("error", "Unable to load .3d file");
          return;
        }
        if (data3d.type === "3DHOP") {
          if (data3d.data["3dhop"].thumbnail) {
            const thumb_name = short_path + data3d.data["3dhop"].thumbnail;
            const srcImgThumb = md5(thumb_name);
            const srcImgSmall = srcImgThumb;
            store.set(`thumbnail_${srcImgThumb}`, thumb_name);
            store.set(`small_${srcImgThumb}`, thumb_name);

            image_data.srcImgThumb = srcImgThumb;
            image_data.srcImgSmall = srcImgSmall;
          }
        } else {
          if (data3d.data.potree.thumbnail) {
            const thumb_name = short_path + data3d.data.potree.thumbnail;
            const srcImgThumb = md5(thumb_name);
            const srcImgSmall = srcImgThumb;
            store.set(`thumbnail_${srcImgThumb}`, thumb_name);
            store.set(`small_${srcImgThumb}`, thumb_name);

            image_data.srcImgThumb = srcImgThumb;
            image_data.srcImgSmall = srcImgSmall;
          }
        }
      } else if (data.file_ext === "json") {
        image_data.srcImgThumb = "json";
        image_data.srcImgSmall = "json";
        image_data.srcImgHd = helpers.setImageHd(file, pathFile);
      } else if (data.file_ext === "zip") {
        image_data.srcImgThumb = "zip";
        image_data.srcImgSmall = "zip";
        image_data.srcImgHd = helpers.setImageHd(file, pathFile);
      } else if (data.file_ext === "png") {
        image_data.srcImgThumb = helpers.setImageThumbFake(file, pathFile);
        image_data.srcImgSmall = helpers.setImageSmallFake(file, pathFile);
        image_data.srcImgHd = helpers.setImageHd(file, pathFile);
      } else if (acceptedPrevisuFormat.includes(data.file_ext.toLowerCase())) {
        image_data.srcImgThumb = helpers.setImageThumb(file, pathFile);
        image_data.srcImgSmall = helpers.setImageSmall(file, pathFile);
        image_data.srcImgHd = helpers.setImageHd(file, pathFile);
      } else {
        image_data.srcImgThumb = "";
        image_data.srcImgSmall = "";
        image_data.srcImgHd = helpers.setImageHd(file, pathFile);
      }
      // TODO : watermark ??

      // les modeles seulement pour le projet
      const model = await req.fetchApi(
        `/api/metadatamodelProject/${req.params.root},${mainFolder.mainfolder},${language}`,
      );

      const model_list = [];
      const modelComplet = [];
      // On récupère tous les modèles utilisés par le projet pour le type d'item visionné
      for (const i in model) {
        if (model[i].metadata_type === type) {
          model_list.push(model[i].name);
          modelComplet.push({
            name: model[i].name,
            label: model[i].label,
            description: model[i].description,
          });
        }
      }

      const thes = await req.fetchApi(`/api/allThesaurusName/${branch},${language}`);
      const fileViewer = await req.fetchApi(`/api/fileViewer/${branch},${file_id}`);

      const imageViewer = [];
      for (const file_element of fileViewer) {
        if (file_element.path) {
          imageViewer.push({
            value: file_element.value,
            id: file_element.id,
            folderId: file_element.id_folder,
            file: helpers.getFileName(file_element.path),
            srcImgThumb: helpers.setImageThumb(
              helpers.getFileName(file_element.path),
              file_element.path,
            ),
          });
        }
      }

      const metadata = await archeogrid_client.metadata.allMetadata.query({
        branch,
        language,
        type,
        id: file_id,
      });

      helpers.parseAdvancedModelItems(metadata);

      const thesmulti = await archeogrid_client.thesaurus.multiItem.query({
        branch,
        language,
        type,
        item_id: file_id,
      });

      if (metadata.size){
        size = helpers.convertBytes(metadata.size);
      }
      // tous les item de thesaurus multli (sans modèle de métadonnées forcément)
      const datathesPactols = await req.fetchApi(
        `/api/ThesaurusPactolsItem/${branchConfig},${type},${file_id}`,
        {
          lng: res.locals.lang,
        },
      );
      const datathesPactolsGeo = await req.fetchApi(
        `/api/thesaurusPactolsGeoItem/${branchConfig},${type},${file_id}`,
      );
      const datathesMaison = await req.fetchApi(
        `/api/ThesaurusMaisonItem/${branchConfig},${type},${file_id}`,
      );
      let doi_info = {};
      try {
        // First try to get file info from fileFromId endpoint
        const response = await req.fetchApi(`/api/fileFromId/${branch},${file_id}`);
        // Check if the response contains an error property (indicating it's not valid JSON)
        if (response.error) {
          console.error(`Error fetching file info: ${response.error}`);
          // If we have raw content, try to extract useful information
          if (response.rawContent) {
            console.log(`Raw content preview: ${response.rawContent.substring(0, 100)}...`);
            // Try to extract useful information from the raw content
            try {
              // Some APIs might return JSON with incorrect content-type
              const parsedContent = JSON.parse(response.rawContent);
              doi_info = parsedContent;
              console.log('Successfully parsed raw content as JSON');
            } catch (parseError) {
              console.log('Raw content is not valid JSON');

              // Try fallback to prepareImage endpoint if fileFromId fails
              try {
                console.log('Trying fallback to prepareImage endpoint...');
                const fallbackResponse = await req.fetchApi(`/api/prepareImage/${file_id},${branch}`);
                if (!fallbackResponse.error) {
                  doi_info = fallbackResponse;
                  console.log('Successfully fetched file info from fallback endpoint');
                } else {
                  console.error(`Fallback also failed: ${fallbackResponse.error}`);
                }
              } catch (fallbackError) {
                console.error(`Exception in fallback: ${fallbackError}`);
              }
            }
          } else if (response.htmlContent) {
            console.log(`HTML content detected with title: ${response.title || 'Unknown'}`);

            // Try fallback to prepareImage endpoint if fileFromId returns HTML
            try {
              console.log('Trying fallback to prepareImage endpoint...');
              const fallbackResponse = await req.fetchApi(`/api/prepareImage/${file_id},${branch}`);
              if (!fallbackResponse.error) {
                doi_info = fallbackResponse;
                console.log('Successfully fetched file info from fallback endpoint');
              } else {
                console.error(`Fallback also failed: ${fallbackResponse.error}`);
              }
            } catch (fallbackError) {
              console.error(`Exception in fallback: ${fallbackError}`);
            }
          }
        } else {
          doi_info = response;
        }
      } catch (error) {
        console.error(`Exception fetching file info: ${error}`);

        // Try fallback to prepareImage endpoint if fileFromId throws an exception
        try {
          console.log('Trying fallback to prepareImage endpoint after exception...');
          const fallbackResponse = await req.fetchApi(`/api/prepareImage/${file_id},${branch}`);
          if (!fallbackResponse.error) {
            doi_info = fallbackResponse;
            console.log('Successfully fetched file info from fallback endpoint');
          } else {
            console.error(`Fallback also failed: ${fallbackResponse.error}`);
          }
        } catch (fallbackError) {
          console.error(`Exception in fallback: ${fallbackError}`);
        }
      }
      // 27/09/2022 les licenses ne sont plus retournés par cette route
      const iptc_exif = await req.fetchApi(`/api/IPTC_EXIFCode/${branch},${language}`);
      const objectdatafromfile = await req.fetchApi(
        `/api/objectsFromFile/${file_id},${branch}`,
      );

      const objectsViewer = [];
      let nbObjects = 0;
      if (objectdatafromfile) {
        for (let i = 0; i < objectdatafromfile.length; i++) {
          if (objectdatafromfile[i].path) {
            objectsViewer.push({
              type: "object",
              date_integration: objectdatafromfile[i].date_integration,
              root_dir: objectdatafromfile[i].root_dir,
              name: objectdatafromfile[i].name,
              id: objectdatafromfile[i].id_file_representative,
              itemId: objectdatafromfile[i].id,
              itemFolder: objectdatafromfile[i].item_folder,
              filename: helpers.getFileName(objectdatafromfile[i].path),
              idfolder: objectdatafromfile[i].id_folder,
              file: helpers.getFileName(objectdatafromfile[i].path),
              srcImgThumb: helpers.setImageThumb(
                helpers.getFileName(objectdatafromfile[i].path),
                objectdatafromfile[i].path,
              ),
            });
          } else {
            // pas de previsu de l'objet
            objectsViewer.push({
              type: "object",
              date_integration: objectdatafromfile[i].date_integration,
              root_dir: objectdatafromfile[i].root_dir,
              name: objectdatafromfile[i].name,
              id: null,
              itemId: objectdatafromfile[i].id,
              itemFolder: objectdatafromfile[i].item_folder,
              filename: null,
              idfolder: objectdatafromfile[i].id_folder,
              file: null,
              srcImgThumb: null,
            });
          }
        }
      }
      if (objectdatafromfile.length > 0) nbObjects = objectdatafromfile.length;


      const dataTag = await req.fetchApi(`/api/tag/${req.params.root},file,${file_id}`);

      const periodo = await archeogrid_client.thesaurus.thesaurusPeriodO.query({ branch });
      let modelview = (req.params.context === 'v') ? 'visionneusevitrine' : 'visionneuse'
      let layout = (req.params.context === 'v') ? 'layout' : 'layout_simple';

      // Set the branch variable for proper CSS loading
      res.locals.branch = branch;
      // On n'affiche plus les infos de métadonnées des objects rataché à un fichier
      // pour les extension en majuscule, fix bug pour mettre un lien vers le viewer
      data.file_ext = data.file_ext.toLowerCase();
      res.render(modelview, {
        idFile: file_id,
        user: visioUser,
        root: req.params.root,
        branch: branchConfig,
        context: req.params.context,
        folderId: req.params.idFolder,
        image: { ...data, ...image_data },
        size,
        imageFormat: acceptedImageFormats,
        videoFormat: acceptedVideoFormats,
        viewerFormat,
        iptc_exif,
        model: model_list,
        modelComplet,
        metadata,
        objects: objectsViewer,
        modelFile,
        rights,
        HDrights,
        thes,
        tagThesmulti: thesmulti.tagthes,
        tagPactols: datathesPactols,
        tagPactolsGeo: datathesPactolsGeo,
        tagMaison: datathesMaison,
        mainFolder,
        comments,
        viewer: imageViewer,
        nbObjects,
        doi: dataDOI,
        layout: layout,
        unicos,
        thesaurus_info,
        thesaurus_data: thesaurus_info,
        tags: dataTag,
        license: dataLicense,
        doi_info,
        periodo,
        downloadable : status.uploadable,
        lng: res.locals.lang,
        itemType: 'file',
      });
    } else {
      // public folder OK USER NOT logged in necessarily
      // item dans un folder public : tout le monde a accès à la HD et au téléchargement
      // On revoit cette position (01/2025):téléchargeable seulement si dans le statut du dossier, uploadable = 'true'
      HDrights = true;
      // seul admin et scribe peuvent ecire si droits
      // ET le user qui a des droits W (nouveauté 23/03/2020)
      if (req.user) {
        rights =
          (req.user.user_status === "user" && req.user.write.includes(folder_id)) ||
          (req.user.user_status === "scribe" && req.user.read.includes(folder_id)) ||
          req.user.user_status === "admin";
      }
      const data = await archeogrid_client.files.prepareImage.query({ branch, file_id });
      const image_data = {
        urlTarget: "",
        download: "",
        srcImgHd: "",
        srcImg3d: "",
        srcImgThumb: "",
        srcImgSmall: "",
      };

      const file = data.filename;
      const file_path = data.path;
      const short_path = `${file_path.substring(0, file_path.lastIndexOf("/"))}/`;
      image_data.srcImgThumb = helpers.setImageThumb(file, file_path);
      image_data.srcImgSmall = helpers.setImageSmall(file, file_path);
      if (data.file_ext === "url") {
        const contenuUrl = fs.readFileSync(data.path, "utf-8");
        const urlFich = JSON.parse(contenuUrl);
        if (Object.hasOwn(urlFich, "url")) {
          image_data.urlTarget = urlFich.url;
        }
      } else if (data.file_ext === "3d") {
        // TODO : récupérer ce qui a été fait dans la partie explore
        const srcImg3d = md5(file_path);
        store.set(`image3d_${srcImg3d}`, file_path);
        image_data.srcImg3d = srcImg3d;

        if (!data.path) {
          req.flash("error", "Unable to get path for 3d file");
          return;
        }

        const data3d = await readAndParse3DFile(data.path);
        if (data3d == null) {
          req.flash("error", "Unable to load .3d file");
          return;
        }
        if (data3d.type === "3DHOP") {
          if (data3d.data["3dhop"].thumbnail) {
            //thumb_name = path_tocourt + data3d['3dhop']['thumbnail']
            const thumb_name = short_path + data3d.data["3dhop"].thumbnail;
            const srcImgThumb = md5(thumb_name);
            const srcImgSmall = srcImgThumb;
            store.set(`thumbnail_${srcImgThumb}`, thumb_name);
            store.set(`small_${srcImgThumb}`, thumb_name);

            image_data.srcImgThumb = srcImgThumb;
            image_data.srcImgSmall = srcImgSmall;
          }
        } else {
          if (data3d.data.potree.thumbnail) {
            //thumb_name = path_tocourt + data3d['potree']['thumbnail']
            const thumb_name = short_path + data3d.data.potree.thumbnail;
            const srcImgThumb = md5(thumb_name);
            const srcImgSmall = srcImgThumb;
            store.set(`thumbnail_${srcImgThumb}`, thumb_name);
            store.set(`small_${srcImgThumb}`, thumb_name);

            image_data.srcImgThumb = srcImgThumb;
            image_data.srcImgSmall = srcImgSmall;
          }
        }
      } else if (data.file_ext === "json") {
        image_data.srcImgThumb = "json";
        image_data.srcImgSmall = "json";
        image_data.srcImgHd = helpers.setImageHd(file, file_path);
      } else if (data.file_ext === "zip") {
        image_data.srcImgThumb = "zip";
        image_data.srcImgSmall = "zip";
        image_data.srcImgHd = helpers.setImageHd(file, file_path);
      } else if (data.file_ext === "png") {
        image_data.srcImgThumb = helpers.setImageThumbFake(file, file_path);
        image_data.srcImgSmall = helpers.setImageSmallFake(file, file_path);
        image_data.srcImgHd = helpers.setImageHd(file, file_path);
      } else if (acceptedPrevisuFormat.includes(data.file_ext.toLowerCase())) {
        image_data.srcImgThumb = helpers.setImageThumb(file, file_path);
        image_data.srcImgSmall = helpers.setImageSmall(file, file_path);
        image_data.srcImgHd = helpers.setImageHd(file, file_path);
      } else {
        image_data.srcImgThumb = "";
        image_data.srcImgSmall = "";
        image_data.srcImgHd = helpers.setImageHd(file, file_path);
      }
      image_data.srcImgHd = helpers.setImageHd(file, file_path);
      // TODO : watermark ??
      // les modeles seulement pour le projet
      const model = await req.fetchApi(
        `/api/metadatamodelProject/${branch},${mainFolder.mainfolder},${language}`,
      );
      const modellist = [];
      const modelComplet = [];

      // On récupère tous les modèles utilisés par le projet pour le type d'item visionné
      for (const model_item of model) {
        if (model_item.metadata_type === type) {
          modellist.push(model_item.name);
          modelComplet.push({
            name: model_item.name,
            label: model_item.label,
            description: model_item.description,
          });
        }
      }

      const thes = await req.fetchApi(`/api/allThesaurusName/${branch},${language}`);
      const fileViewer = await req.fetchApi(`/api/fileViewer/${branch},${file_id}`);
      const imageViewer = [];

      for (let i = 0; i < fileViewer.length; i++) {
        if (fileViewer[i].path) {
          imageViewer.push({
            value: fileViewer[i].value,
            id: fileViewer[i].id,
            folderId: fileViewer[i].id_folder,
            file: helpers.getFileName(fileViewer[i].path),
            srcImgThumb: helpers.setImageThumb(
              helpers.getFileName(fileViewer[i].path),
              fileViewer[i].path,
            ),
          });
        }
      }

      const metadata = await archeogrid_client.metadata.allMetadata.query({
        branch,
        language,
        type,
        id: file_id,
      });

      helpers.parseAdvancedModelItems(metadata);

      size = helpers.convertBytes(metadata?.size) ?? 0;
      // tous les item de thesaurus multli (sans modèle de métadonnées forcément)
      const thesmulti = await archeogrid_client.thesaurus.multiItem.query({
        branch,
        language,
        type,
        item_id: file_id,
      });

      const datathesPactols = await req.fetchApi(
        `/api/ThesaurusPactolsItem/${branch},${type},${file_id}`,
        {
          lng: res.locals.lang,
        },
      );
      const datathesPactolsGeo = await req.fetchApi(
        `/api/ThesaurusPactolsGeoItem/${branch},${type},${file_id}`,
        {
          lng: res.locals.lang,
        },
      );
      const datathesMaison = await req.fetchApi(
        `/api/ThesaurusMaisonItem/${branch},${type},${file_id}`,
      );
      // 27/09/2022 les licenses ne sont plus retournés par cette route
      let doi_info = {};
      try {
        const response = await req.fetchApi(`/api/fileFromId/${branch},${file_id}`);
        // Check if the response contains an error or text property (indicating it's not valid JSON)
        if (response.error || response.text) {
          console.error(`Error fetching file info: ${response.error || 'Invalid JSON response'}`);
        } else {
          doi_info = response;
        }
      } catch (error) {
        console.error(`Exception fetching file info: ${error}`);
      }
      const iptc_exif = await req.fetchApi(`/api/IPTC_EXIFCode/${branch},${language}`);
      const objectdatafromfile = await req.fetchApi(
        `/api/objectsFromFile/${file_id},${branch}`,
      );

      const objectsViewer = [];
      let nbObjects = 0;
      if (objectdatafromfile) {
        for (let i = 0; i < objectdatafromfile.length; i++) {
          if (objectdatafromfile[i].path) {
            objectsViewer.push({
              type: "object",
              date_integration: objectdatafromfile[i].date_integration,
              root_dir: objectdatafromfile[i].root_dir,
              name: objectdatafromfile[i].name,
              id: objectdatafromfile[i].id_file_representative,
              itemId: objectdatafromfile[i].id,
              itemFolder: objectdatafromfile[i].item_folder,
              filename: helpers.getFileName(objectdatafromfile[i].path),
              idfolder: objectdatafromfile[i].id_folder,
              file: helpers.getFileName(objectdatafromfile[i].path),
              srcImgThumb: helpers.setImageThumb(
                helpers.getFileName(objectdatafromfile[i].path),
                objectdatafromfile[i].path,
              ),
            });
          } else {
            objectsViewer.push({
              type: "object",
              date_integration: objectdatafromfile[i].date_integration,
              root_dir: objectdatafromfile[i].root_dir,
              name: objectdatafromfile[i].name,
              id: null,
              itemId: objectdatafromfile[i].id,
              itemFolder: objectdatafromfile[i].item_folder,
              filename: null,
              idfolder: objectdatafromfile[i].id_folder,
              file: null,
              srcImgThumb: null,
            });
          }
        }
      }
      if (objectdatafromfile.length > 0) nbObjects = objectdatafromfile.length;

      const dataTag = await req.fetchApi(`/api/tag/${req.params.root},file,${file_id}`);

      const periodo = await archeogrid_client.thesaurus.thesaurusPeriodO.query({ branch });
      let modelview = (req.params.context === 'v') ? 'visionneusevitrine' : 'visionneuse'
      let layout = (req.params.context === 'v') ? 'layout' : 'layout_simple';

      // Set the branch variable for proper CSS loading
      res.locals.branch = branch;
      // On n'affiche plus les infos de métadonnées des objects rataché à un fichier
      // pour les extension en majuscule, fix bug pour mettre un lien vers le viewer
      data.file_ext = data.file_ext.toLowerCase();
      res.render(modelview, {
        idFile: file_id,
        user: visioUser,
        root: req.params.root,
        branch: branchConfig,
        context: req.params.context,
        folderId: req.params.idFolder,
        image: { ...data, ...image_data },
        size,
        imageFormat: acceptedImageFormats,
        videoFormat: acceptedVideoFormats,
        viewerFormat,
        iptc_exif,
        model: modellist,
        modelComplet,
        metadata,
        objects: objectsViewer,
        modelFile,
        rights,
        HDrights,
        thes: thes,
        tagThesmulti: thesmulti.tagthes,
        tagPactols: datathesPactols,
        tagPactolsGeo: datathesPactolsGeo,
        tagMaison: datathesMaison,
        mainFolder,
        comments,
        viewer: imageViewer,
        nbObjects,
        doi: dataDOI,
        layout: layout,
        unicos,
        thesaurus_info,
        thesaurus_data: thesaurus_info,
        tags: dataTag,
        license: dataLicense,
        doi_info,
        periodo,
        downloadable: status.uploadable,
        lng: res.locals.lang,
        itemType: 'file',
      });
    }
  }),
);

// Visionneuse for Unico (more simply than file and object
viewer3d.get(
  "/visionneuseUnico,:id-:idFolder,:branche-:context,:rights",
  asyncHandler(
    async (
      req: Request<{
        id: string;
        idFolder: string;
        branche: Branch;
        context: string;
        rights: string;
      }>,
      res,
    ) => {
      const branch = req.params.branche;
      const id = Number.parseInt(req.params.id);
      const folderId = req.params.idFolder;
      const rights = Boolean(Number.parseInt(req.params.rights));
      const language = res.locals.lang;

      const unico = await archeogrid_client.unico.getUnico.query({ branch, id });

      if (!unico) {
        req.flash("error", "No data");
        return;
      }

      const image = await archeogrid_client.files.prepareImage.query({
        branch,
        file_id: unico.id_file,
      });

      const fileInfos = await archeogrid_client.unico.getFileUnicos.query({
        branch,
        language,
        project_id: image.root_folder,
        file_id: unico.id_file,
      });

      const mainFolder = await archeogrid_client.folders.mainFolder.query({
        branch: req.params.branche,
        folder_id: Number.parseInt(req.params.idFolder),
      });

      const thesaurus_info = await archeogrid_client.thesaurus.getAllThesaurusForProject.query({
        branch,
        language,
        project_id: image.root_folder,
      });

      // on récuère les infos de tous les unicos de ce fichier => on ne garde que l'unico qui nous intéresse
      const thesaurus_data = fileInfos.find((info) => info.id === id)?.thesaurus ?? [];

      // Set the branch variable for proper CSS loading
      res.locals.branch = branch;

      // on format l'information pour la récupérer telle qu'elle est dans la visionneuse normale pour
      // utiliser les infos à rediriger dans la modale pour faire une éventuelle mise à jour des infos
      res.render("visionneuseUnico", {
        id,
        mainFolder,
        viewerFormat,
        folderId,
        idFile: image.id,
        unico,
        image,
        rights,
        context: req.params.context,
        thesaurus_info,
        thesaurus_data,
        branch: branch,
        layout: "layout_simple",
        comments: [],
        language
      });
    },
  ),
);

viewer3d.get("/visionneuseUnicoV2,:id_unico,:id_folder,:branch-:contexte,:rights", async (req: Request<{id_unico: string, id_folder: string, branch: Branch, contexte: string}>, res) => {
  const id_unico = parseInt(req.params.id_unico);
  const id_folder = parseInt(req.params.id_folder);
  const branch = req.params.branch;
  const language = res.locals.lang;
  const visioUser = req.user ?? { id: 0, user_status: "guest" };

  // Get the unico data
  let unico = await archeogrid_client.unico.getUnico.query({branch: branch, id: id_unico});

  if (!unico) {
    req.flash("error", "No data");
    return;
  }

  const mainFolder = await archeogrid_client.folders.mainFolder.query({
    branch: req.params.branch,
    folder_id: id_folder,
  });

  if (!mainFolder?.mainfolder) throw new Error("Main folder not found!");

  // Get all project models and filter for unico type
  const model = (await req.fetchApi(`/api/metadatamodelProject/${branch},${mainFolder.mainfolder},${language}`)).filter((m: any) => m.metadata_type === 'unico');

  // Get unico metadata models
  let metadata_models: any[] = [];
  for(let m of model){
    const metadata = await archeogrid_client.unico.getMetadata.query({branch: branch, metadata_model: m.name, language: 'fr', id: id_unico})
    if(metadata.length === 0) continue;
    metadata_models.push({
      name: m.name,
      label: m.label,
      description: m.description,
      metadata
    });
  }

  // Prepare image data for the unico
  const image = await archeogrid_client.files.prepareImage.query({
    branch,
    file_id: unico.id_file,
  });

  // Create unico-specific image data structure for visionneusevitrine
  const image_data = {
    urlTarget: "",
    download: "",
    srcImgHd: `/crop/${id_folder}_${unico.id_file}/${unico.x},${unico.y},${unico.width},${unico.height}`,
    srcImg3d: "",
    srcImgThumb: `/crop/${id_folder}_${unico.id_file}/${unico.x},${unico.y},${unico.width},${unico.height}`,
    srcImgSmall: `/crop/${id_folder}_${unico.id_file}/${unico.x},${unico.y},${unico.width},${unico.height}`,
  };

  const thesaurus_info = await archeogrid_client.thesaurus.getAllThesaurusForProject.query({
    branch,
    language,
    project_id: image.root_folder,
  });

  const fileInfos = await archeogrid_client.unico.getFileUnicos.query({
    branch,
    language,
    project_id: image.root_folder,
    file_id: unico.id_file,
  });

  const thesmulti = await archeogrid_client.thesaurus.multiItem.query({
    branch,
    language,
    type: 'unico',
    item_id: id_unico,
  });

  const datathesPactols = await req.fetchApi(
    `/api/ThesaurusPactolsItem/${branchConfig},unico,${id_unico}`,
    {
      lng: res.locals.lang,
    },
  );
  const datathesPactolsGeo = await req.fetchApi(
    `/api/thesaurusPactolsGeoItem/${branchConfig},unico,${id_unico}`,
  );
  const datathesMaison = await req.fetchApi(
    `/api/ThesaurusMaisonItem/${branchConfig},unico,${id_unico}`,
  );

  let rights = false;
  if (req.user) {
    rights =
      (req.user.user_status === "user" && req.user.write.includes(id_folder)) ||
      (req.user.user_status === "scribe" && req.user.read.includes(id_folder)) ||
      req.user.user_status === "admin";
  }

  const thesaurus_data = fileInfos.find((info) => info.id === id_unico)?.thesaurus ?? [];

  const comments = await archeogrid_client.comments.getComments.query({
    branch,
    type: 'unico',
    user_id: req.user?.id,
    item_id: id_unico,
  });

  const periodo = (await archeogrid_client.thesaurus.thesaurusPeriodO.query({ branch : branch }));

  // Get tags for the unico
  const dataTag = await req.fetchApi(`/api/tag/${branch},unico,${id_unico}`);

  // Get license data
  const dataLicense = await req.fetchApi(
    `/api/licenseItem/${branch},${id_unico},unico,${language}`,
  );

  // Prepare metadata in the format expected by visionneusevitrine
  const metadata: any = {};
  for (let model_data of metadata_models) {
    for (let meta_item of model_data.metadata) {
      if (!metadata[model_data.name]) {
        metadata[model_data.name] = [];
      }
      metadata[model_data.name].push(meta_item);
    }
  }

  // Prepare model data for visionneusevitrine
  const modellist: string[] = [];
  const modelComplet: any[] = [];
  for (let model_data of metadata_models) {
    modellist.push(model_data.name);
    modelComplet.push({
      name: model_data.name,
      label: model_data.label,
      description: model_data.description,
    });
  }

  let modelview = (req.params.contexte === 'v') ? 'visionneusevitrine' : 'visionneusevitrine';
  let layout = (req.params.contexte === 'v') ? 'layout' : 'layout_simple';

  // Set the branch variable for proper CSS loading
  res.locals.branch = branch;

  let modelFile = "DublinCore"; // Default model

  // Render visionneusevitrine with unico-specific data
  res.render(modelview, {
    // Unico-specific data
    idUnico: id_unico,
    unico: unico,
    modelFile: modelFile,
    // Standard visionneusevitrine data adapted for unico
    idFile: image.id,
    user: visioUser,
    root: branch,
    context: req.params.contexte,
    folderId: id_folder,
    image: { ...image, ...image_data, tag: dataTag.map((tag: any) => tag.name) },
    imageFormat: acceptedImageFormats,
    videoFormat: acceptedVideoFormats,
    viewerFormat,
    model: modellist,
    modelComplet: modelComplet,
    metadata: metadata,
    items: [], // No associated items for unicos
    modelObject: modellist.length > 0 ? modellist[0] : "DublinCore",
    Wrights: rights,
    HDrights: true, // Always allow HD for unicos
    thes: [], // Will be populated by thesaurus_info
    tagThesmulti: thesmulti.tagthes,
    tagPactols: datathesPactols,
    tagPactolsGeo: datathesPactolsGeo,
    tagMaison: datathesMaison,
    mainFolder: mainFolder,
    comments: comments,
    viewer: [], // No viewer data for unicos
    nbObjects: 0,
    nbFiles: 0,
    nbJson: 0,
    tags: dataTag,
    itemType: 'unico', // Special type for unicos
    layout: layout,
    license: dataLicense,
    periodo: periodo,
    lng: res.locals.lang,
    unicos: [], // Empty for individual unico view
    downloadable: true,
    rights: rights,
    doi: { doi: false },
    doi_info: {},
    size: "",
    thesaurus_info: thesaurus_info,
    thesaurus_data: thesaurus_data,
    language: language
  });
});

export default viewer3d;
