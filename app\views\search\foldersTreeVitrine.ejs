<%# Simple tree-like folder view for vitrine pages %>
<%
// Helper function to check if a folder is selected
function isFolderSelected(folderId) {
  return folderId == selectedFolder;
}

// Helper function to calculate the immediate file count for a folder
function calculateImmediateFolderCount(folder, isVirtual) {
  if (isVirtual) {
    const nbImages = folder.nb_images || 0;
    const nbObjects = folder.nb_objects || 0;
    const nbUnicos = folder.nb_unicos || 0;
    return nbImages + nbObjects + nbUnicos;
  } else {
    return folder.nb_images || 0;
  }
}

// Helper function to calculate the total file count for a folder (including subtree)
function calculateTotalFolderCount(folder, isVirtual) {
  if (isVirtual) {
    if (typeof folder.nb_tot_items !== 'undefined' && folder.nb_tot_items !== null) {
      return folder.nb_tot_items;
    }
    else if (
      (typeof folder.nb_tot_images !== 'undefined' && folder.nb_tot_images !== null) ||
      (typeof folder.nb_tot_objects !== 'undefined' && folder.nb_tot_objects !== null) ||
      (typeof folder.nb_tot_unicos !== 'undefined' && folder.nb_tot_unicos !== null)
    ) {
      const nbTotImages = folder.nb_tot_images || 0;
      const nbTotObjects = folder.nb_tot_objects || 0;
      const nbTotUnicos = folder.nb_tot_unicos || 0;
      return nbTotImages + nbTotObjects + nbTotUnicos;
    }
    else {
      const nbImages = folder.nb_images || 0;
      const nbObjects = folder.nb_objects || 0;
      const nbUnicos = folder.nb_unicos || 0;
      return nbImages + nbObjects + nbUnicos;
    }
  } else {
    if (typeof folder.nb_tot_images !== 'undefined' && folder.nb_tot_images !== null) {
      return folder.nb_tot_images;
    }
    else {
      return folder.nb_images || 0;
    }
  }
}
%>

<div id="folders-container">
  <% if (tree && tree.length > 0) { %>
    <%
    const processedFolders = new Set(); // Track folders we've already processed
    const normalRootFolders = [];
    const virtualRootFolders = [];
    const folderMap = {};

    // First pass: create folder objects and mark virtual folders
    tree.forEach(folder => {
      // Only folders that are explicitly marked as virtual should be in Collections
      const isVirtual = folder.virtual === 1 || folder.folder_name === null;
      folderMap[folder.id] = {
        ...folder,
        children: [],
        isVirtual: isVirtual,
        virtual: isVirtual ? 1 : (folder.virtual || 0)
      };
    });

    // Second pass: build parent-child relationships
    tree.forEach(folder => {
      if (processedFolders.has(folder.id)) return;

      processedFolders.add(folder.id);

      const folderObj = folderMap[folder.id];

      if (!folder.id_parent) {
        if (folderObj.isVirtual) {
          virtualRootFolders.push(folderObj);
        } else {
          normalRootFolders.push(folderObj);
        }
      } else if (folderMap[folder.id_parent]) {
        folderMap[folder.id_parent].children.push(folderObj);
      } else {
        if (folderObj.isVirtual) {
          virtualRootFolders.push(folderObj);
        } else {
          normalRootFolders.push(folderObj);
        }
      }
    });

    // Third pass: separate virtual folder trees from normal folder trees
    // This ensures virtual folders and their descendants are in the Collections section
    function extractVirtualFolderTrees() {
      function isInVirtualTree(folderId, visited = new Set()) {
        if (visited.has(folderId)) return false; // Prevent infinite loops
        visited.add(folderId);

        const folder = folderMap[folderId];
        if (!folder) return false;

        if (folder.isVirtual || folder.virtual === 1 || folder.folder_name === null) return true;

        if (folder.id_parent) {
          return isInVirtualTree(folder.id_parent, visited);
        }

        return false;
      }

      function processAllFolders() {
        const explicitVirtualFolderIds = new Set();

        Object.values(folderMap).forEach(folder => {
          if (folder.isVirtual || folder.virtual === 1 || folder.folder_name === null) {
            explicitVirtualFolderIds.add(folder.id);
          }
        });

        const foldersToMove = [];
        normalRootFolders.forEach((folder, index) => {
          if (explicitVirtualFolderIds.has(folder.id)) {
            foldersToMove.push({ folder, index });
          }
        });

        for (let i = foldersToMove.length - 1; i >= 0; i--) {
          const { folder, index } = foldersToMove[i];
          normalRootFolders.splice(index, 1);
          virtualRootFolders.push(folder);
        }
      }

      processAllFolders();

      function extractVirtualChildren() {
        let changed = false;

        function extractFromFolder(parentFolder) {
          if (!parentFolder.children || parentFolder.children.length === 0) return false;

          const virtualChildren = [];
          const normalChildren = [];

          parentFolder.children.forEach(child => {
            if (child.isVirtual || child.virtual === 1 || child.folder_name === null) {
              virtualChildren.push(child);
            } else {
              normalChildren.push(child);
              extractFromFolder(child);
            }
          });

          if (virtualChildren.length > 0) {
            parentFolder.children = normalChildren;

            virtualChildren.forEach(child => {
              child.id_parent = null;
              virtualRootFolders.push(child);
            });

            changed = true;
          }

          return changed;
        }

        normalRootFolders.forEach(folder => {
          extractFromFolder(folder);
        });

        return changed;
      }

      let iterations = 0;
      while (extractVirtualChildren() && iterations < 5) {
        iterations++;
      }
    }

    extractVirtualFolderTrees();

    function renderFolders(folders, ulId) {
      %>
      <ul id="<%= ulId %>" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
        <%
        folders.forEach(folder => {
          const hasAccess = user.read.indexOf(folder.id) !== -1 || user.user_status === 'admin';
          if (hasAccess) {
            renderFolder(folder, 0);
          }
        });
        %>
      </ul>
      <%
    }

    function renderFolder(folder, level) {
      const hasAccess = user.read.indexOf(folder.id) !== -1 || user.user_status === 'admin';
      if (!hasAccess) return;

      const hasChildren = folder.children && folder.children.length > 0;
      const isSelected = isFolderSelected(folder.id);
      const isVirtual = folder.isVirtual || folder.virtual === 1 || folder.folder_name === null;
      %>
      <li class="<%- isSelected ? 'folder-selected' : '' %> <%- isVirtual ? 'virtual' : '' %>"
          folderId="<%- folder.id %>"
          rank="<%- folder.global_rank || '' %>"
          data-level="<%- level %>">
        <div class="d-flex align-items-center">
          <% if (hasChildren) { %>
            <span class="folder-parent"></span>
          <% } else { %>
            <span class="folder-spacer"></span>
          <% } %>
          <div class="folder-name-container">
            <span class="folder-name" title="<%- folder.name %>" data-full-name="<%- folder.name %>">
              <i class="fas fa-folder"></i> <%- folder.name %>
            </span>
            <span class="folder-number">
              <%
              const immediateCount = calculateImmediateFolderCount(folder, isVirtual);
              const totalCount = calculateTotalFolderCount(folder, isVirtual);
              %>
              <% if (immediateCount > 0) { %>
                [<%- immediateCount %>]
                <% if (totalCount !== immediateCount && totalCount > 0) { %>(<%- totalCount %>)<% } %>
              <% } else if (totalCount > 0) { %>
                (<%- totalCount %>)
              <% } %>
            </span>
          </div>
        </div>

        <% if (hasChildren) { %>
          <ul class="folder-children">
            <% folder.children.forEach(child => {
              renderFolder(child, level + 1);
            }); %>
          </ul>
        <% } %>
      </li>
      <%
    }

    %>
      <div class="project-section">
        <div class="project-header">
          <span class="project-name"><a href="/projectV/<%- projectId %>" class="header-link"><%-__('Project')%> <%- project && project.data ? project.data.name : '' %></a></span>
        </div>
      </div>
    <%# Options Header Section - Start %>
    <% if (typeof accessProject !== 'undefined' && accessProject) { %>
      <div class="custom-options-area">
        <%# Comments Link %>
        <% if (typeof comment !== 'undefined' && comment && typeof comment.length !== 'undefined' && typeof user !== 'undefined' && user && typeof user.user_status !== 'undefined' && (user.user_status !== 'guest') && (comment.length > 0)) { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="javascript:;"
                   class="header-link"
                   onclick="getComments('<%- branch %>', '<%- projectId %>', '<%- user.id %>', '<%- lng %>', '<%- comment.length %>');">
                  <i class="far fa-comments"></i> <%- __('note') %><% if (comment.length > 1) { %>s<% } %>
                </a>
              </span>
            </div>
          </div>
        <% } %>
  
        <%# Thesaurus Links %>
        <% let varThesinfo = 0; %>
        <% if (typeof thesinfo !== 'undefined' && typeof thesSimpleinfo !== 'undefined' && ((thesinfo === 'yes') || (thesSimpleinfo === 'yes')) ) { varThesinfo = 1; } %>
        
        <% if (varThesinfo === 1 ) { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="/thesaurusV/<%- branch %>,<%- projectId %>" class="header-link">
                  <i class="fas fa-book"></i> <%- __('thesaurusOnly').charAt(0).toUpperCase() + __('thesaurusOnly').slice(1) %>
                </a>
              </span>
            </div>
          </div>
        <% } else if (varThesinfo === 0) { if (typeof branch !== 'undefined' && branch !== 'corpus') { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="/thesaurusPactolsV/<%- branch %>,<%- projectId %>" class="header-link">
                  <i class="fas fa-book"></i> <%- __('thesaurusOnly').charAt(0).toUpperCase() + __('thesaurusOnly').slice(1) %> Pactols
                </a>
              </span>
            </div>
          </div>
        <% } } %>
        
        <%# Unicos Link %>
        <% if (typeof unicosNb !== 'undefined' && unicosNb) { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="javascript:;" onclick="exploreUnicos('<%- projectId %>');" class="header-link">
                    <i class="fa fa-file-image" aria-hidden="true"></i> Unicos
                </a>
              </span>
            </div>
          </div>
        <% } %>
      </div>
      <% if ( (typeof comment !== 'undefined' && comment && comment.length > 0 && user.user_status !== 'guest') || (varThesinfo === 1) || (varThesinfo === 0 && typeof branch !== 'undefined' && branch !== 'corpus') || (typeof unicosNb !== 'undefined' && unicosNb) ) { %>
        <div class="tree-separator"></div>
      <% } %>
    <% } %>
    <%# Options Header Section - End %>
    <%

    %>
      <div class="folders-section">
        <div class="folders-header">
          <span><a href="/projectV/<%- projectId %>" class="header-link"><%-__('foldersHeader')%></a></span>
        </div>
        <div class="folders-tree-container">
        <%
        if (normalRootFolders.length > 0) {
          const allChildFolders = [];
          normalRootFolders.forEach(rootFolder => {
            if (rootFolder.children && rootFolder.children.length > 0) {
              allChildFolders.push(...rootFolder.children);
            }
          });

          if (allChildFolders.length > 0) {
            %>
            <ul id="normal-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
              <%
              allChildFolders.forEach(folder => {
                const hasAccess = user.read.indexOf(folder.id) !== -1 || user.user_status === 'admin';
                if (hasAccess) {
                  renderFolder(folder, 0);
                }
              });
              %>
            </ul>
            <%
          } else {
            renderFolders(normalRootFolders, "normal-folders-tree");
          }
        } else { %>
          <ul id="normal-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
            <li><span><%-__('noFolders')%></span></li>
          </ul>
        <% } %>
        </div>
      </div>
    <%

    %>
      <div class="tree-separator"></div>
      <div class="collections-section">
        <div class="collections-header">
          <span><a href="/projectV/<%- projectId %>?collections=true" class="header-link"><%-__('collections')%></a></span>
          <button id="add-collection-btn" class="btn btn-sm" title="<%-__('createVirtualFolder')%>"><i class="fas fa-plus"></i></button>
        </div>
        <div class="folders-tree-container">
        <%
        if (virtualRootFolders.length > 0) {
          renderFolders(virtualRootFolders, "virtual-folders-tree");
        } else { %>
          <ul id="virtual-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
            <li><span><%-__('noVirtualFolders')%></span></li>
          </ul>
        <% } %>
        </div>
      </div>
  <% } else { %>
    <div class="project-section">
      <div class="project-header">
        <span class="header-link project-name"><a href="/projectV/<%- projectId %>"><%-__('Project')%> <%- project && project.data ? project.data.name : '' %></a></span>
      </div>
    </div>
    <%# Options Header Section - Start %>
    <% if (typeof accessProject !== 'undefined' && accessProject) { %>
      <div class="custom-options-area">
        <%# Comments Link %>
        <% if (typeof comment !== 'undefined' && comment && typeof comment.length !== 'undefined' && typeof user !== 'undefined' && user && typeof user.user_status !== 'undefined' && (user.user_status !== 'guest') && (comment.length > 0)) { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="javascript:;"
                   class="header-link"
                   onclick="getComments('<%- branch %>', '<%- projectId %>', '<%- user.id %>', '<%- lng %>', '<%- comment.length %>');">
                  <i class="far fa-comments"></i> <%- __('note') %><% if (comment.length > 1) { %>s<% } %>
                </a>
              </span>
            </div>
          </div>
        <% } %>
  
        <%# Thesaurus Links %>
        <% let varThesinfo = 0; %>
        <% if (typeof thesinfo !== 'undefined' && typeof thesSimpleinfo !== 'undefined' && ((thesinfo === 'yes') || (thesSimpleinfo === 'yes')) ) { varThesinfo = 1; } %>
        
        <% if (varThesinfo === 1 ) { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="/thesaurusV/<%- branch %>,<%- projectId %>" class="header-link">
                  <i class="fas fa-book"></i> <%- __('thesaurusOnly').charAt(0).toUpperCase() + __('thesaurusOnly').slice(1) %>
                </a>
              </span>
            </div>
          </div>
        <% } else if (varThesinfo === 0) { if (typeof branch !== 'undefined' && branch !== 'corpus') { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="/thesaurusPactolsV/<%- branch %>,<%- projectId %>" class="header-link">
                  <i class="fas fa-book"></i> <%- __('thesaurusOnly').charAt(0).toUpperCase() + __('thesaurusOnly').slice(1) %> Pactols
                </a>
              </span>
            </div>
          </div>
        <% } } %>
        
        <%# Unicos Link %>
        <% if (typeof unicosNb !== 'undefined' && unicosNb) { %>
          <div class="options-section">
            <div class="options-entry-header">
              <span>
                <a href="javascript:;" onclick="exploreUnicos('<%- projectId %>');" class="header-link">
                    <i class="fa fa-file-image" aria-hidden="true"></i> Unicos
                </a>
              </span>
            </div>
          </div>
        <% } %>
      </div>
      <% if ( (typeof comment !== 'undefined' && comment && comment.length > 0 && user.user_status !== 'guest') || (varThesinfo === 1) || (varThesinfo === 0 && typeof branch !== 'undefined' && branch !== 'corpus') || (typeof unicosNb !== 'undefined' && unicosNb) ) { %>
        <div class="tree-separator"></div>
      <% } %>
    <% } %>
    <%# Options Header Section - End %>

    <div class="folders-section">
      <div class="folders-header">
        <a href="/projectV/<%- projectId %>" class="header-link"><%-__('foldersHeader')%></a>
      </div>
      <div class="folders-tree-container">
        <ul id="normal-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
          <li><span><%-__('noFolders')%></span></li>
        </ul>
      </div>
    </div>

    <div class="tree-separator"></div>
    <div class="collections-section">
      <div class="collections-header">
        <a href="/projectV/<%- projectId %>?collections=true" class="header-link"><%-__('collections')%></a>
        <button id="add-collection-btn" class="btn btn-sm" title="<%-__('createVirtualFolder')%>"><i class="fas fa-plus"></i></button>
      </div>
      <div class="folders-tree-container">
        <ul id="virtual-folders-tree" class="folders-tree pt-1 px-2" data-user-status="<%- user.user_status %>" data-lng="<%- lng %>" data-user-write="<%- JSON.stringify(user.write) %>">
          <li><span><%-__('noVirtualFolders')%></span></li>
        </ul>
      </div>
    </div>
  <% } %>
</div>

<!-- Styles moved to folders-tree-vitrine.css -->

<%# Define utility functions that can be called from other parts of the template %>
<%
function expandTree() { %>
  <script>expandTreeNodes();</script>
<% } %>

<%
function collapseTree() { %>
  <script>collapseTreeNodes();</script>
<% } %>

<%
function emptyTree() { %>
  <script>clearTreeSelection();</script>
<% } %>

<%# Add data attributes to the folders container for JavaScript access %>
<script>
  $(document).ready(function() {
    $('#folders-container').attr({
      'data-user-status': '<%- user.user_status %>',
      'data-lng': '<%- lng %>',
      'data-user-write': '<%- JSON.stringify(user.write) %>'
    });

    initFoldersTreeVitrine({
      projectId: '<%- projectId %>',
      lng: '<%- lng %>',
      selectedFolder: typeof selectedFolder !== 'undefined' ? selectedFolder : null
    });

    if (typeof setupRightClickMenu === 'function') {
      setupRightClickMenu();
    } else {
      console.error('setupRightClickMenu function is not available!');
    }
  });
</script>
