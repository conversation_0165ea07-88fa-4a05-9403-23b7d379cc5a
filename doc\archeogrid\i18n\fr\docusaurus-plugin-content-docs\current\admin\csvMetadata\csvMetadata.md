---
sidebar_position: 1
---

# Métadonnées CSV

Ensemble de fonctionnalités permettant d'introduire de nouvelles tables et modèles de métadonnées dans ArcheoGRID via un fichier CSV. Cet ensemble de fonctionnalités est disponible pour toutes les branches d'ArcheoGRID (pft3d, cnd3d et corpus).

Il y a trois fonctionnalités principales:
- Créer une table dans la base de données à partir des colonnes d'un fichier CSV.
- Créer un modèle de métadonnées à partir des colonnes d'un fichier CSV.
- Ingestion des données du CSV dans la table et/ou le modèle de métadonnées.

![](menuCsvmetadata-FR.png)

Pour être pleinement utilisable, les trois étapes ci-dessus doivent être exécutées dans l'ordre (création de table -> création de modèle -> ingestion de CSV). Pour accéder aux différentes étapes pour un projet, vous devez avoir des autorisations sur le projet.

> **Note**: Pour extraire les colonnes d'un fichier CSV, il doit avoir un en-tête.

La première colonne du fichier CSV doit être le nom de l'élément que les autres colonnes décrivent, tel qui est indiqué dans la base de données. Un élément peut être un fichier, un objet ou un dossier. Les fichiers csv doivent être placés dans un dossier appelé 'metadata' à la racine du projet.

